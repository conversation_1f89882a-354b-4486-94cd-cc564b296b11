# Claude Context & Project Information

## 🤖 FOR CLAUDE: MANDATORY WORKFLOW - READ FIRST
**BEFORE ANY CODE WORK:**
1. ✅ **ALWAYS read** `/Users/<USER>/Working/VibeCoding/WordFormation/documentation/DEVELOPMENT_STANDARDS.md`
2. ✅ **ALWAYS verify** TypeScript compilation with `npx tsc --noEmit`
3. ✅ **ALWAYS check** ESLint compliance with `npm run lint`
4. ✅ **ALWAYS follow** 3-file pattern: `.tsx` (UI only) → `.handler.ts` (logic) → `.module.css` (optional)
5. ✅ **ALWAYS use** service layer - NO direct API calls in components
6. ✅ **FOR DATABASE WORK** - ALWAYS use Supabase MCP tools for schema inspection, data queries, and database operations

## 🚨 NON-NEGOTIABLES (MUST STOP IF VIOLATED)
- ❌ **NEVER** use `any` types - Use proper TypeScript
- ❌ **NEVER** allow TypeScript compilation errors
- ❌ **NEVER** put business logic in .tsx files
- ❌ **NEVER** make direct API calls from components
- ❌ **NEVER** violate import order conventions
- ✅ **ALWAYS** use handler hooks for state management
- ✅ **ALWAYS** follow 3-file pattern for components
- ✅ **ALWAYS** include proper error handling

## ✅ SUCCESS CRITERIA FOR EVERY TASK
- Zero TypeScript errors: `npx tsc --noEmit`
- All components follow 3-file pattern
- Business logic in `.handler.ts` files only
- Service layer used for all API calls
- ESLint passes: `npm run lint`
- Tests passing: `npm test`

## 🔄 GIT COMMIT WORKFLOW (MANDATORY)
**BEFORE EVERY COMMIT:**
1. ✅ **ALWAYS** run TypeScript compilation: `npx tsc --noEmit`
2. ✅ **ALWAYS** run tests: `npm test`
3. ✅ **ALWAYS** verify no broken functionality
4. ✅ **ALWAYS** write descriptive commit message with migration context
5. ✅ **ALWAYS** include 🤖 Generated with Claude Code footer

**COMMIT MESSAGE FORMAT:**
```
<type>: <short description>

<detailed explanation>
- What was changed
- Why it was changed  
- Any migration phase context

```

## 📋 QUICK VALIDATION CHECKLIST
Before finishing any code task:
- [ ] Read relevant sections of CODING_STANDARDS.md
- [ ] TypeScript compiles without errors
- [ ] ESLint passes without warnings
- [ ] 3-file pattern maintained
- [ ] No direct API calls in .tsx files
- [ ] Proper import order followed

## Project Overview
**Word Formation Quiz Platform** - A comprehensive language learning application with user-facing frontend and admin content management portal.

📚 **For complete project understanding, see the unified documentation:**
- **[README.md](./README.md)** - Project overview, quick start, and key achievements
- **[Technical Architecture](./documentation/TECHNICAL_ARCHITECTURE.md)** - System design and architectural patterns
- **[Development Standards](./documentation/DEVELOPMENT_STANDARDS.md)** - Complete coding guidelines and practices
- **[CSS Architecture](./documentation/CSS_ARCHITECTURE.md)** - Styling system and shared modules documentation

## Quick Reference Commands
```bash
# Frontend Development
cd wf-frontend && npm run dev        # Runs on http://localhost:5173
cd wf-frontend && npm run build      # Production build
cd wf-frontend && npm run test       # Run tests

# Admin Portal Development  
cd wf-admin-portal && npm run dev    # Runs on http://localhost:3001
cd wf-admin-portal && npm run build  # Production build

# Database Operations (ALWAYS USE SUPABASE MCP)
# Use mcp__supabase__* commands for all database operations
```

## Project Structure (🚧 MIGRATION IN PROGRESS)
```
WordFormation/
├── wf-shared/                    # ✅ Safe shared code (types, constants, utils)
├── wf-frontend/                  # ✅ RENAMED: frontend → wf-frontend  
├── wf-admin-portal/              # ✅ RENAMED: admin-portal → wf-admin-portal
├── database/                     # SQL schema and sample data
├── documentation/                # Project docs and specifications
├── .claude/                     # Claude context files
└── HYBRID_ARCHITECTURE_MIGRATION_PLAN.md  # 📋 Migration tracking
```

## ✅ MIGRATION STATUS: ALL PHASES COMPLETED
**STATUS**: Hybrid architecture migration successfully completed with security validation
**ACHIEVEMENTS**: 
- CSS Modules: 62% admin portal reduction, 19% frontend reduction
- Bundle isolation validated - no admin code leakage
- Repository pattern fully implemented
- Comprehensive shared modules system established

**CURRENT FOCUS**: Documentation unification and maintenance

## Key Technologies
- **Frontend Framework**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS + Custom Design System
- **State Management**: TanStack Query (React Query)
- **Backend**: Supabase (PostgreSQL + Auth + APIs)
- **Database Tools**: Supabase MCP for direct database operations
- **UI Components**: Radix UI + Custom Components
- **Icons**: Lucide React
- **Testing**: Vitest + React Testing Library

## Database Schema (Supabase)
- **Core Tables**: users, levels, categories, quizzes, questions, question_options
- **Quiz System**: quiz_types, quiz_attempts, user_answers, explanations
- **Progress Tracking**: user_progress, badges, user_badges
- **Levels**: A1, A2, B1, B2, C1, C2 (CEFR standard)
- **Categories**: grammar, vocabulary, reading, etc.

## 🗄️ Supabase MCP Tools (MANDATORY FOR DATABASE WORK)

### Schema Inspection Commands
```bash
mcp__supabase__list_tables              # List all tables and their schemas
mcp__supabase__list_extensions          # Show installed Postgres extensions
mcp__supabase__list_migrations          # View migration history
```

### Data Operations Commands  
```bash
mcp__supabase__execute_sql              # Run SELECT, INSERT, UPDATE, DELETE queries
mcp__supabase__apply_migration          # Apply DDL schema changes
mcp__supabase__generate_typescript_types # Generate types after schema changes
```

### Development & Debugging
```bash
mcp__supabase__get_logs                 # Check service logs (api, postgres, auth, etc.)
mcp__supabase__get_advisors             # Security and performance recommendations
mcp__supabase__get_project_url          # Get current project API URL
mcp__supabase__get_anon_key            # Get anonymous API key
```

### Branch Management (for larger changes)
```bash
mcp__supabase__list_branches           # List development branches
mcp__supabase__create_branch           # Create isolated dev environment
mcp__supabase__merge_branch            # Merge changes to production
```

### 🚨 MANDATORY MCP USAGE RULES
- ❌ **NEVER** run raw SQL commands via Bash tool
- ❌ **NEVER** modify database without MCP tools
- ✅ **ALWAYS** use `mcp__supabase__list_tables` before schema work
- ✅ **ALWAYS** use `mcp__supabase__execute_sql` for data queries
- ✅ **ALWAYS** use `mcp__supabase__apply_migration` for schema changes
- ✅ **ALWAYS** regenerate types with `mcp__supabase__generate_typescript_types`
- ✅ **ALWAYS** check advisors after schema changes with `mcp__supabase__get_advisors`

## 📋 Unified Documentation Reference
**SINGLE SOURCE OF TRUTH**: All coding standards and guidelines are now unified in one place:

### **[🎯 DEVELOPMENT_STANDARDS.md - THE COMPLETE GUIDE](./documentation/DEVELOPMENT_STANDARDS.md)**
This file contains EVERYTHING you need:
- **🤖 Claude mandatory workflow** (at the top)
- **📋 Development checklists** (pre/during/post development)
- **🚨 Red flags and blockers** (immediate stops)
- **💻 TypeScript standards** and naming conventions
- **⚛️ React patterns** and component architecture
- **🎨 UI patterns** (loading, empty, error states)
- **🔐 Security standards** and input validation
- **🧪 Testing patterns** and requirements

### Supporting Architecture Documentation
- **[Documentation Index](./documentation/README.md)** - 📍 **COMPLETE DOCUMENTATION INDEX**
- **[Technical Architecture](./documentation/TECHNICAL_ARCHITECTURE.md)** - System design and API specifications
- **[CSS Architecture](./documentation/CSS_ARCHITECTURE.md)** - CSS modules and shared styling patterns
- **[Business Documentation](./documentation/business-documentation.md)** - Requirements and user stories

## 🚨 MANDATORY DOCUMENTATION RULES FOR CLAUDE

### ❌ **NEVER CREATE MARKDOWN FILES IN:**
- `wf-frontend/` directory (use documentation/ instead)
- `wf-admin-portal/` directory (use documentation/ instead)  
- `wf-shared/` directory (use documentation/ instead)
- Any `src/` subdirectories
- Any subproject subdirectories

### ✅ **ALWAYS CREATE DOCUMENTATION IN:**
- `/Users/<USER>/Working/VibeCoding/WordFormation/documentation/` folder ONLY
- Use descriptive UPPERCASE names (e.g., `FEATURE_IMPLEMENTATION_GUIDE.md`)
- Update `documentation/README.md` index when adding new files
- Cross-reference related documents

### 📋 **Documentation Location Rules:**
- **Code standards** → `documentation/DEVELOPMENT_STANDARDS.md` (single source of truth)
- **System architecture** → `documentation/TECHNICAL_ARCHITECTURE.md`
- **Implementation guides** → `documentation/[FEATURE]_IMPLEMENTATION_GUIDE.md`
- **User guides** → `documentation/[COMPONENT]_USER_GUIDE.md`
- **API documentation** → `documentation/[SERVICE]_API_GUIDE.md`
- **Testing docs** → `documentation/TESTING_[TYPE].md`

### 🎯 **Why This Matters:**
- **Single source of truth** - No duplicate or scattered documentation
- **Easy maintenance** - All docs in one location
- **Better discoverability** - Complete index in documentation/README.md
- **Consistent structure** - Clear naming and organization patterns

### Templates Available
- **[.claude/templates/](/.claude/templates/)** - Updated component templates (CSS modules ready)

## Current Implementation Status

### ✅ Completed Features
1. **Frontend Application**
   - User authentication & registration
   - Quiz browsing and filtering
   - Quiz taking with multiple modes (practice/test)
   - Progress tracking and results
   - Responsive design with mobile support
   - Multi-language support (EN/VI/FR)

2. **Admin Portal**
   - Dashboard with real-time statistics
   - Quiz management with CRUD operations
   - Question bank with explanations tracking
   - Content validation system
   - Bulk import functionality (CSV/Excel)
   - Settings and configuration

3. **Database & Content**
   - Complete database schema
   - 32+ quiz files imported with proper titles
   - Type safety with generated Supabase types
   - Proper relationships and constraints

### 🔧 Technical Implementation Details

#### Authentication Flow
- Supabase Auth with email/password
- Protected routes with role-based access
- Session persistence and auto-refresh

#### Data Management
- TanStack Query for caching and synchronization
- Optimistic updates for better UX
- Proper error handling with user feedback
- Real-time data updates

#### File Organization
Every page follows the 3-file pattern:
```
PageName/
├── PageName.tsx          # Pure UI component
├── PageName.handler.ts   # All business logic
├── PageName.style.ts     # Style definitions (optional)
└── index.ts             # Clean export
```

## Development Workflow

### Starting Development
1. **Frontend**: `cd frontend && npm run dev`
2. **Admin Portal**: `cd admin-portal && npm run dev`  
3. **Database**: Use Supabase MCP commands for schema changes

### Testing Strategy
- Unit tests for handlers and utilities
- Component tests for UI interactions
- Integration tests for critical user flows
- Manual testing for UX validation

### Code Quality Checks
- TypeScript strict mode enabled
- ESLint + Prettier for code formatting
- Pre-commit hooks for quality assurance
- Component-level error boundaries

## Supabase Configuration
- **Project URL**: https://wumsrmqsqtdwgzmxwpjn.supabase.co
- **Environment**: Development setup with sample data
- **RLS**: Row-level security enabled for data protection
- **API**: Auto-generated APIs for all database operations

## Common Tasks & Solutions

### Adding New Quiz Data (USE SUPABASE MCP)
1. **ALWAYS** use `mcp__supabase__list_tables` to understand current schema
2. **ALWAYS** use `mcp__supabase__execute_sql` to INSERT new quiz data
3. **ALWAYS** verify data integrity with `mcp__supabase__execute_sql` SELECT queries
4. **ALWAYS** check relationships and foreign key constraints
5. Update any affected quiz counts or references in admin portal

### Creating New Admin Features
1. Create page directory with 3-file structure
2. Implement service methods in `services/adminService.ts`
3. Add route to `admin-portal/src/App.tsx`
4. Update navigation in `components/Layout.tsx`

### Frontend Page Development
1. Follow 3-file pattern in `screens/` directory
2. Implement handler with proper state management
3. Use TanStack Query for data fetching
4. Add proper loading, error, and empty states
5. Include responsive design considerations

### Database Schema Changes (USE SUPABASE MCP)
1. **ALWAYS** use `mcp__supabase__list_tables` to inspect current schema
2. **ALWAYS** use `mcp__supabase__execute_sql` for data queries  
3. **ALWAYS** use `mcp__supabase__apply_migration` for schema changes
4. **ALWAYS** use `mcp__supabase__generate_typescript_types` after schema changes
5. Update affected service methods and test data flows

### 🏗️ HYBRID ARCHITECTURE RULES (COMPLETED)
1. **ALWAYS** check migration status in documentation before major changes
2. **ALWAYS** maintain repository pattern: handler → service → repository (api/storage)
3. **NEVER** mix admin and user code in shared packages
4. **SECURITY**: Bundle isolation validated - maintain separation
5. **CSS MODULES**: Use shared patterns from `wf-frontend/src/styles/shared/`

### 🔄 DEVELOPMENT CONTINUITY PROCEDURE
If conversation is interrupted during development:
1. Check current task status in todo list
2. Review relevant documentation sections for context
3. Verify TypeScript compilation: `npx tsc --noEmit`
4. Check git status for uncommitted changes
5. Resume from current task with proper validation

## Environment Variables
```bash
# Frontend (.env)
VITE_SUPABASE_URL=https://wumsrmqsqtdwgzmxwpjn.supabase.co  
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Admin Portal (.env)  
VITE_SUPABASE_URL=https://wumsrmqsqtdwgzmxwpjn.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Performance Considerations
- Lazy loading for route components
- Image optimization for quiz assets
- Database query optimization with proper indexing
- Pagination for large data sets
- Caching strategies with TanStack Query

## Security Implementation
- Input validation on all forms
- SQL injection prevention via Supabase
- XSS protection with proper escaping
- Authentication state management
- Role-based access control

## Deployment & Production
- Frontend: Static site deployment (Vercel/Netlify)
- Admin Portal: Protected deployment with authentication
- Database: Supabase production environment
- Environment-specific configurations
- CI/CD pipeline for automated deployments

## 📚 Documentation Navigation

### Quick Access
- **Getting Started**: [README.md](./README.md) - Start here for project overview
- **Development Rules**: [.claude/CODING_STANDARDS.md](./.claude/CODING_STANDARDS.md) - Mandatory reading
- **Architecture Guide**: [Technical Architecture](./documentation/TECHNICAL_ARCHITECTURE.md) - System design
- **Coding Patterns**: [Development Standards](./documentation/DEVELOPMENT_STANDARDS.md) - Complete guidelines

### Key Achievements Reference
- **CSS Modules**: [CSS Architecture](./documentation/CSS_ARCHITECTURE.md) - 62% admin reduction achieved
- **Security Validation**: Bundle analysis confirms no admin code leakage
- **Performance**: 19% frontend component reduction with shared patterns
- **Type Safety**: TypeScript strict mode across entire codebase

### Additional Resources
- **File Patterns**: Use 3-file pattern (.tsx → .handler.ts → .style.ts)
- **CSS Composition**: Leverage shared modules from `wf-frontend/src/styles/shared/`
- **Database Operations**: Always use Supabase MCP tools for schema work
- **Testing**: Comprehensive unit and integration test coverage

---

**Last Updated**: 2025-07-24 - Documentation unified, architecture migration completed
**Status**: Production Ready ✅ | All phases completed with security validation