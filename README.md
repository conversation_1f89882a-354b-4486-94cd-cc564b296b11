# Word Formation Quiz Platform

> A comprehensive language learning platform with user-facing frontend and admin content management portal.

[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-18+-61DAFB.svg)](https://reactjs.org/)
[![Vite](https://img.shields.io/badge/Vite-4.5+-646CFF.svg)](https://vitejs.dev/)
[![Supabase](https://img.shields.io/badge/Supabase-3FCF8E.svg)](https://supabase.com/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.3+-38B2AC.svg)](https://tailwindcss.com/)

## 🎯 Project Overview

Word Formation Quiz Platform is a modern language learning application built with React, TypeScript, and Supabase. It features a secure hybrid architecture with separate user and admin interfaces, comprehensive CSS modules system, and multi-language support.

### Key Features
- 📚 **Interactive Quiz System** - Word formation exercises with multiple difficulty levels
- 🌍 **Multi-Language Support** - English, Vietnamese, French
- 📊 **Progress Tracking** - Detailed analytics and performance monitoring
- 🎨 **Modern UI/UX** - Responsive design with dark mode support
- 🔐 **Secure Architecture** - Separated user and admin interfaces
- ⚡ **High Performance** - Optimized bundles and caching strategies

## 🏗️ Architecture Overview

```
WordFormation/
├── wf-frontend/          # User-facing React application (Port 5173)
├── wf-admin-portal/      # Admin content management (Port 3001)
├── wf-shared/            # Shared types, utilities, and services
├── database/             # SQL schema and sample data
├── documentation/        # Project documentation
└── .claude/             # Development standards and guidelines
```

### Technology Stack

| Layer | Technology | Purpose |
|-------|------------|---------|
| **Frontend** | React 18 + TypeScript | Modern UI with type safety |
| **Styling** | Tailwind CSS + CSS Modules | Utility-first with shared patterns |
| **State Management** | TanStack Query | Server state and caching |
| **Backend** | Supabase | PostgreSQL database + APIs + Auth |
| **Build System** | Vite | Fast development and optimized builds |
| **Testing** | Vitest + React Testing Library | Unit and integration testing |

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account

### Installation

```bash
# Clone repository
git clone <repository-url>
cd WordFormation

# Install dependencies
npm install

# Install frontend dependencies
cd wf-frontend && npm install

# Install admin portal dependencies
cd ../wf-admin-portal && npm install

# Install shared package dependencies
cd ../wf-shared && npm install
```

### Development Setup

```bash
# Start frontend development server
cd wf-frontend && npm run dev        # http://localhost:5173

# Start admin portal development server  
cd wf-admin-portal && npm run dev    # http://localhost:3001

# Run tests
npm run test

# Type checking
npx tsc --noEmit
```

### Environment Variables

Create `.env` files in both `wf-frontend/` and `wf-admin-portal/`:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 📚 Documentation Structure

### 🎯 **Start Here**
- **README.md** (this file) - Project overview and quick start
- **[CLAUDE.md](./CLAUDE.md)** - Development workflow and coding standards
- **[Technical Architecture](./documentation/TECHNICAL_ARCHITECTURE.md)** - System design and patterns

### 🏗️ **Architecture & Development**
- **[Development Standards](./documentation/DEVELOPMENT_STANDARDS.md)** - Complete coding guidelines
- **[Component Patterns](./documentation/COMPONENT_PATTERNS.md)** - React component best practices
- **[CSS Architecture](./documentation/CSS_ARCHITECTURE.md)** - Styling system and shared modules

### 📈 **Project Evolution**
- **[Migration History](./documentation/MIGRATION_HISTORY.md)** - Major architectural changes
- **[Performance Achievements](./documentation/PERFORMANCE_ACHIEVEMENTS.md)** - Optimization results
- **[Security Validation](./documentation/SECURITY_VALIDATION.md)** - Bundle analysis and isolation

### 🎨 **Frontend Specifics**
- **[wf-frontend/README.md](./wf-frontend/README.md)** - Frontend-specific setup
- **[wf-admin-portal/README.md](./wf-admin-portal/README.md)** - Admin portal setup
- **[wf-shared/README.md](./wf-shared/README.md)** - Shared package usage

### 🔧 **Development Tools**
- **[.claude/CODING_STANDARDS.md](./.claude/CODING_STANDARDS.md)** - Detailed coding rules
- **[.claude/README.md](./.claude/README.md)** - Claude AI development context

## 🏆 Key Achievements

### CSS Modules Transformation
- **62% CSS reduction** in admin portal (2,864 lines eliminated)
- **19% component reduction** in frontend (3,069 → 2,480 lines)
- **Shared modules architecture** with 8 comprehensive modules
- **3.5% bundle size improvement** with better maintainability

### Hybrid Architecture Migration
- **✅ 6-phase migration completed** with security validation
- **Complete code isolation** between user and admin interfaces
- **Bundle analysis confirmed** no admin code leakage
- **Repository pattern implemented** for clean architecture

### Performance & Quality
- **TypeScript strict mode** throughout entire codebase
- **ESLint + Prettier** with pre-commit hooks
- **Comprehensive testing** with Vitest and React Testing Library
- **Optimized builds** with code splitting and lazy loading

## 🛡️ Security Features

- **Role-based authentication** with Supabase Auth
- **Row-level security** in database
- **Bundle isolation** prevents admin code exposure
- **Input validation** and sanitization
- **HTTPS-only** in production

## 🎨 CSS Architecture

The project uses a sophisticated CSS modules system with shared patterns:

### Shared Modules (wf-frontend/src/styles/shared/)
```
shared/
├── components/          # UI component patterns
│   ├── buttons.module.css      (20+ variants)
│   ├── cards.module.css        (20+ variants)  
│   ├── typography.module.css   (25+ variants)
│   └── forms.module.css        (35+ variants)
├── layouts/            # Layout patterns
│   ├── containers.module.css   (15+ variants)
│   └── grids.module.css        (30+ variants)
└── animations/         # Motion design
    ├── transitions.module.css  (25+ variants)
    └── keyframes.module.css    (30+ variants)
```

### Usage Example
```typescript
import { buttonsStyles, cardStyles } from '@/styles/shared';

<button className={buttonsStyles.buttonPrimary}>
  Click me
</button>
```

## 🧪 Testing Strategy

- **Unit Tests**: Component logic and utilities
- **Integration Tests**: Component interactions
- **Bundle Analysis**: Security and performance validation
- **Manual Testing**: User experience validation

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Type checking
npx tsc --noEmit
```

## 📦 Build & Deployment

```bash
# Build frontend
cd wf-frontend && npm run build

# Build admin portal
cd wf-admin-portal && npm run build

# Build shared package
cd wf-shared && npm run build
```

### Bundle Analysis
- **Frontend Bundle**: ~835KB (optimized for users)
- **Admin Bundle**: Separate build (admin functionality isolated)
- **Shared Code**: Common utilities and types

## 🤝 Contributing

### Development Workflow
1. **Read**: [CLAUDE.md](./CLAUDE.md) for mandatory workflow
2. **Follow**: [Development Standards](./documentation/DEVELOPMENT_STANDARDS.md)
3. **Use**: 3-file component pattern (.tsx → .handler.ts → .style.ts)
4. **Test**: TypeScript compilation and ESLint before commits
5. **Validate**: All tests pass and no type errors

### Code Standards
- **TypeScript strict mode** - No `any` types allowed
- **3-file pattern** - Separate UI, logic, and styles
- **Service layer** - No direct API calls in components
- **Shared modules** - Use existing patterns before creating new ones

## 📞 Support & Resources

- **Development Issues**: Check [CLAUDE.md](./CLAUDE.md) workflow
- **Architecture Questions**: Review [Technical Architecture](./documentation/TECHNICAL_ARCHITECTURE.md)
- **CSS Patterns**: Reference [CSS Architecture](./documentation/CSS_ARCHITECTURE.md)
- **Component Examples**: See existing implementations in both applications

## 📄 License

This project is proprietary software for educational purposes.

---

**Last Updated**: 2025-07-24  
**Documentation Version**: 2.0  
**Project Status**: Production Ready ✅