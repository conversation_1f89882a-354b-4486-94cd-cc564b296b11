{"source": {"include": ["./wf-shared/src/", "./wf-frontend/src/", "./wf-admin-portal/src/"], "includePattern": "\\.(ts|tsx|js|jsx)$", "exclude": ["node_modules/", "**/node_modules/", "**/dist/", "**/coverage/", "**/*.test.ts", "**/*.test.tsx", "**/*.test.js", "**/*.test.jsx", "**/__tests__/", "**/test-utils/", "**/test/", "**/types/vite-env.d.ts", "**/types/env.d.ts"]}, "opts": {"destination": "./docs/", "recurse": true, "readme": "./README.md"}, "plugins": ["plugins/markdown", "node_modules/jsdoc-babel"], "babel": {"presets": ["@babel/preset-env", "@babel/preset-typescript"], "plugins": []}, "templates": {"cleverLinks": false, "monospaceLinks": false}, "metadata": {"title": "WordFormation Monorepo Documentation", "description": "Complete API documentation for the WordFormation learning platform"}}