// Import shared types from wf-shared package
export * from 'wf-shared/types'

// Admin-specific types
export interface AdminUser {
  id: string
  email: string
  role: 'admin' | 'editor' | 'curator'
  permissions: string[]
}

export interface QuizStats {
  id: string
  title: string
  totalQuestions: number
  completionRate: number
  averageScore: number
  lastUpdated: string
  status: 'active' | 'draft' | 'archived'
}

export interface ValidationIssue {
  id: string
  type: 'missing_explanation' | 'invalid_option' | 'duplicate_question' | 'formatting_error' | 'orphaned_data' | 'data_inconsistency'
  severity: 'high' | 'medium' | 'low'
  entity: 'quiz' | 'question' | 'option' | 'question_option'
  entityId: string
  message: string
  suggestions?: string[]
}

export interface BulkImportResult {
  success: boolean
  imported: number
  failed: number
  errors: Array<{
    row: number
    field: string
    message: string
  }>
}

// Use actual Supabase types instead of custom interface
import type { SupabaseClient } from '@supabase/supabase-js'
export type SupabaseQueryBuilder = ReturnType<SupabaseClient['from']>

// Dashboard activity and trends types
export interface ActivityTrend {
  date: string
  attempts: number
  totalScore: number
  avgScore: number
}

export interface QuizPerformance {
  quizId: string
  title: string
  attempts: number
  totalScore: number
  avgScore: number
}

// Validation Issue Types already defined above

// Import UserWithAdminRelations from wf-shared instead of defining locally

export interface RecentActivity {
  created_at: string
  quiz_id: string
  quizzes?: { title: string }
}

// Form and mutation types
export interface QuizFormData {
  title: string
  description?: string
  category_id: string
  level_id: string
  quiz_type_id: string
  is_active: boolean
}

export interface QuestionFormData {
  question_text: string
  quiz_id: string
  question_type_id: string
  order_index?: number
  is_active: boolean
}