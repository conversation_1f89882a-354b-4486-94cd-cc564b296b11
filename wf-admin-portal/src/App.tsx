import { Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from '@/components/ui/toaster'
import { AuthProvider, useAuth } from '@/contexts/AuthContext'
import Layout from '@/components/Layout'
import Login from '@/pages/Login'
import Dashboard from '@/pages/Dashboard'
import QuizManagement from '@/pages/QuizManagement'
import QuizView from '@/pages/QuizView'
import QuizEdit from '@/pages/QuizEdit'
import QuizPreview from '@/pages/QuizPreview'
import QuestionBank from '@/pages/QuestionBank'
import UserManagement from '@/pages/UserManagement'
import ContentValidation from '@/pages/ContentValidation'
import BulkImport from '@/pages/BulkImport'
import Settings from '@/pages/Settings'

function AppContent() {
  const { user, isLoading } = useAuth()



  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Development mode bypass - remove this in production
  const isDevelopment = import.meta.env.DEV
  const bypassAuth = isDevelopment && window.location.search.includes('bypass=true')

  return (
    <div className="min-h-screen bg-background">
      <Routes>
        {!user && !bypassAuth ? (
          <>
            <Route path="/login" element={<Login />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </>
        ) : (
          <Route path="/" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="quiz-management" element={<QuizManagement />} />
            <Route path="create-quiz" element={<QuizEdit />} />
            <Route path="quiz/:id/view" element={<QuizView />} />
            <Route path="quiz/:id/edit" element={<QuizEdit />} />
            <Route path="quiz/:id/preview" element={<QuizPreview />} />
            <Route path="questions" element={<QuestionBank />} />
            <Route path="users" element={<UserManagement />} />
            <Route path="validation" element={<ContentValidation />} />
            <Route path="import" element={<BulkImport />} />
            <Route path="settings" element={<Settings />} />
            <Route path="/login" element={<Navigate to="/" replace />} />
          </Route>
        )}
      </Routes>
      <Toaster />

      {/* Development helper */}
      {isDevelopment && !user && (
        <div style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          background: '#f0f0f0',
          padding: '10px',
          borderRadius: '5px',
          fontSize: '12px',
          zIndex: 1000,
        }}>
          <p>Development Mode</p>
          <p>Add ?bypass=true to URL to skip auth</p>
          <p>Or use: <EMAIL></p>
        </div>
      )}
    </div>
  )
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  )
}

export default App