import React, { createContext, useContext, useEffect, useState } from 'react'
import { repositories } from '@/repositories/init'
import type { User } from '@supabase/supabase-js'

interface AuthUser extends User {
  user_metadata: {
    first_name?: string
    last_name?: string
    role?: string
  }
}

interface AuthContextType {
  user: AuthUser | null
  isAdmin: boolean
  isLoading: boolean
  signIn: (email: string, password: string) => Promise<{ error?: any }>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    repositories.container.supabaseClient.auth.getSession().then(({ data: { session } }: any) => {
      setUser(session?.user || null)
      setIsLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = repositories.container.supabaseClient.auth.onAuthStateChange(
      async (_event: any, session: any) => {
        setUser(session?.user || null)
        setIsLoading(false)
      },
    )

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    try {
      console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL)
      console.log('Supabase Key exists:', !!import.meta.env.VITE_SUPABASE_ANON_KEY)
      console.log('Attempting to sign in with:', { email, passwordLength: password.length })
      
      // Test basic connectivity first
      console.log('Testing Supabase connectivity...')
      
      const { data, error } = await repositories.container.supabaseClient.auth.signInWithPassword({
        email,
        password,
      })

      console.log('Supabase auth response:', { 
        user: data?.user?.email, 
        session: !!data?.session,
        error: error ? {
          message: error.message,
          status: error.status,
          name: error.name,
        } : null, 
      })

      if (error) {
        console.error('Authentication failed:', {
          message: error.message,
          status: error.status,
          details: error,
        })
        return { error }
      }

      // TODO: Check if user is admin (role field needs to be added to users table)
      // For now, allow all authenticated users to access admin portal
      if (data.user?.email) {
        console.log('User authenticated successfully:', data.user.email)
        // const { data: userData, error: userError } = await supabase
        //   .from('users')
        //   .select('role')
        //   .eq('email', data.user.email)
        //   .single()

        // if (userError || userData?.role !== 'admin') {
        //   await repositories.container.supabaseClient.auth.signOut()
        //   return { error: { message: 'Access denied. Admin role required.' } }
        // }
      }

      return { error: null }
    } catch (error) {
      console.error('SignIn catch error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      return { error: { message: `Network error: ${errorMessage}` } }
    }
  }

  const signOut = async () => {
    await repositories.container.supabaseClient.auth.signOut()
  }

  const isAdmin = user ? true : false // For now, any authenticated user in admin portal is admin

  const value = {
    user,
    isAdmin,
    isLoading,
    signIn,
    signOut,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}