import { useState, useEffect, useCallback } from 'react'
import { QuestionService } from '@/services/QuestionService'
import { CategoryService, LevelService, QuestionTypeService } from '@/services/ReferenceService'
import { QuizService } from '@/services/QuizService'
import type { QuestionWithRelations, DbCategory, DbLevel, DbQuestionType } from 'wf-shared/types'

interface QuestionFilters {
  search: string
  categoryId: string
  levelId: string
  questionTypeId: string
  difficultyLevel: string
}

interface UseAddQuestionsModalProps {
  quizId: string
  existingQuestionIds?: string[]
}

export const useAddQuestionsModal = ({ quizId, existingQuestionIds = [] }: UseAddQuestionsModalProps) => {
  const [questions, setQuestions] = useState<QuestionWithRelations[]>([])
  const [filteredQuestions, setFilteredQuestions] = useState<QuestionWithRelations[]>([])
  const [selectedQuestionIds, setSelectedQuestionIds] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [adding, setAdding] = useState(false)
  
  // Filter states
  const [filters, setFilters] = useState<QuestionFilters>({
    search: '',
    categoryId: '',
    levelId: '',
    questionTypeId: '',
    difficultyLevel: '',
  })
  const [showFilters, setShowFilters] = useState(false)
  
  // Reference data
  const [categories, setCategories] = useState<DbCategory[]>([])
  const [levels, setLevels] = useState<DbLevel[]>([])
  const [questionTypes, setQuestionTypes] = useState<DbQuestionType[]>([])
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const pageSize = 20
  
  // Configuration
  const [defaultPoints, setDefaultPoints] = useState(1)

  const loadReferenceData = useCallback(async () => {
    try {
      const [categoriesResult, levelsResult, questionTypesResult] = await Promise.all([
        CategoryService.getCategories(),
        LevelService.getLevels(),
        QuestionTypeService.getQuestionTypes(),
      ])

      if (categoriesResult.data) {
        setCategories(categoriesResult.data)
      }
      if (levelsResult.data) {
        setLevels(levelsResult.data)
      }
      if (questionTypesResult.data) {
        setQuestionTypes(questionTypesResult.data)
      }
    } catch (error) {
      // console.error('Failed to load reference data:', error)
    }
  }, [])

  const loadQuestions = useCallback(async () => {
    setLoading(true)
    try {
      const result = await QuestionService.getQuestions({
        page: 1,
        pageSize: 1000, // Load more questions for filtering
        category: '',
        level: '',
        status: '',
      })

      if (result.data) {
        // Filter out questions already in the quiz
        const availableQuestions = result.data.data.filter(q => !existingQuestionIds.includes(q.id))
        // console.log(`Total questions: ${result.data.data.length}, Existing: ${existingQuestionIds.length}, Available: ${availableQuestions.length}`)
        // console.log('Existing question IDs:', existingQuestionIds)
        setQuestions(availableQuestions)
      }
    } catch (error) {
      // console.error('Failed to load questions:', error)
    } finally {
      setLoading(false)
    }
  }, [existingQuestionIds])

  const applyFilters = useCallback(() => {
    let filtered = [...questions]

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(q => 
        q.question_text?.toLowerCase().includes(searchLower) ||
        q.id.toLowerCase().includes(searchLower),
      )
    }

    // Category filter - questions don't have direct category_id, skip for now
    // Level filter - questions don't have direct level_id, skip for now

    // Question type filter
    if (filters.questionTypeId) {
      filtered = filtered.filter(q => q.question_type_id === filters.questionTypeId)
    }

    // Difficulty level filter
    if (filters.difficultyLevel) {
      const difficultyNum = parseInt(filters.difficultyLevel)
      if (difficultyNum <= 2) {
        filtered = filtered.filter(q => (q.difficulty_level || 3) <= 2)
      } else if (difficultyNum === 3) {
        filtered = filtered.filter(q => (q.difficulty_level || 3) === 3)
      } else {
        filtered = filtered.filter(q => (q.difficulty_level || 3) >= 4)
      }
    }

    setFilteredQuestions(filtered)
    setTotalPages(Math.ceil(filtered.length / pageSize))
    setCurrentPage(1)
  }, [questions, filters])

  const handleFilterChange = (key: keyof QuestionFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      categoryId: '',
      levelId: '',
      questionTypeId: '',
      difficultyLevel: '',
    })
  }

  const toggleQuestionSelection = (questionId: string) => {
    setSelectedQuestionIds(prev => 
      prev.includes(questionId)
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId],
    )
  }

  const selectAllVisible = () => {
    const visibleQuestionIds = getCurrentPageQuestions().map(q => q.id)
    setSelectedQuestionIds(prev => [...new Set([...prev, ...visibleQuestionIds])])
  }

  const clearSelection = () => {
    setSelectedQuestionIds([])
  }

  const getCurrentPageQuestions = () => {
    const startIndex = (currentPage - 1) * pageSize
    const endIndex = startIndex + pageSize
    return filteredQuestions.slice(startIndex, endIndex)
  }

  const handleAddQuestions = async () => {
    if (selectedQuestionIds.length === 0) return

    // Check for duplicate questions before attempting to add
    const duplicateQuestions = selectedQuestionIds.filter(id => existingQuestionIds.includes(id))

    if (duplicateQuestions.length > 0) {
      const duplicateCount = duplicateQuestions.length
      const totalSelected = selectedQuestionIds.length

      let errorMessage = ''
      if (duplicateCount === totalSelected) {
        errorMessage = `All ${totalSelected} selected question${totalSelected > 1 ? 's are' : ' is'} already in this quiz.`
      } else {
        errorMessage = `${duplicateCount} of ${totalSelected} selected question${duplicateCount > 1 ? 's are' : ' is'} already in this quiz. Please deselect the duplicate question${duplicateCount > 1 ? 's' : ''} and try again.`
      }

      throw new Error(errorMessage)
    }

    setAdding(true)
    try {
      // console.log(`Attempting to add ${selectedQuestionIds.length} questions to quiz ${quizId}`)
      // console.log('Selected question IDs:', selectedQuestionIds)
      // console.log('Existing question IDs:', existingQuestionIds)

      const result = await QuizService.addQuestionsToQuiz(quizId, selectedQuestionIds, {
        defaultPoints,
      })

      if (result.error) {
        throw new Error(result.error)
      }

      // console.log('Successfully added questions')
      return { success: true }
    } catch (error) {
      // console.error('Failed to add questions:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    } finally {
      setAdding(false)
    }
  }

  const getDifficultyLabel = (level: number) => {
    if (level <= 2) return 'Easy'
    if (level === 3) return 'Medium'
    return 'Hard'
  }

  // Effects
  useEffect(() => {
    applyFilters()
  }, [applyFilters])

  return {
    // Data
    questions,
    filteredQuestions,
    selectedQuestionIds,
    categories,
    levels,
    questionTypes,
    
    // States
    loading,
    adding,
    showFilters,
    filters,
    currentPage,
    totalPages,
    defaultPoints,
    
    // Computed
    getCurrentPageQuestions,
    getDifficultyLabel,
    
    // Actions
    loadReferenceData,
    loadQuestions,
    handleFilterChange,
    clearFilters,
    toggleQuestionSelection,
    selectAllVisible,
    clearSelection,
    handleAddQuestions,
    setShowFilters,
    setCurrentPage,
    setDefaultPoints,
  }
}
