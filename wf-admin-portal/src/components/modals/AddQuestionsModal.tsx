import React, { useEffect } from 'react'
import { X, Search, Filter, Plus, ChevronDown, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { useAddQuestionsModal } from './AddQuestionsModal.handler'
// import type { QuestionWithRelations } from '@/types'

interface AddQuestionsModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
  quizId: string
  quizTitle: string
  existingQuestionIds?: string[]
}

const AddQuestionsModal: React.FC<AddQuestionsModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  quizId,
  quizTitle,
  existingQuestionIds = [],
}) => {
  const [error, setError] = React.useState<string | null>(null)
  const {
    // Data
    filteredQuestions,
    selectedQuestionIds,
    categories,
    levels,
    questionTypes,

    // States
    loading,
    adding,
    showFilters,
    filters,
    currentPage,
    totalPages,
    defaultPoints,

    // Computed
    getCurrentPageQuestions,
    getDifficultyLabel,

    // Actions
    loadReferenceData,
    loadQuestions,
    handleFilterChange,
    clearFilters,
    toggleQuestionSelection,
    selectAllVisible,
    clearSelection,
    handleAddQuestions,
    setShowFilters,
    setCurrentPage,
    setDefaultPoints,
  } = useAddQuestionsModal({ quizId, existingQuestionIds })

  useEffect(() => {
    if (isOpen) {
      loadReferenceData()
      loadQuestions()
    }
  }, [isOpen, loadReferenceData, loadQuestions])

  const handleAddQuestionsClick = async () => {
    setError(null) // Clear any previous errors
    try {
      await handleAddQuestions()
      onSuccess?.()
      onClose()
    } catch (error) {
      // console.error('Failed to add questions:', error)
      setError(error instanceof Error ? error.message : 'Failed to add questions')
    }
  }

  // Clear error when modal closes
  React.useEffect(() => {
    if (!isOpen) {
      setError(null)
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Add Questions to Quiz</h2>
            <p className="text-sm text-gray-600 mt-1">
              Adding questions to: <span className="font-medium">{quizTitle}</span>
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Filters Section */}
        <div className="p-6 border-b bg-gray-50">
          <div className="flex items-center gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search questions by text or ID..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
              <ChevronDown className={`h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </Button>
          </div>

          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  value={filters.categoryId}
                  onChange={(e) => handleFilterChange('categoryId', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Categories</option>
                  {categories.map(cat => (
                    <option key={cat.id} value={cat.id}>{cat.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Level</label>
                <select
                  value={filters.levelId}
                  onChange={(e) => handleFilterChange('levelId', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Levels</option>
                  {levels.map(level => (
                    <option key={level.id} value={level.id}>{level.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Question Type</label>
                <select
                  value={filters.questionTypeId}
                  onChange={(e) => handleFilterChange('questionTypeId', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Types</option>
                  {questionTypes.map(type => (
                    <option key={type.id} value={type.id}>{type.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty</label>
                <select
                  value={filters.difficultyLevel}
                  onChange={(e) => handleFilterChange('difficultyLevel', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Difficulties</option>
                  <option value="1">Easy (1-2)</option>
                  <option value="3">Medium (3)</option>
                  <option value="4">Hard (4-5)</option>
                </select>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">
                {filteredQuestions.length} questions found
              </span>
              {selectedQuestionIds.length > 0 && (
                <Badge variant="secondary">
                  {selectedQuestionIds.length} selected
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={selectAllVisible}>
                Select Page
              </Button>
              <Button variant="outline" size="sm" onClick={clearSelection}>
                Clear All
              </Button>
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Clear Filters
              </Button>
            </div>
          </div>
        </div>

        {/* Questions List */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-gray-500">Loading questions...</div>
            </div>
          ) : getCurrentPageQuestions().length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-gray-500">No questions found matching your criteria</div>
            </div>
          ) : (
            <div className="space-y-3">
              {getCurrentPageQuestions().map((question) => (
                <Card key={question.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={selectedQuestionIds.includes(question.id)}
                        onCheckedChange={() => toggleQuestionSelection(question.id)}
                        className="mt-1"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-4">
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900 line-clamp-2">
                              {question.question_text}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">ID: {question.id}</p>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            <Badge variant="outline" className="text-xs">
                              {getDifficultyLabel(question.difficulty_level || 3)}
                            </Badge>
                            {question.question_types && (
                              <Badge variant="secondary" className="text-xs">
                                {question.question_types.name}
                              </Badge>
                            )}
                          </div>
                        </div>
                        {question.question_options && question.question_options.length > 0 && (
                          <div className="mt-2 text-xs text-gray-600">
                            {question.question_options.length} options available
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-3 border-t bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="px-6 py-4 border-t border-red-200 bg-red-50">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium text-red-800">Error Adding Questions</p>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setError(null)}
                className="text-red-500 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="p-6 border-t bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Default Points per Question
                </label>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={defaultPoints}
                  onChange={(e) => setDefaultPoints(parseInt(e.target.value) || 1)}
                  className="w-20"
                />
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={handleAddQuestionsClick}
                disabled={selectedQuestionIds.length === 0 || adding}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                {adding ? 'Adding...' : `Add ${selectedQuestionIds.length} Questions`}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AddQuestionsModal
