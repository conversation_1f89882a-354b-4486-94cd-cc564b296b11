/**
 * Shared CSS Modules - Admin Portal
 * Centralized exports for all shared style modules
 */

// Import all shared modules
import buttonStyles from './buttons.module.css'
import formStyles from './forms.module.css'
import stateStyles from './states.module.css'
import badgeStyles from './badges.module.css'
import cardStyles from './cards.module.css'
import layoutStyles from './layouts.module.css'

// Export individual modules
export {
  buttonStyles,
  formStyles,
  stateStyles,
  badgeStyles,
  cardStyles,
  layoutStyles,
}

// Export combined styles object for convenience
export const sharedStyles = {
  buttons: buttonStyles,
  forms: formStyles,
  states: stateStyles,
  badges: badgeStyles,
  cards: cardStyles,
  layouts: layoutStyles,
}

// Type definitions for better TypeScript support
export type SharedStylesType = typeof sharedStyles

// Utility function to combine shared classes with component-specific ones
export const combineClasses = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ')
}

// Common style combinations used across components
export const commonCombinations = {
  // Primary button with icon
  primaryButtonWithIcon: `${buttonStyles.buttonPrimary}`,
  
  // Card with hover effect
  interactiveCard: `${cardStyles.cardHover}`,
  
  // Form section with title
  formSection: `${formStyles.formSection}`,
  
  // Stats grid layout
  statsLayout: `${layoutStyles.statsGrid}`,
  
  // Loading state container
  loadingState: `${stateStyles.loadingContainer}`,
  
  // Error state container
  errorState: `${stateStyles.errorContainer}`,
  
  // Empty state wrapper
  emptyState: `${stateStyles.emptyStateWrapper}`,
  
  // Status badge active
  activeBadge: `${badgeStyles.badgeStatusActive}`,
  
  // Status badge inactive
  inactiveBadge: `${badgeStyles.badgeStatusInactive}`,
}