/**
 * Shared Form Styles - Admin Portal
 * Common form field patterns used across all components
 */

/* Form Structure */
.formContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.6');
}

.formSection {
  background-color: white;
  border: 1px solid theme('colors.gray.200');
  border-radius: theme('borderRadius.lg');
  padding: theme('spacing.6');
}

.formSectionTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.4');
  padding-bottom: theme('spacing.2');
  border-bottom: 1px solid theme('colors.gray.200');
}

.formSectionDescription {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  margin-bottom: theme('spacing.4');
  line-height: theme('lineHeight.relaxed');
}

/* Form Fields */
.formField {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.1');
}

.formFieldsContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

.formFieldGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: theme('spacing.4');
}

@media (min-width: theme('screens.md')) {
  .formFieldGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Labels */
.fieldLabel {
  display: block;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.700');
  margin-bottom: theme('spacing.1');
}

.fieldLabelRequired::after {
  content: ' *';
  color: theme('colors.red.500');
}

/* Input Fields */
.fieldInput,
.fieldTextarea,
.fieldSelect {
  width: 100%;
  padding: theme('spacing.2') theme('spacing.3');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.md');
  font-size: theme('fontSize.sm');
  transition: all 0.2s ease;
}

.fieldInput:focus,
.fieldTextarea:focus,
.fieldSelect:focus {
  outline: none;
  ring: 2px;
  ring-color: theme('colors.blue.500');
  border-color: theme('colors.blue.500');
}

.fieldInput:disabled,
.fieldTextarea:disabled,
.fieldSelect:disabled {
  background-color: theme('colors.gray.50');
  color: theme('colors.gray.500');
  cursor: not-allowed;
}

.fieldTextarea {
  resize: vertical;
  min-height: theme('spacing.20');
}

.fieldSelect {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right theme('spacing.2') center;
  background-repeat: no-repeat;
  background-size: theme('spacing.4');
  padding-right: theme('spacing.10');
}

/* Checkbox and Radio */
.fieldCheckboxContainer {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.fieldCheckbox {
  height: theme('spacing.4');
  width: theme('spacing.4');
  color: theme('colors.blue.600');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.DEFAULT');
  transition: all 0.2s ease;
}

.fieldCheckbox:focus {
  ring: 2px;
  ring-color: theme('colors.blue.500');
}

.fieldCheckboxLabel {
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.700');
  cursor: pointer;
}

/* Input Groups */
.inputGroup {
  display: flex;
  align-items: center;
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.md');
  transition: all 0.2s ease;
}

.inputGroup:focus-within {
  ring: 2px;
  ring-color: theme('colors.blue.500');
  border-color: theme('colors.blue.500');
}

.inputGroupAddon {
  padding: theme('spacing.2') theme('spacing.3');
  background-color: theme('colors.gray.50');
  border-right: 1px solid theme('colors.gray.300');
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.500');
}

.inputGroupField {
  flex: 1;
  border: none;
  padding: theme('spacing.2') theme('spacing.3');
  font-size: theme('fontSize.sm');
}

.inputGroupField:focus {
  outline: none;
}

/* Field States */
.fieldError {
  font-size: theme('fontSize.xs');
  color: theme('colors.red.600');
  margin-top: theme('spacing.1');
}

.fieldHint {
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.500');
  margin-top: theme('spacing.1');
}

.fieldSuccess {
  font-size: theme('fontSize.xs');
  color: theme('colors.green.600');
  margin-top: theme('spacing.1');
}

/* Field with Error State */
.fieldInputError,
.fieldTextareaError,
.fieldSelectError {
  border-color: theme('colors.red.300');
}

.fieldInputError:focus,
.fieldTextareaError:focus,
.fieldSelectError:focus {
  ring-color: theme('colors.red.500');
  border-color: theme('colors.red.500');
}

/* Field with Success State */
.fieldInputSuccess,
.fieldTextareaSuccess,
.fieldSelectSuccess {
  border-color: theme('colors.green.300');
}

.fieldInputSuccess:focus,
.fieldTextareaSuccess:focus,
.fieldSelectSuccess:focus {
  ring-color: theme('colors.green.500');
  border-color: theme('colors.green.500');
}

/* Search Fields */
.searchContainer {
  position: relative;
}

.searchIcon {
  position: absolute;
  left: theme('spacing.3');
  top: 50%;
  transform: translateY(-50%);
  color: theme('colors.gray.400');
  height: theme('spacing.4');
  width: theme('spacing.4');
}

.searchInput {
  width: 100%;
  padding-left: theme('spacing.10');
  padding-right: theme('spacing.4');
  padding-top: theme('spacing.2');
  padding-bottom: theme('spacing.2');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.md');
  transition: all 0.2s ease;
}

.searchInput:focus {
  outline: none;
  ring: 2px;
  ring-color: theme('colors.blue.500');
  border-color: theme('colors.blue.500');
}

/* Form Actions */
.formActions {
  display: flex;
  justify-content: flex-end;
  gap: theme('spacing.3');
  padding: theme('spacing.4') theme('spacing.6');
  background-color: white;
  border-top: 1px solid theme('colors.gray.200');
  border-radius: 0 0 theme('borderRadius.lg') theme('borderRadius.lg');
  margin-top: theme('spacing.6');
}

/* Filter Controls */
.filtersGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: theme('spacing.4');
}

@media (min-width: theme('screens.md')) {
  .filtersGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.filterLabel {
  display: block;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.700');
  margin-bottom: theme('spacing.2');
}

.filterSelect {
  width: 100%;
  padding: theme('spacing.2');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.md');
  transition: all 0.2s ease;
}

.filterSelect:focus {
  outline: none;
  ring: 2px;
  ring-color: theme('colors.blue.500');
  border-color: theme('colors.blue.500');
}

.clearButton {
  width: 100%;
}

/* Dark Mode Support */
:global(.dark) .formSection {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .formSectionTitle {
  color: white;
  border-bottom-color: theme('colors.gray.700');
}

:global(.dark) .formSectionDescription {
  color: theme('colors.gray.400');
}

:global(.dark) .fieldLabel,
:global(.dark) .fieldCheckboxLabel,
:global(.dark) .filterLabel {
  color: theme('colors.gray.300');
}

:global(.dark) .fieldInput,
:global(.dark) .fieldTextarea,
:global(.dark) .fieldSelect,
:global(.dark) .searchInput,
:global(.dark) .filterSelect {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.600');
  color: white;
}

:global(.dark) .fieldInput:focus,
:global(.dark) .fieldTextarea:focus,
:global(.dark) .fieldSelect:focus,
:global(.dark) .searchInput:focus,
:global(.dark) .filterSelect:focus {
  border-color: theme('colors.blue.400');
  ring-color: theme('colors.blue.400');
}

:global(.dark) .fieldInput:disabled,
:global(.dark) .fieldTextarea:disabled,
:global(.dark) .fieldSelect:disabled {
  background-color: theme('colors.gray.800');
  color: theme('colors.gray.500');
}

:global(.dark) .inputGroup {
  border-color: theme('colors.gray.600');
}

:global(.dark) .inputGroup:focus-within {
  border-color: theme('colors.blue.400');
  ring-color: theme('colors.blue.400');
}

:global(.dark) .inputGroupAddon {
  background-color: theme('colors.gray.800');
  border-right-color: theme('colors.gray.600');
  color: theme('colors.gray.400');
}

:global(.dark) .inputGroupField {
  background-color: theme('colors.gray.700');
  color: white;
}

:global(.dark) .formActions {
  background-color: theme('colors.gray.800');
  border-top-color: theme('colors.gray.700');
}

:global(.dark) .fieldHint {
  color: theme('colors.gray.400');
}