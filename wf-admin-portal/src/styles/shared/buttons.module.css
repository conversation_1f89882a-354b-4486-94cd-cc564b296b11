/**
 * Shared <PERSON>ton Styles - Admin Portal
 * Common button variants used across all components
 */

/* Base Button */
.buttonBase {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: theme('spacing.2');
  padding: theme('spacing.3') theme('spacing.6');
  border-radius: theme('borderRadius.lg');
  font-weight: theme('fontWeight.medium');
  font-size: theme('fontSize.sm');
  transition: all 0.2s ease;
  cursor: pointer;
  text-decoration: none;
}

.buttonBase:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.buttonPrimary {
  composes: buttonBase;
  background-color: theme('colors.blue.600');
  color: white;
  border: 1px solid theme('colors.blue.600');
}

.buttonPrimary:hover:not(:disabled) {
  background-color: theme('colors.blue.700');
  border-color: theme('colors.blue.700');
}

.buttonSecondary {
  composes: buttonBase;
  background-color: theme('colors.gray.600');
  color: white;
  border: 1px solid theme('colors.gray.600');
}

.buttonSecondary:hover:not(:disabled) {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.700');
}

.buttonOutline {
  composes: buttonBase;
  background-color: white;
  color: theme('colors.gray.700');
  border: 1px solid theme('colors.gray.300');
}

.buttonOutline:hover:not(:disabled) {
  background-color: theme('colors.gray.50');
  border-color: theme('colors.gray.400');
}

.buttonGhost {
  composes: buttonBase;
  background-color: transparent;
  color: theme('colors.gray.700');
  border: 1px solid transparent;
  padding: theme('spacing.2') theme('spacing.4');
}

.buttonGhost:hover:not(:disabled) {
  background-color: theme('colors.gray.100');
}

.buttonSuccess {
  composes: buttonBase;
  background-color: theme('colors.green.600');
  color: white;
  border: 1px solid theme('colors.green.600');
}

.buttonSuccess:hover:not(:disabled) {
  background-color: theme('colors.green.700');
  border-color: theme('colors.green.700');
}

.buttonDanger {
  composes: buttonBase;
  background-color: theme('colors.red.600');
  color: white;
  border: 1px solid theme('colors.red.600');
}

.buttonDanger:hover:not(:disabled) {
  background-color: theme('colors.red.700');
  border-color: theme('colors.red.700');
}

/* Button Sizes */
.buttonSmall {
  padding: theme('spacing.1') theme('spacing.3');
  font-size: theme('fontSize.xs');
}

.buttonLarge {
  padding: theme('spacing.4') theme('spacing.8');
  font-size: theme('fontSize.base');
}

/* Button Icon Styles */
.buttonIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
  flex-shrink: 0;
}

.buttonIconLarge {
  height: theme('spacing.5');
  width: theme('spacing.5');
  flex-shrink: 0;
}

/* Loading Button */
.buttonLoading {
  position: relative;
}

.buttonLoadingSpinner {
  height: theme('spacing.4');
  width: theme('spacing.4');
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: theme('borderRadius.full');
  animation: spin 1s linear infinite;
}

/* Dark Mode Support */
:global(.dark) .buttonOutline {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.200');
  border-color: theme('colors.gray.600');
}

:global(.dark) .buttonOutline:hover:not(:disabled) {
  background-color: theme('colors.gray.600');
  border-color: theme('colors.gray.500');
}

:global(.dark) .buttonGhost {
  color: theme('colors.gray.300');
}

:global(.dark) .buttonGhost:hover:not(:disabled) {
  background-color: theme('colors.gray.800');
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}