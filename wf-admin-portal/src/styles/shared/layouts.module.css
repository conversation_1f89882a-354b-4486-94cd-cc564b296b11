/**
 * Shared Layout Styles - Admin Portal
 * Common layout patterns and utilities used across all components
 */

/* Container Layouts */
.container {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.6');
}

.containerLarge {
  composes: container;
  gap: theme('spacing.8');
}

.containerCompact {
  composes: container;
  gap: theme('spacing.4');
}

.containerCentered {
  composes: container;
  align-items: center;
  justify-content: center;
  min-height: theme('spacing.96');
}

/* Grid Layouts */
.grid {
  display: grid;
  gap: theme('spacing.4');
}

.gridCols1 {
  composes: grid;
  grid-template-columns: 1fr;
}

.gridCols2 {
  composes: grid;
  grid-template-columns: repeat(2, 1fr);
}

.gridCols3 {
  composes: grid;
  grid-template-columns: repeat(3, 1fr);
}

.gridCols4 {
  composes: grid;
  grid-template-columns: repeat(4, 1fr);
}

.gridCols5 {
  composes: grid;
  grid-template-columns: repeat(5, 1fr);
}

/* Responsive Grids */
.gridResponsive {
  composes: grid;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .gridResponsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .gridResponsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

.gridResponsive2 {
  composes: grid;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .gridResponsive2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

.gridResponsive4 {
  composes: grid;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .gridResponsive4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .gridResponsive4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Form Grid Layouts */
.formGrid {
  composes: grid;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.lg')) {
  .formGrid {
    grid-template-columns: 1fr 1fr;
  }
}

.formGridFields {
  composes: grid;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .formGridFields {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Flex Layouts */
.flex {
  display: flex;
  gap: theme('spacing.4');
}

.flexCol {
  composes: flex;
  flex-direction: column;
}

.flexRow {
  composes: flex;
  flex-direction: row;
}

.flexWrap {
  composes: flex;
  flex-wrap: wrap;
}

.flexNoWrap {
  composes: flex;
  flex-wrap: nowrap;
}

/* Flex Alignment */
.flexCenter {
  composes: flex;
  align-items: center;
  justify-content: center;
}

.flexBetween {
  composes: flex;
  justify-content: space-between;
  align-items: center;
}

.flexStart {
  composes: flex;
  align-items: flex-start;
}

.flexEnd {
  composes: flex;
  align-items: flex-end;
}

.flexStretch {
  composes: flex;
  align-items: stretch;
}

/* Flex Item Properties */
.flexGrow {
  flex-grow: 1;
}

.flexShrink {
  flex-shrink: 1;
}

.flexNoShrink {
  flex-shrink: 0;
}

.flex1 {
  flex: 1;
}

.flexAuto {
  flex: auto;
}

.flexNone {
  flex: none;
}

/* Stack Layouts */
.stack {
  display: flex;
  flex-direction: column;
}

.stackSmall {
  composes: stack;
  gap: theme('spacing.2');
}

.stackMedium {
  composes: stack;
  gap: theme('spacing.4');
}

.stackLarge {
  composes: stack;
  gap: theme('spacing.6');
}

.stackXLarge {
  composes: stack;
  gap: theme('spacing.8');
}

/* Inline Stack */
.inlineStack {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.inlineStackSmall {
  composes: inlineStack;
  gap: theme('spacing.2');
}

.inlineStackMedium {
  composes: inlineStack;
  gap: theme('spacing.4');
}

.inlineStackLarge {
  composes: inlineStack;
  gap: theme('spacing.6');
}

/* Header Layouts */
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: theme('spacing.4');
}

.headerContent {
  flex: 1;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  flex-shrink: 0;
}

.headerTitle {
  font-size: theme('fontSize.3xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.gray.900');
}

.headerSubtitle {
  color: theme('colors.gray.600');
  margin-top: theme('spacing.2');
  line-height: theme('lineHeight.relaxed');
}

/* Sidebar Layouts */
.sidebarLayout {
  display: flex;
  gap: theme('spacing.6');
}

.sidebar {
  width: theme('spacing.64');
  flex-shrink: 0;
}

.sidebarContent {
  flex: 1;
  min-width: 0;
}

@media (max-width: theme('screens.lg')) {
  .sidebarLayout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
  }
}

/* Split Layouts */
.splitLayout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: theme('spacing.6');
}

@media (max-width: theme('screens.lg')) {
  .splitLayout {
    grid-template-columns: 1fr;
  }
}

.splitLayoutAsymmetric {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: theme('spacing.6');
}

@media (max-width: theme('screens.lg')) {
  .splitLayoutAsymmetric {
    grid-template-columns: 1fr;
  }
}

/* Content Layouts */
.contentContainer {
  max-width: theme('maxWidth.4xl');
  margin: 0 auto;
  padding: theme('spacing.8') theme('spacing.4');
}

@media (min-width: theme('screens.sm')) {
  .contentContainer {
    padding: theme('spacing.8') theme('spacing.6');
  }
}

@media (min-width: theme('screens.lg')) {
  .contentContainer {
    padding: theme('spacing.8') theme('spacing.8');
  }
}

.contentContainerWide {
  composes: contentContainer;
  max-width: theme('maxWidth.6xl');
}

.contentContainerNarrow {
  composes: contentContainer;
  max-width: theme('maxWidth.2xl');
}

/* Section Layouts */
.section {
  padding: theme('spacing.8') 0;
}

.sectionLarge {
  composes: section;
  padding: theme('spacing.12') 0;
}

.sectionCompact {
  composes: section;
  padding: theme('spacing.4') 0;
}

/* List Layouts */
.list {
  display: flex;
  flex-direction: column;
}

.listSpaced {
  composes: list;
  gap: theme('spacing.4');
}

.listCompact {
  composes: list;
  gap: theme('spacing.2');
}

.listLarge {
  composes: list;
  gap: theme('spacing.6');
}

/* Navigation Layouts */
.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: theme('spacing.6') 0 0;
  border-top: 1px solid theme('colors.gray.200');
}

.navigationActions {
  display: flex;
  gap: theme('spacing.4');
}

/* Toolbar Layouts */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: theme('spacing.4');
  background-color: theme('colors.gray.50');
  border: 1px solid theme('colors.gray.200');
  border-radius: theme('borderRadius.lg');
}

.toolbarLeft {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

.toolbarRight {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

/* Stats Layouts */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: theme('spacing.4');
}

@media (min-width: theme('screens.md')) {
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .statsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.statsContainer {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

/* Responsive Utilities */
.hiddenMobile {
  display: none;
}

@media (min-width: theme('screens.sm')) {
  .hiddenMobile {
    display: block;
  }
}

.hiddenDesktop {
  display: block;
}

@media (min-width: theme('screens.sm')) {
  .hiddenDesktop {
    display: none;
  }
}

.showMobile {
  display: block;
}

@media (min-width: theme('screens.sm')) {
  .showMobile {
    display: none;
  }
}

.showDesktop {
  display: none;
}

@media (min-width: theme('screens.sm')) {
  .showDesktop {
    display: block;
  }
}

/* Spacing Utilities */
.spaceY2 > * + * {
  margin-top: theme('spacing.2');
}

.spaceY4 > * + * {
  margin-top: theme('spacing.4');
}

.spaceY6 > * + * {
  margin-top: theme('spacing.6');
}

.spaceY8 > * + * {
  margin-top: theme('spacing.8');
}

.spaceX2 > * + * {
  margin-left: theme('spacing.2');
}

.spaceX4 > * + * {
  margin-left: theme('spacing.4');
}

.spaceX6 > * + * {
  margin-left: theme('spacing.6');
}

/* Position Utilities */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* Overflow Utilities */
.overflowHidden {
  overflow: hidden;
}

.overflowAuto {
  overflow: auto;
}

.overflowScroll {
  overflow: scroll;
}

.overflowXAuto {
  overflow-x: auto;
}

.overflowYAuto {
  overflow-y: auto;
}

/* Dark Mode Support */
:global(.dark) .headerTitle {
  color: white;
}

:global(.dark) .headerSubtitle {
  color: theme('colors.gray.300');
}

:global(.dark) .navigation {
  border-top-color: theme('colors.gray.700');
}

:global(.dark) .toolbar {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}