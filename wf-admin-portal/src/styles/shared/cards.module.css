/**
 * Shared Card Styles - Admin Portal
 * Common card layouts and patterns used across all components
 */

/* Base Card */
.cardBase {
  background-color: white;
  border: 1px solid theme('colors.gray.200');
  border-radius: theme('borderRadius.lg');
  box-shadow: theme('boxShadow.sm');
}

.cardPadded {
  composes: cardBase;
  padding: theme('spacing.6');
}

.cardCompact {
  composes: cardBase;
  padding: theme('spacing.4');
}

.cardLarge {
  composes: cardBase;
  padding: theme('spacing.8');
}

/* Card with Hover Effects */
.cardHover {
  composes: cardBase;
  transition: all 0.2s ease;
  cursor: pointer;
}

.cardHover:hover {
  box-shadow: theme('boxShadow.md');
  border-color: theme('colors.gray.300');
  transform: translateY(-1px);
}

.cardClickable {
  composes: cardHover;
  padding: theme('spacing.4');
}

/* Card Headers */
.cardHeader {
  padding: theme('spacing.4') theme('spacing.6');
  border-bottom: 1px solid theme('colors.gray.200');
}

.cardHeaderCompact {
  composes: cardHeader;
  padding: theme('spacing.3') theme('spacing.4');
}

.cardHeaderLarge {
  composes: cardHeader;
  padding: theme('spacing.6') theme('spacing.8');
}

.cardTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
}

.cardTitleLarge {
  composes: cardTitle;
  font-size: theme('fontSize.xl');
}

.cardTitleSmall {
  composes: cardTitle;
  font-size: theme('fontSize.base');
}

.cardSubtitle {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  margin-top: theme('spacing.1');
}

.cardHeaderWithActions {
  composes: cardHeader;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardHeaderActions {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

/* Card Content */
.cardContent {
  padding: theme('spacing.6');
}

.cardContentCompact {
  composes: cardContent;
  padding: theme('spacing.4');
}

.cardContentLarge {
  composes: cardContent;
  padding: theme('spacing.8');
}

.cardContentNoPadding {
  padding: 0;
}

/* Card Footer */
.cardFooter {
  padding: theme('spacing.4') theme('spacing.6');
  border-top: 1px solid theme('colors.gray.200');
  background-color: theme('colors.gray.50');
  border-radius: 0 0 theme('borderRadius.lg') theme('borderRadius.lg');
}

.cardFooterActions {
  composes: cardFooter;
  display: flex;
  justify-content: flex-end;
  gap: theme('spacing.2');
}

.cardFooterBetween {
  composes: cardFooter;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Stats Cards */
.statCard {
  composes: cardBase;
  padding: theme('spacing.4');
  text-align: center;
}

.statCardHorizontal {
  composes: cardBase;
  padding: theme('spacing.4');
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

.statCardValue {
  font-size: theme('fontSize.2xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.gray.900');
}

.statCardValueLarge {
  composes: statCardValue;
  font-size: theme('fontSize.3xl');
}

.statCardLabel {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  margin-top: theme('spacing.1');
}

.statCardIcon {
  height: theme('spacing.8');
  width: theme('spacing.8');
  color: theme('colors.blue.600');
}

.statCardIconSuccess {
  composes: statCardIcon;
  color: theme('colors.green.600');
}

.statCardIconWarning {
  composes: statCardIcon;
  color: theme('colors.yellow.600');
}

.statCardIconDanger {
  composes: statCardIcon;
  color: theme('colors.red.600');
}

/* Info Cards */
.infoCard {
  composes: cardBase;
  padding: theme('spacing.4');
  background-color: theme('colors.blue.50');
  border-color: theme('colors.blue.200');
}

.infoCardTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.blue.900');
  margin-bottom: theme('spacing.2');
}

.infoCardContent {
  font-size: theme('fontSize.sm');
  color: theme('colors.blue.800');
  line-height: theme('lineHeight.relaxed');
}

/* Warning Cards */
.warningCard {
  composes: cardBase;
  padding: theme('spacing.4');
  background-color: theme('colors.yellow.50');
  border-color: theme('colors.yellow.200');
}

.warningCardTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.yellow.900');
  margin-bottom: theme('spacing.2');
}

.warningCardContent {
  font-size: theme('fontSize.sm');
  color: theme('colors.yellow.800');
  line-height: theme('lineHeight.relaxed');
}

/* Success Cards */
.successCard {
  composes: cardBase;
  padding: theme('spacing.4');
  background-color: theme('colors.green.50');
  border-color: theme('colors.green.200');
}

.successCardTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.green.900');
  margin-bottom: theme('spacing.2');
}

.successCardContent {
  font-size: theme('fontSize.sm');
  color: theme('colors.green.800');
  line-height: theme('lineHeight.relaxed');
}

/* Error Cards */
.errorCard {
  composes: cardBase;
  padding: theme('spacing.4');
  background-color: theme('colors.red.50');
  border-color: theme('colors.red.200');
}

.errorCardTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.red.900');
  margin-bottom: theme('spacing.2');
}

.errorCardContent {
  font-size: theme('fontSize.sm');
  color: theme('colors.red.800');
  line-height: theme('lineHeight.relaxed');
}

/* List Cards */
.listCard {
  composes: cardBase;
}

.listCardItem {
  padding: theme('spacing.4');
  border-bottom: 1px solid theme('colors.gray.200');
  transition: background-color 0.2s ease;
}

.listCardItem:last-child {
  border-bottom: none;
}

.listCardItem:hover {
  background-color: theme('colors.gray.50');
}

.listCardItemActive {
  composes: listCardItem;
  background-color: theme('colors.blue.50');
  border-left: 4px solid theme('colors.blue.500');
}

/* Media Cards */
.mediaCard {
  composes: cardBase;
  display: flex;
  align-items: center;
  padding: theme('spacing.4');
  gap: theme('spacing.4');
}

.mediaCardImage {
  width: theme('spacing.16');
  height: theme('spacing.16');
  border-radius: theme('borderRadius.lg');
  object-fit: cover;
  background-color: theme('colors.gray.200');
}

.mediaCardContent {
  flex: 1;
}

.mediaCardTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.1');
}

.mediaCardDescription {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  line-height: theme('lineHeight.relaxed');
}

/* Compact Cards */
.compactCard {
  composes: cardBase;
  padding: theme('spacing.3');
}

.compactCardTitle {
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.1');
}

.compactCardValue {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
}

/* Feature Cards */
.featureCard {
  composes: cardBase;
  padding: theme('spacing.6');
  text-align: center;
}

.featureCardIcon {
  height: theme('spacing.12');
  width: theme('spacing.12');
  color: theme('colors.blue.600');
  margin: 0 auto theme('spacing.4');
}

.featureCardTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.2');
}

.featureCardDescription {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  line-height: theme('lineHeight.relaxed');
}

/* Pricing Cards */
.pricingCard {
  composes: cardBase;
  padding: theme('spacing.6');
  text-align: center;
  position: relative;
}

.pricingCardPopular {
  composes: pricingCard;
  border-color: theme('colors.blue.500');
  box-shadow: theme('boxShadow.lg');
}

.pricingCardBadge {
  position: absolute;
  top: theme('spacing.4');
  right: theme('spacing.4');
  background-color: theme('colors.blue.600');
  color: white;
  padding: theme('spacing.1') theme('spacing.2');
  border-radius: theme('borderRadius.md');
  font-size: theme('fontSize.xs');
  font-weight: theme('fontWeight.medium');
}

.pricingCardPrice {
  font-size: theme('fontSize.3xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.2');
}

.pricingCardPeriod {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  margin-bottom: theme('spacing.4');
}

/* Card Grids */
.cardGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: theme('spacing.4');
}

@media (min-width: theme('screens.md')) {
  .cardGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .cardGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.cardGrid2 {
  composes: cardGrid;
}

@media (min-width: theme('screens.lg')) {
  .cardGrid2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

.cardGrid4 {
  composes: cardGrid;
}

@media (min-width: theme('screens.lg')) {
  .cardGrid4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Dark Mode Support */
:global(.dark) .cardBase,
:global(.dark) .cardPadded,
:global(.dark) .cardCompact,
:global(.dark) .cardLarge,
:global(.dark) .cardHover,
:global(.dark) .cardClickable,
:global(.dark) .statCard,
:global(.dark) .statCardHorizontal,
:global(.dark) .listCard,
:global(.dark) .mediaCard,
:global(.dark) .compactCard,
:global(.dark) .featureCard,
:global(.dark) .pricingCard {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .cardHover:hover {
  border-color: theme('colors.gray.600');
}

:global(.dark) .cardHeader,
:global(.dark) .cardHeaderCompact,
:global(.dark) .cardHeaderLarge,
:global(.dark) .cardHeaderWithActions {
  border-bottom-color: theme('colors.gray.700');
}

:global(.dark) .cardTitle,
:global(.dark) .cardTitleLarge,
:global(.dark) .cardTitleSmall,
:global(.dark) .statCardValue,
:global(.dark) .statCardValueLarge,
:global(.dark) .mediaCardTitle,
:global(.dark) .compactCardTitle,
:global(.dark) .compactCardValue,
:global(.dark) .featureCardTitle,
:global(.dark) .pricingCardPrice {
  color: white;
}

:global(.dark) .cardSubtitle,
:global(.dark) .statCardLabel,
:global(.dark) .mediaCardDescription,
:global(.dark) .featureCardDescription,
:global(.dark) .pricingCardPeriod {
  color: theme('colors.gray.300');
}

:global(.dark) .cardFooter,
:global(.dark) .cardFooterActions,
:global(.dark) .cardFooterBetween {
  background-color: theme('colors.gray.700');
  border-top-color: theme('colors.gray.600');
}

:global(.dark) .listCardItem {
  border-bottom-color: theme('colors.gray.700');
}

:global(.dark) .listCardItem:hover {
  background-color: theme('colors.gray.700');
}

:global(.dark) .listCardItemActive {
  background-color: rgba(59, 130, 246, 0.1);
  border-left-color: theme('colors.blue.400');
}

:global(.dark) .infoCard {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: theme('colors.blue.700');
}

:global(.dark) .infoCardTitle {
  color: theme('colors.blue.300');
}

:global(.dark) .infoCardContent {
  color: theme('colors.blue.200');
}

:global(.dark) .warningCard {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: theme('colors.yellow.700');
}

:global(.dark) .warningCardTitle {
  color: theme('colors.yellow.300');
}

:global(.dark) .warningCardContent {
  color: theme('colors.yellow.200');
}

:global(.dark) .successCard {
  background-color: rgba(34, 197, 94, 0.1);
  border-color: theme('colors.green.700');
}

:global(.dark) .successCardTitle {
  color: theme('colors.green.300');
}

:global(.dark) .successCardContent {
  color: theme('colors.green.200');
}

:global(.dark) .errorCard {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: theme('colors.red.700');
}

:global(.dark) .errorCardTitle {
  color: theme('colors.red.300');
}

:global(.dark) .errorCardContent {
  color: theme('colors.red.200');
}

:global(.dark) .mediaCardImage {
  background-color: theme('colors.gray.700');
}