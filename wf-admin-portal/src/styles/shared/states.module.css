/**
 * Shared State Styles - Admin Portal
 * Common loading, error, and empty states used across all components
 */

/* Loading States */
.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: theme('spacing.96');
}

.loadingContent {
  text-align: center;
}

.loadingSpinner {
  animation: spin 1s linear infinite;
  border-radius: theme('borderRadius.full');
  height: theme('spacing.8');
  width: theme('spacing.8');
  border: 2px solid transparent;
  border-bottom-color: theme('colors.blue.600');
  margin: 0 auto theme('spacing.4');
}

.loadingSpinnerSmall {
  composes: loadingSpinner;
  height: theme('spacing.4');
  width: theme('spacing.4');
  margin-bottom: theme('spacing.2');
}

.loadingSpinnerLarge {
  composes: loadingSpinner;
  height: theme('spacing.12');
  width: theme('spacing.12');
  margin-bottom: theme('spacing.6');
}

.loadingText {
  color: theme('colors.gray.600');
  font-size: theme('fontSize.sm');
}

.loadingTextLarge {
  composes: loadingText;
  font-size: theme('fontSize.base');
}

/* Loading Skeleton */
.loadingSkeletonGrid {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

.loadingSkeletonItem {
  height: theme('spacing.20');
  background-color: theme('colors.gray.200');
  border-radius: theme('borderRadius.lg');
}

.loadingSkeletonItemSmall {
  composes: loadingSkeletonItem;
  height: theme('spacing.12');
}

.loadingSkeletonItemLarge {
  composes: loadingSkeletonItem;
  height: theme('spacing.32');
}

/* Error States */
.errorContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.6');
}

.errorContent {
  text-align: center;
  padding: theme('spacing.8') 0;
}

.errorIcon {
  height: theme('spacing.12');
  width: theme('spacing.12');
  color: theme('colors.red.500');
  margin: 0 auto theme('spacing.4');
}

.errorIconSmall {
  composes: errorIcon;
  height: theme('spacing.8');
  width: theme('spacing.8');
}

.errorTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.2');
}

.errorDescription {
  color: theme('colors.gray.600');
  margin-bottom: theme('spacing.4');
  line-height: theme('lineHeight.relaxed');
}

.errorActions {
  display: flex;
  gap: theme('spacing.2');
  justify-content: center;
}

/* Empty States */
.emptyStateWrapper {
  text-align: center;
  padding: theme('spacing.8') 0;
}

.emptyStateWrapperLarge {
  composes: emptyStateWrapper;
  padding: theme('spacing.12') 0;
}

.emptyStateIcon {
  height: theme('spacing.12');
  width: theme('spacing.12');
  color: theme('colors.gray.400');
  margin: 0 auto theme('spacing.4');
}

.emptyStateIconLarge {
  composes: emptyStateIcon;
  height: theme('spacing.16');
  width: theme('spacing.16');
}

.emptyStateTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.2');
}

.emptyStateTitleLarge {
  composes: emptyStateTitle;
  font-size: theme('fontSize.xl');
}

.emptyStateDescription {
  color: theme('colors.gray.500');
  margin-bottom: theme('spacing.4');
  line-height: theme('lineHeight.relaxed');
}

.emptyStateActions {
  display: flex;
  gap: theme('spacing.2');
  justify-content: center;
}

/* Success States */
.successMessage {
  background-color: theme('colors.green.50');
  border: 1px solid theme('colors.green.200');
  border-radius: theme('borderRadius.md');
  padding: theme('spacing.3') theme('spacing.4');
  margin-bottom: theme('spacing.4');
}

.successMessageText {
  font-size: theme('fontSize.sm');
  color: theme('colors.green.800');
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.successMessageIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
}

/* Warning States */
.warningMessage {
  background-color: theme('colors.yellow.50');
  border: 1px solid theme('colors.yellow.200');
  border-radius: theme('borderRadius.md');
  padding: theme('spacing.3') theme('spacing.4');
  margin-bottom: theme('spacing.4');
}

.warningMessageText {
  font-size: theme('fontSize.sm');
  color: theme('colors.yellow.800');
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.warningMessageIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
}

/* Info States */
.infoMessage {
  background-color: theme('colors.blue.50');
  border: 1px solid theme('colors.blue.200');
  border-radius: theme('borderRadius.md');
  padding: theme('spacing.3') theme('spacing.4');
  margin-bottom: theme('spacing.4');
}

.infoMessageText {
  font-size: theme('fontSize.sm');
  color: theme('colors.blue.800');
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.infoMessageIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
}

/* Progress Indicators */
.progressIndicator {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  padding: theme('spacing.2') theme('spacing.3');
  background-color: theme('colors.blue.50');
  border: 1px solid theme('colors.blue.200');
  border-radius: theme('borderRadius.md');
  font-size: theme('fontSize.sm');
  color: theme('colors.blue.800');
}

.progressIndicatorIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
  animation: spin 1s linear infinite;
}

/* Validation Summary */
.validationSummary {
  background-color: theme('colors.red.50');
  border: 1px solid theme('colors.red.200');
  border-radius: theme('borderRadius.md');
  padding: theme('spacing.4');
  margin-bottom: theme('spacing.4');
}

.validationSummaryTitle {
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.red.800');
  margin-bottom: theme('spacing.2');
}

.validationSummaryList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.validationSummaryItem {
  font-size: theme('fontSize.xs');
  color: theme('colors.red.700');
  padding: theme('spacing.1') 0;
}

/* No Results State */
.noResultsState {
  composes: emptyStateWrapper;
  background-color: theme('colors.gray.50');
  border: 1px solid theme('colors.gray.200');
  border-radius: theme('borderRadius.lg');
  margin: theme('spacing.4') 0;
}

.noResultsIcon {
  composes: emptyStateIcon;
  color: theme('colors.gray.300');
}

.noResultsTitle {
  composes: emptyStateTitle;
  color: theme('colors.gray.700');
}

.noResultsDescription {
  composes: emptyStateDescription;
  color: theme('colors.gray.500');
}

/* Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(theme('spacing.2'));
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Dark Mode Support */
:global(.dark) .loadingText,
:global(.dark) .loadingTextLarge {
  color: theme('colors.gray.400');
}

:global(.dark) .loadingSkeletonItem,
:global(.dark) .loadingSkeletonItemSmall,
:global(.dark) .loadingSkeletonItemLarge {
  background-color: theme('colors.gray.700');
}

:global(.dark) .errorTitle,
:global(.dark) .emptyStateTitle,
:global(.dark) .emptyStateTitleLarge {
  color: white;
}

:global(.dark) .errorDescription,
:global(.dark) .emptyStateDescription {
  color: theme('colors.gray.400');
}

:global(.dark) .successMessage {
  background-color: rgba(34, 197, 94, 0.1);
  border-color: theme('colors.green.700');
}

:global(.dark) .successMessageText {
  color: theme('colors.green.300');
}

:global(.dark) .warningMessage {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: theme('colors.yellow.700');
}

:global(.dark) .warningMessageText {
  color: theme('colors.yellow.300');
}

:global(.dark) .infoMessage {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: theme('colors.blue.700');
}

:global(.dark) .infoMessageText {
  color: theme('colors.blue.300');
}

:global(.dark) .progressIndicator {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: theme('colors.blue.700');
  color: theme('colors.blue.300');
}

:global(.dark) .validationSummary {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: theme('colors.red.700');
}

:global(.dark) .validationSummaryTitle {
  color: theme('colors.red.300');
}

:global(.dark) .validationSummaryItem {
  color: theme('colors.red.400');
}

:global(.dark) .noResultsState {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .noResultsTitle {
  color: theme('colors.gray.300');
}

:global(.dark) .noResultsDescription {
  color: theme('colors.gray.500');
}