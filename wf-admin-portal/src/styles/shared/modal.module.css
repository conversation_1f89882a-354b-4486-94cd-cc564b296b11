/* Shared Modal Styles - Common patterns across all modals */

/* Modal overlay - identical across all modals */
.overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

/* Modal container - base styles for all modals */
.modal {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  width: 100%;
  margin: 1rem;
  max-height: 90vh;
  overflow-y: auto;
}

/* Size variants for modal containers */
.modalSmall {
  composes: modal;
  max-width: 32rem; /* 2xl */
}

.modalMedium {
  composes: modal;
  max-width: 42rem; /* 3xl */
}

.modalLarge {
  composes: modal;
  max-width: 56rem; /* 4xl */
}

.modalXLarge {
  composes: modal;
  max-width: 64rem; /* 5xl */
}

/* Header - consistent across all modals */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  color: rgb(17, 24, 39);
}

/* Form structure - common pattern */
.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Grid layouts - reusable patterns */
.grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .grid {
    grid-template-columns: 1fr 1fr;
  }
}

.threeColumnGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .threeColumnGrid {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

/* Field groups - consistent spacing */
.fieldGroup {
  display: flex;
  flex-direction: column;
}

/* Labels - consistent styling */
.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(55, 65, 81);
  margin-bottom: 0.5rem;
}

.labelOptional {
  color: rgb(107, 114, 128);
  font-size: 0.75rem;
}

/* Input fields - base styles */
.input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid rgb(209, 213, 219);
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.input:focus {
  outline: none;
  ring: 2px;
  ring-color: rgb(59, 130, 246);
  border-color: rgb(59, 130, 246);
}

.input.error {
  border-color: rgb(239, 68, 68);
}

/* Input variants */
.textarea {
  composes: input;
  resize: vertical;
  min-height: 4rem;
}

.select {
  composes: input;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  appearance: none;
}

/* Helper text and messages */
.helperText {
  color: rgb(107, 114, 128);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.errorMessage {
  color: rgb(239, 68, 68);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.charCounter {
  font-size: 0.75rem;
  color: rgb(107, 114, 128);
}

/* Checkbox styling */
.checkboxContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-top: 2rem;
}

.checkbox {
  border-radius: 0.25rem;
  cursor: pointer;
}

.checkboxLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(55, 65, 81);
  cursor: pointer;
}

/* Form actions - consistent across all modals */
.actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgb(229, 231, 235);
}

/* Dark mode support - consistent across all modals */
:global(.dark) .modal {
  background-color: rgb(31, 41, 55);
  color: white;
}

:global(.dark) .title {
  color: white;
}

:global(.dark) .label {
  color: rgb(209, 213, 219);
}

:global(.dark) .input {
  background-color: rgb(55, 65, 81);
  border-color: rgb(75, 85, 99);
  color: white;
}

:global(.dark) .input:focus {
  border-color: rgb(59, 130, 246);
  ring-color: rgb(59, 130, 246);
}

:global(.dark) .charCounter {
  color: rgb(156, 163, 175);
}

:global(.dark) .helperText {
  color: rgb(156, 163, 175);
}

:global(.dark) .checkboxLabel {
  color: rgb(209, 213, 219);
}

:global(.dark) .actions {
  border-top-color: rgb(75, 85, 99);
}