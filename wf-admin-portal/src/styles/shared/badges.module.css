/**
 * Shared Badge Styles - Admin Portal
 * Common badge variants used across all components
 */

/* Base Badge */
.badgeBase {
  display: inline-flex;
  align-items: center;
  padding: theme('spacing.1') theme('spacing.2');
  border-radius: theme('borderRadius.full');
  font-size: theme('fontSize.xs');
  font-weight: theme('fontWeight.medium');
}

.badgeLarge {
  composes: badgeBase;
  padding: theme('spacing.1') theme('spacing.3');
  font-size: theme('fontSize.sm');
}

.badgeSmall {
  composes: badgeBase;
  padding: theme('spacing[0.5]') theme('spacing[1.5]');
  font-size: theme('fontSize.xs');
}

/* Status Badges */
.badgeStatusActive {
  composes: badgeBase;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.badgeStatusInactive {
  composes: badgeBase;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

.badgeStatusPending {
  composes: badgeBase;
  background-color: theme('colors.yellow.100');
  color: theme('colors.yellow.800');
}

.badgeStatusDraft {
  composes: badgeBase;
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.800');
}

.badgeStatusPublished {
  composes: badgeBase;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.badgeStatusArchived {
  composes: badgeBase;
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.800');
}

/* Category Badges */
.badgeCategory {
  composes: badgeBase;
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

.badgeCategoryGrammar {
  composes: badgeBase;
  background-color: theme('colors.purple.100');
  color: theme('colors.purple.800');
}

.badgeCategoryVocabulary {
  composes: badgeBase;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.badgeCategoryReading {
  composes: badgeBase;
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

.badgeCategoryListening {
  composes: badgeBase;
  background-color: theme('colors.orange.100');
  color: theme('colors.orange.800');
}

/* Level Badges */
.badgeLevel {
  composes: badgeBase;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.badgeLevelA1 {
  composes: badgeBase;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.badgeLevelA2 {
  composes: badgeBase;
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

.badgeLevelB1 {
  composes: badgeBase;
  background-color: theme('colors.yellow.100');
  color: theme('colors.yellow.800');
}

.badgeLevelB2 {
  composes: badgeBase;
  background-color: theme('colors.orange.100');
  color: theme('colors.orange.800');
}

.badgeLevelC1 {
  composes: badgeBase;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

.badgeLevelC2 {
  composes: badgeBase;
  background-color: theme('colors.purple.100');
  color: theme('colors.purple.800');
}

/* Difficulty Badges */
.badgeDifficulty {
  composes: badgeBase;
}

.badgeDifficultyEasy,
.badgeDifficulty1,
.badgeDifficulty2 {
  composes: badgeBase;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.badgeDifficultyMedium,
.badgeDifficulty3 {
  composes: badgeBase;
  background-color: theme('colors.yellow.100');
  color: theme('colors.yellow.800');
}

.badgeDifficultyHard,
.badgeDifficulty4,
.badgeDifficulty5 {
  composes: badgeBase;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

/* Quiz Type Badges */
.badgeQuizType {
  composes: badgeBase;
  background-color: theme('colors.purple.100');
  color: theme('colors.purple.800');
}

.badgeQuizTypePractice {
  composes: badgeBase;
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

.badgeQuizTypeTest {
  composes: badgeBase;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

.badgeQuizTypeAssessment {
  composes: badgeBase;
  background-color: theme('colors.purple.100');
  color: theme('colors.purple.800');
}

/* Role Badges */
.badgeRole {
  composes: badgeBase;
}

.badgeRoleAdmin {
  composes: badgeBase;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

.badgeRoleUser {
  composes: badgeBase;
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

.badgeRoleModerator {
  composes: badgeBase;
  background-color: theme('colors.yellow.100');
  color: theme('colors.yellow.800');
}

/* Question Type Badges */
.badgeQuestionType {
  composes: badgeBase;
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.800');
}

.badgeQuestionTypeMultiple {
  composes: badgeBase;
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

.badgeQuestionTypeSingle {
  composes: badgeBase;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.badgeQuestionTypeText {
  composes: badgeBase;
  background-color: theme('colors.purple.100');
  color: theme('colors.purple.800');
}

/* Priority Badges */
.badgePriority {
  composes: badgeBase;
}

.badgePriorityHigh {
  composes: badgeBase;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

.badgePriorityMedium {
  composes: badgeBase;
  background-color: theme('colors.yellow.100');
  color: theme('colors.yellow.800');
}

.badgePriorityLow {
  composes: badgeBase;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

/* Severity Badges */
.badgeSeverity {
  composes: badgeBase;
}

.badgeSeverityCritical {
  composes: badgeBase;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

.badgeSeverityWarning {
  composes: badgeBase;
  background-color: theme('colors.yellow.100');
  color: theme('colors.yellow.800');
}

.badgeSeverityInfo {
  composes: badgeBase;
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

/* Badge with Icon */
.badgeWithIcon {
  composes: badgeBase;
  gap: theme('spacing.1');
}

.badgeIcon {
  height: theme('spacing.3');
  width: theme('spacing.3');
}

.badgeIconSmall {
  height: theme('spacing[2.5]');
  width: theme('spacing[2.5]');
}

/* Badge Dot Indicator */
.badgeDot {
  height: theme('spacing.2');
  width: theme('spacing.2');
  border-radius: theme('borderRadius.full');
  margin-right: theme('spacing.1');
}

.badgeDotGreen {
  composes: badgeDot;
  background-color: theme('colors.green.500');
}

.badgeDotRed {
  composes: badgeDot;
  background-color: theme('colors.red.500');
}

.badgeDotYellow {
  composes: badgeDot;
  background-color: theme('colors.yellow.500');
}

.badgeDotBlue {
  composes: badgeDot;
  background-color: theme('colors.blue.500');
}

/* Badge Group */
.badgeGroup {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  flex-wrap: wrap;
}

.badgeGroupVertical {
  composes: badgeGroup;
  flex-direction: column;
  align-items: flex-start;
}

/* Interactive Badges */
.badgeInteractive {
  composes: badgeBase;
  cursor: pointer;
  transition: all 0.2s ease;
}

.badgeInteractive:hover {
  transform: translateY(-1px);
  box-shadow: theme('boxShadow.sm');
}

/* Badge Sizes */
.badgeExtraSmall {
  composes: badgeBase;
  padding: theme('spacing[0.5]') theme('spacing.1');
  font-size: theme('fontSize.xs');
}

.badgeExtraLarge {
  composes: badgeBase;
  padding: theme('spacing.2') theme('spacing.4');
  font-size: theme('fontSize.base');
}

/* Dark Mode Support */
:global(.dark) .badgeStatusActive,
:global(.dark) .badgeStatusPublished,
:global(.dark) .badgeLevelA1,
:global(.dark) .badgeDifficultyEasy,
:global(.dark) .badgeDifficulty1,
:global(.dark) .badgeDifficulty2,
:global(.dark) .badgeQuizTypePractice,
:global(.dark) .badgeCategoryVocabulary,
:global(.dark) .badgePriorityLow {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.300');
}

:global(.dark) .badgeStatusInactive,
:global(.dark) .badgeDifficultyHard,
:global(.dark) .badgeDifficulty4,
:global(.dark) .badgeDifficulty5,
:global(.dark) .badgeQuizTypeTest,
:global(.dark) .badgeLevelC1,
:global(.dark) .badgeRoleAdmin,
:global(.dark) .badgePriorityHigh,
:global(.dark) .badgeSeverityCritical {
  background-color: rgba(239, 68, 68, 0.2);
  color: theme('colors.red.300');
}

:global(.dark) .badgeStatusPending,
:global(.dark) .badgeDifficultyMedium,
:global(.dark) .badgeDifficulty3,
:global(.dark) .badgeLevelB1,
:global(.dark) .badgeRoleModerator,
:global(.dark) .badgePriorityMedium,
:global(.dark) .badgeSeverityWarning {
  background-color: rgba(245, 158, 11, 0.2);
  color: theme('colors.yellow.300');
}

:global(.dark) .badgeCategory,
:global(.dark) .badgeCategoryReading,
:global(.dark) .badgeLevelA2,
:global(.dark) .badgeQuestionTypeMultiple,
:global(.dark) .badgeRoleUser,
:global(.dark) .badgeSeverityInfo {
  background-color: rgba(59, 130, 246, 0.2);
  color: theme('colors.blue.300');
}

:global(.dark) .badgeStatusDraft,
:global(.dark) .badgeStatusArchived,
:global(.dark) .badgeQuestionType {
  background-color: rgba(107, 114, 128, 0.2);
  color: theme('colors.gray.300');
}

:global(.dark) .badgeQuizType,
:global(.dark) .badgeCategoryGrammar,
:global(.dark) .badgeLevelC2,
:global(.dark) .badgeQuizTypeAssessment,
:global(.dark) .badgeQuestionTypeText {
  background-color: rgba(168, 85, 247, 0.2);
  color: theme('colors.purple.300');
}

:global(.dark) .badgeCategoryListening,
:global(.dark) .badgeLevelB2 {
  background-color: rgba(249, 115, 22, 0.2);
  color: theme('colors.orange.300');
}