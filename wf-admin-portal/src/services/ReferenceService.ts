import { repositories } from '@/repositories'
import { handleRepositoryCall } from 'wf-shared'
import type { DbCategory, DbLevel, DbQuizType, DbQuestionType } from 'wf-shared/types'

// Categories Service
export class CategoryService {
  /**
   * Get all categories
   */
  static async getCategories(): Promise<{
    data: DbCategory[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getCategories(),
      'Categories fetch error',
      'Failed to fetch categories',
    )
  }

  /**
   * Get category by ID
   */
  static async getCategoryById(id: string): Promise<{
    data: DbCategory | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getCategoryById(id),
      'Category fetch error',
      'Failed to fetch category',
    )
  }

  /**
   * Create new category
   */
  static async createCategory(category: Partial<DbCategory>): Promise<{
    data: DbCategory | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.createCategory(category),
      'Category creation error',
      'Failed to create category',
    )
  }

  /**
   * Update category
   */
  static async updateCategory(id: string, updates: Partial<DbCategory>): Promise<{
    data: DbCategory | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.updateCategory(id, updates),
      'Category update error',
      'Failed to update category',
    )
  }

  /**
   * Delete category
   */
  static async deleteCategory(id: string): Promise<{
    data: null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.deleteCategory(id),
      'Category deletion error',
      'Failed to delete category',
    )
  }
}

// Subcategories Service
export class SubcategoryService {
  /**
   * Get subcategories by category ID
   */
  static async getSubcategoriesByCategory(categoryId: string): Promise<{
    data: DbCategory[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getSubcategoriesByCategory(categoryId),
      'Subcategories fetch error',
      'Failed to fetch subcategories',
    )
  }

  /**
   * Get all subcategories
   */
  static async getSubcategories(): Promise<{
    data: DbCategory[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getSubcategories(),
      'Subcategories fetch error',
      'Failed to fetch subcategories',
    )
  }

  /**
   * Get subcategory by ID
   */
  static async getSubcategoryById(id: string): Promise<{
    data: DbCategory | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getSubcategoryById(id),
      'Subcategory fetch error',
      'Failed to fetch subcategory',
    )
  }
}

// Levels Service
export class LevelService {
  /**
   * Get all levels
   */
  static async getLevels(): Promise<{
    data: DbLevel[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getLevels(),
      'Levels fetch error',
      'Failed to fetch levels',
    )
  }

  /**
   * Get level by ID
   */
  static async getLevelById(id: string): Promise<{
    data: DbLevel | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getLevelById(id),
      'Level fetch error',
      'Failed to fetch level',
    )
  }

  /**
   * Create new level
   */
  static async createLevel(level: Partial<DbLevel>): Promise<{
    data: DbLevel | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.createLevel(level),
      'Level creation error',
      'Failed to create level',
    )
  }

  /**
   * Update level
   */
  static async updateLevel(id: string, updates: Partial<DbLevel>): Promise<{
    data: DbLevel | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.updateLevel(id, updates),
      'Level update error',
      'Failed to update level',
    )
  }

  /**
   * Delete level
   */
  static async deleteLevel(id: string): Promise<{
    data: null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.deleteLevel(id),
      'Level deletion error',
      'Failed to delete level',
    )
  }

  /**
   * Create a new quiz type
   */
  static async createQuizType(quizTypeData: Partial<DbQuizType>): Promise<{
    data: DbQuizType | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.referenceApi.createQuizType(quizTypeData),
      'Quiz type creation error',
      'Failed to create quiz type',
    )
  }

  /**
   * Update a quiz type
   */
  static async updateQuizType(id: string, updates: Partial<DbQuizType>): Promise<{
    data: DbQuizType | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.referenceApi.updateQuizType(id, updates),
      'Quiz type update error',
      'Failed to update quiz type',
    )
  }

  /**
   * Delete a quiz type
   */
  static async deleteQuizType(id: string): Promise<{
    data: boolean | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.referenceApi.deleteQuizType(id),
      'Quiz type deletion error',
      'Failed to delete quiz type',
    )
  }
}

// Quiz Types Service
export class QuizTypeService {
  /**
   * Get all quiz types
   */
  static async getQuizTypes(): Promise<{
    data: DbQuizType[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getQuizTypes(),
      'Quiz types fetch error',
      'Failed to fetch quiz types',
    )
  }

  /**
   * Get quiz type by ID
   */
  static async getQuizTypeById(id: string): Promise<{
    data: DbQuizType | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getQuizTypeById(id),
      'Quiz type fetch error',
      'Failed to fetch quiz type',
    )
  }

  // Quiz type methods are now in the first section above
}

// Question Types Service
export class QuestionTypeService {
  /**
   * Get all question types
   */
  static async getQuestionTypes(): Promise<{
    data: DbQuestionType[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getQuestionTypes(),
      'Question types fetch error',
      'Failed to fetch question types',
    )
  }

  /**
   * Get question type by ID
   */
  static async getQuestionTypeById(id: string): Promise<{
    data: DbQuestionType | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.getQuestionTypeById(id),
      'Question type fetch error',
      'Failed to fetch question type',
    )
  }

  /**
   * Create new question type
   */
  static async createQuestionType(questionType: Partial<DbQuestionType>): Promise<{
    data: DbQuestionType | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.createQuestionType(questionType),
      'Question type creation error',
      'Failed to create question type',
    )
  }

  /**
   * Update question type
   */
  static async updateQuestionType(id: string, updates: Partial<DbQuestionType>): Promise<{
    data: DbQuestionType | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.updateQuestionType(id, updates),
      'Question type update error',
      'Failed to update question type',
    )
  }

  /**
   * Delete question type
   */
  static async deleteQuestionType(id: string): Promise<{
    data: null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.referenceApi.deleteQuestionType(id),
      'Question type deletion error',
      'Failed to delete question type',
    )
  }
}