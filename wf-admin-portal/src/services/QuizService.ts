import { repositories } from '@/repositories'
import { handleRepositoryCall } from 'wf-shared'
import type { PaginationParams, ContentFilter, DbQuiz, QuizWithRelations } from 'wf-shared/types'

export class QuizService {
  /**
   * Get quizzes with pagination and filtering
   */
  static async getQuizzes(params: PaginationParams & ContentFilter): Promise<{
    data: { data: QuizWithRelations[]; count: number; totalPages: number } | null;
    error: string | null;
  }> {
    const result = await handleRepositoryCall(
      () => repositories.container.quizApi.getQuizzesWithRelations(params),
      'Quizzes fetch error',
      'Failed to fetch quizzes',
    )

    // Transform the repository response to match the expected format
    if (result.data) {
      return {
        data: {
          data: result.data.data,
          count: result.data.total,
          totalPages: Math.ceil(result.data.total / params.pageSize),
        },
        error: null,
      }
    }

    return { data: null, error: result.error }
  }

  /**
   * Get quiz by ID with full relationships
   */
  static async getQuizById(id: string): Promise<{
    data: QuizWithRelations | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.quizApi.getById(id),
      'Quiz fetch error',
      'Failed to fetch quiz',
    )
  }

  /**
   * Get quiz by ID with questions and all relationships
   */
  static async getQuizWithQuestionsById(id: string): Promise<{
    data: QuizWithRelations | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.quizApi.getQuizWithQuestionsById(id),
      'Quiz with questions fetch error',
      'Failed to fetch quiz with questions',
    )
  }

  /**
   * Create new quiz
   */
  static async createQuiz(quiz: Partial<DbQuiz>): Promise<{
    data: DbQuiz | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.quizApi.create(quiz),
      'Quiz creation error',
      'Failed to create quiz',
    )
  }

  /**
   * Update quiz
   */
  static async updateQuiz(id: string, updates: Partial<DbQuiz>): Promise<{
    data: DbQuiz | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.quizApi.update(id, updates),
      'Quiz update error',
      'Failed to update quiz',
    )
  }

  /**
   * Delete quiz with safety checks
   */
  static async deleteQuiz(id: string): Promise<{
    data: null;
    error: string | null;
  }> {
    const result = await handleRepositoryCall(
      () => repositories.container.quizApi.deleteQuizSafely(id),
      'Quiz deletion error',
      'Failed to delete quiz',
    )

    return {
      data: null,
      error: result.error,
    }
  }

  /**
   * Get total count of quizzes
   */
  static async getQuizzesCount(): Promise<{
    data: number | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.quizApi.getQuizzesCount(),
      'Quiz count error',
      'Failed to get quiz count',
    )
  }

  /**
   * Get count of active quizzes
   */
  static async getActiveQuizzesCount(): Promise<{
    data: number | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.quizApi.getActiveQuizzesCount(),
      'Active quiz count error',
      'Failed to get active quiz count',
    )
  }

  /**
   * Get quizzes by category
   */
  static async getQuizzesByCategory(categoryId: string): Promise<{
    data: DbQuiz[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.quizApi.getQuizzesByCategory(categoryId),
      'Quizzes by category fetch error',
      'Failed to fetch quizzes by category',
    )
  }

  /**
   * Get quizzes by level
   */
  static async getQuizzesByLevel(levelId: string): Promise<{
    data: DbQuiz[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.quizApi.getQuizzesByLevel(levelId),
      'Quizzes by level fetch error',
      'Failed to fetch quizzes by level',
    )
  }

  /**
   * Toggle quiz active status
   */
  static async toggleQuizStatus(id: string, isActive: boolean): Promise<{
    data: DbQuiz | null;
    error: string | null;
  }> {
    return QuizService.updateQuiz(id, { is_active: isActive })
  }

  /**
   * Duplicate quiz with new title
   */
  static async duplicateQuiz(id: string, newTitle: string): Promise<{
    data: DbQuiz | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.quizApi.duplicateQuiz(id, newTitle),
      'Quiz duplication error',
      'Failed to duplicate quiz',
    )
  }

  /**
   * Add multiple questions to a quiz in bulk
   */
  static async addQuestionsToQuiz(
    quizId: string,
    questionIds: string[],
    options: {
      defaultPoints?: number
      startingOrderIndex?: number
    } = {},
  ): Promise<{
    data: void | null;
    error: string | null;
  }> {
    try {
      const { defaultPoints = 1, startingOrderIndex } = options


      // Get existing questions to determine order index
      const existingQuestions = await repositories.container.quizApi.getQuizQuestions(quizId)

      if (!existingQuestions.success) {
        return {
          data: null,
          error: `Failed to get existing questions: ${existingQuestions.error?.message}`,
        }
      }

      // Get current max order index if starting index not provided
      let orderIndex = startingOrderIndex
      if (orderIndex === undefined) {
        if (existingQuestions.data && existingQuestions.data.length > 0) {
          const maxOrder = Math.max(0, ...existingQuestions.data.map(q => q.order_index || 0))
          orderIndex = maxOrder + 1
        } else {
          orderIndex = 1
        }
      }


      // Add questions one by one with incremental order
      for (let i = 0; i < questionIds.length; i++) {

        const result = await repositories.container.quizApi.addQuestionToQuiz(
          quizId,
          questionIds[i],
          {
            order_index: orderIndex + i,
            points: defaultPoints,
            is_active: true,
          },
        )


        if (!result.success) {
          const errorMsg = `Failed to add question ${questionIds[i]}: ${result.error?.message || 'Unknown error'}`
          return {
            data: null,
            error: errorMsg,
          }
        }
      }

      return { data: null, error: null }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred'
      return {
        data: null,
        error: errorMsg,
      }
    }
  }
}