import { UserService } from '../UserService'
import { ServiceContainer } from '../ServiceContainer'

describe('UserService Integration Tests', () => {
  it('should be instantiable', () => {
    const userService = new UserService()
    expect(userService).toBeInstanceOf(UserService)
  })

  it('should work with ServiceContainer', () => {
    const container = ServiceContainer.getInstance()
    const userService = container.userService
    expect(userService).toBeInstanceOf(UserService)
  })

  it('should have the correct method signatures', () => {
    const userService = new UserService()
    
    expect(typeof userService.getUsers).toBe('function')
    expect(typeof userService.getUserById).toBe('function')
    expect(typeof userService.updateUser).toBe('function')
    expect(typeof userService.getUsersCount).toBe('function')
    expect(typeof userService.getUsersByRole).toBe('function')
    expect(typeof userService.toggleUserStatus).toBe('function')
  })

  it('should handle parameters correctly', () => {
    const userService = new UserService()
    
    // These should not throw errors when called with proper parameters
    expect(() => {
      userService.getUsers({ page: 1, pageSize: 10 })
    }).not.toThrow()
    
    expect(() => {
      userService.getUserById('test-id')
    }).not.toThrow()
    
    expect(() => {
      userService.updateUser('test-id', { first_name: 'Test' })
    }).not.toThrow()
  })
})