import { UserService } from '../UserService'
import { ServiceContainer } from '../ServiceContainer'

// Simple mock database for testing
class MockDatabase {
  private mockData: any = {}
  private lastQuery: any = null

  from = (table: string) => {
    const mockQuery = {
      select: (columns?: string, options?: any) => {
        // Only set type to select if no primary operation has been set
        if (!this.lastQuery || !this.lastQuery.primaryType) {
          this.lastQuery = { table, columns, options, type: 'select', primaryType: 'select' }
        } else {
          this.lastQuery = { ...this.lastQuery, columns, options }
        }
        return mockQuery
      },
      insert: (data: any) => {
        this.lastQuery = { table, data, type: 'insert', primaryType: 'insert' }
        return mockQuery
      },
      update: (data: any) => {
        this.lastQuery = { table, data, type: 'update', primaryType: 'update' }
        return mockQuery
      },
      delete: () => {
        this.lastQuery = { table, type: 'delete', primaryType: 'delete' }
        return mockQuery
      },
      eq: (column: string, value: any) => {
        this.lastQuery = { ...this.lastQuery, eq: { column, value } }
        return mockQuery
      },
      neq: (column: string, value: any) => {
        this.lastQuery = { ...this.lastQuery, neq: { column, value } }
        return mockQuery
      },
      gte: (column: string, value: any) => {
        this.lastQuery = { ...this.lastQuery, gte: { column, value } }
        return mockQuery
      },
      limit: (count: number) => {
        this.lastQuery = { ...this.lastQuery, limit: count }
        return mockQuery
      },
      range: (from: number, to: number) => {
        this.lastQuery = { ...this.lastQuery, range: { from, to } }
        return mockQuery
      },
      order: (column: string, options?: any) => {
        this.lastQuery = { ...this.lastQuery, order: { column, options } }
        return mockQuery
      },
      single: () => {
        this.lastQuery = { ...this.lastQuery, single: true }
        return Promise.resolve({ 
          data: this.mockData[table]?.[0] || null, 
          error: null,
        })
      },
    }

    // Make the query thenable
    const thenable = {
      ...mockQuery,
      then: (onfulfilled?: any, onrejected?: any) => {
        // Handle different query types
        let result
        const tableData = this.mockData[table] || []
        
        if (this.lastQuery?.single) {
          result = {
            data: tableData[0] || null,
            error: null,
          }
        } else if (this.lastQuery?.options?.head) {
          // For count queries with head: true
          result = {
            data: null,
            error: null,
            count: tableData.length,
          }
        } else {
          result = {
            data: tableData,
            error: null,
            count: tableData.length,
          }
        }
        return Promise.resolve(result).then(onfulfilled, onrejected)
      },
    }

    return thenable
  }

  setMockData(table: string, data: any[]) {
    this.mockData[table] = data
  }

  getLastQuery() {
    return this.lastQuery
  }

  clearMockData() {
    this.mockData = {}
    this.lastQuery = null
  }
}

describe('UserService', () => {
  let userService: typeof UserService
  let mockDb: MockDatabase

  beforeEach(() => {
    mockDb = new MockDatabase()
    userService = UserService // UserService is now static, uses repository pattern
  })

  afterEach(() => {
    mockDb.clearMockData()
  })

  describe('getUsers', () => {
    it('should call the correct database query with pagination', async () => {
      const mockUsers = [
        { id: '1', email: '<EMAIL>', first_name: 'John', last_name: 'Doe' },
        { id: '2', email: '<EMAIL>', first_name: 'Jane', last_name: 'Smith' },
      ]
      
      mockDb.setMockData('users', mockUsers)
      
      const result = await userService.getUsers({
        page: 1,
        pageSize: 10,
      })
      
      // The actual implementation returns UserWithRelations[], not exact input
      expect(result.data).toEqual(mockUsers)
      expect(result.count).toBe(2)
      expect(result.totalPages).toBe(1)
      
      const lastQuery = mockDb.getLastQuery()
      expect(lastQuery.table).toBe('users')
      expect(lastQuery.primaryType).toBe('select')
    })

    it('should handle pagination correctly for page 2', async () => {
      const mockUsers = Array.from({ length: 15 }, (_, i) => ({
        id: (i + 1).toString(),
        email: `user${i + 1}@test.com`,
        first_name: 'User',
        last_name: `${i + 1}`,
      }))
      
      mockDb.setMockData('users', mockUsers)
      
      await userService.getUsers({
        page: 2,
        pageSize: 10,
      })
      
      const lastQuery = mockDb.getLastQuery()
      expect(lastQuery.range).toEqual({ from: 10, to: 19 })
    })
  })

  describe('updateUser', () => {
    it('should update user with correct parameters', async () => {
      const userId = '1'
      const updates = { first_name: 'Updated Name' }
      
      mockDb.setMockData('users', [{ id: userId, ...updates }])
      
      await userService.updateUser(userId, updates)
      
      const lastQuery = mockDb.getLastQuery()
      expect(lastQuery.table).toBe('users')
      expect(lastQuery.primaryType).toBe('update')
      expect(lastQuery.data).toEqual(updates)
      expect(lastQuery.eq).toEqual({ column: 'id', value: userId })
    })
  })

  describe('getUsersCount', () => {
    it('should return the correct count of users', async () => {
      const mockUsers = [
        { id: '1', email: '<EMAIL>' },
        { id: '2', email: '<EMAIL>' },
        { id: '3', email: '<EMAIL>' },
      ]
      
      mockDb.setMockData('users', mockUsers)
      
      const count = await userService.getUsersCount()
      expect(count).toBe(3)
    })
  })

  describe('getUsersByRole', () => {
    it('should filter users by admin role', async () => {
      const mockUsers = [
        { id: '1', email: '<EMAIL>', role: 'admin' },
        { id: '2', email: '<EMAIL>', role: 'user' },
      ]
      
      mockDb.setMockData('users', mockUsers)
      
      await userService.getUsersByRole('admin')
      
      const lastQuery = mockDb.getLastQuery()
      expect(lastQuery.eq).toEqual({ column: 'role', value: 'admin' })
    })
  })

  describe('toggleUserStatus', () => {
    it('should update user with current timestamp', async () => {
      const userId = '1'
      const mockUser = { id: userId, email: '<EMAIL>' }
      
      mockDb.setMockData('users', [mockUser])
      
      await userService.toggleUserStatus(userId, true)
      
      const lastQuery = mockDb.getLastQuery()
      expect(lastQuery.table).toBe('users')
      expect(lastQuery.primaryType).toBe('update')
      expect(lastQuery.data).toHaveProperty('updated_at')
      expect(typeof lastQuery.data.updated_at).toBe('string')
    })

    it('should handle deactivation case', async () => {
      const userId = '1'
      const mockUser = { id: userId, email: '<EMAIL>' }
      
      mockDb.setMockData('users', [mockUser])
      
      await userService.toggleUserStatus(userId, false)
      
      const lastQuery = mockDb.getLastQuery()
      expect(lastQuery.data).toHaveProperty('updated_at')
    })
  })
})

describe('ServiceContainer Integration', () => {
  it('should allow dependency injection for testing', () => {
    const testContainer = ServiceContainer.createTestInstance()
    
    // Services created through container use repository pattern (no direct database access)
    const userService = testContainer.userService
    expect(userService).toBe(UserService)
  })
})