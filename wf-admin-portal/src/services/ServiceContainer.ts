import { UserService } from './UserService'
import { QuizService } from './QuizService'
import { QuestionService } from './QuestionService'
import { CategoryService, SubcategoryService, LevelService, QuizTypeService, QuestionTypeService } from './ReferenceService'
import { ValidationService } from './ValidationService'
import { DashboardService } from './DashboardService'

/**
 * Service Container for accessing services
 * Hybrid approach: UserService and QuestionService are static, others are instance-based
 */
export class ServiceContainer {
  private static instance: ServiceContainer
  private services: Map<string, unknown> = new Map()

  private constructor() {
    // No database dependencies - services use repository pattern
  }

  static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer()
    }
    return ServiceContainer.instance
  }

  // Refactored services (static)
  get userService() {
    return UserService
  }

  get questionService() {
    return QuestionService
  }

  get categoryService() {
    return CategoryService
  }

  get subcategoryService() {
    return SubcategoryService
  }

  get levelService() {
    return LevelService
  }

  get quizTypeService() {
    return QuizTypeService
  }

  get questionTypeService() {
    return QuestionTypeService
  }

  get quizService() {
    return QuizService
  }

  get validationService() {
    return ValidationService
  }

  get dashboardService() {
    return DashboardService
  }

  // Non-refactored services (instance-based)

  // For testing: allows replacing services with mocks
  setService<T>(name: string, service: T): void {
    this.services.set(name, service)
  }

  // For testing: clear all services
  clear(): void {
    this.services.clear()
  }

  // For testing: create new instance for test isolation
  static createTestInstance(): ServiceContainer {
    return new ServiceContainer()
  }
}

/**
 * Default container instance
 * Use this for accessing services throughout the application
 */
export const serviceContainer = ServiceContainer.getInstance()

/**
 * Direct access to refactored static service classes
 * Alternative to using the container for refactored services
 */
export const services = {
  userService: UserService,
  questionService: QuestionService,
  categoryService: CategoryService,
  subcategoryService: SubcategoryService,
  levelService: LevelService,
  quizTypeService: QuizTypeService,
  questionTypeService: QuestionTypeService,
  quizService: QuizService,
  validationService: ValidationService,
  dashboardService: DashboardService,
} as const