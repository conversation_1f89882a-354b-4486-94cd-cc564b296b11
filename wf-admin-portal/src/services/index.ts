// Service Container (recommended way to access services)
export { ServiceContainer, serviceContainer, services } from './ServiceContainer'

// Base service for non-refactored services
export { BaseService } from './BaseService'

// Individual service classes (for advanced usage or testing)
export { UserService } from './UserService'
export { QuizService } from './QuizService'
export { QuestionService } from './QuestionService'
export { CategoryService, SubcategoryService, LevelService, QuizTypeService, QuestionTypeService } from './ReferenceService'
export { ValidationService } from './ValidationService'
export { DashboardService } from './DashboardService'

// Note: Legacy AdminService has been removed as it was redundant with other services