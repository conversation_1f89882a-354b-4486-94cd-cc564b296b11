import { repositories } from '@/repositories'
import { handleRepositoryCall } from 'wf-shared'
import type { PaginationParams, ContentFilter, DbQuestion, DbQuestionOption, DbExplanation, QuestionWithRelations } from 'wf-shared/types'

/**
 * Question Service
 * Acts as middleware between UI and QuestionApiRepository
 * Handles all question-related operations and business logic
 */
export class QuestionService {

  /**
   * Get questions with pagination and filtering
   */
  static async getQuestions(params: PaginationParams & ContentFilter): Promise<{
    data: {
      data: QuestionWithRelations[]
      count: number
      totalPages: number
    } | null;
    error: string | null;
  }> {
    const result = await handleRepositoryCall(
      () => repositories.container.questionApi.getQuestionsWithRelations(params),
      'Questions fetch error',
      'Failed to fetch questions',
    )

    // Transform the repository response to match the expected format
    if (result.data) {
      return {
        data: {
          data: result.data.data,
          count: result.data.total,
          totalPages: Math.ceil(result.data.total / (params.pageSize || 10)),
        },
        error: null,
      }
    }

    return { data: null, error: result.error }
  }

  /**
   * Get question by ID with relationships
   */
  static async getQuestionById(id: string): Promise<{
    data: DbQuestion | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.questionApi.getById(id),
      'Question fetch error',
      'Failed to fetch question details',
    )
  }

  /**
   * Create new question with validation
   */
  static async createQuestion(question: Partial<DbQuestion>): Promise<{
    data: DbQuestion | null;
    error: string | null;
  }> {
    // Business logic: Validate question data before creation
    const validationError = QuestionService.validateQuestionData(question)
    if (validationError) {
      return {
        data: null,
        error: validationError,
      }
    }

    return handleRepositoryCall(
      () => repositories.container.questionApi.create(question),
      'Question create error',
      'Failed to create question',
    )
  }

  /**
   * Update question with validation
   */
  static async updateQuestion(id: string, updates: Partial<DbQuestion>): Promise<{
    data: DbQuestion | null;
    error: string | null;
  }> {
    // Business logic: Validate update data
    const validationError = QuestionService.validateQuestionData(updates)
    if (validationError) {
      return {
        data: null,
        error: validationError,
      }
    }

    return handleRepositoryCall(
      () => repositories.container.questionApi.update(id, updates),
      'Question update error',
      'Failed to update question',
    )
  }

  /**
   * Delete question with safety checks and business logic
   */
  static async deleteQuestion(id: string): Promise<{
    data: null;
    error: string | null;
  }> {
    const result = await handleRepositoryCall(
      () => repositories.container.questionApi.deleteQuestionWithSafetyChecks(id),
      'Question delete error',
      'Failed to delete question',
    )

    return {
      data: null,
      error: result.error,
    }
  }

  /**
   * Get total count of questions
   */
  static async getQuestionsCount(): Promise<{
    data: number | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.questionApi.getCount(),
      'Questions count error',
      'Failed to get questions count',
    )
  }

  /**
   * Get questions by quiz ID
   */
  static async getQuestionsByQuiz(quizId: string): Promise<{
    data: DbQuestion[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.questionApi.getQuestionsByQuiz(quizId),
      'Questions by quiz fetch error',
      'Failed to fetch questions by quiz',
    )
  }

  /**
   * Get questions without explanations
   */
  static async getQuestionsWithoutExplanations(): Promise<{
    data: { id: string; question_text: string; explanations: { id: string }[] }[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.questionApi.getQuestionsWithoutExplanations(),
      'Questions without explanations fetch error',
      'Failed to fetch questions without explanations',
    )
  }

  /**
   * Get questions with few options
   */
  static async getQuestionsWithFewOptions(): Promise<{
    data: { id: string; question_text: string; question_options: { id: string }[] }[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.questionApi.getQuestionsWithFewOptions(),
      'Questions with few options fetch error',
      'Failed to fetch questions with few options',
    )
  }

  /**
   * Create question with options and explanation (enhanced with validation)
   */
  static async createQuestionWithOptionsAndExplanation(data: {
    question: Partial<DbQuestion>
    options: Partial<DbQuestionOption>[]
    explanation?: Partial<DbExplanation>
  }): Promise<{
    data: DbQuestion | null;
    error: string | null;
  }> {
    // Business logic: Validate complete question data
    const validationError = QuestionService.validateCompleteQuestionData(data)
    if (validationError) {
      return {
        data: null,
        error: validationError,
      }
    }

    return handleRepositoryCall(
      () => repositories.container.questionApi.createQuestionWithOptionsAndExplanation(data),
      'Question with options creation error',
      'Failed to create question with options and explanation',
    )
  }

  /**
   * Search questions by text content
   */
  static async searchQuestions(searchTerm: string, params?: PaginationParams): Promise<{
    data: {
      data: QuestionWithRelations[]
      count: number
      totalPages: number
    } | null;
    error: string | null;
  }> {
    if (!searchTerm || searchTerm.trim().length < 2) {
      return {
        data: null,
        error: 'Search term must be at least 2 characters long',
      }
    }

    const result = await handleRepositoryCall(
      () => repositories.container.questionApi.searchQuestions(searchTerm, params),
      'Question search error',
      'Failed to search questions',
    )

    if (result.data) {
      return {
        data: {
          data: result.data.data,
          count: result.data.total,
          totalPages: Math.ceil(result.data.total / ((params?.pageSize) || 10)),
        },
        error: null,
      }
    }

    return { data: null, error: result.error }
  }

  /**
   * Get questions by quiz ID with pagination
   */
  static async getQuestionsByQuizId(quizId: string, params?: PaginationParams): Promise<{
    data: {
      data: QuestionWithRelations[]
      count: number
      totalPages: number
    } | null;
    error: string | null;
  }> {
    const result = await handleRepositoryCall(
      () => repositories.container.questionApi.getQuestionsByQuizId(quizId, params),
      'Questions by quiz fetch error',
      'Failed to fetch questions by quiz',
    )

    if (result.data) {
      return {
        data: {
          data: result.data.data,
          count: result.data.total,
          totalPages: Math.ceil(result.data.total / ((params?.pageSize) || 10)),
        },
        error: null,
      }
    }

    return { data: null, error: result.error }
  }

  /**
   * Get questions that need validation
   */
  static async getQuestionsNeedingValidation(params?: PaginationParams): Promise<{
    data: {
      data: QuestionWithRelations[]
      count: number
      totalPages: number
    } | null;
    error: string | null;
  }> {
    const result = await handleRepositoryCall(
      () => repositories.container.questionApi.getQuestionsNeedingValidation(params),
      'Questions validation fetch error',
      'Failed to fetch questions needing validation',
    )

    if (result.data) {
      return {
        data: {
          data: result.data.data,
          count: result.data.total,
          totalPages: Math.ceil(result.data.total / ((params?.pageSize) || 10)),
        },
        error: null,
      }
    }

    return { data: null, error: result.error }
  }

  // Business Logic and Validation Methods

  /**
   * Validate question data
   */
  private static validateQuestionData(question: Partial<DbQuestion>): string | null {
    if (question.question_text && question.question_text.trim().length < 10) {
      return 'Question text must be at least 10 characters long'
    }

    if (question.question_text && question.question_text.length > 1000) {
      return 'Question text must not exceed 1000 characters'
    }

    if (question.difficulty_level !== undefined && (question.difficulty_level < 1 || question.difficulty_level > 5)) {
      return 'Difficulty level must be between 1 and 5'
    }

    return null
  }

  /**
   * Validate complete question data with options and explanation
   */
  private static validateCompleteQuestionData(data: {
    question: Partial<DbQuestion>
    options: Partial<DbQuestionOption>[]
    explanation?: Partial<DbExplanation>
  }): string | null {
    // Validate question
    const questionError = QuestionService.validateQuestionData(data.question)
    if (questionError) {
      return questionError
    }

    // Validate options
    if (!data.options || data.options.length < 2) {
      return 'Question must have at least 2 options'
    }

    if (data.options.length > 6) {
      return 'Question cannot have more than 6 options'
    }

    const correctOptions = data.options.filter(option => option.is_correct)
    if (correctOptions.length === 0) {
      return 'Question must have at least one correct option'
    }

    // Validate option texts
    for (const option of data.options) {
      if (!option.option_text || option.option_text.trim().length < 1) {
        return 'All options must have text'
      }
      if (option.option_text.length > 500) {
        return 'Option text must not exceed 500 characters'
      }
    }

    // Validate explanation if provided
    if (data.explanation && data.explanation.content) {
      if (data.explanation.content.length > 2000) {
        return 'Explanation content must not exceed 2000 characters'
      }
    }

    return null
  }

  /**
   * Calculate question difficulty based on user performance
   */
  static async calculateQuestionDifficulty(_questionId: string): Promise<{
    data: {
      difficulty: number
      totalAttempts: number
      correctAttempts: number
      successRate: number
    } | null;
    error: string | null;
  }> {
    // This would typically involve analyzing user answers from the database
    // For now, return a placeholder implementation
    // TODO: Implement actual difficulty calculation based on user_answers table
    return {
      data: {
        difficulty: 3,
        totalAttempts: 0,
        correctAttempts: 0,
        successRate: 0,
      },
      error: null,
    }
  }

  /**
   * Get question statistics
   */
  static async getQuestionStatistics(): Promise<{
    data: {
      total: number
      byDifficulty: Record<number, number>
      withExplanations: number
      withoutExplanations: number
      averageOptions: number
    } | null;
    error: string | null;
  }> {
    // This would involve aggregating data from the repository
    // For now, return a placeholder implementation
    return {
      data: {
        total: 0,
        byDifficulty: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        withExplanations: 0,
        withoutExplanations: 0,
        averageOptions: 4,
      },
      error: null,
    }
  }

  /**
   * Add a question to a quiz (new many-to-many relationship)
   */
  static async addQuestionToQuiz(
    quizId: string,
    questionId: string,
    orderIndex?: number,
    points: number = 1,
  ): Promise<{
    data: unknown | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.questionApi.addQuestionToQuiz(quizId, questionId, orderIndex, points),
      'Question assignment error',
      'Failed to assign question to quiz',
    )
  }

  /**
   * Remove a question from a quiz
   */
  static async removeQuestionFromQuiz(
    quizId: string,
    questionId: string,
  ): Promise<{
    data: unknown | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.questionApi.removeQuestionFromQuiz(quizId, questionId),
      'Question removal error',
      'Failed to remove question from quiz',
    )
  }

  /**
   * Update quiz-question relationship (order, points, etc.)
   */
  static async updateQuizQuestion(
    quizId: string,
    questionId: string,
    updates: { order_index?: number; points?: number; is_active?: boolean },
  ): Promise<{
    data: unknown | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.questionApi.updateQuizQuestion(quizId, questionId, updates),
      'Quiz question update error',
      'Failed to update quiz question relationship',
    )
  }
}