/**
 * @deprecated BaseService is deprecated and should not be used.
 * Services should use the repository pattern instead.
 * Use repositories from @/repositories for database operations.
 * 
 * This class is kept for backward compatibility but will be removed in a future version.
 * All methods now throw errors directing to use the repository pattern.
 */
export abstract class BaseService {
  protected handleError(error: any, context: string): never {
    console.error(`Error in ${context}:`, error)
    throw error instanceof Error ? error : new Error(`Unknown error in ${context}`)
  }

  protected async getList(): Promise<never> {
    throw new Error('BaseService.getList() is deprecated. Use repository pattern instead. Import repositories from @/repositories and use the appropriate repository method.')
  }

  protected async getById(): Promise<never> {
    throw new Error('BaseService.getById() is deprecated. Use repository pattern instead. Import repositories from @/repositories and use the appropriate repository method.')
  }

  protected async create(): Promise<never> {
    throw new Error('BaseService.create() is deprecated. Use repository pattern instead. Import repositories from @/repositories and use the appropriate repository method.')
  }

  protected async update(): Promise<never> {
    throw new Error('BaseService.update() is deprecated. Use repository pattern instead. Import repositories from @/repositories and use the appropriate repository method.')
  }

  protected async delete(): Promise<never> {
    throw new Error('BaseService.delete() is deprecated. Use repository pattern instead. Import repositories from @/repositories and use the appropriate repository method.')
  }

  protected async deleteRecord(): Promise<never> {
    throw new Error('BaseService.deleteRecord() is deprecated. Use repository pattern instead. Import repositories from @/repositories and use the appropriate repository method.')
  }

  protected async getCount(): Promise<never> {
    throw new Error('BaseService.getCount() is deprecated. Use repository pattern instead. Import repositories from @/repositories and use the appropriate repository method.')
  }

  protected async getReferenceData(): Promise<never> {
    throw new Error('BaseService.getReferenceData() is deprecated. Use repository pattern instead. Import repositories from @/repositories and use the appropriate repository method.')
  }
}
