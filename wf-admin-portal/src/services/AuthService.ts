// Authentication service using repository pattern
// Handles authentication logic with proper separation of concerns

import type { AuthStorageRepository, RememberedCredentials } from '@/repositories'
import type { LoginCredentials, AuthResult, UserSession, Database } from 'wf-shared/types'
import type { SupabaseClient } from '@supabase/supabase-js'

export class AuthService {
  private database: SupabaseClient<Database>
  private authStorage: AuthStorageRepository

  constructor(database: SupabaseClient<Database>, authStorage: AuthStorageRepository) {
    this.database = database
    this.authStorage = authStorage
  }

  /**
   * Sign in user with email and password
   */
  async signIn(credentials: LoginCredentials): Promise<AuthResult> {
    try {
      const { error } = await this.database.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      })

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.name },
        }
      }

      // Save credentials if remember me is enabled
      if (credentials.rememberMe) {
        await this.authStorage.saveCredentials(credentials.email, true)
      } else {
        await this.authStorage.clearCredentials()
      }

      return {
        success: true,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Sign in failed', code: 'SIGNIN_ERROR' },
      }
    }
  }

  /**
   * Sign out current user
   */
  async signOut(): Promise<void> {
    try {
      await this.database.auth.signOut()
      // Keep remembered credentials if they exist, only clear session
    } catch (error) {
      console.error('Sign out error:', error)
      throw error
    }
  }

  /**
   * Get current user session
   */
  async getCurrentSession(): Promise<UserSession | null> {
    try {
      const { data: { session }, error } = await this.database.auth.getSession()

      if (error || !session?.user) {
        return null
      }

      return {
        id: session.user.id,
        email: session.user.email || '',
        isAuthenticated: true,
      }
    } catch (error) {
      console.error('Get session error:', error)
      return null
    }
  }

  /**
   * Get stored credentials for remember me functionality
   */
  async getStoredCredentials(): Promise<RememberedCredentials> {
    return await this.authStorage.loadCredentials()
  }

  /**
   * Clear stored credentials
   */
  async clearStoredCredentials(): Promise<void> {
    await this.authStorage.clearCredentials()
  }

  /**
   * Check if user has stored credentials
   */
  async hasStoredCredentials(): Promise<boolean> {
    return await this.authStorage.hasStoredCredentials()
  }

  /**
   * Refresh current session
   */
  async refreshSession(): Promise<AuthResult> {
    try {
      const { error } = await this.database.auth.refreshSession()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.name },
        }
      }

      return {
        success: true,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Session refresh failed', code: 'REFRESH_ERROR' },
      }
    }
  }

  /**
   * Reset password for email
   */
  async resetPassword(email: string): Promise<AuthResult> {
    try {
      const { error } = await this.database.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.name },
        }
      }

      return {
        success: true,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Password reset failed', code: 'RESET_ERROR' },
      }
    }
  }
}