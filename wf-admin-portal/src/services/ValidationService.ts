import { repositories } from '@/repositories'
import { handleRepositoryCall } from 'wf-shared'
import type { ValidationIssue } from '@/types'

export class ValidationService {
  /**
   * Validate all content and return issues
   */
  static async validateContent(): Promise<{
    data: ValidationIssue[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      async () => {
        const issues: ValidationIssue[] = []

        // Run validation checks in parallel
        const [
          questionsWithoutExplanations,
          questionsWithFewOptions,
          orphanedOptions,
          inactiveQuizzesWithAttempts,
        ] = await Promise.all([
          ValidationService.checkQuestionsWithoutExplanations(),
          ValidationService.checkQuestionsWithFewOptions(),
          ValidationService.checkOrphanedOptions(),
          ValidationService.checkInactiveQuizzesWithAttempts(),
        ])

        issues.push(...questionsWithoutExplanations)
        issues.push(...questionsWithFewOptions)
        issues.push(...orphanedOptions)
        issues.push(...inactiveQuizzesWithAttempts)

        return {
          success: true,
          data: issues,
        }
      },
      'Content validation error',
      'Failed to validate content',
    )
  }

  /**
   * Check for questions without explanations
   */
  private static async checkQuestionsWithoutExplanations(): Promise<ValidationIssue[]> {
    try {
      const response = await repositories.validationApi.getQuestionsWithoutExplanations()

      if (!response.success || !response.data) {
        return []
      }

      const issues = response.data.map((question: { id: string; question_text: string }) => ({
        id: `missing-explanation-${question.id}`,
        type: 'missing_explanation' as const,
        severity: 'medium' as const,
        entity: 'question' as const,
        entityId: question.id,
        message: `Question "${question.question_text.substring(0, 50)}..." is missing explanation`,
        suggestions: ['Add detailed explanation for this question'],
      }))

      return issues
    } catch (error) {
      return []
    }
  }

  /**
   * Check for questions with few options
   */
  private static async checkQuestionsWithFewOptions(): Promise<ValidationIssue[]> {
    try {
      const response = await repositories.validationApi.getQuestionsWithFewOptions()

      if (!response.success || !response.data) {
        return []
      }

      // The filtering is now done in the repository, so we just map the results
      const issues = response.data.map((question: { id: string; question_text: string }) => ({
        id: `few-options-${question.id}`,
        type: 'invalid_option' as const,
        severity: 'high' as const,
        entity: 'question' as const,
        entityId: question.id,
        message: `Question "${question.question_text.substring(0, 50)}..." has fewer than 2 options`,
        suggestions: ['Add more answer options', 'Review question type requirements'],
      }))

      return issues
    } catch (error) {
      return []
    }
  }

  /**
   * Check for orphaned options
   */
  private static async checkOrphanedOptions(): Promise<ValidationIssue[]> {
    try {
      const response = await repositories.validationApi.getOrphanedOptions()

      if (!response.success || !response.data) {
        return []
      }

      return response.data.map((option: { id: string; option_text: string }) => ({
        id: `orphaned-option-${option.id}`,
        type: 'orphaned_data' as const,
        severity: 'low' as const,
        entity: 'question_option' as const,
        entityId: option.id,
        message: `Option "${option.option_text}" has no associated question`,
        suggestions: ['Remove orphaned option', 'Associate with correct question'],
      }))
    } catch (error) {
      return []
    }
  }

  /**
   * Check for inactive quizzes with attempts
   */
  private static async checkInactiveQuizzesWithAttempts(): Promise<ValidationIssue[]> {
    try {
      const response = await repositories.validationApi.getInactiveQuizzesWithAttempts()

      if (!response.success || !response.data) {
        return []
      }

      return response.data
        .filter((quiz: { quiz_attempts?: { id: string }[] }) => (quiz.quiz_attempts?.length || 0) > 0)
        .map((quiz: { id: string; title: string | null }) => ({
          id: `inactive-quiz-with-attempts-${quiz.id}`,
          type: 'data_inconsistency' as const,
          severity: 'medium' as const,
          entity: 'quiz' as const,
          entityId: quiz.id,
          message: `Inactive quiz "${quiz.title || 'Untitled'}" still has user attempts`,
          suggestions: ['Reactivate quiz', 'Archive user attempts', 'Review quiz status'],
        }))
    } catch (error) {
      return []
    }
  }

  /**
   * Get validation statistics
   */
  static async getValidationStats(): Promise<{
    data: {
      total: number
      high: number
      medium: number
      low: number
    } | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      async () => {
        const issuesResult = await ValidationService.validateContent()

        if (!issuesResult.data) {
          return {
            success: false,
            error: { message: 'Failed to get validation issues', code: 'VALIDATION_FAILED' },
          }
        }

        const issues = issuesResult.data

        const stats = {
          total: issues.length,
          high: issues.filter(issue => issue.severity === 'high').length,
          medium: issues.filter(issue => issue.severity === 'medium').length,
          low: issues.filter(issue => issue.severity === 'low').length,
        }

        return {
          success: true,
          data: stats,
        }
      },
      'Validation stats error',
      'Failed to get validation statistics',
    )
  }

  /**
   * Fix a validation issue
   */
  static async fixIssue(issueId: string): Promise<{
    data: boolean | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      async () => {
        // Parse issue ID to determine type and entity
        const [type, entityId] = issueId.split('-').slice(1)

        switch (type) {
          case 'explanation': {
            // Auto-generate a basic explanation
            const explanationResponse = await repositories.validationApi.createExplanation(
              entityId,
              'Explanation pending review',
              'basic',
            )

            if (!explanationResponse.success) {
              return {
                success: false,
                error: explanationResponse.error,
              }
            }

            return {
              success: true,
              data: true,
            }
          }

          case 'options':
            // Can't auto-fix, requires manual intervention
            return {
              success: true,
              data: false,
            }

          case 'option': {
            // Delete orphaned option
            const optionResponse = await repositories.validationApi.deleteOption(entityId)

            if (!optionResponse.success) {
              return {
                success: false,
                error: optionResponse.error,
              }
            }

            return {
              success: true,
              data: true,
            }
          }

          default:
            return {
              success: true,
              data: false,
            }
        }
      },
      'Issue fix error',
      'Failed to fix validation issue',
    )
  }
}