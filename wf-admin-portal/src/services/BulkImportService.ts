import { repositories } from '@/repositories'
import type { BulkImportResult } from '@/types'

export interface QuizImportData {
  key: string
  title: string
  description: string
  category_key: string
  level_key: string
  quiz_type_key: string
  difficulty_level: number
  total_questions: number
  time_limit_minutes: number
  instructions: string
  quiz_config?: string
  is_active: boolean
}

export interface QuestionImportData {
  quiz_key: string
  question_type_key: string
  question_text: string
  difficulty_level: number
  order_index: number
  metadata?: string
  is_active: boolean
  options: QuestionOptionImportData[]
  explanation?: ExplanationImportData
}

export interface QuestionOptionImportData {
  option_key: string
  option_text: string
  is_correct: boolean
  sort_order: number
}

export interface ExplanationImportData {
  explanation_type: string
  explanation_text: string
  metadata?: string
}

export class BulkImportService {
  /**
   * Import quizzes from CSV data
   */
  static async importQuizzes(csvData: string): Promise<BulkImportResult> {
    try {
      const lines = csvData.trim().split('\n')
      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
      
      let imported = 0
      let failed = 0
      const errors: Array<{ row: number; field: string; message: string }> = []

      for (let i = 1; i < lines.length; i++) {
        const values = this.parseCSVLine(lines[i])
        
        if (values.length !== headers.length) {
          errors.push({
            row: i + 1,
            field: 'general',
            message: `Expected ${headers.length} columns, got ${values.length}`,
          })
          failed++
          continue
        }

        const quizData: Partial<QuizImportData> = {}
        headers.forEach((header, index) => {
          (quizData as any)[header] = values[index]
        })

        // Validate required fields
        const validation = this.validateQuizData(quizData as QuizImportData, i + 1)
        if (validation.errors.length > 0) {
          errors.push(...validation.errors)
          failed++
          continue
        }

        // Import the quiz
        const result = await repositories.bulkImportApi.importQuiz(quizData as QuizImportData)
        if (result.success) {
          imported++
        } else {
          errors.push({
            row: i + 1,
            field: 'general',
            message: result.error?.message || 'Failed to import quiz',
          })
          failed++
        }
      }

      return {
        success: true,
        imported,
        failed,
        errors,
      }
    } catch (error) {
      return {
        success: false,
        imported: 0,
        failed: 0,
        errors: [{
          row: 0,
          field: 'general',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
        }],
      }
    }
  }

  /**
   * Import questions from CSV data
   */
  static async importQuestions(csvData: string): Promise<BulkImportResult> {
    try {
      const lines = csvData.trim().split('\n')
      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
      
      let imported = 0
      let failed = 0
      const errors: Array<{ row: number; field: string; message: string }> = []

      for (let i = 1; i < lines.length; i++) {
        const values = this.parseCSVLine(lines[i])
        
        if (values.length !== headers.length) {
          errors.push({
            row: i + 1,
            field: 'general',
            message: `Expected ${headers.length} columns, got ${values.length}`,
          })
          failed++
          continue
        }

        const questionData: Partial<QuestionImportData> = {}
        headers.forEach((header, index) => {
          if (header.startsWith('option_') || header.startsWith('explanation_')) {
            // Handle options and explanations separately
            return
          }
          questionData[header as keyof QuestionImportData] = values[index] as any
        })

        // Parse options and explanations
        questionData.options = this.parseOptionsFromCSV(headers, values)
        questionData.explanation = this.parseExplanationFromCSV(headers, values)

        // Validate required fields
        const validation = this.validateQuestionData(questionData as QuestionImportData, i + 1)
        if (validation.errors.length > 0) {
          errors.push(...validation.errors)
          failed++
          continue
        }

        // Import the question
        const result = await repositories.bulkImportApi.importQuestion(questionData as QuestionImportData)
        if (result.success) {
          imported++
        } else {
          errors.push({
            row: i + 1,
            field: 'general',
            message: result.error?.message || 'Failed to import question',
          })
          failed++
        }
      }

      return {
        success: true,
        imported,
        failed,
        errors,
      }
    } catch (error) {
      return {
        success: false,
        imported: 0,
        failed: 0,
        errors: [{
          row: 0,
          field: 'general',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
        }],
      }
    }
  }

  /**
   * Execute SQL file import
   */
  static async importFromSQL(sqlContent: string): Promise<BulkImportResult> {
    try {
      const result = await repositories.bulkImportApi.executeSQLImport(sqlContent)
      
      if (result.success) {
        return {
          success: true,
          imported: result.data?.imported || 0,
          failed: result.data?.failed || 0,
          errors: result.data?.errors || [],
        }
      } else {
        return {
          success: false,
          imported: 0,
          failed: 1,
          errors: [{
            row: 0,
            field: 'sql',
            message: result.error?.message || 'SQL execution failed',
          }],
        }
      }
    } catch (error) {
      return {
        success: false,
        imported: 0,
        failed: 1,
        errors: [{
          row: 0,
          field: 'sql',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
        }],
      }
    }
  }

  /**
   * Generate CSV template for quizzes
   */
  static generateQuizTemplate(): string {
    const headers = [
      'key', 'title', 'description', 'category_key', 'level_key', 'quiz_type_key',
      'difficulty_level', 'total_questions', 'time_limit_minutes', 'instructions',
      'quiz_config', 'is_active',
    ]

    const sampleRows = [
      [
        'wf_quiz_a2_001',
        'Basic Word Formation A2',
        'Practice forming nouns and adjectives from base words at A2 level',
        'word_formation',
        'A2',
        'word_formation',
        '2',
        '10',
        '15',
        'Choose the correct word form to complete each sentence.',
        '{"shuffle_questions": true, "show_feedback_immediately": false, "attempts_allowed": 1}',
        'true',
      ],
      [
        'wf_quiz_b1_001',
        'Intermediate Word Formation B1',
        'Advanced word formation exercises for B1 level students',
        'word_formation',
        'B1',
        'word_formation',
        '3',
        '15',
        '20',
        'Select the appropriate word form for each context.',
        '{"shuffle_questions": true, "show_feedback_immediately": true, "attempts_allowed": 2}',
        'true',
      ],
      [
        'grammar_a2_001',
        'Present Tense Practice',
        'Basic present tense exercises for beginners',
        'grammar',
        'A2',
        'multiple_choice',
        '2',
        '12',
        '10',
        'Choose the correct present tense form.',
        '{"shuffle_questions": false, "show_feedback_immediately": true, "attempts_allowed": 3}',
        'true',
      ],
    ]

    const csvContent = [headers.join(',')]
    sampleRows.forEach(row => {
      csvContent.push(row.map(cell => `"${cell}"`).join(','))
    })

    return csvContent.join('\n')
  }

  /**
   * Generate CSV template for questions
   */
  static generateQuestionTemplate(): string {
    const headers = [
      'quiz_key', 'question_type_key', 'question_text', 'difficulty_level', 'order_index',
      'metadata', 'is_active', 'option_a_key', 'option_a_text', 'option_a_correct',
      'option_b_key', 'option_b_text', 'option_b_correct', 'option_c_key', 'option_c_text',
      'option_c_correct', 'option_d_key', 'option_d_text', 'option_d_correct',
      'explanation_type', 'explanation_text', 'explanation_metadata',
    ]

    const sampleRows = [
      [
        'wf_quiz_a2_001',
        'fill_in_blank',
        'He is a very ______ person.',
        '2',
        '1',
        '{"focus": "Adjective suffix -ive"}',
        'true',
        'A',
        'create',
        'false',
        'B',
        'creative',
        'true',
        'C',
        'creation',
        'false',
        'D',
        'creatively',
        'false',
        'correct_answer',
        'The adjective "creative" describes someone who is imaginative and able to create new ideas.',
        '{"option_key": "B"}',
      ],
      [
        'wf_quiz_a2_001',
        'fill_in_blank',
        'The ______ of the new building is very modern.',
        '2',
        '2',
        '{"focus": "Noun suffix -ion"}',
        'true',
        'A',
        'design',
        'false',
        'B',
        'designer',
        'false',
        'C',
        'designing',
        'false',
        'D',
        'design',
        'true',
        'correct_answer',
        'The noun "design" refers to the plan or style of something that is made.',
        '{"option_key": "D"}',
      ],
      [
        'grammar_a2_001',
        'multiple_choice',
        'She ______ to school every day.',
        '2',
        '1',
        '{"focus": "Present simple tense"}',
        'true',
        'A',
        'go',
        'false',
        'B',
        'goes',
        'true',
        'C',
        'going',
        'false',
        'D',
        'went',
        'false',
        'correct_answer',
        'Use "goes" for third person singular in present simple tense.',
        '{"option_key": "B"}',
      ],
      [
        'vocabulary_b1_001',
        'true_false',
        'The word "enormous" means very small.',
        '3',
        '1',
        '{"focus": "Vocabulary - size adjectives"}',
        'true',
        'A',
        'True',
        'false',
        'B',
        'False',
        'true',
        '',
        '',
        '',
        '',
        '',
        '',
        'correct_answer',
        '"Enormous" means very large, not small. The opposite would be "tiny" or "minuscule".',
        '{"option_key": "B"}',
      ],
    ]

    const csvContent = [headers.join(',')]
    sampleRows.forEach(row => {
      csvContent.push(row.map(cell => `"${cell}"`).join(','))
    })

    return csvContent.join('\n')
  }

  // Helper methods
  private static parseCSVLine(line: string): string[] {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current.trim())
    return result
  }

  private static validateQuizData(data: QuizImportData, row: number) {
    const errors: Array<{ row: number; field: string; message: string }> = []

    if (!data.key) errors.push({ row, field: 'key', message: 'Quiz key is required' })
    if (!data.title) errors.push({ row, field: 'title', message: 'Quiz title is required' })
    if (!data.category_key) errors.push({ row, field: 'category_key', message: 'Category key is required' })
    if (!data.level_key) errors.push({ row, field: 'level_key', message: 'Level key is required' })

    return { errors }
  }

  private static validateQuestionData(data: QuestionImportData, row: number) {
    const errors: Array<{ row: number; field: string; message: string }> = []

    if (!data.quiz_key) errors.push({ row, field: 'quiz_key', message: 'Quiz key is required' })
    if (!data.question_text) errors.push({ row, field: 'question_text', message: 'Question text is required' })
    if (!data.options || data.options.length === 0) {
      errors.push({ row, field: 'options', message: 'At least one option is required' })
    }

    return { errors }
  }

  private static parseOptionsFromCSV(headers: string[], values: string[]): QuestionOptionImportData[] {
    const options: QuestionOptionImportData[] = []
    const optionKeys = ['a', 'b', 'c', 'd']

    optionKeys.forEach((key, index) => {
      const keyIndex = headers.indexOf(`option_${key}_key`)
      const textIndex = headers.indexOf(`option_${key}_text`)
      const correctIndex = headers.indexOf(`option_${key}_correct`)

      if (textIndex !== -1 && values[textIndex] && values[textIndex].trim() !== '') {
        options.push({
          option_key: keyIndex !== -1 && values[keyIndex] ? values[keyIndex] : key.toUpperCase(),
          option_text: values[textIndex],
          is_correct: correctIndex !== -1 ? values[correctIndex].toLowerCase() === 'true' : false,
          sort_order: index + 1,
        })
      }
    })

    return options
  }

  private static parseExplanationFromCSV(headers: string[], values: string[]): ExplanationImportData | undefined {
    const typeIndex = headers.indexOf('explanation_type')
    const textIndex = headers.indexOf('explanation_text')
    const metadataIndex = headers.indexOf('explanation_metadata')

    if (typeIndex !== -1 && textIndex !== -1 && values[textIndex] && values[textIndex].trim() !== '') {
      return {
        explanation_type: values[typeIndex] || 'correct_answer',
        explanation_text: values[textIndex],
        metadata: metadataIndex !== -1 && values[metadataIndex] ? values[metadataIndex] : '{}',
      }
    }

    return undefined
  }
}
