import { repositories } from '@/repositories'
import { handleRepositoryCall } from 'wf-shared'
import type { PaginationParams, DbUser, UserWithAdminRelations } from 'wf-shared/types'

/**
 * Helper function for repository calls that return success/error format
 */
async function handleRepositoryCallSuccess(
  repositoryCall: () => Promise<{ success: boolean; error?: { message: string; code?: string } }>,
  _errorContext: string,
  defaultErrorMessage: string,
): Promise<{ success: boolean; error: string | null }> {
  try {
    const result = await repositoryCall()

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || defaultErrorMessage,
      }
    }

    return { success: true, error: null }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    }
  }
}

/**
 * User Service
 * Handles all user-related operations and business logic
 * Acts as middleware between UI and repository layer
 */
export class UserService {

  /**
   * Get users with pagination and filtering
   */
  static async getUsers(params: PaginationParams): Promise<{
    data: { data: UserWithAdminRelations[]; count: number; totalPages: number } | null;
    error: string | null;
  }> {
    const result = await handleRepositoryCall(
      () => repositories.container.userApi.getUsersWithLevels(params),
      'Users fetch error',
      'Failed to fetch users',
    )

    // Transform the repository response to match the expected format
    if (result.data) {
      return {
        data: {
          data: result.data.data,
          count: result.data.total,
          totalPages: Math.ceil(result.data.total / params.pageSize),
        },
        error: null,
      }
    }

    return { data: null, error: result.error }
  }

  /**
   * Get user by ID with detailed relationships
   */
  static async getUserById(id: string): Promise<{
    data: UserWithAdminRelations | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.userApi.getUserById(id),
      'User fetch error',
      'Failed to fetch user details',
    )
  }

  /**
   * Update user information
   */
  static async updateUser(id: string, updates: Partial<DbUser>): Promise<{
    data: DbUser | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.userApi.update(id, updates),
      'User update error',
      'Failed to update user',
    )
  }

  /**
   * Delete user with safety checks
   */
  static async deleteUser(id: string): Promise<{
    success: boolean;
    error: string | null;
  }> {
    return handleRepositoryCallSuccess(
      () => repositories.container.userApi.deleteUserWithSafetyChecks(id),
      'User delete error',
      'Failed to delete user',
    )
  }

  /**
   * Get total count of users
   */
  static async getUsersCount(): Promise<{
    data: number | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.userApi.getCount(),
      'Users count error',
      'Failed to get users count',
    )
  }

  /**
   * Get count of active users
   */
  static async getActiveUsersCount(): Promise<{
    data: number | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.userApi.getActiveUsersCount(),
      'Active users count error',
      'Failed to get active users count',
    )
  }

  /**
   * Get count of new users this month
   */
  static async getNewUsersThisMonth(): Promise<{
    data: number | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.userApi.getNewUsersThisMonth(),
      'New users count error',
      'Failed to get new users count',
    )
  }

  /**
   * Get users by role
   */
  static async getUsersByRole(role: 'admin' | 'user'): Promise<{
    data: DbUser[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.userApi.getUsersByRole(role),
      'Users by role fetch error',
      'Failed to fetch users by role',
    )
  }

  /**
   * Toggle user status (activate/deactivate)
   */
  static async toggleUserStatus(id: string, _isActive: boolean): Promise<{
    data: DbUser | null;
    error: string | null;
  }> {
    const updates = {
      updated_at: new Date().toISOString(),
      // TODO: Add is_active field to users table or use a different approach
      // deleted_at: isActive ? null : new Date().toISOString() // Field doesn't exist in schema
    }

    return handleRepositoryCall(
      () => repositories.container.userApi.update(id, updates),
      'User status toggle error',
      'Failed to toggle user status',
    )
  }
}