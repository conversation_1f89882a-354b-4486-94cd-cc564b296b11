import { repositories } from '@/repositories'
import { handleRepositoryCall } from 'wf-shared'
import type { ActivityTrend, QuizPerformance, RecentActivity } from '@/types'

/**
 * Dashboard service for admin portal
 * Acts as middleware between UI and DashboardApiRepository
 * Handles dashboard-related operations and statistics
 */
export class DashboardService {
  /**
   * Get basic dashboard statistics
   */
  static async getDashboardStats(): Promise<{
    data: {
      totalQuizzes: number
      totalQuestions: number
      totalUsers: number
      recentActivity: RecentActivity[]
    } | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.dashboardApi.getDashboardStats(),
      'Dashboard stats error',
      'Failed to fetch dashboard statistics',
    )
  }

  /**
   * Get recent activity
   */
  static async getRecentActivity(): Promise<{
    data: RecentActivity[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.dashboardApi.getRecentActivity(),
      'Recent activity fetch error',
      'Failed to fetch recent activity',
    )
  }

  /**
   * Get detailed dashboard statistics
   */
  static async getDetailedStats(): Promise<{
    data: {
      quizzes: { total: number; active: number; inactive: number }
      questions: { total: number; withExplanations: number; withoutExplanations: number; completionRate: number }
      users: { total: number; active: number; newThisMonth: number }
    } | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.dashboardApi.getDetailedStats(),
      'Detailed dashboard stats error',
      'Failed to fetch detailed dashboard statistics',
    )
  }

  /**
   * Get activity trends for specified number of days
   */
  static async getActivityTrends(days: number = 7): Promise<{
    data: ActivityTrend[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.dashboardApi.getActivityTrends(days),
      'Activity trends error',
      'Failed to fetch activity trends',
    )
  }

  /**
   * Get top performing quizzes
   */
  static async getTopPerformingQuizzes(limit: number = 5): Promise<{
    data: QuizPerformance[] | null;
    error: string | null;
  }> {
    return handleRepositoryCall(
      () => repositories.container.dashboardApi.getTopPerformingQuizzes(limit),
      'Top performing quizzes error',
      'Failed to fetch top performing quizzes',
    )
  }
}
