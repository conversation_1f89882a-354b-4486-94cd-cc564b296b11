// Repository exports for admin portal
// Centralized export of all repository classes and interfaces

// Interfaces (re-exported from wf-shared)
export type { IRepository, IStorageRepository, BaseApiRepository, BaseStorageRepository } from 'wf-shared/repositories'
export { DatabaseProvider } from 'wf-shared/repositories'

// API Repositories
export { UserApiRepository } from './api/UserApiRepository'
export { QuizApiRepository } from './api/QuizApiRepository'
export { QuestionApiRepository } from './api/QuestionApiRepository'

// Storage Repositories
export { AuthStorageRepository } from './storage/AuthStorageRepository'
export type { RememberedCredentials } from './storage/AuthStorageRepository'

// Container
export { RepositoryContainer } from './RepositoryContainer'

// Repository Manager
export { AdminRepositoryManager } from './AdminRepositoryManager'

// Pre-initialized repositories (auto-initializes when imported)
export { repositories } from './init'