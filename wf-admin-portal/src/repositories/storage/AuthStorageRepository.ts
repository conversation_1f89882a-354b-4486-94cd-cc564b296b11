// Auth storage repository for admin portal
// Handles authentication-related data in localStorage using repository pattern

import { BaseStorageRepository } from 'wf-shared/repositories'
import { APP_CONSTANTS } from 'wf-shared/constants'

export interface RememberedCredentials {
  email: string
  rememberMe: boolean
}

export class AuthStorageRepository extends BaseStorageRepository<RememberedCredentials> {
  protected storage = localStorage
  protected keyPrefix = 'admin'

  /**
   * Save user credentials for remember me functionality
   */
  async saveCredentials(email: string, rememberMe: boolean): Promise<void> {
    if (rememberMe) {
      const credentials: RememberedCredentials = { email, rememberMe }
      await this.save(APP_CONSTANTS.STORAGE_KEYS.ADMIN_CREDENTIALS, credentials)
    } else {
      await this.clearCredentials()
    }
  }

  /**
   * Load remembered credentials
   */
  async loadCredentials(): Promise<RememberedCredentials> {
    const credentials = await this.load(APP_CONSTANTS.STORAGE_KEYS.ADMIN_CREDENTIALS)
    
    if (credentials) {
      return {
        email: credentials.email || '',
        rememberMe: credentials.rememberMe || false,
      }
    }

    return { email: '', rememberMe: false }
  }

  /**
   * Clear stored credentials
   */
  async clearCredentials(): Promise<void> {
    await this.remove(APP_CONSTANTS.STORAGE_KEYS.ADMIN_CREDENTIALS)
  }

  /**
   * Check if credentials are stored
   */
  async hasStoredCredentials(): Promise<boolean> {
    return await this.exists(APP_CONSTANTS.STORAGE_KEYS.ADMIN_CREDENTIALS)
  }
}