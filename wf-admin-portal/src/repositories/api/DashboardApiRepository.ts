// Dashboard API repository for admin portal
// Handles dashboard-related database operations using repository pattern

import { BaseApiRepository } from 'wf-shared/repositories'
import type { ApiResponse, Database, Tables } from 'wf-shared/types'
import type { SupabaseClient } from '@supabase/supabase-js'

export interface RecentActivity {
  created_at: string
  quiz_id: string
  quizzes?: {
    title: string
  }
}

export interface ActivityTrend {
  date: string
  attempts: number
  totalScore: number
  avgScore: number
}

export interface QuizPerformance {
  quizId: string
  title: string
  attempts: number
  totalScore: number
  avgScore: number
}

export interface DashboardStats {
  totalQuizzes: number
  totalQuestions: number
  totalUsers: number
  recentActivity: RecentActivity[]
}

export interface DetailedDashboardStats {
  quizzes: { total: number; active: number; inactive: number }
  questions: { total: number; withExplanations: number; withoutExplanations: number; completionRate: number }
  users: { total: number; active: number; newThisMonth: number }
}

export class DashboardApiRepository extends BaseApiRepository<Tables<'users'>, Tables<'users'>, Tables<'users'>> {
  protected tableName = 'users' as const // Not used for dashboard operations but required by base class

  constructor(database: SupabaseClient<Database>) {
    super(database)
  }

  /**
   * Get basic dashboard statistics
   */
  async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    try {
      // Get counts from different tables in parallel
      const [quizzesCount, questionsCount, usersCount, recentActivity] = await Promise.all([
        this.getQuizzesCount(),
        this.getQuestionsCount(),
        this.getUsersCount(),
        this.getRecentActivity(),
      ])

      // Check for errors in any of the calls
      if (quizzesCount.error || questionsCount.error || usersCount.error || recentActivity.error) {
        return {
          success: false,
          error: {
            message: 'Failed to fetch some dashboard statistics',
            code: 'PARTIAL_STATS_ERROR',
          },
        }
      }

      return {
        success: true,
        data: {
          totalQuizzes: quizzesCount.data || 0,
          totalQuestions: questionsCount.data || 0,
          totalUsers: usersCount.data || 0,
          recentActivity: recentActivity.data || [],
        },
      }
    } catch (error) {
      return this.handleError(error, 'Failed to fetch dashboard statistics')
    }
  }

  /**
   * Get detailed dashboard statistics
   */
  async getDetailedStats(): Promise<ApiResponse<DetailedDashboardStats>> {
    try {
      const [
        totalQuizzes,
        activeQuizzes,
        totalQuestions,
        questionsWithExplanations,
        totalUsers,
        activeUsers,
        newUsersThisMonth,
      ] = await Promise.all([
        this.getQuizzesCount(),
        this.getActiveQuizzesCount(),
        this.getQuestionsCount(),
        this.getQuestionsWithExplanationsCount(),
        this.getUsersCount(),
        this.getActiveUsersCount(),
        this.getNewUsersThisMonth(),
      ])

      // Check for errors
      const hasErrors = [
        totalQuizzes, activeQuizzes, totalQuestions, questionsWithExplanations,
        totalUsers, activeUsers, newUsersThisMonth,
      ].some(result => result.error)

      if (hasErrors) {
        return {
          success: false,
          error: {
            message: 'Failed to fetch detailed dashboard statistics',
            code: 'DETAILED_STATS_ERROR',
          },
        }
      }

      const totalQuestionsCount = totalQuestions.data || 0
      const totalUsersCount = totalUsers.data || 0
      const activeUsersCount = activeUsers.data || 0
      const newUsersThisMonthCount = newUsersThisMonth.data || 0
      const totalQuizzesCount = totalQuizzes.data || 0
      const activeQuizzesCount = activeQuizzes.data || 0
      const questionsWithExplanationsCount = questionsWithExplanations.data || 0

      return {
        success: true,
        data: {
          quizzes: {
            total: totalQuizzesCount,
            active: activeQuizzesCount,
            inactive: totalQuizzesCount - activeQuizzesCount,
          },
          questions: {
            total: totalQuestionsCount,
            withExplanations: questionsWithExplanationsCount,
            withoutExplanations: totalQuestionsCount - questionsWithExplanationsCount,
            completionRate: totalQuestionsCount > 0 ? Math.round((questionsWithExplanationsCount / totalQuestionsCount) * 100) : 0,
          },
          users: {
            total: totalUsersCount,
            active: activeUsersCount,
            newThisMonth: newUsersThisMonthCount,
          },
        },
      }
    } catch (error) {
      return this.handleError(error, 'Failed to fetch detailed dashboard statistics')
    }
  }

  /**
   * Get recent activity from quiz attempts
   */
  async getRecentActivity(): Promise<ApiResponse<RecentActivity[]>> {
    try {
      const { data, error } = await this.db
        .from('quiz_attempts')
        .select('created_at, quiz_id, quizzes(title)')
        .order('created_at', { ascending: false })
        .limit(10)

      if (error) {
        return this.handleError(error, 'Failed to fetch recent activity')
      }

      const recentActivity = (data || []).map((item: unknown) => ({
        created_at: (item as { created_at: string }).created_at,
        quiz_id: (item as { quiz_id: string }).quiz_id,
        quizzes: (item as { quizzes?: { title: string } | { title: string }[] }).quizzes ? (Array.isArray((item as { quizzes?: { title: string } | { title: string }[] }).quizzes) ? ((item as { quizzes?: { title: string } | { title: string }[] }).quizzes as { title: string }[])[0] : (item as { quizzes?: { title: string } | { title: string }[] }).quizzes) : undefined,
      })) as RecentActivity[]

      return {
        success: true,
        data: recentActivity,
      }
    } catch (error) {
      return this.handleError(error, 'Failed to fetch recent activity')
    }
  }

  /**
   * Get activity trends for specified number of days
   */
  async getActivityTrends(days: number = 7): Promise<ApiResponse<ActivityTrend[]>> {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const { data, error } = await this.db
        .from('quiz_attempts')
        .select('created_at, score')
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true })

      if (error) {
        return this.handleError(error, 'Failed to fetch activity trends')
      }

      // Group by day
      const trends = (data || []).reduce((acc: Record<string, ActivityTrend>, attempt: unknown) => {
        const date = new Date((attempt as { created_at: string }).created_at).toDateString()
        if (!acc[date]) {
          acc[date] = { date, attempts: 0, totalScore: 0, avgScore: 0 }
        }
        acc[date].attempts++
        acc[date].totalScore += (attempt as { score?: number }).score || 0
        acc[date].avgScore = acc[date].totalScore / acc[date].attempts
        return acc
      }, {})

      return {
        success: true,
        data: Object.values(trends),
      }
    } catch (error) {
      return this.handleError(error, 'Failed to fetch activity trends')
    }
  }

  /**
   * Get top performing quizzes
   */
  async getTopPerformingQuizzes(limit: number = 5): Promise<ApiResponse<QuizPerformance[]>> {
    try {
      const { data, error } = await this.db
        .from('quiz_attempts')
        .select(`
          quiz_id,
          score,
          quizzes(title)
        `)
        .not('score', 'is', null)

      if (error) {
        return this.handleError(error, 'Failed to fetch top performing quizzes')
      }

      // Group by quiz and calculate average scores
      const quizStats = (data || []).reduce((acc: Record<string, QuizPerformance>, attempt: unknown) => {
        const quizId = (attempt as { quiz_id: string }).quiz_id
        if (!acc[quizId]) {
          acc[quizId] = {
            quizId,
            title: ((attempt as { quizzes?: { title: string } }).quizzes?.title) || 'Unknown Quiz',
            attempts: 0,
            totalScore: 0,
            avgScore: 0,
          }
        }
        acc[quizId].attempts++
        acc[quizId].totalScore += (attempt as { score: number }).score
        acc[quizId].avgScore = acc[quizId].totalScore / acc[quizId].attempts
        return acc
      }, {})

      const topQuizzes = Object.values(quizStats)
        .sort((a: QuizPerformance, b: QuizPerformance) => b.avgScore - a.avgScore)
        .slice(0, limit)

      return {
        success: true,
        data: topQuizzes,
      }
    } catch (error) {
      return this.handleError(error, 'Failed to fetch top performing quizzes')
    }
  }

  // Helper methods for getting counts
  private async getQuizzesCount(): Promise<ApiResponse<number>> {
    try {
      const { count, error } = await this.db
        .from('quizzes')
        .select('*', { count: 'exact', head: true })

      if (error) {
        return this.handleError(error, 'Failed to get quizzes count')
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return this.handleError(error, 'Failed to get quizzes count')
    }
  }

  private async getActiveQuizzesCount(): Promise<ApiResponse<number>> {
    try {
      const { count, error } = await this.db
        .from('quizzes')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      if (error) {
        return this.handleError(error, 'Failed to get active quizzes count')
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return this.handleError(error, 'Failed to get active quizzes count')
    }
  }

  private async getQuestionsCount(): Promise<ApiResponse<number>> {
    try {
      const { count, error } = await this.db
        .from('questions')
        .select('*', { count: 'exact', head: true })

      if (error) {
        return this.handleError(error, 'Failed to get questions count')
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return this.handleError(error, 'Failed to get questions count')
    }
  }

  private async getQuestionsWithExplanationsCount(): Promise<ApiResponse<number>> {
    try {
      const { count, error } = await this.db
        .from('questions')
        .select('id', { count: 'exact', head: true })
        .not('explanations', 'is', null)

      if (error) {
        return this.handleError(error, 'Failed to get questions with explanations count')
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return this.handleError(error, 'Failed to get questions with explanations count')
    }
  }

  private async getUsersCount(): Promise<ApiResponse<number>> {
    try {
      const { count, error } = await this.db
        .from('users')
        .select('*', { count: 'exact', head: true })

      if (error) {
        return this.handleError(error, 'Failed to get users count')
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return this.handleError(error, 'Failed to get users count')
    }
  }

  private async getActiveUsersCount(): Promise<ApiResponse<number>> {
    try {
      // Consider users active if they've logged in within the last 30 days
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const { count, error } = await this.db
        .from('users')
        .select('*', { count: 'exact', head: true })
        .gte('last_sign_in_at', thirtyDaysAgo.toISOString())

      if (error) {
        return this.handleError(error, 'Failed to get active users count')
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return this.handleError(error, 'Failed to get active users count')
    }
  }

  private async getNewUsersThisMonth(): Promise<ApiResponse<number>> {
    try {
      const startOfMonth = new Date()
      startOfMonth.setDate(1)
      startOfMonth.setHours(0, 0, 0, 0)

      const { count, error } = await this.db
        .from('users')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', startOfMonth.toISOString())

      if (error) {
        return this.handleError(error, 'Failed to get new users count')
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return this.handleError(error, 'Failed to get new users count')
    }
  }

  /**
   * Apply filters to query (required by BaseApiRepository)
   */
  protected applyFilters(query: unknown, _filters: Record<string, unknown>): unknown {
    // Not used for dashboard operations
    return query
  }

  /**
   * Handle errors consistently
   */
  private handleError(error: unknown, defaultMessage: string) {
    return {
      success: false,
      error: { message: (error as { message?: string })?.message || defaultMessage, code: (error as { code?: string })?.code || 'UNKNOWN_ERROR' },
    }
  }
}
