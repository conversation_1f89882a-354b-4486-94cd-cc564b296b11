// User API repository for admin portal
// Handles user-related database operations using repository pattern

import { BaseApiRepository } from 'wf-shared/repositories'
import type { DbUser, UserWithAdminRelations, ApiResponse, PaginationParams, Database } from 'wf-shared/types'
import type { SupabaseClient } from '@supabase/supabase-js'

export class UserApiRepository extends BaseApiRepository<UserWithAdminRelations, Partial<DbUser>, Partial<DbUser>> {
  protected tableName = 'users' as const

  constructor(database: SupabaseClient<Database>) {
    super(database)
  }

  /**
   * Get users with level relationships
   */
  async getUsersWithLevels(params?: PaginationParams & Record<string, unknown>): Promise<ApiResponse<{
    data: UserWithAdminRelations[]
    total: number
    page: number
    pageSize: number
  }>> {
    try {
      const { page = 1, pageSize = 10, ...filters } = params || {}
      const offset = (page - 1) * pageSize

      let query = this.db
        .from(this.tableName)
        .select(`
          *,
          levels(id, name, key)
        `, { count: 'exact' })

      // Apply filters
      query = this.applyFilters(query, filters)

      // Apply pagination
      const sortBy = (filters && 'sortBy' in filters && filters.sortBy) ? filters.sortBy : 'created_at'
      const sortOrder = (filters && 'sortOrder' in filters && filters.sortOrder) ? filters.sortOrder : 'desc'
      query = query.range(offset, offset + pageSize - 1)
        .order(sortBy, { 
          ascending: sortOrder === 'asc', 
        })

      const { data, error, count } = await query

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: {
          data: data as UserWithAdminRelations[],
          total: count || 0,
          page,
          pageSize,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch users with levels', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get a single user with relationships by ID
   */
  async getUserWithRelationsById(id: string): Promise<ApiResponse<UserWithAdminRelations>> {
    try {
      const { data, error } = await this.db
        .from(this.tableName)
        .select(`
          *,
          levels(id, name, key),
          user_progress(*),
          quiz_attempts(*)
        `)
        .eq('id', id)
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data as UserWithAdminRelations,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch user with relations', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Search users by email or name
   */
  async searchUsers(searchTerm: string, params?: PaginationParams): Promise<ApiResponse<{
    data: UserWithAdminRelations[]
    total: number
    page: number
    pageSize: number
  }>> {
    try {
      const { page = 1, pageSize = 10 } = params || {}
      const offset = (page - 1) * pageSize

      const { data, error, count } = await this.db
        .from(this.tableName)
        .select(`
          *,
          levels(id, name, key)
        `, { count: 'exact' })
        .or(`email.ilike.%${searchTerm}%,first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%`)
        .range(offset, offset + pageSize - 1)
        .order('created_at', { ascending: false })

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: {
          data: data as UserWithAdminRelations[],
          total: count || 0,
          page,
          pageSize,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to search users', code: 'SEARCH_ERROR' },
      }
    }
  }

  /**
   * Apply filters to user queries
   */
  protected applyFilters(query: any, filters: Record<string, unknown>): any {
    let filteredQuery = query as any

    if (filters.email) {
      filteredQuery = filteredQuery.ilike('email', `%${filters.email}%`)
    }

    if (filters.level_id) {
      filteredQuery = filteredQuery.eq('level_id', filters.level_id)
    }

    if (filters.created_after) {
      filteredQuery = filteredQuery.gte('created_at', filters.created_after)
    }

    if (filters.created_before) {
      filteredQuery = filteredQuery.lte('created_at', filters.created_before)
    }

    return filteredQuery
  }

  /**
   * Delete user with safety checks
   */
  async deleteUserWithSafetyChecks(id: string): Promise<ApiResponse<void>> {
    try {
      // Check if user has quiz attempts
      const { data: attempts, error: attemptsError } = await this.db
        .from('quiz_attempts')
        .select('id')
        .eq('user_id', id)
        .limit(1)

      if (attemptsError) {
        return {
          success: false,
          error: { message: attemptsError.message, code: attemptsError.code },
        }
      }

      if (attempts && attempts.length > 0) {
        return {
          success: false,
          error: { message: 'Cannot delete user with quiz attempts. Deactivate account instead.' },
        }
      }

      return this.delete(id)
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get count of active users
   */
  async getActiveUsersCount(): Promise<ApiResponse<number>> {
    try {
      const { count, error } = await this.db
        .from(this.tableName)
        .select('*', { count: 'exact', head: true })
        .is('deleted_at', null) // Assuming deleted_at field exists or adjust as needed

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get count of new users this month
   */
  async getNewUsersThisMonth(): Promise<ApiResponse<number>> {
    try {
      const oneMonthAgo = new Date()
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)

      const { count, error } = await this.db
        .from(this.tableName)
        .select('*', { count: 'exact', head: true })
        .gte('created_at', oneMonthAgo.toISOString())

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get users by role
   */
  async getUsersByRole(role: string): Promise<ApiResponse<DbUser[]>> {
    try {
      const { data, error } = await this.db
        .from(this.tableName)
        .select('*')
        .eq('role', role)

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get user by ID with detailed relationships
   */
  async getUserById(id: string): Promise<ApiResponse<UserWithAdminRelations>> {
    try {
      const { data, error } = await this.db
        .from(this.tableName)
        .select(`
          *,
          levels(id, name, key, system, description, is_active, sort_order),
          user_progress(
            *,
            categories(name),
            levels(name)
          ),
          quiz_attempts(
            id,
            score,
            completed_at,
            correct_answers,
            created_at,
            is_completed,
            mode,
            quiz_id,
            time_taken_seconds,
            total_questions,
            updated_at,
            user_id,
            quizzes(title)
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get total count of users
   */
  async getCount(): Promise<ApiResponse<number>> {
    try {
      const { count, error } = await this.db
        .from(this.tableName)
        .select('*', { count: 'exact', head: true })

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }
}