import { BaseApiRepository } from 'wf-shared/repositories'
import type { ApiResponse, Database, Tables } from 'wf-shared/types'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { QuizImportData, QuestionImportData } from '@/services/BulkImportService'

export interface SQLImportResult {
  imported: number
  failed: number
  errors: Array<{ row: number; field: string; message: string }>
}

export class BulkImportApiRepository extends BaseApiRepository<Tables<'quizzes'>, Tables<'quizzes'>, Tables<'quizzes'>> {
  protected tableName = 'quizzes' as const

  constructor(database: SupabaseClient<Database>) {
    super(database)
  }

  /**
   * Import a single quiz from structured data
   */
  async importQuiz(quizData: QuizImportData): Promise<ApiResponse<Tables<'quizzes'>>> {
    try {
      // First, get the foreign key IDs
      const categoryResult = await this.db
        .from('categories')
        .select('id')
        .eq('key', quizData.category_key)
        .single()

      if (categoryResult.error) {
        return {
          success: false,
          error: { message: `Category '${quizData.category_key}' not found`, code: 'CATEGORY_NOT_FOUND' },
        }
      }

      const levelResult = await this.db
        .from('levels')
        .select('id')
        .eq('key', quizData.level_key)
        .single()

      if (levelResult.error) {
        return {
          success: false,
          error: { message: `Level '${quizData.level_key}' not found`, code: 'LEVEL_NOT_FOUND' },
        }
      }

      const quizTypeResult = await this.db
        .from('quiz_types')
        .select('id')
        .eq('key', quizData.quiz_type_key)
        .single()

      if (quizTypeResult.error) {
        return {
          success: false,
          error: { message: `Quiz type '${quizData.quiz_type_key}' not found`, code: 'QUIZ_TYPE_NOT_FOUND' },
        }
      }

      // Insert the quiz
      const { data, error } = await this.db
        .from('quizzes')
        .insert({
          key: quizData.key,
          title: quizData.title,
          description: quizData.description,
          category_id: categoryResult.data.id,
          level_id: levelResult.data.id,
          quiz_type_id: quizTypeResult.data.id,
          difficulty_level: quizData.difficulty_level,
          total_questions: quizData.total_questions,
          time_limit_minutes: quizData.time_limit_minutes,
          instructions: quizData.instructions,
          quiz_config: quizData.quiz_config ? JSON.parse(quizData.quiz_config) : null,
          is_active: quizData.is_active,
        })
        .select()
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to import quiz', code: 'IMPORT_ERROR' },
      }
    }
  }

  /**
   * Import a single question with options and explanation
   */
  async importQuestion(questionData: QuestionImportData): Promise<ApiResponse<Tables<'questions'>>> {
    try {
      // Get quiz ID
      const quizResult = await this.db
        .from('quizzes')
        .select('id')
        .eq('key', questionData.quiz_key)
        .single()

      if (quizResult.error) {
        return {
          success: false,
          error: { message: `Quiz '${questionData.quiz_key}' not found`, code: 'QUIZ_NOT_FOUND' },
        }
      }

      // Get question type ID
      const questionTypeResult = await this.db
        .from('question_types')
        .select('id')
        .eq('key', questionData.question_type_key)
        .single()

      if (questionTypeResult.error) {
        return {
          success: false,
          error: { message: `Question type '${questionData.question_type_key}' not found`, code: 'QUESTION_TYPE_NOT_FOUND' },
        }
      }

      // Insert the question
      const { data: questionInsertData, error: questionError } = await this.db
        .from('questions')
        .insert({
          quiz_id: quizResult.data.id,
          question_type_id: questionTypeResult.data.id,
          question_text: questionData.question_text,
          difficulty_level: questionData.difficulty_level,
          order_index: questionData.order_index,
          metadata: questionData.metadata ? JSON.parse(questionData.metadata) : null,
          is_active: questionData.is_active,
        })
        .select()
        .single()

      if (questionError) {
        return {
          success: false,
          error: { message: questionError.message, code: questionError.code },
        }
      }

      // Insert options
      if (questionData.options && questionData.options.length > 0) {
        const optionsToInsert = questionData.options.map(option => ({
          question_id: questionInsertData.id,
          option_key: option.option_key,
          option_text: option.option_text,
          is_correct: option.is_correct,
          sort_order: option.sort_order,
        }))

        const { error: optionsError } = await this.db
          .from('question_options')
          .insert(optionsToInsert)

        if (optionsError) {
          // Rollback question if options fail
          await this.db.from('questions').delete().eq('id', questionInsertData.id)
          return {
            success: false,
            error: { message: `Failed to insert options: ${optionsError.message}`, code: 'OPTIONS_INSERT_ERROR' },
          }
        }
      }

      // Insert explanation if provided
      if (questionData.explanation) {
        const { error: explanationError } = await this.db
          .from('explanations')
          .insert({
            question_id: questionInsertData.id,
            explanation_type: questionData.explanation.explanation_type,
            content: questionData.explanation.explanation_text,
            metadata: questionData.explanation.metadata ? JSON.parse(questionData.explanation.metadata) : null,
          })

        if (explanationError) {
          // Don't rollback for explanation errors, just log
          console.warn('Failed to insert explanation:', explanationError.message)
        }
      }

      return {
        success: true,
        data: questionInsertData,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to import question', code: 'IMPORT_ERROR' },
      }
    }
  }

  /**
   * Execute SQL import directly
   */
  async executeSQLImport(_sqlContent: string): Promise<ApiResponse<SQLImportResult>> {
    try {
      // For now, return a message that SQL import needs to be done manually
      // In a production environment, you would need to set up proper SQL execution
      // through a secure backend service or database function

      return {
        success: false,
        error: {
          message: 'SQL import must be executed manually in the database. Please run the SQL file directly in your database management tool.',
          code: 'SQL_MANUAL_EXECUTION_REQUIRED',
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to execute SQL import', code: 'SQL_EXECUTION_ERROR' },
      }
    }
  }

  /**
   * Validate import data before processing
   */
  async validateImportData(type: 'quiz' | 'question', data: unknown[]): Promise<ApiResponse<{ valid: boolean; errors: Array<{ row: number; field: string; message: string }> }>> {
    try {
      const validationErrors: Array<{ row: number; field: string; message: string }> = []

      if (type === 'quiz') {
        // Validate quiz data
        for (let i = 0; i < data.length; i++) {
          const quiz = data[i] as QuizImportData
          
          // Check if quiz key already exists
          const existingQuiz = await this.db
            .from('quizzes')
            .select('id')
            .eq('key', quiz.key)
            .single()

          if (existingQuiz.data) {
            validationErrors.push({
              row: i + 1,
              field: 'key',
              message: `Quiz with key '${quiz.key}' already exists`,
            })
          }

          // Validate foreign keys exist
          const categoryExists = await this.db
            .from('categories')
            .select('id')
            .eq('key', quiz.category_key)
            .single()

          if (categoryExists.error) {
            validationErrors.push({
              row: i + 1,
              field: 'category_key',
              message: `Category '${quiz.category_key}' does not exist`,
            })
          }
        }
      }

      return {
        success: true,
        data: {
          valid: validationErrors.length === 0,
          errors: validationErrors,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Validation failed', code: 'VALIDATION_ERROR' },
      }
    }
  }

  /**
   * Get import statistics
   */
  async getImportStats(): Promise<ApiResponse<{ totalQuizzes: number; totalQuestions: number; totalOptions: number }>> {
    try {
      const { data: quizCount } = await this.db
        .from('quizzes')
        .select('id', { count: 'exact' })

      const { data: questionCount } = await this.db
        .from('questions')
        .select('id', { count: 'exact' })

      const { data: optionCount } = await this.db
        .from('question_options')
        .select('id', { count: 'exact' })

      return {
        success: true,
        data: {
          totalQuizzes: quizCount?.length || 0,
          totalQuestions: questionCount?.length || 0,
          totalOptions: optionCount?.length || 0,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to get import stats', code: 'STATS_ERROR' },
      }
    }
  }

  /**
   * Apply filters to query (required by BaseApiRepository)
   */
  protected applyFilters(query: unknown, _filters: Record<string, unknown>): unknown {
    // Not used for bulk import operations
    return query
  }
}
