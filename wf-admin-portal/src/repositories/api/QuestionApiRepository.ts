// Question API repository for admin portal
// Handles question-related database operations using repository pattern

import { BaseApiRepository } from 'wf-shared/repositories'
import type { DbQuestion, DbQuestionOption, DbExplanation, QuestionWithRelations, ApiResponse, PaginationParams, Database } from 'wf-shared/types'
import type { SupabaseClient } from '@supabase/supabase-js'

// Temporary type until database types are regenerated
interface TempQuizQuestion {
  id: string
  quiz_id: string
  question_id: string
  order_index: number
  points: number | null
  is_active: boolean | null
  created_at: string | null
  updated_at: string | null
}

// Temporary type for question with quiz context
interface TempQuestionWithQuizContext extends QuestionWithRelations {
  order_index: number
  points: number | null
  is_quiz_active: boolean
}

export class QuestionApiRepository extends BaseApiRepository<QuestionWithRelations, Partial<DbQuestion>, Partial<DbQuestion>> {
  protected tableName = 'questions' as const

  constructor(database: SupabaseClient<Database>) {
    super(database)
  }

  /**
   * Get questions with quiz relationships via junction table
   */
  async getQuestionsWithRelations(params?: PaginationParams & Record<string, unknown>): Promise<ApiResponse<{
    data: QuestionWithRelations[]
    total: number
    page: number
    pageSize: number
  }>> {
    try {
      const { page = 1, pageSize = 10, ...filters } = params || {}
      const offset = (page - 1) * pageSize

      // Execute query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        let query = this.db
          .from(this.tableName)
          .select(`
            *,
            question_types(id, name, key),
            question_options(*),
            explanations(*),
            quiz_questions(
              id,
              quiz_id,
              order_index,
              points,
              is_active,
              quizzes(id, title, key)
            )
          `, { count: 'exact' })

        // Apply filters
        query = this.applyFilters(query, filters)

        // Apply pagination
        const sortBy = (filters && 'sortBy' in filters && filters.sortBy) ? filters.sortBy : 'created_at'
        const sortOrder = (filters && 'sortOrder' in filters && filters.sortOrder) ? filters.sortOrder : 'desc'
        query = query.range(offset, offset + pageSize - 1)
          .order(sortBy, {
            ascending: sortOrder !== 'desc',
          })

        return await query
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch questions with relations', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error, count } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: {
          data: data as QuestionWithRelations[],
          total: count || 0,
          page,
          pageSize,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch questions with relations', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get questions for a specific quiz with quiz context (order, points, etc.)
   */
  async getQuestionsByQuizId(quizId: string, params?: PaginationParams): Promise<ApiResponse<{
    data: TempQuestionWithQuizContext[]
    total: number
    page: number
    pageSize: number
  }>> {
    try {
      const { page = 1, pageSize = 50 } = params || {}
      const offset = (page - 1) * pageSize

      // Execute query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        // Temporary workaround using unknown type until database types are regenerated
        return await (this.db as unknown as SupabaseClient<Database>)
          .from('quiz_questions')
          .select(`
            order_index,
            points,
            is_active,
            questions(
              *,
              question_types(id, name, key),
              question_options(*),
              explanations(*)
            )
          `, { count: 'exact' })
          .eq('quiz_id', quizId)
          .eq('is_active', true)
          .range(offset, offset + pageSize - 1)
          .order('order_index', { ascending: true })
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch questions by quiz', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error, count } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      // Transform the data to match TempQuestionWithQuizContext interface
      const transformedData: TempQuestionWithQuizContext[] = (data || []).map((item: unknown) => ({
        ...(item as { questions: QuestionWithRelations }).questions,
        order_index: (item as { order_index: number }).order_index,
        points: (item as { points: number }).points,
        is_quiz_active: (item as { is_active: boolean }).is_active,
      }))

      return {
        success: true,
        data: {
          data: transformedData,
          total: count || 0,
          page,
          pageSize,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch questions for quiz', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Add a question to a quiz
   */
  async addQuestionToQuiz(
    quizId: string,
    questionId: string,
    orderIndex?: number,
    points: number = 1,
  ): Promise<ApiResponse<TempQuizQuestion>> {
    try {
      // Use the database function through circuit breaker
      const rpcResult = await this.writeCircuitBreaker.execute(async () => {
        // Temporary workaround using unknown type until database types are regenerated
        return await (this.db as any)
          .rpc('add_question_to_quiz', {
            p_quiz_id: quizId,
            p_question_id: questionId,
            p_order_index: orderIndex,
            p_points: points,
          })
      })

      if (!rpcResult.success) {
        return {
          success: false,
          error: { message: rpcResult.error || 'Circuit breaker: Failed to add question to quiz', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error } = rpcResult.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      // Fetch the created quiz_question record through circuit breaker
      const fetchResult = await this.readCircuitBreaker.execute(async () => {
        return await (this.db as unknown as SupabaseClient<Database>)
          .from('quiz_questions')
          .select('*')
          .eq('id', data)
          .single()
      })

      if (!fetchResult.success) {
        return {
          success: false,
          error: { message: fetchResult.error || 'Circuit breaker: Failed to fetch created quiz question', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data: quizQuestion, error: fetchError } = fetchResult.data!

      if (fetchError) {
        return {
          success: false,
          error: { message: fetchError.message, code: fetchError.code },
        }
      }

      return {
        success: true,
        data: quizQuestion,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to add question to quiz', code: 'ADD_ERROR' },
      }
    }
  }

  /**
   * Remove a question from a quiz
   */
  async removeQuestionFromQuiz(quizId: string, questionId: string): Promise<ApiResponse<void>> {
    try {
      // Execute delete through circuit breaker
      const result = await this.writeCircuitBreaker.execute(async () => {
        return await (this.db as unknown as SupabaseClient<Database>)
          .from('quiz_questions')
          .delete()
          .eq('quiz_id', quizId)
          .eq('question_id', questionId)
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to remove question from quiz', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to remove question from quiz', code: 'REMOVE_ERROR' },
      }
    }
  }

  /**
   * Update quiz-question relationship (order, points, etc.)
   */
  async updateQuizQuestion(
    quizId: string,
    questionId: string,
    updates: Partial<Pick<TempQuizQuestion, 'order_index' | 'points' | 'is_active'>>,
  ): Promise<ApiResponse<TempQuizQuestion>> {
    try {
      // Execute update through circuit breaker
      const result = await this.writeCircuitBreaker.execute(async () => {
        return await (this.db as unknown as SupabaseClient<Database>)
          .from('quiz_questions')
          .update(updates)
          .eq('quiz_id', quizId)
          .eq('question_id', questionId)
          .select()
          .single()
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to update quiz question', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to update quiz question', code: 'UPDATE_ERROR' },
      }
    }
  }

  /**
   * Search questions by text content
   */
  async searchQuestions(searchTerm: string, params?: PaginationParams): Promise<ApiResponse<{
    data: QuestionWithRelations[]
    total: number
    page: number
    pageSize: number
  }>> {
    try {
      const { page = 1, pageSize = 10 } = params || {}
      const offset = (page - 1) * pageSize

      // Execute search through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select(`
            *,
            question_types(id, name, key),
            question_options(*),
            explanations(*),
            quiz_questions(
              id,
              quiz_id,
              order_index,
              points,
              is_active,
              quizzes(id, title, key)
            )
          `, { count: 'exact' })
          .ilike('question_text', `%${searchTerm}%`)
          .range(offset, offset + pageSize - 1)
          .order('created_at', { ascending: false })
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to search questions', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error, count } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: {
          data: data as QuestionWithRelations[],
          total: count || 0,
          page,
          pageSize,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to search questions', code: 'SEARCH_ERROR' },
      }
    }
  }

  /**
   * Get questions that need validation (missing explanations, options, etc.)
   */
  async getQuestionsNeedingValidation(params?: PaginationParams): Promise<ApiResponse<{
    data: QuestionWithRelations[]
    total: number
    page: number
    pageSize: number
  }>> {
    try {
      const { page = 1, pageSize = 10 } = params || {}
      const offset = (page - 1) * pageSize

      // Get questions with missing explanations or options through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select(`
            *,
            quizzes(id, title, key),
            question_types(id, name, key),
            question_options(*),
            explanations(*)
          `, { count: 'exact' })
          .range(offset, offset + pageSize - 1)
          .order('created_at', { ascending: false })
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch questions needing validation', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      // Filter questions that need validation on the client side
      const questionsNeedingValidation = (data as QuestionWithRelations[]).filter(question => {
        const hasOptions = question.question_options && question.question_options.length > 0
        const hasExplanations = question.explanations && question.explanations.length > 0
        return !hasOptions || !hasExplanations
      })

      return {
        success: true,
        data: {
          data: questionsNeedingValidation,
          total: questionsNeedingValidation.length,
          page,
          pageSize,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch questions needing validation', code: 'VALIDATION_ERROR' },
      }
    }
  }

  /**
   * Apply filters to question queries
   */
  protected applyFilters(query: any, _filters: Record<string, unknown>): any {
    let filteredQuery = query

    if (_filters.quiz_id) {
      filteredQuery = filteredQuery.eq('quiz_id', _filters.quiz_id)
    }

    if (_filters.question_type_id) {
      filteredQuery = filteredQuery.eq('question_type_id', _filters.question_type_id)
    }

    if (_filters.is_active !== undefined) {
      filteredQuery = filteredQuery.eq('is_active', _filters.is_active)
    }

    if (_filters.difficulty_min) {
      filteredQuery = filteredQuery.gte('difficulty_level', _filters.difficulty_min)
    }

    if (_filters.difficulty_max) {
      filteredQuery = filteredQuery.lte('difficulty_level', _filters.difficulty_max)
    }

    if (_filters.created_after) {
      filteredQuery = filteredQuery.gte('created_at', _filters.created_after)
    }

    if (_filters.created_before) {
      filteredQuery = filteredQuery.lte('created_at', _filters.created_before)
    }

    return filteredQuery
  }

  /**
   * Delete question with safety checks
   */
  async deleteQuestionWithSafetyChecks(id: string): Promise<ApiResponse<void>> {
    try {
      // Check if question has user answers through circuit breaker
      const answersResult = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('user_answers')
          .select('id')
          .eq('question_id', id)
          .limit(1)
      })

      if (!answersResult.success) {
        return {
          success: false,
          error: { message: answersResult.error || 'Circuit breaker: Failed to check user answers', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data: answers, error: answersError } = answersResult.data!

      if (answersError) {
        return {
          success: false,
          error: { message: answersError.message, code: answersError.code },
        }
      }

      if (answers && answers.length > 0) {
        return {
          success: false,
          error: { message: 'Cannot delete question with existing user answers. Deactivate it instead.' },
        }
      }

      // Delete options and explanations first through circuit breaker
      const deleteResult = await this.writeCircuitBreaker.execute(async () => {
        const [optionsResult, explanationsResult] = await Promise.all([
          this.db.from('question_options').delete().eq('question_id', id),
          this.db.from('explanations').delete().eq('question_id', id),
        ])
        return { optionsResult, explanationsResult }
      })

      if (!deleteResult.success) {
        return {
          success: false,
          error: { message: deleteResult.error || 'Circuit breaker: Failed to delete question dependencies', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { optionsResult, explanationsResult } = deleteResult.data!

      if (optionsResult.error) {
        return {
          success: false,
          error: { message: optionsResult.error.message, code: optionsResult.error.code },
        }
      }

      if (explanationsResult.error) {
        return {
          success: false,
          error: { message: explanationsResult.error.message, code: explanationsResult.error.code },
        }
      }

      // Then delete the question
      return this.delete(id)
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get questions by quiz ID (through quiz_questions junction table)
   */
  async getQuestionsByQuiz(quizId: string): Promise<ApiResponse<DbQuestion[]>> {
    try {
      // Execute query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quiz_questions')
          .select(`
            questions(
              *,
              question_types(name, key),
              question_options(*),
              explanations(*)
            )
          `)
          .eq('quiz_id', quizId)
          .eq('is_active', true)
          .order('order_index', { ascending: true })
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to get questions by quiz', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      // Extract questions from the junction table results
      const questions = data?.map((item: unknown) => (item as { questions: DbQuestion }).questions).filter(Boolean) || []

      return {
        success: true,
        data: questions,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get questions without explanations
   */
  async getQuestionsWithoutExplanations(): Promise<ApiResponse<{ id: string; question_text: string; explanations: { id: string }[] }[]>> {
    try {
      // Execute query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select('id, question_text, explanations(id)')
          .is('explanations.id', null)
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to get questions without explanations', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get questions with few options
   */
  async getQuestionsWithFewOptions(): Promise<ApiResponse<{ id: string; question_text: string; question_options: { id: string }[] }[]>> {
    try {
      // Execute query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select('id, question_text, question_options(id)')
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to get questions with few options', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      // Filter questions with less than 2 options
      const filteredData = (data || []).filter((question: unknown) =>
        ((question as { question_options?: { id: string }[] }).question_options?.length || 0) < 2,
      )

      return {
        success: true,
        data: filteredData,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Create question with options and explanation
   */
  async createQuestionWithOptionsAndExplanation(data: {
    question: Partial<DbQuestion>
    options: Partial<DbQuestionOption>[]
    explanation?: Partial<DbExplanation>
  }): Promise<ApiResponse<DbQuestion>> {
    try {
      // Create question first
      const questionResult = await this.create(data.question)

      if (!questionResult.success || !questionResult.data) {
        return questionResult
      }

      const questionData = questionResult.data

      // Create options
      const optionsWithQuestionId = data.options.map(option => ({
        option_text: option.option_text || '',
        is_correct: option.is_correct || false,
        sort_order: option.sort_order || 0,
        option_key: option.option_key || `option_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        question_id: questionData.id,
      }))

      // Insert options through circuit breaker
      const optionsResult = await this.writeCircuitBreaker.execute(async () => {
        return await this.db
          .from('question_options')
          .insert(optionsWithQuestionId)
      })

      if (!optionsResult.success) {
        return {
          success: false,
          error: { message: optionsResult.error || 'Circuit breaker: Failed to create question options', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { error: optionsError } = optionsResult.data!

      if (optionsError) {
        return {
          success: false,
          error: { message: optionsError.message, code: optionsError.code },
        }
      }

      // Create explanation if provided
      if (data.explanation) {
        // Insert explanation through circuit breaker
        const explanationResult = await this.writeCircuitBreaker.execute(async () => {
          return await this.db
            .from('explanations')
            .insert({
              content: data.explanation?.content || '',
              explanation_type: data.explanation?.explanation_type || 'general',
              question_id: questionData.id,
            })
        })

        if (!explanationResult.success) {
          return {
            success: false,
            error: { message: explanationResult.error || 'Circuit breaker: Failed to create explanation', code: 'CIRCUIT_BREAKER_ERROR' },
          }
        }

        const { error: explanationError } = explanationResult.data!

        if (explanationError) {
          return {
            success: false,
            error: { message: explanationError.message, code: explanationError.code },
          }
        }
      }

      return {
        success: true,
        data: questionData,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get total count of questions
   */
  async getCount(): Promise<ApiResponse<number>> {
    try {
      // Execute count through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select('*', { count: 'exact', head: true })
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to get question count', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { count, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }
}