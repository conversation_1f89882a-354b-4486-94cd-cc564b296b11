// Reference API repository for admin portal
// Handles reference data operations (categories, levels, quiz types, question types) using repository pattern

import { BaseApiRepository } from 'wf-shared/repositories'
import type { DbCategory, DbLevel, DbQuizType, DbQuestionType, ApiResponse, Database } from 'wf-shared/types'
import type { SupabaseClient } from '@supabase/supabase-js'

export class ReferenceApiRepository extends BaseApiRepository<any, any, any> {
  protected tableName = 'categories' as const // Not used for reference repository but required by base class

  constructor(database: SupabaseClient<Database>) {
    super(database)
  }

  /**
   * Get all categories
   */
  async getCategories(): Promise<ApiResponse<DbCategory[]>> {
    try {
      const { data, error } = await this.db
        .from('categories')
        .select('*')
        .order('name')

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch categories', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get category by ID
   */
  async getCategoryById(id: string): Promise<ApiResponse<DbCategory>> {
    try {
      const { data, error } = await this.db
        .from('categories')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch category', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Create new category
   */
  async createCategory(category: Partial<DbCategory>): Promise<ApiResponse<DbCategory>> {
    try {
      const { data, error } = await this.db
        .from('categories')
        .insert(category as any)
        .select()
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to create category', code: 'CREATE_ERROR' },
      }
    }
  }

  /**
   * Update category
   */
  async updateCategory(id: string, updates: Partial<DbCategory>): Promise<ApiResponse<DbCategory>> {
    try {
      const { data, error } = await this.db
        .from('categories')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to update category', code: 'UPDATE_ERROR' },
      }
    }
  }

  /**
   * Delete category
   */
  async deleteCategory(id: string): Promise<ApiResponse<null>> {
    try {
      const { error } = await this.db
        .from('categories')
        .delete()
        .eq('id', id)

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: null,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to delete category', code: 'DELETE_ERROR' },
      }
    }
  }

  /**
   * Get subcategories by parent category ID
   */
  async getSubcategoriesByCategory(categoryId: string): Promise<ApiResponse<DbCategory[]>> {
    try {
      const { data, error } = await this.db
        .from('categories')
        .select('*')
        .eq('parent_id', categoryId)
        .order('name')

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch subcategories', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get all subcategories (categories with parent_id)
   */
  async getSubcategories(): Promise<ApiResponse<DbCategory[]>> {
    try {
      const { data, error } = await this.db
        .from('categories')
        .select('*')
        .not('parent_id', 'is', null)
        .order('name')

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch subcategories', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get subcategory by ID (category with parent_id)
   */
  async getSubcategoryById(id: string): Promise<ApiResponse<DbCategory>> {
    try {
      const { data, error } = await this.db
        .from('categories')
        .select('*')
        .eq('id', id)
        .not('parent_id', 'is', null)
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch subcategory', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get all levels
   */
  async getLevels(): Promise<ApiResponse<DbLevel[]>> {
    try {
      const { data, error } = await this.db
        .from('levels')
        .select('*')
        .order('name')

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch levels', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get level by ID
   */
  async getLevelById(id: string): Promise<ApiResponse<DbLevel>> {
    try {
      const { data, error } = await this.db
        .from('levels')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch level', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Create new level
   */
  async createLevel(level: Partial<DbLevel>): Promise<ApiResponse<DbLevel>> {
    try {
      const { data, error } = await this.db
        .from('levels')
        .insert(level as any)
        .select()
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to create level', code: 'CREATE_ERROR' },
      }
    }
  }

  /**
   * Update level
   */
  async updateLevel(id: string, updates: Partial<DbLevel>): Promise<ApiResponse<DbLevel>> {
    try {
      const { data, error } = await this.db
        .from('levels')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to update level', code: 'UPDATE_ERROR' },
      }
    }
  }

  /**
   * Delete level
   */
  async deleteLevel(id: string): Promise<ApiResponse<null>> {
    try {
      const { error } = await this.db
        .from('levels')
        .delete()
        .eq('id', id)

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: null,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to delete level', code: 'DELETE_ERROR' },
      }
    }
  }

  /**
   * Get all quiz types
   */
  async getQuizTypes(): Promise<ApiResponse<DbQuizType[]>> {
    try {
      const { data, error } = await this.db
        .from('quiz_types')
        .select('*')
        .order('name')

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz types', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get quiz type by ID
   */
  async getQuizTypeById(id: string): Promise<ApiResponse<DbQuizType>> {
    try {
      const { data, error } = await this.db
        .from('quiz_types')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz type', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get all question types
   */
  async getQuestionTypes(): Promise<ApiResponse<DbQuestionType[]>> {
    try {
      const { data, error } = await this.db
        .from('question_types')
        .select('*')
        .order('name')

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch question types', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get question type by ID
   */
  async getQuestionTypeById(id: string): Promise<ApiResponse<DbQuestionType>> {
    try {
      const { data, error } = await this.db
        .from('question_types')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch question type', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Create new question type
   */
  async createQuestionType(questionType: Partial<DbQuestionType>): Promise<ApiResponse<DbQuestionType>> {
    try {
      const { data, error } = await this.db
        .from('question_types')
        .insert(questionType as any)
        .select()
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to create question type', code: 'CREATE_ERROR' },
      }
    }
  }

  /**
   * Update question type
   */
  async updateQuestionType(id: string, updates: Partial<DbQuestionType>): Promise<ApiResponse<DbQuestionType>> {
    try {
      const { data, error } = await this.db
        .from('question_types')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to update question type', code: 'UPDATE_ERROR' },
      }
    }
  }

  /**
   * Delete question type
   */
  async deleteQuestionType(id: string): Promise<ApiResponse<null>> {
    try {
      const { error } = await this.db
        .from('question_types')
        .delete()
        .eq('id', id)

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: null,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to delete question type', code: 'DELETE_ERROR' },
      }
    }
  }

  /**
   * Create a new quiz type
   */
  async createQuizType(quizTypeData: Partial<DbQuizType>): Promise<ApiResponse<DbQuizType>> {
    try {
      const { data, error } = await this.db
        .from('quiz_types')
        .insert(quizTypeData as any)
        .select()
        .single()

      if (error) {
        return { success: false, error: { message: error.message, code: error.code } }
      }

      return { success: true, data }
    } catch (error: any) {
      return { success: false, error: { message: error.message, code: 'UNKNOWN_ERROR' } }
    }
  }

  /**
   * Update a quiz type
   */
  async updateQuizType(id: string, updates: Partial<DbQuizType>): Promise<ApiResponse<DbQuizType>> {
    try {
      const { data, error } = await this.db
        .from('quiz_types')
        .update(updates as any)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        return { success: false, error: { message: error.message, code: error.code } }
      }

      return { success: true, data }
    } catch (error: any) {
      return { success: false, error: { message: error.message, code: 'UNKNOWN_ERROR' } }
    }
  }

  /**
   * Delete a quiz type
   */
  async deleteQuizType(id: string): Promise<ApiResponse<boolean>> {
    try {
      const { error } = await this.db
        .from('quiz_types')
        .delete()
        .eq('id', id)

      if (error) {
        return { success: false, error: { message: error.message, code: error.code } }
      }

      return { success: true, data: true }
    } catch (error: any) {
      return { success: false, error: { message: error.message, code: 'UNKNOWN_ERROR' } }
    }
  }

  /**
   * Apply filters to query (required by BaseApiRepository)
   */
  protected applyFilters(query: any, _filters: Record<string, any>): any {
    // Not used for reference operations
    return query
  }
}
