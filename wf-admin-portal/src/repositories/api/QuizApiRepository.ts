// Quiz API repository for admin portal
// Handles quiz-related database operations using repository pattern

import { BaseApiRepository } from 'wf-shared/repositories'
import type { DbQuiz, QuizWithRelations, ApiResponse, PaginationParams, Database } from 'wf-shared/types'
import type { SupabaseClient } from '@supabase/supabase-js'

export class QuizApiRepository extends BaseApiRepository<QuizWithRelations, Partial<DbQuiz>, Partial<DbQuiz>> {
  protected tableName = 'quizzes' as const

  constructor(database: SupabaseClient<Database>) {
    super(database)
  }

  /**
   * Get quizzes with category, level, and question count relationships
   */
  async getQuizzesWithRelations(params?: PaginationParams & Record<string, unknown>): Promise<ApiResponse<{
    data: QuizWithRelations[]
    total: number
    page: number
    pageSize: number
  }>> {
    try {
      const { page = 1, pageSize = 10, ...filters } = params || {}
      const offset = (page - 1) * pageSize

      let query = this.db
        .from(this.tableName)
        .select(`
          *,
          categories(id, name, key),
          levels(id, name, key),
          quiz_types(id, name, key)
        `, { count: 'exact' })

      // Apply filters
      query = this.applyFilters(query, filters)

      // Apply pagination
      const sortBy = (filters && 'sortBy' in filters && filters.sortBy) ? filters.sortBy : 'created_at'
      const sortOrder = (filters && 'sortOrder' in filters && filters.sortOrder) ? filters.sortOrder : 'desc'
      query = query.range(offset, offset + pageSize - 1)
        .order(sortBy, { 
          ascending: sortOrder === 'asc', 
        })

      const { data, error, count } = await query

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: {
          data: data as unknown as QuizWithRelations[],
          total: count || 0,
          page,
          pageSize,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch quizzes with relations', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Get a single quiz with all relationships including questions
   */
  async getQuizWithQuestionsById(id: string): Promise<ApiResponse<QuizWithRelations>> {
    try {
      // First get the quiz with basic relations using circuit breaker
      const quizResult = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select(`
            *,
            categories(id, name, key),
            levels(id, name, key),
            quiz_types(id, name, key)
          `)
          .eq('id', id)
          .single()
      })

      if (!quizResult.success) {
        return {
          success: false,
          error: { message: quizResult.error || 'Circuit breaker: Failed to fetch quiz', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data: quizData, error: quizError } = quizResult.data!

      if (quizError) {
        return {
          success: false,
          error: { message: quizError.message, code: quizError.code },
        }
      }

      // Then get the quiz questions separately using circuit breaker
      const questionsResult = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quiz_questions')
          .select(`
            id,
            order_index,
            points,
            is_active,
            questions(
              *,
              question_options(*),
              question_types(id, name, key),
              explanations(*)
            )
          `)
          .eq('quiz_id', id)
          .order('order_index', { ascending: true })
      })

      if (!questionsResult.success) {
        return {
          success: false,
          error: { message: questionsResult.error || 'Circuit breaker: Failed to fetch quiz questions', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data: questionsData, error: questionsError } = questionsResult.data!

      if (questionsError) {
        return {
          success: false,
          error: { message: questionsError.message, code: questionsError.code },
        }
      }

      // Transform quiz_questions to a clean questions array for the UI
      const questions = (questionsData || []).map((qq: unknown) => ({
        // Question data from the nested questions object
        ...(qq as any).questions,
        // Quiz-specific metadata from quiz_questions junction table
        order_index: (qq as any).order_index,
        points: (qq as any).points,
        quiz_question_id: (qq as any).id,
        is_quiz_active: (qq as any).is_active,
      }))

      // Combine the data with UI-friendly structure
      const data = {
        ...quizData,
        quiz_questions: questionsData || [], // Keep original for other uses
        questions: questions, // Clean array for UI consumption
      }

      return {
        success: true,
        data: data as unknown as QuizWithRelations,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz with questions', code: 'FETCH_ERROR' },
      }
    }
  }

  /**
   * Search quizzes by title or description
   */
  async searchQuizzes(searchTerm: string, params?: PaginationParams): Promise<ApiResponse<{
    data: QuizWithRelations[]
    total: number
    page: number
    pageSize: number
  }>> {
    try {
      const { page = 1, pageSize = 10 } = params || {}
      const offset = (page - 1) * pageSize

      // Use circuit breaker for search operation
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select(`
            *,
            categories(id, name, key),
            levels(id, name, key),
            quiz_types(id, name, key)
          `, { count: 'exact' })
          .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
          .range(offset, offset + pageSize - 1)
          .order('created_at', { ascending: false })
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to search quizzes', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error, count } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: {
          data: data as unknown as QuizWithRelations[],
          total: count || 0,
          page,
          pageSize,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to search quizzes', code: 'SEARCH_ERROR' },
      }
    }
  }

  /**
   * Get quiz statistics
   */
  async getQuizStatistics(id: string): Promise<ApiResponse<{
    totalAttempts: number
    averageScore: number
    completionRate: number
    popularityRank: number
  }>> {
    try {
      // Use circuit breaker for statistics query
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quiz_attempts')
          .select('score, is_completed')
          .eq('quiz_id', id)
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch quiz statistics', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data: attempts, error: attemptsError } = result.data!

      if (attemptsError) {
        return {
          success: false,
          error: { message: attemptsError.message, code: attemptsError.code },
        }
      }

      const totalAttempts = attempts?.length || 0
      const completedAttempts = attempts?.filter((a: unknown) => (a as any).is_completed) || []
      const scores = completedAttempts.map((a: unknown) => (a as any).score || 0)
      
      const averageScore = scores.length > 0 
        ? scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length
        : 0

      const completionRate = totalAttempts > 0 
        ? (completedAttempts.length / totalAttempts) * 100 
        : 0

      return {
        success: true,
        data: {
          totalAttempts,
          averageScore: Math.round(averageScore * 10) / 10,
          completionRate: Math.round(completionRate * 10) / 10,
          popularityRank: 0, // TODO: Implement ranking logic
        },
      }
    } catch (error) {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz statistics', code: 'STATS_ERROR' },
      }
    }
  }

  /**
   * Apply filters to quiz queries
   */
  protected applyFilters(query: any, filters: Record<string, unknown>): any {
    let filteredQuery = query as any

    if (filters.category_id) {
      filteredQuery = filteredQuery.eq('category_id', filters.category_id)
    }

    if (filters.level_id) {
      filteredQuery = filteredQuery.eq('level_id', filters.level_id)
    }

    if (filters.quiz_type_id) {
      filteredQuery = filteredQuery.eq('quiz_type_id', filters.quiz_type_id)
    }

    if (filters.is_active !== undefined) {
      filteredQuery = filteredQuery.eq('is_active', filters.is_active)
    }

    if (filters.difficulty_min) {
      filteredQuery = filteredQuery.gte('difficulty_level', filters.difficulty_min)
    }

    if (filters.difficulty_max) {
      filteredQuery = filteredQuery.lte('difficulty_level', filters.difficulty_max)
    }

    if (filters.created_after) {
      filteredQuery = filteredQuery.gte('created_at', filters.created_after)
    }

    if (filters.created_before) {
      filteredQuery = filteredQuery.lte('created_at', filters.created_before)
    }

    return filteredQuery
  }

  /**
   * Get quiz questions (from quiz_questions junction table)
   */
  async getQuizQuestions(quizId: string): Promise<ApiResponse<Array<{
    id: string
    quiz_id: string
    question_id: string
    order_index: number
    points: number | null
    is_active: boolean | null
  }>>> {
    try {
      // Use circuit breaker for quiz questions query
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quiz_questions')
          .select('*')
          .eq('quiz_id', quizId)
          .order('order_index', { ascending: true })
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch quiz questions', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Add a question to a quiz (creates entry in quiz_questions junction table)
   */
  async addQuestionToQuiz(
    quizId: string,
    questionId: string,
    options: {
      order_index?: number
      points?: number
      is_active?: boolean
    } = {},
  ): Promise<ApiResponse<void>> {
    try {
      const { order_index = 1, points = 1, is_active = true } = options

      // Use circuit breaker for write operation
      const result = await this.writeCircuitBreaker.execute(async () => {
        return await this.db
          .from('quiz_questions')
          .insert({
            quiz_id: quizId,
            question_id: questionId,
            order_index,
            points,
            is_active,
          })
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to add question to quiz', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: undefined,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Remove a question from a quiz (deletes entry from quiz_questions junction table)
   */
  async removeQuestionFromQuiz(quizId: string, questionId: string): Promise<ApiResponse<void>> {
    try {
      // Use circuit breaker for delete operation
      const result = await this.writeCircuitBreaker.execute(async () => {
        return await this.db
          .from('quiz_questions')
          .delete()
          .eq('quiz_id', quizId)
          .eq('question_id', questionId)
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to remove question from quiz', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: undefined,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Delete quiz with safety checks
   */
  async deleteQuizSafely(id: string): Promise<ApiResponse<void>> {
    try {
      // First, check if quiz has any attempts using circuit breaker
      const attemptsResult = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quiz_attempts')
          .select('id')
          .eq('quiz_id', id)
          .limit(1)
      })

      if (!attemptsResult.success) {
        return {
          success: false,
          error: { message: attemptsResult.error || 'Circuit breaker: Failed to check quiz attempts', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data: attempts, error: attemptsError } = attemptsResult.data!

      if (attemptsError) {
        return {
          success: false,
          error: { message: attemptsError.message, code: attemptsError.code },
        }
      }

      if (attempts && attempts.length > 0) {
        return {
          success: false,
          error: { message: 'Cannot delete quiz with existing attempts. Archive it instead.', code: 'QUIZ_HAS_ATTEMPTS' },
        }
      }

      // Delete questions and related data first (cascade delete) using circuit breaker
      const questionsDeleteResult = await this.writeCircuitBreaker.execute(async () => {
        return await this.db
          .from('questions')
          .delete()
          .eq('quiz_id', id)
      })

      if (!questionsDeleteResult.success) {
        return {
          success: false,
          error: { message: questionsDeleteResult.error || 'Circuit breaker: Failed to delete questions', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { error: questionsError } = questionsDeleteResult.data!

      if (questionsError) {
        return {
          success: false,
          error: { message: questionsError.message, code: questionsError.code },
        }
      }

      // Then delete the quiz using circuit breaker
      const quizDeleteResult = await this.writeCircuitBreaker.execute(async () => {
        return await this.db
          .from('quizzes')
          .delete()
          .eq('id', id)
      })

      if (!quizDeleteResult.success) {
        return {
          success: false,
          error: { message: quizDeleteResult.error || 'Circuit breaker: Failed to delete quiz', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { error: quizError } = quizDeleteResult.data!

      if (quizError) {
        return {
          success: false,
          error: { message: quizError.message, code: quizError.code },
        }
      }

      return {
        success: true,
        data: undefined,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get total count of quizzes
   */
  async getQuizzesCount(): Promise<ApiResponse<number>> {
    try {
      // Use circuit breaker for count query
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quizzes')
          .select('*', { count: 'exact', head: true })
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to get quiz count', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { count, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get count of active quizzes
   */
  async getActiveQuizzesCount(): Promise<ApiResponse<number>> {
    try {
      // Use circuit breaker for active count query
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quizzes')
          .select('*', { count: 'exact', head: true })
          .eq('is_active', true)
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to get active quiz count', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { count, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: count || 0,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get quizzes by category
   */
  async getQuizzesByCategory(categoryId: string): Promise<ApiResponse<DbQuiz[]>> {
    try {
      // Use circuit breaker for category query
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quizzes')
          .select('*')
          .eq('category_id', categoryId)
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to get quizzes by category', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Get quizzes by level
   */
  async getQuizzesByLevel(levelId: string): Promise<ApiResponse<DbQuiz[]>> {
    try {
      // Use circuit breaker for level query
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quizzes')
          .select('*')
          .eq('level_id', levelId)
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to get quizzes by level', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      return {
        success: true,
        data: data || [],
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }

  /**
   * Duplicate quiz with new title
   */
  async duplicateQuiz(id: string, newTitle: string): Promise<ApiResponse<DbQuiz>> {
    try {
      // Get the original quiz
      const originalQuizResult = await this.getById(id)

      if (!originalQuizResult.success || !originalQuizResult.data) {
        return {
          success: false,
          error: { message: 'Original quiz not found', code: 'QUIZ_NOT_FOUND' },
        }
      }

      const originalQuiz = originalQuizResult.data

      // Create new quiz (exclude questions and system fields)
      const {
        questions,
        id: originalId,
        created_at,
        updated_at,
        ...quizData
      } = originalQuiz as DbQuiz & { questions?: any[] }

      // Use circuit breaker for duplicate operation
      const duplicateResult = await this.writeCircuitBreaker.execute(async () => {
        return await this.db
          .from('quizzes')
          .insert({
            ...quizData,
            title: newTitle,
            key: `${quizData.key}_copy`,
          })
          .select()
          .single()
      })

      if (!duplicateResult.success) {
        return {
          success: false,
          error: { message: duplicateResult.error || 'Circuit breaker: Failed to duplicate quiz', code: 'CIRCUIT_BREAKER_ERROR' },
        }
      }

      const { data, error } = duplicateResult.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code },
        }
      }

      // TODO: Also duplicate questions if needed

      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error occurred' },
      }
    }
  }
}