// Admin Repository Manager - Singleton access to repositories
// Handles admin portal-specific repository initialization and access

import { RepositoryManager, type RepositoryConfig } from 'wf-shared/repositories'
import { RepositoryContainer } from './RepositoryContainer'

export class AdminRepositoryManager extends RepositoryManager {
  private static instance?: AdminRepositoryManager

  private constructor() {
    super()
  }

  /**
   * Initialize admin repository system
   */
  static initialize(config: RepositoryConfig): void {
    // Initialize base repository system
    super.initialize(config)
    
    // Create and set admin container
    const container = new RepositoryContainer()
    this.setContainer(container as unknown as Record<string, unknown>)
  }

  /**
   * Get admin repository container
   */
  static getRepositories(): RepositoryContainer {
    return super.getContainer() as unknown as RepositoryContainer
  }

  /**
   * Convenience method to get singleton instance
   */
  static getInstance(): AdminRepositoryManager {
    if (!this.instance) {
      this.instance = new AdminRepositoryManager()
    }
    return this.instance
  }
}

// Export convenient access to repositories
export const repositories = {
  get container() {
    return AdminRepositoryManager.getRepositories()
  },
  get userApi() {
    return this.container.userApi
  },
  get quizApi() {
    return this.container.quizApi
  },
  get questionApi() {
    return this.container.questionApi
  },
  get referenceApi() {
    return this.container.referenceApi
  },
  get validationApi() {
    return this.container.validationApi
  },
  get bulkImportApi() {
    return this.container.bulkImportApi
  },
  get authStorage() {
    return this.container.authStorage
  },
}