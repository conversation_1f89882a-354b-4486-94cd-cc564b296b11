// Admin Portal Repository Initialization
// Initializes repository system with environment configuration

import { AdminRepositoryManager } from './AdminRepositoryManager'

// Initialize repository system
const supabaseUrl: string = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey: string = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Initialize the repository system
AdminRepositoryManager.initialize({
  supabaseUrl,
  supabaseAnonKey,
})

// Export the repositories for use throughout the app
export { repositories } from './AdminRepositoryManager'