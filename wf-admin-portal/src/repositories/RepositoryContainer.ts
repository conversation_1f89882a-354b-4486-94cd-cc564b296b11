// Repository container for dependency injection and management
// Provides centralized access to all repositories in the admin portal

// API Repositories
import { UserApiRepository } from './api/UserApiRepository'
import { QuizApiRepository } from './api/QuizApiRepository'
import { QuestionApiRepository } from './api/QuestionApiRepository'
import { DashboardApiRepository } from './api/DashboardApiRepository'
import { ReferenceApiRepository } from './api/ReferenceApiRepository'
import { ValidationApiRepository } from './api/ValidationApiRepository'
import { BulkImportApiRepository } from './api/BulkImportApiRepository'

// Storage Repositories
import { AuthStorageRepository } from './storage/AuthStorageRepository'

// Database Provider
import { DatabaseProvider } from 'wf-shared/repositories'

export class RepositoryContainer {
  // API Repositories
  private _userApi?: UserApiRepository
  private _quizApi?: QuizApiRepository
  private _questionApi?: QuestionApiRepository
  private _dashboardApi?: DashboardApiRepository
  private _referenceApi?: ReferenceApiRepository
  private _validationApi?: ValidationApiRepository
  private _bulkImportApi?: BulkImportApiRepository

  // Storage Repositories
  private _authStorage?: AuthStorageRepository

  constructor() {
    // No database injection needed - repositories use default client
  }

  // API Repository Getters (lazy initialization)
  get userApi(): UserApiRepository {
    if (!this._userApi) {
      this._userApi = new UserApiRepository(this.supabaseClient)
    }
    return this._userApi
  }

  get quizApi(): QuizApiRepository {
    if (!this._quizApi) {
      this._quizApi = new QuizApiRepository(this.supabaseClient)
    }
    return this._quizApi
  }

  get questionApi(): QuestionApiRepository {
    if (!this._questionApi) {
      this._questionApi = new QuestionApiRepository(this.supabaseClient)
    }
    return this._questionApi
  }

  get dashboardApi(): DashboardApiRepository {
    if (!this._dashboardApi) {
      this._dashboardApi = new DashboardApiRepository(this.supabaseClient)
    }
    return this._dashboardApi
  }

  get referenceApi(): ReferenceApiRepository {
    if (!this._referenceApi) {
      this._referenceApi = new ReferenceApiRepository(this.supabaseClient)
    }
    return this._referenceApi
  }

  get validationApi(): ValidationApiRepository {
    if (!this._validationApi) {
      this._validationApi = new ValidationApiRepository(this.supabaseClient)
    }
    return this._validationApi
  }

  get bulkImportApi(): BulkImportApiRepository {
    if (!this._bulkImportApi) {
      this._bulkImportApi = new BulkImportApiRepository(this.supabaseClient)
    }
    return this._bulkImportApi
  }

  // Storage Repository Getters (lazy initialization)
  get authStorage(): AuthStorageRepository {
    if (!this._authStorage) {
      this._authStorage = new AuthStorageRepository()
    }
    return this._authStorage
  }

  // Database Client Access
  get supabaseClient() {
    return DatabaseProvider.getInstance().getDefaultClient()
  }

  /**
   * Reset all repositories (useful for testing or cleanup)
   */
  reset(): void {
    this._userApi = undefined
    this._quizApi = undefined
    this._questionApi = undefined
    this._dashboardApi = undefined
    this._referenceApi = undefined
    this._validationApi = undefined
    this._bulkImportApi = undefined
    this._authStorage = undefined
  }
}