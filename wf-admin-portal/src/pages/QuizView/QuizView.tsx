import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Clock, Users, BarChart3, CheckCircle, XCircle, FileText, HelpCircle, Play, Copy, Plus } from 'lucide-react'
import { Pagination } from '@/components/ui/pagination'
import AddQuestionsModal from '@/components/modals/AddQuestionsModal'
import { useQuizViewHandler } from './QuizView.handler'
import styles from './QuizView.module.css'
import { sharedStyles } from '../../styles/shared'

export default function QuizView() {
  const {
    quiz,
    currentQuestions,
    currentPage,
    showAddQuestionsModal,
    isLoading,
    error,
    quizResponse,
    totalPages,
    startIndex,
    endIndex,
    showPagination,
    hasQuestions,
    totalQuestions,
    isActive,
    existingQuestionIds,
    setCurrentPage,
    openAddQuestionsModal,
    closeAddQuestionsModal,
    handleAddQuestionsSuccess,
    navigateToQuizManagement,
    navigateToQuizEdit,
    navigateToQuizPreview,
    copyQuizId,
    getDifficultyLabel,
    getTimeLimit,
    getCategoryLabel,
    getLevelLabel,
    getQuizTypeLabel,
    formatQuestionNumber,
    formatOptionLetter,
    formatCreatedDate,
    formatUpdatedDate,
  } = useQuizViewHandler()

  if (isLoading) {
    return (
      <div className={sharedStyles.states.loadingContainer}>
        <div className={sharedStyles.states.loadingContent}>
          <div className={sharedStyles.states.loadingSpinner}></div>
          <p className={sharedStyles.states.loadingText}>Loading quiz...</p>
        </div>
      </div>
    )
  }

  if (error || !quiz) {
    return (
      <div className={sharedStyles.layouts.container}>
        <Card className={sharedStyles.cards.cardBase}>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.states.errorContainer}>
              <XCircle className={sharedStyles.states.errorIcon} />
              <h2 className={sharedStyles.states.errorTitle}>Quiz Not Found</h2>
              <p className={sharedStyles.states.errorDescription}>
                {quizResponse?.error || 'The quiz you are looking for does not exist.'}
              </p>
              <Button className={sharedStyles.buttons.buttonOutline} onClick={navigateToQuizManagement}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Quiz Management
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={sharedStyles.layouts.container}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className={sharedStyles.layouts.header}>
            <div className={sharedStyles.layouts.headerContent}>
              <div className={styles.headerTitleContainer}>
                <Button
                  className={sharedStyles.buttons.buttonGhost}
                  onClick={navigateToQuizManagement}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <h1 className={sharedStyles.layouts.headerTitle}>{quiz.title}</h1>
              </div>
              <p className={sharedStyles.layouts.headerSubtitle}>{quiz.description}</p>
              <div className={styles.headerMetaContainer}>
                <span className={styles.headerMetaItem}>
                  <FileText className="h-4 w-4" />
                  Quiz Key: <code className={styles.headerQuizKey}>{quiz.key}</code>
                </span>
                <span className={styles.headerMetaItem}>
                  <Clock className="h-4 w-4" />
                  Created: {formatCreatedDate(quiz.created_at || '')}
                </span>
                <span className={styles.headerMetaItem}>
                  <Clock className="h-4 w-4" />
                  Updated: {formatUpdatedDate(quiz.updated_at || '')}
                </span>
              </div>
            </div>
            <div className={sharedStyles.layouts.headerActions}>
              <Button 
                variant="outline" 
                size="sm"
                onClick={copyQuizId}
                title="Copy Quiz ID"
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                className={sharedStyles.buttons.buttonOutline}
                onClick={navigateToQuizPreview}
              >
                <Play className="h-4 w-4 mr-2" />
                Preview Quiz
              </Button>
              <Button
                className={sharedStyles.buttons.buttonOutline}
                onClick={openAddQuestionsModal}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Questions
              </Button>
              <Button className={sharedStyles.buttons.buttonPrimary} onClick={navigateToQuizEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Quiz
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Quiz Details */}
      <div className={styles.statsGrid}>
        <Card>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCard}>
              <BarChart3 className={styles.statCardIcon} />
              <div>
                <p className={styles.statCardLabel}>Difficulty</p>
                <p className={styles.statCardValue}>
                  {getDifficultyLabel(quiz.difficulty_level)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCard}>
              <Clock className={styles.statCardIcon} />
              <div>
                <p className={styles.statCardLabel}>Time Limit</p>
                <p className={styles.statCardValue}>
                  {getTimeLimit(quiz.time_limit_minutes)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCard}>
              <Users className={styles.statCardIcon} />
              <div>
                <p className={styles.statCardLabel}>Questions</p>
                <p className={styles.statCardValue}>{quiz.total_questions}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCard}>
              {isActive ? (
                <CheckCircle className={styles.statCardIcon} />
              ) : (
                <XCircle className={styles.statCardIcon} />
              )}
              <div>
                <p className={styles.statCardLabel}>Status</p>
                <p className={styles.statCardValue}>
                  {isActive ? 'Active' : 'Inactive'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quiz Information */}
      <div className={sharedStyles.layouts.gridTwo}>
        <Card>
          <CardHeader>
            <CardTitle>Quiz Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={sharedStyles.forms.formFieldGrid}>
              <div>
                <label className={sharedStyles.forms.fieldLabel}>Category</label>
                <div className={sharedStyles.forms.fieldContainer}>
                  <span className={sharedStyles.badges.badgeSecondary}>
                    {getCategoryLabel(quiz.categories)}
                  </span>
                </div>
              </div>

              <div>
                <label className={sharedStyles.forms.fieldLabel}>Level</label>
                <div className={sharedStyles.forms.fieldContainer}>
                  <span className={sharedStyles.badges.badgeSecondary}>
                    {getLevelLabel(quiz.levels)}
                  </span>
                </div>
              </div>

              <div className={sharedStyles.forms.formField}>
                <label className={sharedStyles.forms.fieldLabel}>Quiz Type</label>
                <div className={sharedStyles.forms.fieldContainer}>
                  <span className={sharedStyles.badges.badgeSecondary}>
                    {getQuizTypeLabel(quiz.quiz_types)}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className={styles.instructionsHeader}>
            <CardTitle className={styles.instructionsTitle}>
              <FileText className={styles.instructionsIcon} />
              Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            {quiz.instructions ? (
              <p className={styles.instructionsContent}>{quiz.instructions}</p>
            ) : (
              <p className={sharedStyles.states.emptyStateDescription}>No instructions provided</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Questions with Pagination */}
      {hasQuestions ? (
        <Card>
          <CardHeader className={styles.questionsHeader}>
            <CardTitle className={styles.questionsTitle}>
              <HelpCircle className="h-5 w-5 mr-2" />
              Questions ({totalQuestions})
            </CardTitle>
            <div className={styles.questionsActions}>
              <span>
                Showing {startIndex + 1}-{Math.min(endIndex, totalQuestions)} of {totalQuestions}
              </span>
            </div>
          </CardHeader>
          <CardContent>
            <div className={sharedStyles.layouts.container}>
              {currentQuestions.map((question, index: number) => (
                <div key={question.id} className={styles.questionItem}>
                  <div className={styles.questionHeader}>
                    <h4 className={styles.questionText}>
                      <span className={styles.questionNumber}>
                        {formatQuestionNumber(index)}
                      </span>
                      {question.question_text}
                    </h4>
                    <div className={styles.questionBadges}>
                      <span className={styles.questionBadgeType}>
                        Type ID: {question.question_type_id}
                      </span>
                      <span className={styles.questionBadgeDifficulty}>
                        Difficulty: {question.difficulty_level || 1}
                      </span>
                    </div>
                  </div>
                  
                  {/* Question Options */}
                  {question.question_options && question.question_options.length > 0 && (
                    <div className={styles.questionOptions}>
                      <p>Options:</p>
                      <div className={styles.questionOptions}>
                        {question.question_options.map((option, optionIndex: number) => (
                          <div 
                            key={option.id} 
                            className={option.is_correct 
                              ? styles.questionOptionCorrect
                              : styles.questionOptionIncorrect
                            }
                          >
                            <span>
                              {formatOptionLetter(optionIndex)}.
                            </span> {option.option_text}
                            {option.is_correct && (
                              <CheckCircle className="w-4 h-4 text-green-500 ml-2" />
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* Question Explanation */}
                  {question.explanations && question.explanations.length > 0 && (
                    <div className={styles.questionExplanation}>
                      <p className={styles.questionExplanationTitle}>Explanation:</p>
                      <p className={styles.questionExplanationText}>
                        {question.explanations[0].content}
                      </p>
                    </div>
                  )}
                  
                  {/* Question Metadata */}
                  {question.metadata && (
                    <div className="mt-4">
                      <details>
                        <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                          View Metadata
                        </summary>
                        <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                          {JSON.stringify(question.metadata, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            {/* Pagination Controls */}
            {showPagination && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Questions Found</h3>
            <p className="text-gray-600 mb-6">
              This quiz doesn't have any questions yet.
            </p>
            <div>
              <Button variant="outline" onClick={openAddQuestionsModal}>
                <Plus className="h-4 w-4 mr-2" />
                Add Questions
              </Button>
              <Button variant="outline" onClick={navigateToQuizManagement}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Quiz Management
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add Questions Modal */}
      {quiz && (
        <AddQuestionsModal
          isOpen={showAddQuestionsModal}
          onClose={closeAddQuestionsModal}
          onSuccess={handleAddQuestionsSuccess}
          quizId={quiz.id}
          quizTitle={quiz.title || 'Untitled Quiz'}
          existingQuestionIds={existingQuestionIds}
        />
      )}
    </div>
  )
}