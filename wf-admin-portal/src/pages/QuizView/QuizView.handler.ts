import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { serviceContainer } from '@/services'
import { formatDate } from 'wf-shared/utils'

export interface QuizViewState {
  currentPage: number
  showAddQuestionsModal: boolean
  questionsPerPage: number
}

export const useQuizViewHandler = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  
  // Local state
  const [currentPage, setCurrentPage] = useState(1)
  const [showAddQuestionsModal, setShowAddQuestionsModal] = useState(false)
  const questionsPerPage = 5

  // Fetch quiz data with questions
  const { data: quizResponse, isLoading, error, refetch } = useQuery({
    queryKey: ['quiz-with-questions', id],
    queryFn: () => serviceContainer.quizService.getQuizWithQuestionsById(id!),
    enabled: !!id,
  })

  const quiz = quizResponse?.data

  // Pagination calculations
  const totalPages = Math.ceil((quiz?.questions?.length || 0) / questionsPerPage)
  const startIndex = (currentPage - 1) * questionsPerPage
  const endIndex = startIndex + questionsPerPage
  const currentQuestions = quiz?.questions?.slice(startIndex, endIndex) || []

  // Navigation handlers
  const navigateToQuizManagement = () => {
    navigate('/quiz-management')
  }

  const navigateToQuizEdit = () => {
    navigate(`/quiz/${quiz?.id}/edit`)
  }

  const navigateToQuizPreview = () => {
    window.open(`/quiz/${quiz?.id}/preview`, '_blank')
  }

  // Modal handlers
  const openAddQuestionsModal = () => {
    setShowAddQuestionsModal(true)
  }

  const closeAddQuestionsModal = () => {
    setShowAddQuestionsModal(false)
  }

  const handleAddQuestionsSuccess = () => {
    refetch()
    setShowAddQuestionsModal(false)
  }

  // Utility handlers
  const copyQuizId = () => {
    if (quiz?.id) {
      navigator.clipboard.writeText(quiz.id)
    }
  }

  // Data formatters
  const getDifficultyLabel = (level: number) => {
    if (level === 1 || level === 2) return 'Easy'
    if (level === 3) return 'Medium'
    return 'Hard'
  }

  const getTimeLimit = (minutes: number | null) => {
    return minutes ? `${minutes} min` : 'No limit'
  }

  const getQuestionTypeLabel = (questionTypes: any) => {
    if (Array.isArray(questionTypes)) {
      return questionTypes[0]?.name || 'Unknown Type'
    }
    return questionTypes?.name || 'Unknown Type'
  }

  const getCategoryLabel = (categories: any) => {
    if (Array.isArray(categories)) {
      return categories[0]?.name || 'Unknown Category'
    }
    return categories?.name || 'Unknown Category'
  }

  const getLevelLabel = (levels: any) => {
    if (Array.isArray(levels)) {
      return levels[0]?.name || 'Unknown Level'
    }
    return levels?.name || 'Unknown Level'
  }

  const getQuizTypeLabel = (quizTypes: any) => {
    if (Array.isArray(quizTypes)) {
      return quizTypes[0]?.name || 'Unknown Type'
    }
    return quizTypes?.name || 'Unknown Type'
  }

  const formatQuestionNumber = (index: number) => {
    return startIndex + index + 1
  }

  const formatOptionLetter = (index: number) => {
    return String.fromCharCode(65 + index)
  }

  const formatCreatedDate = (date: string) => {
    return date ? formatDate(date) : 'Unknown'
  }

  const formatUpdatedDate = (date: string) => {
    return date ? formatDate(date) : 'Unknown'
  }

  // Computed values
  const hasQuestions = quiz?.questions && quiz.questions.length > 0
  const totalQuestions = quiz?.questions?.length || 0
  const showPagination = totalQuestions > questionsPerPage
  const isActive = quiz?.is_active
  const existingQuestionIds = quiz?.quiz_questions?.map(qq => qq.question_id) || []

  return {
    // Data
    quiz,
    currentQuestions,
    
    // State
    currentPage,
    showAddQuestionsModal,
    questionsPerPage,
    
    // Loading/Error states
    isLoading,
    error,
    quizResponse,
    
    // Pagination
    totalPages,
    startIndex,
    endIndex,
    showPagination,
    
    // Computed values
    hasQuestions,
    totalQuestions,
    isActive,
    existingQuestionIds,
    
    // Actions
    setCurrentPage,
    openAddQuestionsModal,
    closeAddQuestionsModal,
    handleAddQuestionsSuccess,
    refetch,
    
    // Navigation
    navigateToQuizManagement,
    navigateToQuizEdit,
    navigateToQuizPreview,
    
    // Utilities
    copyQuizId,
    getDifficultyLabel,
    getTimeLimit,
    getQuestionTypeLabel,
    getCategoryLabel,
    getLevelLabel,
    getQuizTypeLabel,
    formatQuestionNumber,
    formatOptionLetter,
    formatCreatedDate,
    formatUpdatedDate,
  }
}