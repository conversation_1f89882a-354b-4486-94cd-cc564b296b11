/**
 * QuizView CSS Module - Refactored with Shared Modules
 * Component-specific styles only, common patterns imported from shared modules
 */

/* QuizView-specific styles only */

/* Header Section - QuizView specific layout */
.headerCard {
  composes: cardBase from '@styles/shared/cards.module.css';
  padding: theme('spacing.6');
}

.headerTitleContainer {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
  margin-bottom: theme('spacing.2');
}

.headerMetaContainer {
  display: flex;
  align-items: center;
  gap: theme('spacing.4');
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.500');
}

.headerMetaItem {
  display: flex;
  align-items: center;
  gap: theme('spacing.1');
}

.headerQuizKey {
  background-color: theme('colors.gray.100');
  padding: theme('spacing.1') theme('spacing.2');
  border-radius: theme('borderRadius.DEFAULT');
  font-size: theme('fontSize.xs');
  font-family: theme('fontFamily.mono');
}

.headerBadge {
  composes: badgeBase from '@styles/shared/badges.module.css';
  padding: theme('spacing.1') theme('spacing.3');
}

.headerBadgeActive {
  composes: headerBadge;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.headerBadgeInactive {
  composes: headerBadge;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

/* Quiz Statistics - QuizView specific stats display */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: theme('spacing.4');
  margin-bottom: theme('spacing.6');
}

@media (min-width: theme('screens.md')) {
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .statsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.statCard {
  composes: cardBase from '@styles/shared/cards.module.css';
  padding: theme('spacing.4');
  text-align: center;
}

.statCardIcon {
  height: theme('spacing.8');
  width: theme('spacing.8');
  margin: 0 auto theme('spacing.2') auto;
  color: theme('colors.blue.600');
}

.statCardValue {
  font-size: theme('fontSize.2xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.1');
}

.statCardLabel {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
}

.statCardTrend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: theme('spacing.1');
  margin-top: theme('spacing.1');
  font-size: theme('fontSize.xs');
}

.statCardTrendUp {
  composes: statCardTrend;
  color: theme('colors.green.600');
}

.statCardTrendDown {
  composes: statCardTrend;
  color: theme('colors.red.600');
}

.statCardTrendNeutral {
  composes: statCardTrend;
  color: theme('colors.gray.500');
}

/* Quiz Instructions - QuizView specific instructions display */
.instructionsCard {
  composes: cardBase from '@styles/shared/cards.module.css';
  margin-bottom: theme('spacing.6');
}

.instructionsHeader {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  margin-bottom: theme('spacing.3');
}

.instructionsIcon {
  height: theme('spacing.5');
  width: theme('spacing.5');
  color: theme('colors.blue.600');
}

.instructionsTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
}

.instructionsContent {
  color: theme('colors.gray.600');
  line-height: theme('lineHeight.relaxed');
}

/* Questions Section - QuizView specific questions display */
.questionsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: theme('spacing.4');
}

.questionsTitle {
  font-size: theme('fontSize.xl');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
}

.questionsActions {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.questionItem {
  composes: cardHover from '@styles/shared/cards.module.css';
  padding: theme('spacing.4');
  margin-bottom: theme('spacing.3');
}

.questionHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: theme('spacing.3');
}

.questionNumber {
  display: flex;
  align-items: center;
  justify-content: center;
  width: theme('spacing.8');
  height: theme('spacing.8');
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
  border-radius: theme('borderRadius.full');
  font-weight: theme('fontWeight.semibold');
  font-size: theme('fontSize.sm');
}

.questionText {
  flex: 1;
  margin-left: theme('spacing.3');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
  line-height: theme('lineHeight.relaxed');
}

.questionActions {
  display: flex;
  align-items: center;
  gap: theme('spacing.1');
}

.questionBadges {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  margin-bottom: theme('spacing.3');
}

.questionBadge {
  composes: badgeBase from '@styles/shared/badges.module.css';
}

.questionBadgeType {
  composes: questionBadge;
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.800');
}

.questionBadgeDifficulty {
  composes: questionBadge;
}

.questionBadgeDifficulty1,
.questionBadgeDifficulty2 {
  composes: questionBadgeDifficulty;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.questionBadgeDifficulty3 {
  composes: questionBadgeDifficulty;
  background-color: theme('colors.yellow.100');
  color: theme('colors.yellow.800');
}

.questionBadgeDifficulty4,
.questionBadgeDifficulty5 {
  composes: questionBadgeDifficulty;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

.questionOptions {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: theme('spacing.2');
  margin-bottom: theme('spacing.3');
}

@media (min-width: theme('screens.md')) {
  .questionOptions {
    grid-template-columns: repeat(2, 1fr);
  }
}

.questionOption {
  padding: theme('spacing.2');
  border-radius: theme('borderRadius.DEFAULT');
  font-size: theme('fontSize.sm');
}

.questionOptionCorrect {
  composes: questionOption;
  background-color: theme('colors.green.50');
  color: theme('colors.green.800');
  border: 1px solid theme('colors.green.200');
}

.questionOptionIncorrect {
  composes: questionOption;
  background-color: theme('colors.gray.50');
  color: theme('colors.gray.700');
  border: 1px solid theme('colors.gray.200');
}

.questionExplanation {
  margin-top: theme('spacing.3');
  padding: theme('spacing.3');
  background-color: theme('colors.blue.50');
  border: 1px solid theme('colors.blue.200');
  border-radius: theme('borderRadius.DEFAULT');
}

.questionExplanationTitle {
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.blue.800');
  margin-bottom: theme('spacing.1');
}

.questionExplanationText {
  font-size: theme('fontSize.sm');
  color: theme('colors.blue.800');
  line-height: theme('lineHeight.relaxed');
}

/* Question Summary - QuizView specific summary display */
.summaryCard {
  composes: cardBase from '@styles/shared/cards.module.css';
  margin-top: theme('spacing.6');
}

.summaryTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.4');
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: theme('spacing.4');
}

@media (min-width: theme('screens.md')) {
  .summaryGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.summaryItem {
  text-align: center;
}

.summaryItemValue {
  font-size: theme('fontSize.xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.blue.600');
  margin-bottom: theme('spacing.1');
}

.summaryItemLabel {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
}

/* Quick Actions - QuizView specific action buttons */
.quickActions {
  display: flex;
  flex-wrap: wrap;
  gap: theme('spacing.2');
  margin-top: theme('spacing.4');
}

.quickActionButton {
  composes: buttonBase from '@styles/shared/buttons.module.css';
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  padding: theme('spacing.2') theme('spacing.3');
  font-size: theme('fontSize.sm');
}

.quickActionButtonPrimary {
  composes: quickActionButton;
  background-color: theme('colors.blue.600');
  color: white;
  border: 1px solid theme('colors.blue.600');
}

.quickActionButtonPrimary:hover {
  background-color: theme('colors.blue.700');
  border-color: theme('colors.blue.700');
}

.quickActionButtonSecondary {
  composes: quickActionButton;
  background-color: white;
  color: theme('colors.gray.700');
  border: 1px solid theme('colors.gray.300');
}

.quickActionButtonSecondary:hover {
  background-color: theme('colors.gray.50');
  border-color: theme('colors.gray.400');
}

.quickActionIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
}

/* Difficulty Progress - QuizView specific difficulty breakdown */
.difficultyProgress {
  margin-top: theme('spacing.4');
}

.difficultyProgressTitle {
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.700');
  margin-bottom: theme('spacing.2');
}

.difficultyProgressBar {
  display: flex;
  height: theme('spacing.2');
  border-radius: theme('borderRadius.full');
  overflow: hidden;
  background-color: theme('colors.gray.200');
}

.difficultyProgressSegment {
  height: 100%;
  transition: width 0.3s ease;
}

.difficultyProgressEasy {
  composes: difficultyProgressSegment;
  background-color: theme('colors.green.500');
}

.difficultyProgressMedium {
  composes: difficultyProgressSegment;
  background-color: theme('colors.yellow.500');
}

.difficultyProgressHard {
  composes: difficultyProgressSegment;
  background-color: theme('colors.red.500');
}

.difficultyLegend {
  display: flex;
  justify-content: space-between;
  margin-top: theme('spacing.2');
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.500');
}

/* Responsive Adjustments - QuizView specific responsive behavior */
@media (max-width: theme('screens.sm')) {
  .headerCard {
    padding: theme('spacing.4');
  }
  
  .headerTitleContainer {
    flex-direction: column;
    align-items: flex-start;
    gap: theme('spacing.2');
  }
  
  .questionsHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: theme('spacing.2');
  }
  
  .questionsActions {
    justify-content: flex-start;
  }
  
  .questionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: theme('spacing.2');
  }
  
  .questionText {
    margin-left: 0;
  }
  
  .quickActions {
    flex-direction: column;
  }
  
  .quickActionButton {
    justify-content: center;
  }
}

/* Dark mode overrides for QuizView-specific elements */
:global(.dark) .headerCard {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .headerQuizKey {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
}

:global(.dark) .headerBadgeActive {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.300');
}

:global(.dark) .headerBadgeInactive {
  background-color: rgba(239, 68, 68, 0.2);
  color: theme('colors.red.300');
}

:global(.dark) .statCard {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .statCardValue {
  color: white;
}

:global(.dark) .statCardLabel {
  color: theme('colors.gray.400');
}

:global(.dark) .instructionsCard {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .instructionsTitle {
  color: white;
}

:global(.dark) .instructionsContent {
  color: theme('colors.gray.300');
}

:global(.dark) .questionsTitle {
  color: white;
}

:global(.dark) .questionItem {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .questionItem:hover {
  background-color: theme('colors.gray.700');
}

:global(.dark) .questionNumber {
  background-color: rgba(59, 130, 246, 0.2);
  color: theme('colors.blue.300');
}

:global(.dark) .questionText {
  color: white;
}

:global(.dark) .questionBadgeType {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
}

:global(.dark) .questionBadgeDifficulty1,
:global(.dark) .questionBadgeDifficulty2 {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.300');
}

:global(.dark) .questionBadgeDifficulty3 {
  background-color: rgba(245, 158, 11, 0.2);
  color: theme('colors.yellow.300');
}

:global(.dark) .questionBadgeDifficulty4,
:global(.dark) .questionBadgeDifficulty5 {
  background-color: rgba(239, 68, 68, 0.2);
  color: theme('colors.red.300');
}

:global(.dark) .questionOptionCorrect {
  background-color: rgba(34, 197, 94, 0.1);
  color: theme('colors.green.300');
  border-color: theme('colors.green.700');
}

:global(.dark) .questionOptionIncorrect {
  background-color: theme('colors.gray.800');
  color: theme('colors.gray.300');
  border-color: theme('colors.gray.600');
}

:global(.dark) .questionExplanation {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: theme('colors.blue.700');
}

:global(.dark) .questionExplanationTitle,
:global(.dark) .questionExplanationText {
  color: theme('colors.blue.300');
}

:global(.dark) .summaryCard {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .summaryTitle {
  color: white;
}

:global(.dark) .summaryItemValue {
  color: theme('colors.blue.400');
}

:global(.dark) .summaryItemLabel {
  color: theme('colors.gray.400');
}

:global(.dark) .quickActionButtonSecondary {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
  border-color: theme('colors.gray.600');
}

:global(.dark) .quickActionButtonSecondary:hover {
  background-color: theme('colors.gray.600');
  border-color: theme('colors.gray.500');
}

:global(.dark) .difficultyProgressTitle {
  color: theme('colors.gray.300');
}

:global(.dark) .difficultyProgressBar {
  background-color: theme('colors.gray.700');
}

:global(.dark) .difficultyLegend {
  color: theme('colors.gray.400');
}