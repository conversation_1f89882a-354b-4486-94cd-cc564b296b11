import { useState } from 'react'
import { useToast } from '@/hooks/use-toast'
import { BulkImportService } from '@/services/BulkImportService'

export const useBulkImportHandler = () => {
  const [importType, setImportType] = useState<'quiz' | 'question' | 'sql'>('quiz')
  const [file, setFile] = useState<File | null>(null)
  const [importing, setImporting] = useState(false)
  const [results, setResults] = useState<any>(null)
  const { toast } = useToast()

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      const isCSV = selectedFile.type === 'text/csv' || selectedFile.name.endsWith('.csv')
      const isExcel = selectedFile.name.endsWith('.xlsx')
      const isSQL = selectedFile.name.endsWith('.sql')

      if (!isCSV && !isExcel && !isSQL) {
        toast({
          title: 'Invalid file type',
          description: 'Please select a CSV, Excel, or SQL file',
          variant: 'destructive',
        })
        return
      }

      // Auto-detect import type based on file extension
      if (isSQL) {
        setImportType('sql')
      }

      setFile(selectedFile)
      setResults(null)
    }
  }

  const handleImport = async () => {
    if (!file) return

    setImporting(true)
    try {
      const fileContent = await readFileContent(file)
      let result

      if (importType === 'sql') {
        result = await BulkImportService.importFromSQL(fileContent)
      } else if (importType === 'quiz') {
        result = await BulkImportService.importQuizzes(fileContent)
      } else if (importType === 'question') {
        result = await BulkImportService.importQuestions(fileContent)
      } else {
        throw new Error('Invalid import type')
      }

      setResults(result)

      if (result.success) {
        toast({
          title: 'Import completed',
          description: `Successfully imported ${result.imported} items with ${result.failed} errors`,
        })
      } else {
        toast({
          title: 'Import failed',
          description: 'Import process failed',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Import failed',
        description: error instanceof Error ? error.message : 'An error occurred during import',
        variant: 'destructive',
      })
    } finally {
      setImporting(false)
    }
  }

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = (_e) => reject(new Error('Failed to read file'))
      reader.readAsText(file)
    })
  }

  const downloadTemplate = (type: 'quiz' | 'question' | 'sql') => {
    try {
      let content: string
      let filename: string

      if (type === 'quiz') {
        content = BulkImportService.generateQuizTemplate()
        filename = 'quiz_template.csv'
      } else if (type === 'question') {
        content = BulkImportService.generateQuestionTemplate()
        filename = 'question_template.csv'
      } else {
        // For SQL, provide a comprehensive sample SQL file based on your actual structure
        content = `-- =========================
-- QUIZ DATA IMPORT TEMPLATE
-- =========================
-- This template follows the same structure as your import_wf_quiz_a2_002.sql file
-- Replace the sample data with your actual quiz content

-- Quiz Metadata
INSERT INTO public.quizzes (id, key, title, description, category_id, level_id, quiz_type_id, difficulty_level, total_questions, time_limit_minutes, instructions, quiz_config, is_active) VALUES
  (uuid_generate_v4(), 'wf_quiz_template', 'Template Quiz Title', 'Description of your quiz content and learning objectives.',
   (SELECT id FROM public.categories WHERE key = 'word_formation'),
   (SELECT id FROM public.levels WHERE key = 'A2'),
   (SELECT id FROM public.quiz_types WHERE key = 'word_formation'),
   2, 3, 10, 'Choose the correct word form to complete each sentence.',
   '{"shuffle_questions": true, "show_feedback_immediately": false, "attempts_allowed": 1}', true);

-- Questions (Add as many as needed)
INSERT INTO public.questions (id, quiz_id, question_type_id, question_text, difficulty_level, order_index, metadata, is_active) VALUES
  -- Question 1
  (uuid_generate_v4(), (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template'), (SELECT id FROM public.question_types WHERE key = 'fill_in_blank'),
   'He is a very ______ person.', 2, 1, '{"focus": "Adjective suffix -ive"}', true),
  -- Question 2
  (uuid_generate_v4(), (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template'), (SELECT id FROM public.question_types WHERE key = 'fill_in_blank'),
   'The ______ of the new building is very modern.', 2, 2, '{"focus": "Noun suffix -ion"}', true),
  -- Question 3
  (uuid_generate_v4(), (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template'), (SELECT id FROM public.question_types WHERE key = 'fill_in_blank'),
   'She is a talented ______.', 2, 3, '{"focus": "Noun suffix -er"}', true);

-- Question Options (4 options per question)
INSERT INTO public.question_options (id, question_id, option_key, option_text, is_correct, sort_order) VALUES
  -- Q1 Options
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 1), 'A', 'create', false, 1),
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 1), 'B', 'creative', true, 2),
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 1), 'C', 'creation', false, 3),
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 1), 'D', 'creatively', false, 4),
  -- Q2 Options
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 2), 'A', 'design', true, 1),
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 2), 'B', 'designer', false, 2),
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 2), 'C', 'designing', false, 3),
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 2), 'D', 'designed', false, 4),
  -- Q3 Options
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 3), 'A', 'sing', false, 1),
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 3), 'B', 'singer', true, 2),
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 3), 'C', 'singing', false, 3),
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 3), 'D', 'song', false, 4);

-- Explanations (One per question)
INSERT INTO public.explanations (id, question_id, explanation_type, explanation_text, metadata) VALUES
  -- Q1 Explanation
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 1), 'correct_answer', 'The adjective "creative" describes someone who is imaginative and able to create new ideas.', '{"option_key": "B"}'),
  -- Q2 Explanation
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 2), 'correct_answer', 'The noun "design" refers to the plan or style of something that is made.', '{"option_key": "A"}'),
  -- Q3 Explanation
  (uuid_generate_v4(), (SELECT id FROM public.questions WHERE quiz_id = (SELECT id FROM public.quizzes WHERE key = 'wf_quiz_template') AND order_index = 3), 'correct_answer', 'The noun "singer" refers to a person who sings, especially professionally.', '{"option_key": "B"}');

-- =========================
-- REFERENCE DATA KEYS
-- =========================
-- Available category keys: word_formation, grammar, vocabulary, toeic, ielts
-- Available level keys: A1, A2, B1, B2, C1, C2
-- Available quiz_type keys: word_formation, multiple_choice, fill_in_blank, true_false
-- Available question_type keys: fill_in_blank, multiple_choice, true_false

-- =========================
-- INSTRUCTIONS
-- =========================
-- 1. Replace 'wf_quiz_template' with your unique quiz key
-- 2. Update quiz metadata (title, description, etc.)
-- 3. Add/modify questions as needed
-- 4. Ensure each question has 4 options (or adjust as needed)
-- 5. Set is_correct = true for the right answer
-- 6. Add explanations for each question
-- 7. Verify all foreign key references exist in your database

-- =========================
-- END OF TEMPLATE
-- =========================`
        filename = 'quiz_import_template.sql'
      }

      // Create and download the file
      const blob = new Blob([content], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: 'Template downloaded',
        description: `${type} template has been downloaded`,
      })
    } catch (error) {
      toast({
        title: 'Download failed',
        description: 'Failed to generate template',
        variant: 'destructive',
      })
    }
  }

  return {
    // State
    importType,
    file,
    importing,
    results,
    
    // Actions
    setImportType,
    handleFileSelect,
    handleImport,
    downloadTemplate,
  }
}