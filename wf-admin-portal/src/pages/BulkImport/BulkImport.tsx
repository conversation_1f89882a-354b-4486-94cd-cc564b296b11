import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Upload, Download, FileText, AlertCircle, CheckCircle } from 'lucide-react'
import { useBulkImportHandler } from './BulkImport.handler'
import styles from './BulkImport.module.css'
import { sharedStyles } from '../../styles/shared'

export default function BulkImport() {
  const {
    importType,
    file,
    importing,
    results,
    setImportType,
    handleFileSelect,
    handleImport,
    downloadTemplate,
  } = useBulkImportHandler()

  return (
    <div className={sharedStyles.layouts.container}>
      <div className={sharedStyles.layouts.header}>
        <div>
          <h1 className={sharedStyles.layouts.headerTitle}>Bulk Import</h1>
          <p className={sharedStyles.layouts.headerSubtitle}>
            Import quizzes and questions from CSV or Excel files
          </p>
        </div>
      </div>

      {/* Import Type Selection */}
      <Card className={sharedStyles.cards.cardBase}>
        <CardHeader className={sharedStyles.cards.cardHeader}>
          <CardTitle>Select Import Type</CardTitle>
        </CardHeader>
        <CardContent className={sharedStyles.cards.cardContent}>
          <div className={styles.importTypeGrid}>
            <button
              onClick={() => setImportType('quiz')}
              className={`${styles.importTypeButton} ${importType === 'quiz' ? styles.importTypeButtonActive : styles.importTypeButtonInactive}`}
            >
              <FileText className={`${styles.importTypeIcon} ${styles.importTypeIconQuiz}`} />
              <h3 className={styles.importTypeTitle}>Import Quizzes</h3>
              <p className={styles.importTypeDescription}>
                Import complete quiz structures from CSV
              </p>
            </button>

            <button
              onClick={() => setImportType('question')}
              className={`${styles.importTypeButton} ${importType === 'question' ? styles.importTypeButtonActive : styles.importTypeButtonInactive}`}
            >
              <FileText className={`${styles.importTypeIcon} ${styles.importTypeIconQuestion}`} />
              <h3 className={styles.importTypeTitle}>Import Questions</h3>
              <p className={styles.importTypeDescription}>
                Import questions with options from CSV
              </p>
            </button>

            <button
              onClick={() => setImportType('sql')}
              className={`${styles.importTypeButton} ${importType === 'sql' ? styles.importTypeButtonActive : styles.importTypeButtonInactive}`}
            >
              <FileText className={`${styles.importTypeIcon} ${styles.importTypeIconSql}`} />
              <h3 className={styles.importTypeTitle}>Import SQL</h3>
              <p className={styles.importTypeDescription}>
                Execute SQL files directly to database
              </p>
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Template Download */}
      <Card className={sharedStyles.cards.cardBase}>
        <CardHeader className={sharedStyles.cards.cardHeader}>
          <CardTitle>Download Template</CardTitle>
        </CardHeader>
        <CardContent className={sharedStyles.cards.cardContent}>
          <div className={styles.templateDownload}>
            <div className={styles.templateInfo}>
              <h3 className={styles.templateTitle}>
                {importType === 'quiz' ? 'Quiz' : importType === 'question' ? 'Question' : 'SQL'} Template
              </h3>
              <p className={styles.templateDescription}>
                {importType === 'sql'
                  ? 'Download sample SQL file with proper structure and syntax'
                  : 'Download the CSV template with proper column headers and sample data'
                }
              </p>
            </div>
            <Button
              className={sharedStyles.buttons.buttonOutline}
              onClick={() => downloadTemplate(importType)}
            >
              <Download className="h-4 w-4 mr-2" />
              Download Template
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* File Upload */}
      <Card className={sharedStyles.cards.cardBase}>
        <CardHeader className={sharedStyles.cards.cardHeader}>
          <CardTitle>Upload File</CardTitle>
        </CardHeader>
        <CardContent className={sharedStyles.cards.cardContent}>
          <div className={styles.uploadArea}>
            <div className={styles.uploadAreaWrapper}>
              <div className="text-center">
                <Upload className={styles.uploadAreaIcon} />
                <h3 className={styles.uploadAreaTitle}>Upload File</h3>
                <div className={styles.uploadAreaDescription}>
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className={styles.uploadAreaLink}>
                      Click to upload
                    </span>{' '}
                    or drag and drop
                  </label>
                  <input
                    id="file-upload"
                    type="file"
                    className={styles.uploadAreaInput}
                    accept=".csv,.xlsx,.sql"
                    onChange={handleFileSelect}
                  />
                </div>
                <p className={styles.uploadAreaHint}>CSV, Excel, or SQL files</p>
              </div>
            </div>

            {file && (
              <div className={styles.filePreview}>
                <div className={styles.filePreviewInfo}>
                  <FileText className={styles.filePreviewIcon} />
                  <div>
                    <div className={styles.filePreviewName}>{file.name}</div>
                    <div className={styles.filePreviewSize}>
                      ({(file.size / 1024).toFixed(1)} KB)
                    </div>
                  </div>
                </div>
                <Button
                  className={sharedStyles.buttons.buttonPrimary}
                  onClick={handleImport}
                  disabled={importing}
                >
                  {importing ? 'Importing...' : 'Import'}
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Import Results */}
      {results && (
        <Card className={sharedStyles.cards.cardBase}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle>Import Results</CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.resultsContainer}>
              {/* Summary */}
              <div className={styles.resultsGrid}>
                <div className={`${styles.resultCard} ${styles.resultCardSuccess}`}>
                  <div className={styles.resultCardHeader}>
                    <CheckCircle className={`${styles.resultCardIcon} ${styles.resultCardIconSuccess}`} />
                    <span className={`${styles.resultCardTitle} ${styles.resultCardTitleSuccess}`}>Imported</span>
                  </div>
                  <div className={`${styles.resultCardValue} ${styles.resultCardValueSuccess}`}>
                    {results.imported}
                  </div>
                </div>

                <div className={`${styles.resultCard} ${styles.resultCardError}`}>
                  <div className={styles.resultCardHeader}>
                    <AlertCircle className={`${styles.resultCardIcon} ${styles.resultCardIconError}`} />
                    <span className={`${styles.resultCardTitle} ${styles.resultCardTitleError}`}>Failed</span>
                  </div>
                  <div className={`${styles.resultCardValue} ${styles.resultCardValueError}`}>
                    {results.failed}
                  </div>
                </div>

                <div className={`${styles.resultCard} ${styles.resultCardInfo}`}>
                  <div className={styles.resultCardHeader}>
                    <FileText className={`${styles.resultCardIcon} ${styles.resultCardIconInfo}`} />
                    <span className={`${styles.resultCardTitle} ${styles.resultCardTitleInfo}`}>Total</span>
                  </div>
                  <div className={`${styles.resultCardValue} ${styles.resultCardValueInfo}`}>
                    {results.imported + results.failed}
                  </div>
                </div>
              </div>

              {/* Errors */}
              {results.errors && results.errors.length > 0 && (
                <div>
                  <h4 className={styles.errorListTitle}>Import Errors</h4>
                  <div className={styles.errorList}>
                    {results.errors.map((error: any, index: number) => (
                      <div key={index} className={styles.errorItem}>
                        <AlertCircle className={styles.errorItemIcon} />
                        <div className={styles.errorItemContent}>
                          <span className={styles.errorItemRow}>Row {error.row}:</span>{' '}
                          <span className={styles.errorItemMessage}>{error.message}</span>
                          {error.field && (
                            <span className={styles.errorItemField}> (Field: {error.field})</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}