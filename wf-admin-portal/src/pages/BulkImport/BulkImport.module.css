/**
 * BulkImport CSS Module - Refactored with Shared Modules
 * Component-specific styles only, common patterns imported from shared modules
 */

/* BulkImport-specific styles only */

/* Import Type Selection - BulkImport specific grid and buttons */
.importTypeGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: theme('spacing.4');
}

@media (min-width: theme('screens.md')) {
  .importTypeGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.importTypeButton {
  composes: buttonOutline from '@styles/shared/buttons.module.css';
  padding: theme('spacing.4');
  text-align: left;
  background-color: white;
}

.importTypeButtonActive {
  composes: importTypeButton;
  border-color: theme('colors.blue.500');
  background-color: theme('colors.blue.50');
}

.importTypeButtonInactive {
  composes: importTypeButton;
  border-color: theme('colors.gray.200');
}

.importTypeButtonInactive:hover {
  border-color: theme('colors.gray.300');
}

.importTypeIcon {
  height: theme('spacing.8');
  width: theme('spacing.8');
  margin-bottom: theme('spacing.2');
}

.importTypeIconQuiz {
  color: theme('colors.blue.500');
}

.importTypeIconQuestion {
  color: theme('colors.green.500');
}

.importTypeIconSql {
  color: theme('colors.purple.500');
}

.importTypeTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
}

.importTypeDescription {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  margin-top: theme('spacing.1');
}

/* Template Download - BulkImport specific layout */
.templateDownload {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.templateInfo {
  flex-grow: 1;
}

.templateTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
}

.templateDescription {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  margin-top: theme('spacing.1');
}

/* File Upload Area - BulkImport specific styling */
.uploadArea {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

.uploadAreaWrapper {
  border: 2px dashed theme('colors.gray.300');
  border-radius: theme('borderRadius.lg');
  padding: theme('spacing.8');
  text-align: center;
  transition: all 0.2s ease;
}

.uploadAreaWrapper:hover {
  border-color: theme('colors.gray.400');
}

.uploadAreaActive {
  border-color: theme('colors.blue.500');
  background-color: theme('colors.blue.50');
}

.uploadAreaIcon {
  height: theme('spacing.12');
  width: theme('spacing.12');
  color: theme('colors.gray.400');
  margin: 0 auto theme('spacing.4');
}

.uploadAreaTitle {
  margin-top: theme('spacing.4');
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
}

.uploadAreaDescription {
  margin-top: theme('spacing.2');
  color: theme('colors.gray.600');
}

.uploadAreaInput {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.uploadAreaLink {
  color: theme('colors.blue.600');
  cursor: pointer;
}

.uploadAreaLink:hover {
  color: theme('colors.blue.500');
}

.uploadAreaHint {
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.500');
  margin-top: theme('spacing.1');
}

/* File Preview */
.filePreview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: theme('spacing.3');
  background-color: theme('colors.gray.50');
  border-radius: theme('borderRadius.lg');
}

.filePreviewInfo {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

.filePreviewIcon {
  height: theme('spacing.5');
  width: theme('spacing.5');
  color: theme('colors.gray.500');
}

.filePreviewName {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.900');
}

.filePreviewSize {
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.500');
}

/* Results Section - BulkImport specific result cards */
.resultsContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

.resultsGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: theme('spacing.4');
}

@media (min-width: theme('screens.md')) {
  .resultsGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.resultCard {
  composes: cardBase from '@styles/shared/cards.module.css';
  padding: theme('spacing.4');
}

.resultCardSuccess {
  composes: resultCard;
  background-color: theme('colors.green.50');
}

.resultCardError {
  composes: resultCard;
  background-color: theme('colors.red.50');
}

.resultCardInfo {
  composes: resultCard;
  background-color: theme('colors.blue.50');
}

.resultCardHeader {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.resultCardIcon {
  height: theme('spacing.5');
  width: theme('spacing.5');
}

.resultCardIconSuccess {
  color: theme('colors.green.500');
}

.resultCardIconError {
  color: theme('colors.red.500');
}

.resultCardIconInfo {
  color: theme('colors.blue.500');
}

.resultCardTitle {
  font-weight: theme('fontWeight.medium');
}

.resultCardTitleSuccess {
  color: theme('colors.green.900');
}

.resultCardTitleError {
  color: theme('colors.red.900');
}

.resultCardTitleInfo {
  color: theme('colors.blue.900');
}

.resultCardValue {
  font-size: theme('fontSize.2xl');
  font-weight: theme('fontWeight.bold');
  margin-top: theme('spacing.1');
}

.resultCardValueSuccess {
  color: theme('colors.green.900');
}

.resultCardValueError {
  color: theme('colors.red.900');
}

.resultCardValueInfo {
  color: theme('colors.blue.900');
}

/* Error List - BulkImport specific error styling */
.errorListTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.3');
}

.errorList {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.2');
}

.errorItem {
  composes: errorCard from '@styles/shared/cards.module.css';
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
  padding: theme('spacing.3');
}

.errorItemIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
  color: theme('colors.red.500');
  flex-shrink: 0;
}

.errorItemContent {
  font-size: theme('fontSize.sm');
}

.errorItemRow {
  font-weight: theme('fontWeight.medium');
}

.errorItemMessage {
  color: theme('colors.red.700');
}

.errorItemField {
  color: theme('colors.gray.600');
}

/* Dark mode overrides for BulkImport-specific elements */
:global(.dark) .importTypeButton {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .importTypeButtonActive {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.blue.400');
}

:global(.dark) .importTypeButtonInactive {
  border-color: theme('colors.gray.700');
}

:global(.dark) .importTypeButtonInactive:hover {
  border-color: theme('colors.gray.600');
}

:global(.dark) .importTypeTitle {
  color: white;
}

:global(.dark) .importTypeDescription {
  color: theme('colors.gray.300');
}

:global(.dark) .templateTitle {
  color: white;
}

:global(.dark) .templateDescription {
  color: theme('colors.gray.300');
}

:global(.dark) .uploadAreaWrapper {
  border-color: theme('colors.gray.700');
}

:global(.dark) .uploadAreaWrapper:hover {
  border-color: theme('colors.gray.600');
}

:global(.dark) .uploadAreaActive {
  background-color: rgba(59, 130, 246, 0.2);
}

:global(.dark) .uploadAreaTitle {
  color: white;
}

:global(.dark) .uploadAreaDescription {
  color: theme('colors.gray.300');
}

:global(.dark) .filePreview {
  background-color: theme('colors.gray.800');
}

:global(.dark) .filePreviewName {
  color: white;
}

:global(.dark) .filePreviewSize {
  color: theme('colors.gray.400');
}

:global(.dark) .resultCardSuccess {
  background-color: rgba(34, 197, 94, 0.1);
}

:global(.dark) .resultCardError {
  background-color: rgba(239, 68, 68, 0.1);
}

:global(.dark) .resultCardInfo {
  background-color: rgba(59, 130, 246, 0.1);
}

:global(.dark) .errorListTitle {
  color: white;
}

:global(.dark) .errorItemMessage {
  color: theme('colors.red.300');
}

:global(.dark) .errorItemField {
  color: theme('colors.gray.400');
}