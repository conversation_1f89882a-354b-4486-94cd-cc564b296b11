import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { serviceContainer } from '@/services'

export interface QuestionOption {
  id: string
  text: string
  is_correct: boolean
}

export interface Question {
  id: string
  text: string
  explanation?: string
  options?: QuestionOption[]
  metadata?: {
    example_usage?: string
  }
  question_type: {
    key: string
    name: string
    requires_options: boolean
  }
}

export interface QuizPreviewState {
  currentQuestionIndex: number
  selectedAnswers: Record<string, string>
  showResults: boolean
  showExplanation: boolean
  showCorrectAnswers: boolean
  timeRemaining: number | null
}

export const useQuizPreviewHandler = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  // Quiz simulation state
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, string>>({})
  const [showResults, setShowResults] = useState(false)
  const [showExplanation, setShowExplanation] = useState(false)
  const [showCorrectAnswers, setShowCorrectAnswers] = useState(false)
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null)

  // Fetch quiz data with questions
  const { data: quizResponse, isLoading, error } = useQuery({
    queryKey: ['quiz-preview', id],
    queryFn: () => serviceContainer.quizService.getQuizWithQuestionsById(id!),
    enabled: !!id,
  })

  const quiz = quizResponse?.data
  const questions = quiz?.questions || []

  // Map questions to consistent format
  const mappedQuestions: Question[] = questions.map((q: any) => ({
    id: q.id,
    text: q.question_text || q.text,
    explanation: q.explanation,
    metadata: q.metadata,
    options: (q.question_options || q.options || []).map((opt: any) => ({
      id: opt.id,
      text: opt.option_text || opt.text,
      is_correct: opt.is_correct,
    })),
    question_type: q.question_types || q.question_type || { key: 'multiple_choice', name: 'Multiple Choice', requires_options: true },
  }))

  const mappedCurrentQuestion = mappedQuestions[currentQuestionIndex]

  // Initialize timer when quiz loads
  if (quiz?.time_limit_minutes && timeRemaining === null) {
    setTimeRemaining(quiz.time_limit_minutes * 60) // Convert to seconds
  }

  // Event handlers
  const handleAnswerSelect = (questionId: string, optionId: string) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: optionId,
    }))
  }

  const handleNextQuestion = () => {
    if (currentQuestionIndex < mappedQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
      setShowExplanation(false) // Reset explanation when moving to next question
    }
  }

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1)
      setShowExplanation(false) // Reset explanation when moving to previous question
    }
  }

  const handleFinishQuiz = () => {
    setShowResults(true)
  }

  const handleRestart = () => {
    setCurrentQuestionIndex(0)
    setSelectedAnswers({})
    setShowResults(false)
    setShowExplanation(false)
    setShowCorrectAnswers(false)
    if (quiz?.time_limit_minutes) {
      setTimeRemaining(quiz.time_limit_minutes * 60)
    }
  }

  const toggleCorrectAnswers = () => {
    setShowCorrectAnswers(prev => !prev)
  }

  const toggleExplanation = () => {
    setShowExplanation(prev => !prev)
  }

  const calculateResults = () => {
    let correct = 0
    let total = 0

    mappedQuestions.forEach(question => {
      if (question.options && question.options.length > 0) {
        total++
        const selectedOptionId = selectedAnswers[question.id]
        const selectedOption = question.options.find(opt => opt.id === selectedOptionId)
        if (selectedOption?.is_correct) {
          correct++
        }
      }
    })

    return { correct, total, percentage: total > 0 ? Math.round((correct / total) * 100) : 0 }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const navigateToQuizView = () => {
    navigate(`/quiz/${id}/view`)
  }

  const navigateToQuizManagement = () => {
    navigate('/quiz-management')
  }

  const navigateToQuestions = () => {
    navigate('/questions')
  }

  // Computed values
  const results = showResults ? calculateResults() : null
  const hasQuestions = mappedQuestions.length > 0
  const isFirstQuestion = currentQuestionIndex === 0
  const isLastQuestion = currentQuestionIndex === mappedQuestions.length - 1
  const answeredCount = Object.keys(selectedAnswers).length
  const progressPercentage = hasQuestions ? ((currentQuestionIndex + 1) / mappedQuestions.length) * 100 : 0

  return {
    // Data
    quiz,
    mappedQuestions,
    mappedCurrentQuestion,
    
    // State
    currentQuestionIndex,
    selectedAnswers,
    showResults,
    showExplanation,
    showCorrectAnswers,
    timeRemaining,
    
    // Loading/Error states
    isLoading,
    error,
    quizResponse,
    
    // Computed values
    results,
    hasQuestions,
    isFirstQuestion,
    isLastQuestion,
    answeredCount,
    progressPercentage,
    
    // Actions
    handleAnswerSelect,
    handleNextQuestion,
    handlePreviousQuestion,
    handleFinishQuiz,
    handleRestart,
    toggleCorrectAnswers,
    toggleExplanation,
    navigateToQuizView,
    navigateToQuizManagement,
    navigateToQuestions,
    
    // Utilities
    formatTime,
    calculateResults,
  }
}