/**
 * QuizPreview CSS Module - Refactored with Shared Modules
 * Component-specific styles only, common patterns imported from shared modules
 */

/* QuizPreview-specific styles only */

/* Container - QuizPreview specific full-height layout */
.container {
  min-height: 100vh;
  background-color: theme('colors.gray.50');
}

/* Header Container - QuizPreview specific header layout */
.headerContainer {
  background-color: white;
  border-bottom: 1px solid theme('colors.gray.200');
  box-shadow: theme('boxShadow.sm');
}

.headerContent {
  max-width: theme('maxWidth.4xl');
  margin: 0 auto;
  padding: 0 theme('spacing.4');
}

@media (min-width: theme('screens.sm')) {
  .headerContent {
    padding: 0 theme('spacing.6');
  }
}

@media (min-width: theme('screens.lg')) {
  .headerContent {
    padding: 0 theme('spacing.8');
  }
}

.headerInner {
  padding: theme('spacing.4') 0;
}

.headerTopRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: theme('spacing.4');
}

.headerLeftSection {
  display: flex;
  align-items: center;
}

.headerCloseButton {
  margin-right: theme('spacing.4');
  padding: theme('spacing.2');
  color: theme('colors.gray.400');
  transition: color 0.2s ease;
  cursor: pointer;
}

.headerCloseButton:hover {
  color: theme('colors.gray.600');
}

.headerRightSection {
  text-align: right;
}

.headerProgressText {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
}

.headerAnsweredText {
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.500');
}

/* Question Card - QuizPreview specific question display */
.questionCard {
  composes: cardBase from '@styles/shared/cards.module.css';
  max-width: theme('maxWidth.4xl');
  margin: 0 auto theme('spacing.6') auto;
  padding: theme('spacing.6');
}

.questionHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: theme('spacing.6');
}

.questionNumber {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.blue.600');
}

.questionText {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
  line-height: theme('lineHeight.relaxed');
  margin-bottom: theme('spacing.6');
}

/* Options - QuizPreview specific option styling */
.optionsContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.3');
  margin-bottom: theme('spacing.6');
}

.optionButton {
  display: flex;
  align-items: center;
  padding: theme('spacing.4');
  border: 2px solid theme('colors.gray.200');
  border-radius: theme('borderRadius.lg');
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.optionButton:hover {
  border-color: theme('colors.blue.300');
  background-color: theme('colors.blue.50');
}

.optionButtonSelected {
  border-color: theme('colors.blue.500');
  background-color: theme('colors.blue.50');
}

.optionButtonCorrect {
  border-color: theme('colors.green.500');
  background-color: theme('colors.green.50');
}

.optionButtonIncorrect {
  border-color: theme('colors.red.500');
  background-color: theme('colors.red.50');
}

.optionRadio {
  margin-right: theme('spacing.3');
  height: theme('spacing.5');
  width: theme('spacing.5');
  color: theme('colors.blue.600');
}

.optionText {
  flex: 1;
  font-size: theme('fontSize.base');
  color: theme('colors.gray.900');
  line-height: theme('lineHeight.relaxed');
}

.optionIcon {
  margin-left: theme('spacing.2');
  height: theme('spacing.5');
  width: theme('spacing.5');
}

.optionIconCorrect {
  composes: optionIcon;
  color: theme('colors.green.600');
}

.optionIconIncorrect {
  composes: optionIcon;
  color: theme('colors.red.600');
}

/* Explanation - QuizPreview specific explanation display */
.explanationContainer {
  margin-top: theme('spacing.4');
  padding: theme('spacing.4');
  background-color: theme('colors.blue.50');
  border: 1px solid theme('colors.blue.200');
  border-radius: theme('borderRadius.lg');
}

.explanationTitle {
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.blue.800');
  margin-bottom: theme('spacing.2');
}

.explanationText {
  font-size: theme('fontSize.sm');
  color: theme('colors.blue.800');
  line-height: theme('lineHeight.relaxed');
}

/* Navigation - QuizPreview specific navigation controls */
.navigationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: theme('maxWidth.4xl');
  margin: 0 auto;
  padding: theme('spacing.4');
}

.navigationLeftSection {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

.navigationRightSection {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

/* Quiz Complete - QuizPreview specific completion display */
.completeContainer {
  max-width: theme('maxWidth.2xl');
  margin: 0 auto;
  padding: theme('spacing.8');
  text-align: center;
}

.completeCard {
  composes: cardBase from '@styles/shared/cards.module.css';
  padding: theme('spacing.8');
}

.completeIcon {
  height: theme('spacing.16');
  width: theme('spacing.16');
  color: theme('colors.green.600');
  margin: 0 auto theme('spacing.4') auto;
}

.completeTitle {
  font-size: theme('fontSize.2xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.2');
}

.completeDescription {
  font-size: theme('fontSize.base');
  color: theme('colors.gray.600');
  margin-bottom: theme('spacing.6');
  line-height: theme('lineHeight.relaxed');
}

.completeStats {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: theme('spacing.4');
  margin-bottom: theme('spacing.6');
}

@media (min-width: theme('screens.md')) {
  .completeStats {
    grid-template-columns: repeat(3, 1fr);
  }
}

.completeStatCard {
  padding: theme('spacing.4');
  background-color: theme('colors.gray.50');
  border-radius: theme('borderRadius.lg');
  text-align: center;
}

.completeStatValue {
  font-size: theme('fontSize.2xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.blue.600');
  margin-bottom: theme('spacing.1');
}

.completeStatLabel {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  text-transform: uppercase;
  letter-spacing: theme('letterSpacing.wide');
}

.completeActions {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.3');
}

@media (min-width: theme('screens.sm')) {
  .completeActions {
    flex-direction: row;
    justify-content: center;
  }
}

/* Admin Controls - QuizPreview specific admin functionality */
.adminControls {
  position: fixed;
  top: theme('spacing.4');
  right: theme('spacing.4');
  display: flex;
  flex-direction: column;
  gap: theme('spacing.2');
  z-index: 50;
}

.adminControlButton {
  composes: buttonBase from '@styles/shared/buttons.module.css';
  background-color: theme('colors.white');
  border: 1px solid theme('colors.gray.300');
  color: theme('colors.gray.700');
  box-shadow: theme('boxShadow.md');
}

.adminControlButton:hover {
  background-color: theme('colors.gray.50');
  border-color: theme('colors.gray.400');
}

/* Timer Display - QuizPreview specific timer functionality */
.timerContainer {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  padding: theme('spacing.2') theme('spacing.3');
  background-color: theme('colors.amber.50');
  border: 1px solid theme('colors.amber.200');
  border-radius: theme('borderRadius.md');
}

.timerIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
  color: theme('colors.amber.600');
}

.timerText {
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.amber.800');
}

.timerWarning {
  composes: timerContainer;
  background-color: theme('colors.red.50');
  border-color: theme('colors.red.200');
}

.timerWarning .timerIcon {
  color: theme('colors.red.600');
}

.timerWarning .timerText {
  color: theme('colors.red.800');
}

/* Question Markers - QuizPreview specific question navigation */
.questionMarkers {
  display: flex;
  flex-wrap: wrap;
  gap: theme('spacing.2');
  margin-bottom: theme('spacing.4');
}

.questionMarker {
  display: flex;
  align-items: center;
  justify-content: center;
  width: theme('spacing.8');
  height: theme('spacing.8');
  border-radius: theme('borderRadius.full');
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  cursor: pointer;
  transition: all 0.2s ease;
}

.questionMarkerUnanswered {
  composes: questionMarker;
  background-color: theme('colors.gray.200');
  color: theme('colors.gray.600');
}

.questionMarkerAnswered {
  composes: questionMarker;
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

.questionMarkerCurrent {
  composes: questionMarker;
  background-color: theme('colors.blue.600');
  color: white;
}

.questionMarkerCorrect {
  composes: questionMarker;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.questionMarkerIncorrect {
  composes: questionMarker;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

/* Responsive Adjustments - QuizPreview specific responsive behavior */
@media (max-width: theme('screens.sm')) {
  .headerTopRow {
    flex-direction: column;
    align-items: flex-start;
    gap: theme('spacing.3');
  }
  
  .headerRightSection {
    text-align: left;
  }
  
  .questionCard {
    margin: 0 theme('spacing.4') theme('spacing.6') theme('spacing.4');
    padding: theme('spacing.4');
  }
  
  .navigationContainer {
    flex-direction: column;
    gap: theme('spacing.4');
  }
  
  .navigationLeftSection,
  .navigationRightSection {
    justify-content: center;
  }
  
  .adminControls {
    position: static;
    flex-direction: row;
    margin: theme('spacing.4');
  }
}

/* Dark mode overrides for QuizPreview-specific elements */
:global(.dark) .container {
  background-color: theme('colors.gray.900');
}

:global(.dark) .headerContainer {
  background-color: theme('colors.gray.800');
  border-bottom-color: theme('colors.gray.700');
}

:global(.dark) .headerProgressText {
  color: theme('colors.gray.300');
}

:global(.dark) .headerAnsweredText {
  color: theme('colors.gray.400');
}

:global(.dark) .questionText {
  color: white;
}

:global(.dark) .optionButton {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.600');
}

:global(.dark) .optionButton:hover {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.blue.400');
}

:global(.dark) .optionButtonSelected {
  background-color: theme('colors.blue.900');
  border-color: theme('colors.blue.400');
}

:global(.dark) .optionButtonCorrect {
  background-color: theme('colors.green.900');
  border-color: theme('colors.green.400');
}

:global(.dark) .optionButtonIncorrect {
  background-color: theme('colors.red.900');
  border-color: theme('colors.red.400');
}

:global(.dark) .optionText {
  color: theme('colors.gray.100');
}

:global(.dark) .explanationContainer {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: theme('colors.blue.700');
}

:global(.dark) .explanationTitle,
:global(.dark) .explanationText {
  color: theme('colors.blue.300');
}

:global(.dark) .completeTitle {
  color: white;
}

:global(.dark) .completeDescription {
  color: theme('colors.gray.300');
}

:global(.dark) .completeStatCard {
  background-color: theme('colors.gray.800');
}

:global(.dark) .completeStatValue {
  color: theme('colors.blue.400');
}

:global(.dark) .completeStatLabel {
  color: theme('colors.gray.400');
}

:global(.dark) .adminControlButton {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.300');
}

:global(.dark) .adminControlButton:hover {
  background-color: theme('colors.gray.600');
  border-color: theme('colors.gray.500');
}

:global(.dark) .timerContainer {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: theme('colors.amber.700');
}

:global(.dark) .timerText {
  color: theme('colors.amber.300');
}

:global(.dark) .timerIcon {
  color: theme('colors.amber.400');
}

:global(.dark) .timerWarning {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: theme('colors.red.700');
}

:global(.dark) .timerWarning .timerText {
  color: theme('colors.red.300');
}

:global(.dark) .timerWarning .timerIcon {
  color: theme('colors.red.400');
}

:global(.dark) .questionMarkerUnanswered {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.400');
}

:global(.dark) .questionMarkerAnswered {
  background-color: rgba(59, 130, 246, 0.2);
  color: theme('colors.blue.300');
}

:global(.dark) .questionMarkerCorrect {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.300');
}

:global(.dark) .questionMarkerIncorrect {
  background-color: rgba(239, 68, 68, 0.2);
  color: theme('colors.red.300');
}