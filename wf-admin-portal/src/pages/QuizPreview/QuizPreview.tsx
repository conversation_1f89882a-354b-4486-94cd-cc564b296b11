import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, CheckCircle, XCircle, BookOpen, X, HelpCircle } from 'lucide-react'
import { useQuizPreviewHandler } from './QuizPreview.handler'
import styles from './QuizPreview.module.css'
import { sharedStyles } from '../../styles/shared'

export default function QuizPreview() {
  const {
    quiz,
    mappedQuestions,
    mappedCurrentQuestion,
    currentQuestionIndex,
    selectedAnswers,
    showResults,
    showExplanation,
    showCorrectAnswers,
    isLoading,
    error,
    quizResponse,
    results,
    hasQuestions,
    isFirstQuestion,
    isLastQuestion,
    answeredCount,
    progressPercentage,
    handleAnswerSelect,
    handleNextQuestion,
    handlePreviousQuestion,
    handleFinishQuiz,
    handleRestart,
    toggleCorrectAnswers,
    toggleExplanation,
    navigateToQuizView,
    navigateToQuizManagement,
    navigateToQuestions,
  } = useQuizPreviewHandler()

  if (isLoading) {
    return (
      <div className={sharedStyles.states.loadingContainer}>
        <div className={sharedStyles.states.loadingContent}>
          <div className={sharedStyles.states.loadingSpinner}></div>
          <p className={sharedStyles.states.loadingText}>Loading quiz preview...</p>
        </div>
      </div>
    )
  }

  if (error || !quiz) {
    return (
      <div className={sharedStyles.layouts.container}>
        <Card className={sharedStyles.cards.cardBase}>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.states.errorContainer}>
              <XCircle className={sharedStyles.states.errorIcon} />
              <h2 className={sharedStyles.states.errorTitle}>Quiz Not Found</h2>
              <p className={sharedStyles.states.errorDescription}>
                {quizResponse?.error || 'The quiz you are trying to preview does not exist.'}
              </p>
              <Button className={sharedStyles.buttons.buttonOutline} onClick={navigateToQuizManagement}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Quiz Management
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!hasQuestions) {
    return (
      <div className={sharedStyles.layouts.container}>
        <Card className={sharedStyles.cards.cardBase}>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.states.emptyStateWrapper}>
              <BookOpen className={sharedStyles.states.emptyStateIcon} />
              <h2 className={sharedStyles.states.emptyStateTitle}>No Questions Available</h2>
              <p className={sharedStyles.states.emptyStateDescription}>
                This quiz doesn't have any questions yet. Add questions to preview the quiz.
              </p>
              <div className={sharedStyles.layouts.buttonContainer}>
                <Button className={sharedStyles.buttons.buttonOutline} onClick={navigateToQuizView}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Quiz
                </Button>
                <Button className={sharedStyles.buttons.buttonPrimary} onClick={navigateToQuestions}>
                  <BookOpen className="h-4 w-4 mr-2" />
                  Manage Questions
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      {/* Quiz Header with Progress */}
      <div className={styles.headerContainer}>
        <div className={styles.headerContent}>
          <div className={styles.headerInner}>
            <div className={styles.headerTopRow}>
              <div className={styles.headerLeftSection}>
                <button
                  onClick={navigateToQuizView}
                  className={styles.headerCloseButton}
                >
                  <X className="h-6 w-6" />
                </button>
                <div>
                  <h1 className={sharedStyles.layouts.headerTitle}>{quiz?.title || 'Quiz Preview'}</h1>
                  <p className={sharedStyles.layouts.headerSubtitle}>
                    🎯 Preview Mode
                  </p>
                </div>
              </div>
              <div className={styles.headerRightSection}>
                <p className={styles.headerProgressText}>
                  Question {currentQuestionIndex + 1} of {mappedQuestions.length}
                </p>
                <p className={styles.headerAnsweredText}>
                  {answeredCount} answered
                </p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className={sharedStyles.states.progressBarContainer}>
              <div
                className={sharedStyles.states.progressBarFill}
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {!showResults ? (
        <div className={sharedStyles.layouts.container}>
          {hasQuestions && (
            <div className={styles.questionCard}>
              <div className={styles.questionHeader}>
                <h2 className={styles.questionText}>
                  {mappedCurrentQuestion?.text}
                </h2>
                {mappedCurrentQuestion?.metadata?.example_usage && (
                  <div className={sharedStyles.cards.cardContent}>
                    <p className={sharedStyles.forms.fieldHint}>
                      <strong>Example:</strong> "{mappedCurrentQuestion.metadata.example_usage}"
                    </p>
                  </div>
                )}
              </div>

              <div className={styles.optionsContainer}>
                {(mappedCurrentQuestion?.options || []).map((option) => {
                  const isSelected = selectedAnswers[mappedCurrentQuestion.id] === option.id
                  const isCorrectOption = option.is_correct
                  const showAnswerFeedback = showCorrectAnswers

                  let optionClass = styles.optionButton
                  if (showAnswerFeedback) {
                    if (isCorrectOption) {
                      optionClass = `${styles.optionButton} ${styles.optionButtonCorrect}`
                    } else if (isSelected && !isCorrectOption) {
                      optionClass = `${styles.optionButton} ${styles.optionButtonIncorrect}`
                    }
                  } else if (isSelected) {
                    optionClass = `${styles.optionButton} ${styles.optionButtonSelected}`
                  }

                  return (
                    <button
                      key={option.id}
                      onClick={() => handleAnswerSelect(mappedCurrentQuestion.id, option.id)}
                      className={optionClass}
                    >
                      <div className="flex items-center">
                        <input
                          type="radio"
                          checked={isSelected}
                          onChange={() => {}}
                          className={styles.optionRadio}
                        />
                        <span className={styles.optionText}>{option.text}</span>
                        {showAnswerFeedback && isCorrectOption && (
                          <CheckCircle className={styles.optionIconCorrect} />
                        )}
                        {showAnswerFeedback && isSelected && !isCorrectOption && (
                          <X className={styles.optionIconIncorrect} />
                        )}
                      </div>
                    </button>
                  )
                })}
              </div>

              {/* Explanation Section */}
              {showExplanation && mappedCurrentQuestion?.explanation && (
                <div className={styles.explanationContainer}>
                  <h4 className={styles.explanationTitle}>Explanation</h4>
                  <div className={styles.explanationText}>
                    {mappedCurrentQuestion.explanation}
                  </div>
                </div>
              )}

              {/* Admin Controls */}
              <div className={styles.adminControls}>
                <button
                  onClick={toggleCorrectAnswers}
                  className={styles.adminControlButton}
                >
                  {showCorrectAnswers ? 'Hide' : 'Show'} Answers
                </button>

                {mappedCurrentQuestion?.explanation && (
                  <button
                    onClick={toggleExplanation}
                    className={styles.adminControlButton}
                  >
                    {showExplanation ? 'Hide' : 'Show'} Explanation
                  </button>
                )}

                <button
                  onClick={handleRestart}
                  className={styles.adminControlButton}
                >
                  Restart Quiz
                </button>
              </div>

              {/* Navigation */}
              <div className={styles.navigationContainer}>
                <button
                  onClick={handlePreviousQuestion}
                  disabled={isFirstQuestion}
                  className={sharedStyles.buttons.buttonOutline}
                >
                  Previous
                </button>

                <div className={styles.navigationRightSection}>
                  {isLastQuestion ? (
                    <button
                      onClick={handleFinishQuiz}
                      className={sharedStyles.buttons.buttonPrimary}
                    >
                      Finish Preview
                    </button>
                  ) : (
                    <button
                      onClick={handleNextQuestion}
                      className={sharedStyles.buttons.buttonPrimary}
                    >
                      Next
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      ) : (
        /* Results Screen */
        <div className={styles.completeContainer}>
          <div className={styles.completeCard}>
            <h2 className={styles.completeTitle}>Quiz Preview Complete</h2>

            <div className={styles.completeStats}>
              <div className={styles.completeStatValue}>
                {results?.percentage}%
              </div>
              <div className={styles.completeDescription}>
                {results?.correct} out of {results?.total} questions correct
              </div>
              <div className={styles.completeActions}>
                {results && results.percentage >= 70 ? (
                  <div className={sharedStyles.states.successMessage}>
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Excellent! This quiz is working well.
                  </div>
                ) : (
                  <div className={sharedStyles.states.warningMessage}>
                    <HelpCircle className="h-5 w-5 mr-2" />
                    Preview complete - review questions as needed.
                  </div>
                )}
              </div>
            </div>

            <div className={styles.completeActions}>
              <button
                onClick={handleRestart}
                className={sharedStyles.buttons.buttonPrimary}
              >
                Preview Again
              </button>
              <button
                onClick={navigateToQuizView}
                className={sharedStyles.buttons.buttonOutline}
              >
                Back to Quiz
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}