import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { serviceContainer } from '@/services'
import type { UserWithAdminRelations } from 'wf-shared/types'

export interface UserEditModalHandlerProps {
  user: UserWithAdminRelations
  onSave: (updates: Partial<UserWithAdminRelations>) => void
  onClose: () => void
  isLoading: boolean
}

export interface UserEditFormData {
  first_name: string
  last_name: string
  email: string
  level_id: string
  role: string
}

export const useUserEditModalHandler = ({ user, onSave, onClose, isLoading }: UserEditModalHandlerProps) => {
  const [formData, setFormData] = useState<UserEditFormData>({
    first_name: user.first_name || '',
    last_name: user.last_name || '',
    email: user.email || '',
    level_id: user.level_id || '',
    role: user.role || 'user',
  })

  const { data: levels } = useQuery({
    queryKey: ['levels'],
    queryFn: () => serviceContainer.levelService.getLevels(),
  })

  const handleInputChange = (field: keyof UserEditFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Only send changed fields
    const updates: Partial<UserWithAdminRelations> = {}
    if (formData.first_name !== user.first_name) updates.first_name = formData.first_name
    if (formData.last_name !== user.last_name) updates.last_name = formData.last_name
    if (formData.email !== user.email) updates.email = formData.email
    if (formData.level_id !== user.level_id) updates.level_id = formData.level_id

    if (Object.keys(updates).length > 0) {
      onSave(updates)
    } else {
      onClose()
    }
  }

  return {
    formData,
    levels: levels?.data || [],
    isLoading,
    handleInputChange,
    handleSubmit,
    onClose,
  }
}