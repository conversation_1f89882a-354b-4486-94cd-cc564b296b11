/* UserEditModal Styles - Utilizing shared modal patterns */
@import '@styles/shared/modal.module.css';
@import '@styles/shared/forms.module.css';
@import '@styles/shared/buttons.module.css';

/* Modal overlay - using shared pattern */
.overlay {
  composes: modalOverlay from '@styles/shared/modal.module.css';
}

/* Modal container - using shared pattern */
.modal {
  composes: modalContainer from '@styles/shared/modal.module.css';
  max-width: 28rem; /* max-w-md */
}

/* Modal header - using shared pattern */
.header {
  composes: modalHeader from '@styles/shared/modal.module.css';
}

.title {
  composes: modalTitle from '@styles/shared/modal.module.css';
}

.closeButton {
  composes: modalCloseButton from '@styles/shared/modal.module.css';
}

/* Form styles - using shared patterns */
.form {
  composes: formContainer from '@styles/shared/forms.module.css';
}

.fieldGroup {
  composes: formField from '@styles/shared/forms.module.css';
}

.label {
  composes: fieldLabel from '@styles/shared/forms.module.css';
}

.input {
  composes: fieldInput from '@styles/shared/forms.module.css';
}

.select {
  composes: fieldSelect from '@styles/shared/forms.module.css';
}

/* Form actions - using shared pattern */
.actions {
  composes: formActions from '@styles/shared/forms.module.css';
}

.cancelButton {
  composes: buttonSecondary from '@styles/shared/buttons.module.css';
}

.saveButton {
  composes: buttonPrimary from '@styles/shared/buttons.module.css';
}

.saveButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}