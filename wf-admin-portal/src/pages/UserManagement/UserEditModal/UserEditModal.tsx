import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'
import { useUserEditModalHandler, type UserEditModalHandlerProps } from './UserEditModal.handler'
import styles from './UserEditModal.module.css'

export default function UserEditModal(props: UserEditModalHandlerProps) {
  const {
    formData,
    levels,
    isLoading,
    handleInputChange,
    handleSubmit,
    onClose,
  } = useUserEditModalHandler(props)

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2 className={styles.title}>Edit User</h2>
          <Button variant="ghost" size="sm" onClick={onClose} className={styles.closeButton}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.fieldGroup}>
            <label className={styles.label}>
              First Name
            </label>
            <input
              type="text"
              value={formData.first_name}
              onChange={(e) => handleInputChange('first_name', e.target.value)}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.fieldGroup}>
            <label className={styles.label}>
              Last Name
            </label>
            <input
              type="text"
              value={formData.last_name}
              onChange={(e) => handleInputChange('last_name', e.target.value)}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.fieldGroup}>
            <label className={styles.label}>
              Email
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.fieldGroup}>
            <label className={styles.label}>
              Level
            </label>
            <select
              value={formData.level_id}
              onChange={(e) => handleInputChange('level_id', e.target.value)}
              className={styles.select}
              required
            >
              <option value="">Select Level</option>
              {levels.map((level: { id: string; name: string; key: string }) => (
                <option key={level.id} value={level.id}>
                  {level.name} ({level.key})
                </option>
              ))}
            </select>
          </div>

          <div className={styles.fieldGroup}>
            <label className={styles.label}>
              Role
            </label>
            <select
              value={formData.role}
              onChange={(e) => handleInputChange('role', e.target.value)}
              className={styles.select}
              required
            >
              <option value="user">User</option>
              <option value="admin">Admin</option>
            </select>
          </div>

          <div className={styles.actions}>
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose}
              className={styles.cancelButton}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading}
              className={styles.saveButton}
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}