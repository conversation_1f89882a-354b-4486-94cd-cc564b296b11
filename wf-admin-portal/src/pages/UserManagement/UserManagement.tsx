import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { formatDate } from 'wf-shared/utils'
import { Users, Eye, Edit, Trash2, UserPlus } from 'lucide-react'
import { useUserManagementHandler } from './UserManagement.handler'
import styles from './UserManagement.module.css'
import { sharedStyles } from '../../styles/shared'
import UserDetailsModal from './UserDetailsModal'
import UserEditModal from './UserEditModal'
import { Pagination } from '@/components/ui/pagination'

export default function UserManagement() {
  const {
    users,
    selectedUser,
    page,
    isLoading,
    error,
    showUserDetails,
    showEditModal,
    isUpdating,
    isDeleting,
    handlePageChange,
    handleViewUser,
    handleEditUser,
    handleUpdateUser,
    handleDeleteUser,
    handleCloseModals,
  } = useUserManagementHandler()

  if (isLoading) {
    return (
      <div className={sharedStyles.layouts.container}>
        <div className={sharedStyles.layouts.header}>
          <h1 className={sharedStyles.layouts.headerTitle}>User Management</h1>
        </div>
        <Card className={sharedStyles.cards.cardBase}>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.states.loadingContainer}>
              {[...Array(5)].map((_, i) => (
                <div key={i} className={sharedStyles.states.loadingSkeletonItem}></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className={sharedStyles.layouts.container}>
        <div className={sharedStyles.layouts.header}>
          <h1 className={sharedStyles.layouts.headerTitle}>User Management</h1>
        </div>
        <Card className={sharedStyles.cards.cardBase}>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.states.errorContainer}>
              <div className={sharedStyles.states.errorIconWrapper}>
                <Users className={sharedStyles.states.errorIcon} />
              </div>
              <h3 className={sharedStyles.states.errorTitle}>Error Loading Users</h3>
              <p className={sharedStyles.states.errorDescription}>
                {error instanceof Error ? error.message : 'An unexpected error occurred'}
              </p>
              <Button className={sharedStyles.buttons.buttonOutline} onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={sharedStyles.layouts.container}>
      <div className={sharedStyles.layouts.header}>
        <div>
          <h1 className={sharedStyles.layouts.headerTitle}>User Management</h1>
          <p className={sharedStyles.layouts.headerSubtitle}>
            Manage system users and their access levels
          </p>
        </div>
        <Button className={sharedStyles.buttons.buttonPrimary}>
          <UserPlus className="h-4 w-4 mr-2" />
          Add User
        </Button>
      </div>

      {/* Stats */}
      <div className={sharedStyles.layouts.statsGrid}>
        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              Total Users
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.cards.statCardValue}>{users?.count || 0}</div>
          </CardContent>
        </Card>

        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              Active Users
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCardChangePositive}>
              {users?.data?.length || 0}
            </div>
          </CardContent>
        </Card>

        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              New This Month
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCardChangePositive}>
              {users?.data?.filter((user) => {
                const oneMonthAgo = new Date()
                oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
                return new Date(user.created_at || '') > oneMonthAgo
              }).length || 0}
            </div>
          </CardContent>
        </Card>

        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              User Levels
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCardChangePositive}>
              {Array.from(new Set(users?.data?.map((user) => user.levels?.key))).filter(Boolean).length || 0}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Users List */}
      <Card className={sharedStyles.cards.cardBase}>
        <CardHeader className={sharedStyles.cards.cardHeader}>
          <CardTitle>Users ({users?.count || 0})</CardTitle>
        </CardHeader>
        <CardContent className={sharedStyles.cards.cardContent}>
          {users?.data && users.data.length > 0 ? (
            <div className={styles.userListContainer}>
              {users.data.map((user) => (
                <div key={user.id} className={styles.userItem}>
                  <div className={styles.userItemHeader}>
                    <div className={styles.userContent}>
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-medium">
                            {user.first_name?.[0]}{user.last_name?.[0]}
                          </span>
                        </div>
                        <div>
                          <h3 className={styles.userName}>
                            {user.first_name} {user.last_name}
                          </h3>
                          <p className={styles.userEmail}>{user.email}</p>
                        </div>
                      </div>
                      <div className={styles.userMetadata}>
                        <span className={styles.badgeLevel}>
                          {user.levels?.name || 'No Level'}
                        </span>
                        <span>
                          Joined {formatDate(user.created_at || '')}
                        </span>
                      </div>
                    </div>
                    
                    <div className={styles.userActions}>
                      <Button 
                        className={sharedStyles.buttons.buttonGhost}
                        onClick={() => handleViewUser(user.id)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button 
                        className={sharedStyles.buttons.buttonGhost}
                        onClick={() => handleEditUser(user)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        className={sharedStyles.buttons.buttonGhost}
                        onClick={() => handleDeleteUser(user.id)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className={sharedStyles.states.emptyStateWrapper}>
              <Users className={sharedStyles.states.emptyStateIcon} />
              <h3 className={sharedStyles.states.emptyStateTitle}>No Users Found</h3>
              <p className={sharedStyles.states.emptyStateDescription}>
                No users are currently registered in the system
              </p>
            </div>
          )}

          {/* Pagination */}
          <Pagination
            currentPage={page}
            totalPages={users?.totalPages || 0}
            onPageChange={handlePageChange}
          />
        </CardContent>
      </Card>

      {/* Modals */}
      {showUserDetails && selectedUser && (
        <UserDetailsModal 
          user={selectedUser} 
          onClose={handleCloseModals}
        />
      )}

      {showEditModal && selectedUser && (
        <UserEditModal 
          user={selectedUser} 
          onSave={handleUpdateUser}
          onClose={handleCloseModals}
          isLoading={isUpdating}
        />
      )}
    </div>
  )
}