/* UserDetailsModal Styles - Utilizing shared modal patterns */
@import '@styles/shared/modal.module.css';

/* Modal overlay - using shared pattern */
.overlay {
  composes: modalOverlay from '@styles/shared/modal.module.css';
}

/* Modal container - larger for details view */
.modal {
  composes: modalContainer from '@styles/shared/modal.module.css';
  max-width: 56rem; /* max-w-4xl */
  width: 100%;
  margin: 1rem;
  max-height: 90vh;
  overflow-y: auto;
}

/* Modal header - using shared pattern */
.header {
  composes: modalHeader from '@styles/shared/modal.module.css';
}

.title {
  composes: modalTitle from '@styles/shared/modal.module.css';
  font-size: 1.5rem; /* text-2xl */
}

.closeButton {
  composes: modalCloseButton from '@styles/shared/modal.module.css';
}

/* Content layout */
.content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 1024px) {
  .content {
    grid-template-columns: 1fr 1fr;
  }
}

/* Card styles */
.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.cardHeader {
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.cardTitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.cardContent {
  padding: 1.5rem;
}

/* Information display */
.infoGrid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.infoItem {
  display: flex;
  flex-direction: column;
}

.infoLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.infoValue {
  color: #111827;
  font-weight: 400;
}

/* Quiz activity styles */
.statsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.statCard {
  text-align: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.statCard.blue {
  background-color: #eff6ff;
}

.statCard.green {
  background-color: #f0fdf4;
}

.statNumber {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.statNumber.blue {
  color: #2563eb;
}

.statNumber.green {
  color: #16a34a;
}

.statLabel {
  font-size: 0.875rem;
}

.statLabel.blue {
  color: #1e40af;
}

.statLabel.green {
  color: #15803d;
}

/* Attempts list */
.attemptsSection {
  margin-top: 1rem;
}

.sectionTitle {
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.5rem;
}

.attemptsList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.attemptItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #f9fafb;
  border-radius: 0.25rem;
}

.attemptQuiz {
  font-size: 0.875rem;
  color: #6b7280;
}

.attemptDetails {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.attemptScore {
  font-size: 0.875rem;
  font-weight: 500;
}

.attemptDate {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Progress grid */
.progressGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .progressGrid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .progressGrid {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

.progressCard {
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}

.progressTitle {
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.5rem;
}

.progressStats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.progressStat {
  display: flex;
  justify-content: space-between;
}

.progressLabel {
  color: #6b7280;
}

.progressValue {
  font-weight: 500;
}

/* Empty states */
.emptyState {
  text-align: center;
  padding: 2rem 0;
}

.emptyIcon {
  width: 3rem;
  height: 3rem;
  color: #9ca3af;
  margin: 0 auto 0.5rem;
}

.emptyText {
  color: #6b7280;
}

/* Full width card */
.fullWidthCard {
  grid-column: 1 / -1;
}

/* Actions */
.actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.closeButton {
  composes: modalCloseButton from '@styles/shared/modal.module.css';
}