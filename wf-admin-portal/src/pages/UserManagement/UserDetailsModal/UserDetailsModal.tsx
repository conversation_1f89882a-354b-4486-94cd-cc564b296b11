import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { X, User, Trophy, BookOpen } from 'lucide-react'
import { useUserDetailsModalHandler, type UserDetailsModalHandlerProps } from './UserDetailsModal.handler'
import styles from './UserDetailsModal.module.css'

export default function UserDetailsModal(props: UserDetailsModalHandlerProps) {
  const {
    userInfo,
    quizStats,
    progressData,
    formatDate,
    onClose,
  } = useUserDetailsModalHandler(props)

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2 className={styles.title}>User Details</h2>
          <Button variant="ghost" size="sm" onClick={onClose} className={styles.closeButton}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className={styles.content}>
          {/* Basic Information */}
          <Card className={styles.card}>
            <CardHeader className={styles.cardHeader}>
              <CardTitle className={styles.cardTitle}>
                <User className="h-5 w-5" />
                <span>Basic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className={styles.cardContent}>
              <div className={styles.infoGrid}>
                <div className={styles.infoItem}>
                  <label className={styles.infoLabel}>Full Name</label>
                  <p className={styles.infoValue}>{userInfo.fullName}</p>
                </div>
                <div className={styles.infoItem}>
                  <label className={styles.infoLabel}>Email</label>
                  <p className={styles.infoValue}>{userInfo.email}</p>
                </div>
                <div className={styles.infoItem}>
                  <label className={styles.infoLabel}>Level</label>
                  <p className={styles.infoValue}>{userInfo.level}</p>
                </div>
                <div className={styles.infoItem}>
                  <label className={styles.infoLabel}>Joined</label>
                  <p className={styles.infoValue}>{userInfo.joinedDate}</p>
                </div>
                <div className={styles.infoItem}>
                  <label className={styles.infoLabel}>Last Updated</label>
                  <p className={styles.infoValue}>{userInfo.lastUpdated}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quiz Activity */}
          <Card className={styles.card}>
            <CardHeader className={styles.cardHeader}>
              <CardTitle className={styles.cardTitle}>
                <BookOpen className="h-5 w-5" />
                <span>Quiz Activity</span>
              </CardTitle>
            </CardHeader>
            <CardContent className={styles.cardContent}>
              {quizStats.totalAttempts > 0 ? (
                <div>
                  <div className={styles.statsGrid}>
                    <div className={`${styles.statCard} ${styles.blue}`}>
                      <div className={`${styles.statNumber} ${styles.blue}`}>
                        {quizStats.totalAttempts}
                      </div>
                      <div className={`${styles.statLabel} ${styles.blue}`}>Total Attempts</div>
                    </div>
                    <div className={`${styles.statCard} ${styles.green}`}>
                      <div className={`${styles.statNumber} ${styles.green}`}>
                        {quizStats.averageScore}%
                      </div>
                      <div className={`${styles.statLabel} ${styles.green}`}>Average Score</div>
                    </div>
                  </div>
                  
                  <div className={styles.attemptsSection}>
                    <h4 className={styles.sectionTitle}>Recent Attempts</h4>
                    <div className={styles.attemptsList}>
                      {quizStats.recentAttempts.map((attempt, index: number) => (
                        <div key={attempt.id || index} className={styles.attemptItem}>
                          <span className={styles.attemptQuiz}>
                            Quiz ID: {attempt.quiz_id || 'Unknown'}
                          </span>
                          <div className={styles.attemptDetails}>
                            <span className={styles.attemptScore}>{attempt.score}%</span>
                            <span className={styles.attemptDate}>
                              {attempt.completed_at ? formatDate(attempt.completed_at) : 'N/A'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className={styles.emptyState}>
                  <BookOpen className={styles.emptyIcon} />
                  <p className={styles.emptyText}>No quiz attempts yet</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Progress Information */}
          <Card className={`${styles.card} ${styles.fullWidthCard}`}>
            <CardHeader className={styles.cardHeader}>
              <CardTitle className={styles.cardTitle}>
                <Trophy className="h-5 w-5" />
                <span>Learning Progress</span>
              </CardTitle>
            </CardHeader>
            <CardContent className={styles.cardContent}>
              {progressData.length > 0 ? (
                <div className={styles.progressGrid}>
                  {progressData.map((progress) => (
                    <div key={progress.id} className={styles.progressCard}>
                      <h4 className={styles.progressTitle}>
                        Category ID: {progress.categoryId}
                      </h4>
                      <div className={styles.progressStats}>
                        <div className={styles.progressStat}>
                          <span className={styles.progressLabel}>Best Score:</span>
                          <span className={styles.progressValue}>{progress.bestScore}%</span>
                        </div>
                        <div className={styles.progressStat}>
                          <span className={styles.progressLabel}>Average:</span>
                          <span className={styles.progressValue}>{progress.averageScore}%</span>
                        </div>
                        <div className={styles.progressStat}>
                          <span className={styles.progressLabel}>Attempts:</span>
                          <span className={styles.progressValue}>{progress.totalAttempts}</span>
                        </div>
                        <div className={styles.progressStat}>
                          <span className={styles.progressLabel}>Streak:</span>
                          <span className={styles.progressValue}>{progress.currentStreak}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className={styles.emptyState}>
                  <Trophy className={styles.emptyIcon} />
                  <p className={styles.emptyText}>No progress data available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className={styles.actions}>
          <Button onClick={onClose}>Close</Button>
        </div>
      </div>
    </div>
  )
}