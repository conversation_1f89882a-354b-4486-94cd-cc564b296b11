import { formatDate } from 'wf-shared/utils'
import type { UserWithAdminRelations } from 'wf-shared/types'

export interface UserDetailsModalHandlerProps {
  user: UserWithAdminRelations
  onClose: () => void
}

export const useUserDetailsModalHandler = ({ user, onClose }: UserDetailsModalHandlerProps) => {
  // Calculate quiz statistics
  const calculateQuizStats = () => {
    if (!user.quiz_attempts || user.quiz_attempts.length === 0) {
      return {
        totalAttempts: 0,
        averageScore: 0,
        recentAttempts: [],
      }
    }

    const totalAttempts = user.quiz_attempts.length
    const averageScore = Math.round(
      user.quiz_attempts.reduce((sum: number, attempt: { score: number }) => sum + attempt.score, 0) / totalAttempts,
    )
    const recentAttempts = user.quiz_attempts.slice(0, 5)

    return {
      totalAttempts,
      averageScore,
      recentAttempts,
    }
  }

  // Format user information
  const getUserInfo = () => ({
    fullName: `${user.first_name} ${user.last_name}`,
    email: user.email,
    level: user.levels?.name || 'No Level Assigned',
    joinedDate: user.created_at ? formatDate(user.created_at) : 'Unknown',
    lastUpdated: (user.updated_at || user.created_at) 
      ? formatDate(user.updated_at || user.created_at!) 
      : 'Unknown',
  })

  // Process progress data
  const getProgressData = () => {
    if (!user.user_progress || user.user_progress.length === 0) {
      return []
    }

    return user.user_progress.map((progress, index: number) => ({
      id: progress.id || index,
      categoryId: progress.category_id,
      bestScore: progress.best_score || 0,
      averageScore: progress.average_score || 0,
      totalAttempts: progress.total_attempts || 0,
      currentStreak: progress.current_streak || 0,
    }))
  }

  const quizStats = calculateQuizStats()
  const userInfo = getUserInfo()
  const progressData = getProgressData()

  return {
    user,
    userInfo,
    quizStats,
    progressData,
    formatDate,
    onClose,
  }
}