/**
 * UserManagement CSS Module - Refactored with Shared Modules
 * Component-specific styles only, common patterns imported from shared modules
 */

/* UserManagement-specific styles only */

/* User List - UserManagement specific user item styling */
.userListContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

.userItem {
  composes: cardHover from '@styles/shared/cards.module.css';
  padding: theme('spacing.4');
}

.userItemHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.userContent {
  flex: 1;
}

.userName {
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
}

.userEmail {
  color: theme('colors.gray.600');
  font-size: theme('fontSize.sm');
}

.userMetadata {
  display: flex;
  align-items: center;
  gap: theme('spacing.4');
  margin-top: theme('spacing.2');
}

.userActions {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

/* Badges - UserManagement specific badge variants */
.badgeLevel {
  composes: badgeBase from '@styles/shared/badges.module.css';
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

/* Stats Cards - UserManagement specific stat styling */
.statCardChangePositive {
  composes: statCardValue from '@styles/shared/cards.module.css';
  color: theme('colors.green.600');
}

.statCardChangeNegative {
  composes: statCardValue from '@styles/shared/cards.module.css';
  color: theme('colors.red.600');
}

/* Pagination - UserManagement specific pagination layout */
.paginationWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: theme('spacing.6');
  padding-top: theme('spacing.4');
  border-top: 1px solid theme('colors.gray.200');
}

.paginationInfo {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.700');
}

.paginationControls {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.paginationButton {
  composes: buttonSecondary from '@styles/shared/buttons.module.css';
  padding: theme('spacing.2') theme('spacing.3');
  font-size: theme('fontSize.sm');
}

.paginationButtonActive {
  composes: buttonPrimary from '@styles/shared/buttons.module.css';
  padding: theme('spacing.2') theme('spacing.3');
  font-size: theme('fontSize.sm');
}

/* User Actions Menu - UserManagement specific dropdown */
.userActionsMenu {
  position: relative;
}

.userActionsButton {
  padding: theme('spacing.1');
  border-radius: theme('borderRadius.md');
  color: theme('colors.gray.500');
  transition: all 0.2s ease;
}

.userActionsButton:hover {
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.700');
}

.userActionsDropdown {
  position: absolute;
  right: 0;
  top: 100%;
  margin-top: theme('spacing.1');
  background-color: white;
  border: 1px solid theme('colors.gray.200');
  border-radius: theme('borderRadius.md');
  box-shadow: theme('boxShadow.md');
  z-index: 10;
  min-width: theme('spacing.40');
}

.userActionsDropdownItem {
  display: block;
  width: 100%;
  padding: theme('spacing.2') theme('spacing.3');
  text-align: left;
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.700');
  transition: background-color 0.2s ease;
}

.userActionsDropdownItem:hover {
  background-color: theme('colors.gray.50');
}

.userActionsDropdownItemDanger {
  color: theme('colors.red.700');
}

.userActionsDropdownItemDanger:hover {
  background-color: theme('colors.red.50');
}

/* Dark mode overrides for UserManagement-specific elements */
:global(.dark) .userName {
  color: white;
}

:global(.dark) .userEmail {
  color: theme('colors.gray.300');
}

:global(.dark) .userItem {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .badgeLevel {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.300');
}

:global(.dark) .statCardChangePositive {
  color: theme('colors.green.400');
}

:global(.dark) .statCardChangeNegative {
  color: theme('colors.red.400');
}

:global(.dark) .paginationWrapper {
  border-top-color: theme('colors.gray.700');
}

:global(.dark) .paginationInfo {
  color: theme('colors.gray.300');
}

:global(.dark) .userActionsButton:hover {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
}

:global(.dark) .userActionsDropdown {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .userActionsDropdownItem {
  color: theme('colors.gray.300');
}

:global(.dark) .userActionsDropdownItem:hover {
  background-color: theme('colors.gray.700');
}

:global(.dark) .userActionsDropdownItemDanger {
  color: theme('colors.red.400');
}

:global(.dark) .userActionsDropdownItemDanger:hover {
  background-color: rgba(239, 68, 68, 0.1);
}