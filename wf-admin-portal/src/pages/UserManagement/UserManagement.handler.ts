import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { serviceContainer } from '@/services'
import { useToast } from '@/hooks/use-toast'
import type { UserWithAdminRelations } from 'wf-shared/types'

export const useUserManagementHandler = () => {
  const [page, setPage] = useState(1)
  const [selectedUser, setSelectedUser] = useState<UserWithAdminRelations | null>(null)
  const [showUserDetails, setShowUserDetails] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const queryClient = useQueryClient()
  const { toast } = useToast()

  const { data: usersResponse, isLoading, error } = useQuery({
    queryKey: ['users', page],
    queryFn: () => serviceContainer.userService.getUsers({
      page,
      pageSize: 10,
    }),
  })

  // Extract the actual data from service response
  const users = usersResponse?.data

  // Show error toast if there's a service error
  if (usersResponse?.error && !isLoading) {
    toast({
      title: 'Error loading users',
      description: usersResponse.error,
      variant: 'destructive',
    })
  }

  const updateUserMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<UserWithAdminRelations> }) => {
      const response = await serviceContainer.userService.updateUser(id, updates)
      if (response.error) {
        throw new Error(response.error)
      }
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast({
        title: 'Success',
        description: 'User updated successfully',
      })
      setShowEditModal(false)
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update user',
        variant: 'destructive',
      })
    },
  })

  const deleteUserMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await serviceContainer.userService.deleteUser(id)
      if (response.error) {
        throw new Error(response.error)
      }
      return response.success
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast({
        title: 'Success',
        description: 'User deleted successfully',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete user',
        variant: 'destructive',
      })
    },
  })

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  const handleViewUser = async (userId: string) => {
    try {
      const userData = await serviceContainer.userService.getUserById(userId)
      setSelectedUser(userData.data)
      setShowUserDetails(true)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load user details',
        variant: 'destructive',
      })
    }
  }

  const handleEditUser = (user: UserWithAdminRelations) => {
    setSelectedUser(user)
    setShowEditModal(true)
  }

  const handleUpdateUser = (updates: Partial<UserWithAdminRelations>) => {
    if (selectedUser) {
      updateUserMutation.mutate({ id: selectedUser.id, updates })
    }
  }

  const handleDeleteUser = (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      deleteUserMutation.mutate(userId)
    }
  }

  const handleCloseModals = () => {
    setShowUserDetails(false)
    setShowEditModal(false)
    setSelectedUser(null)
  }

  return {
    // Data
    users,
    selectedUser,
    
    // State
    page,
    isLoading,
    error,
    showUserDetails,
    showEditModal,
    
    // Loading states
    isUpdating: updateUserMutation.isPending,
    isDeleting: deleteUserMutation.isPending,
    
    // Actions
    handlePageChange,
    handleViewUser,
    handleEditUser,
    handleUpdateUser,
    handleDeleteUser,
    handleCloseModals,
  }
}