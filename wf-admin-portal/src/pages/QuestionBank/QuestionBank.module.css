/**
 * QuestionBank CSS Module - Refactored with Shared Modules
 * Component-specific styles only, common patterns imported from shared modules
 */

/* QuestionBank-specific styles only */

/* Question List - QuestionBank specific question item styling */
.questionListContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

.questionItem {
  composes: cardHover from '@styles/shared/cards.module.css';
  padding: theme('spacing.4');
}

.questionHeader {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.questionContent {
  flex: 1;
}

.questionBadges {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  margin-bottom: theme('spacing.2');
}

.questionTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.2');
  line-height: theme('lineHeight.relaxed');
}

.questionOptionsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: theme('spacing.2');
  margin-bottom: theme('spacing.2');
}

.questionExplanation {
  margin-top: theme('spacing.2');
  padding: theme('spacing.2');
  background-color: theme('colors.blue.50');
  border-radius: theme('borderRadius.DEFAULT');
  font-size: theme('fontSize.sm');
  color: theme('colors.blue.800');
}

.questionTimestamp {
  margin-top: theme('spacing.2');
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.500');
}

.questionActions {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  margin-left: theme('spacing.4');
}

/* Badges - QuestionBank specific badge variants */
.badgeQuiz {
  composes: badgeBase from '@styles/shared/badges.module.css';
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

.badgeQuestionType {
  composes: badgeBase from '@styles/shared/badges.module.css';
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.800');
}

/* Options - QuestionBank specific option styling */
.optionCorrect {
  font-size: theme('fontSize.sm');
  padding: theme('spacing.2');
  border-radius: theme('borderRadius.DEFAULT');
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
  font-weight: theme('fontWeight.medium');
}

.optionIncorrect {
  font-size: theme('fontSize.sm');
  padding: theme('spacing.2');
  border-radius: theme('borderRadius.DEFAULT');
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.600');
}

/* Icons - QuestionBank specific icon styling */
.iconHasExplanation {
  height: theme('spacing.4');
  width: theme('spacing.4');
  color: theme('colors.green.500');
}

.iconNoExplanation {
  height: theme('spacing.4');
  width: theme('spacing.4');
  color: theme('colors.red.500');
}

/* Stats Cards - QuestionBank specific stat values */
.statCardValueGreen {
  composes: statCardValue from '@styles/shared/cards.module.css';
  color: theme('colors.green.600');
}

.statCardValueOrange {
  composes: statCardValue from '@styles/shared/cards.module.css';
  color: theme('colors.orange.600');
}

.statCardValueBlue {
  composes: statCardValue from '@styles/shared/cards.module.css';
  color: theme('colors.blue.600');
}

/* Clear Button - QuestionBank specific styling */
.clearButton {
  composes: buttonOutline from '@styles/shared/buttons.module.css';
  width: 100%;
}

/* Dark mode overrides for QuestionBank-specific elements */
:global(.dark) .questionItem {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .questionItem:hover {
  background-color: theme('colors.gray.700');
}

:global(.dark) .questionTitle {
  color: white;
}

:global(.dark) .questionTimestamp {
  color: theme('colors.gray.400');
}

:global(.dark) .questionExplanation {
  background-color: rgba(59, 130, 246, 0.1);
  color: theme('colors.blue.300');
}

:global(.dark) .badgeQuiz {
  background-color: rgba(59, 130, 246, 0.2);
  color: theme('colors.blue.300');
}

:global(.dark) .badgeQuestionType {
  background-color: rgba(107, 114, 128, 0.2);
  color: theme('colors.gray.300');
}

:global(.dark) .optionCorrect {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.300');
}

:global(.dark) .optionIncorrect {
  background-color: rgba(107, 114, 128, 0.2);
  color: theme('colors.gray.400');
}

:global(.dark) .statCardValueGreen {
  color: theme('colors.green.400');
}

:global(.dark) .statCardValueOrange {
  color: theme('colors.orange.400');
}

:global(.dark) .statCardValueBlue {
  color: theme('colors.blue.400');
}