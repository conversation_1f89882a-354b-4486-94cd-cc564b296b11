import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient, keepPreviousData } from '@tanstack/react-query'
import { serviceContainer } from '@/services'
import { useToast } from '@/hooks/use-toast'
import type { QuestionWithRelations, DbQuestionOption, DbExplanation } from 'wf-shared/types'

interface QuestionFormData {
  question_text: string
  quiz_id?: string // Optional - questions can be created independently
  question_type_id: string
  difficulty_level?: number // New field from updated schema
  is_active: boolean
  options: Partial<DbQuestionOption>[]
  explanation?: Partial<DbExplanation>
  // Quiz-specific fields (used when assigning to quiz)
  sort_order?: number // Optional - used for quiz assignment
  points?: number // Optional - points for this question in specific quiz
}

export const useQuestionBankHandler = () => {
  const [page, setPage] = useState(1)
  const [filters, setFilters] = useState({
    search: '',
    questionType: '',
    difficulty: '',
    status: '',
  })
  const [selectedQuestion, setSelectedQuestion] = useState<QuestionWithRelations | null>(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [isVeryFirstLoad, setIsVeryFirstLoad] = useState(true)
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Load reference data for filters
  const { data: questionTypesResponse, isLoading: questionTypesLoading } = useQuery({
    queryKey: ['questionTypes'],
    queryFn: serviceContainer.questionTypeService.getQuestionTypes,
  })

  const questionTypes = questionTypesResponse?.data

  // Load questions with filters
  const { data: questionsResponse, isLoading, isFetching, error } = useQuery({
    queryKey: ['questions', page, filters],
    queryFn: () => {
      // Transform UI filter format to repository format
      const repositoryFilters: Record<string, any> = {}

      // Map question type key to question_type_id
      if (filters.questionType && questionTypes) {
        const questionType = questionTypes.find(qt => qt.key === filters.questionType)
        if (questionType) {
          repositoryFilters.question_type_id = questionType.id
        }
      }

      // Map difficulty level
      if (filters.difficulty) {
        repositoryFilters.difficulty_level = parseInt(filters.difficulty)
      }

      // Map status to is_active boolean
      if (filters.status) {
        repositoryFilters.is_active = filters.status === 'active'
      }

      // Add search functionality
      if (filters.search && filters.search.trim().length >= 2) {
        return serviceContainer.questionService.searchQuestions(filters.search.trim(), {
          page,
          pageSize: 10,
        })
      }

      return serviceContainer.questionService.getQuestions({
        page,
        pageSize: 10,
        ...repositoryFilters,
      })
    },
    enabled: true, // Always enabled, filters will just be ignored if reference data not loaded
    placeholderData: keepPreviousData, // Keep previous data while loading new data
  })

  // Extract the actual data from service response
  const questions = questionsResponse?.data
  const questionsWithExplanations = questions?.data?.filter(q => q.explanations && q.explanations.length > 0) || []
  const questionsWithoutExplanations = questions?.data?.filter(q => !q.explanations || q.explanations.length === 0) || []
  const completionRate = questions?.data ? Math.round((questionsWithExplanations.length / questions.data.length) * 100) : 0

  // Track when we've made the first query attempt (successful or failed)
  useEffect(() => {
    if (!isLoading && isVeryFirstLoad) {
      setIsVeryFirstLoad(false)
    }
  }, [isLoading, isVeryFirstLoad])

  // Also mark first load as done if there's an error (to prevent full page skeleton on subsequent filter changes)
  useEffect(() => {
    if (error && isVeryFirstLoad) {
      setIsVeryFirstLoad(false)
    }
  }, [error, isVeryFirstLoad])

  // Determine loading states:
  // - isInitialLoading: true ONLY for the very first load of the page (before any data has been loaded)
  // - isRefetching: true for ANY loading after the first successful load (filters, pagination, etc.)
  const isInitialLoading = isLoading && isVeryFirstLoad
  const isRefetching = isFetching && !isVeryFirstLoad

  const stats = {
    total: questions?.count || 0,
    active: questions?.data?.filter(q => q.is_active).length || 0,
    withExplanations: questionsWithExplanations.length,
    withoutExplanations: questionsWithoutExplanations.length,
    withFewOptions: questions?.data?.filter(q => (q.question_options?.length || 0) < 2).length || 0,
    completionRate,
  }

  // Show error toast if there's a service error
  useEffect(() => {
    if (questionsResponse?.error && !isLoading) {
      toast({
        title: 'Error loading questions',
        description: questionsResponse.error,
        variant: 'destructive',
      })
    }
  }, [questionsResponse?.error, isLoading, toast])

  useEffect(() => {
    if (questionTypesResponse?.error) {
      toast({
        title: 'Error loading question types',
        description: questionTypesResponse.error,
        variant: 'destructive',
      })
    }
  }, [questionTypesResponse?.error, toast])

  const handleFilterChange = (filterType: string, value: string): void => {
    setFilters(prev => ({ ...prev, [filterType]: value }))
    setPage(1) // Reset to first page when filters change
  }

  const clearFilters = (): void => {
    setFilters({ search: '', questionType: '', difficulty: '', status: '' })
    setPage(1)
  }

  const handlePageChange = (newPage: number): void => {
    setPage(newPage)
  }

  const deleteQuestionMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await serviceContainer.questionService.deleteQuestion(id)
      if (response.error) {
        throw new Error(response.error)
      }
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      toast({
        title: 'Success',
        description: 'Question deleted successfully',
      })
      setShowDeleteModal(false)
      setSelectedQuestion(null)
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete question',
        variant: 'destructive',
      })
    },
  })

  const createQuestionMutation = useMutation({
    mutationFn: async (questionData: QuestionFormData) => {
      // Create question without quiz_id (new schema)
      const response = await serviceContainer.questionService.createQuestionWithOptionsAndExplanation({
        question: {
          question_text: questionData.question_text,
          question_type_id: questionData.question_type_id,
          difficulty_level: questionData.difficulty_level || 1,
          is_active: questionData.is_active,
        },
        options: questionData.options.filter(opt => opt.option_text?.trim()),
        explanation: questionData.explanation?.content?.trim() ? questionData.explanation : undefined,
      })

      if (response.error) {
        throw new Error(response.error)
      }

      // If a quiz is selected, assign the question to the quiz
      if (questionData.quiz_id && response.data) {
        const assignResponse = await serviceContainer.questionService.addQuestionToQuiz(
          questionData.quiz_id,
          response.data.id,
          questionData.sort_order,
          questionData.points || 1,
        )

        if (assignResponse.error) {
          // Question was created but assignment failed - log warning but don't fail
          console.warn('Question created but quiz assignment failed:', assignResponse.error)
        }
      }

      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      toast({
        title: 'Success',
        description: 'Question created successfully',
      })
      setShowCreateModal(false)
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create question',
        variant: 'destructive',
      })
    },
  })

  const updateQuestionMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: QuestionFormData }) => {
      // For now, we'll update the question basic info
      // TODO: Implement full update with options and explanation
      const response = await serviceContainer.questionService.updateQuestion(id, {
        question_text: updates.question_text,
        question_type_id: updates.question_type_id,
        difficulty_level: updates.difficulty_level,
        is_active: updates.is_active,
      })
      if (response.error) {
        throw new Error(response.error)
      }
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      toast({
        title: 'Success',
        description: 'Question updated successfully',
      })
      setShowEditModal(false)
      setSelectedQuestion(null)
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update question',
        variant: 'destructive',
      })
    },
  })

  const handleAddQuestion = (): void => {
    setShowCreateModal(true)
  }

  const handleEditQuestion = (questionId: string): void => {
    const question = questions?.data?.find(q => q.id === questionId)
    if (question) {
      setSelectedQuestion(question)
      setShowEditModal(true)
    }
  }

  const handleDeleteQuestion = async (questionId: string): Promise<void> => {
    const question = questions?.data?.find(q => q.id === questionId)
    if (question) {
      setSelectedQuestion(question)
      setShowDeleteModal(true)
    }
  }

  const confirmDeleteQuestion = (): void => {
    if (selectedQuestion) {
      deleteQuestionMutation.mutate(selectedQuestion.id)
    }
  }

  const cancelDelete = (): void => {
    setShowDeleteModal(false)
    setSelectedQuestion(null)
  }

  const handleSaveQuestion = (questionData: QuestionFormData): void => {
    if (selectedQuestion) {
      updateQuestionMutation.mutate({ id: selectedQuestion.id, updates: questionData })
    } else {
      createQuestionMutation.mutate(questionData)
    }
  }

  const handleCloseModals = (): void => {
    setShowCreateModal(false)
    setShowEditModal(false)
    setSelectedQuestion(null)
  }

  const hasFilters = Object.values(filters).some(v => v)

  return {
    // Data
    questions,
    stats,
    questionTypes,
    selectedQuestion,

    // State
    page,
    filters,
    isLoading,
    isInitialLoading,
    isRefetching,
    error,
    hasFilters,
    questionTypesLoading,
    showDeleteModal,
    showCreateModal,
    showEditModal,
    isDeleting: deleteQuestionMutation.isPending,
    isCreating: createQuestionMutation.isPending,
    isUpdating: updateQuestionMutation.isPending,

    // Actions
    handleFilterChange,
    clearFilters,
    handlePageChange,
    handleAddQuestion,
    handleEditQuestion,
    handleDeleteQuestion,
    confirmDeleteQuestion,
    cancelDelete,
    handleSaveQuestion,
    handleCloseModals,
  }
}