import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { formatDate } from 'wf-shared/utils'
import { Plus, Edit, Trash2, CheckCircle, XCircle, Loader2, Search, FileText } from 'lucide-react'
import { useQuestionBankHandler } from './QuestionBank.handler'
import styles from './QuestionBank.module.css'
import { sharedStyles } from '../../styles/shared'
import QuestionFormModal from './QuestionFormModal'
import { Pagination } from '@/components/ui/pagination'

export default function QuestionBank() {
  const {
    questions,
    stats,
    questionTypes,
    selectedQuestion,
    page,
    filters,
    isInitialLoading,
    isRefetching,
    hasFilters,
    questionTypesLoading,
    showDeleteModal,
    showCreateModal,
    showEditModal,
    isDeleting,
    isCreating,
    isUpdating,
    handleFilterChange,
    clearFilters,
    handlePageChange,
    handleAddQuestion,
    handleEditQuestion,
    handleDeleteQuestion,
    confirmDeleteQuestion,
    cancelDelete,
    handleSaveQuestion,
    handleCloseModals,
  } = useQuestionBankHandler()


  return (
    <div className={sharedStyles.layouts.container}>
      <div className={sharedStyles.layouts.header}>
        <div>
          <h1 className={sharedStyles.layouts.headerTitle}>Question Bank</h1>
          <p className={sharedStyles.layouts.headerSubtitle}>
            Manage your question database with {stats.total} questions
          </p>
        </div>
        <Button className={sharedStyles.buttons.buttonPrimary} onClick={handleAddQuestion}>
          <Plus className="h-4 w-4 mr-2" />
          Add Question
        </Button>
      </div>

      {/* Stats */}
      <div className={sharedStyles.layouts.statsGrid}>
        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              Total Questions
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.cards.statCardValue}>{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              With Explanations
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCardValueGreen}>
              {stats.withExplanations}
            </div>
          </CardContent>
        </Card>

        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              Missing Explanations
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCardValueOrange}>
              {stats.withoutExplanations}
            </div>
          </CardContent>
        </Card>

        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              Completion Rate
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCardValueBlue}>
              {stats.completionRate}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className={sharedStyles.cards.cardBase}>
        <CardHeader className={sharedStyles.cards.cardHeader}>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent className={sharedStyles.cards.cardContent}>
          <div className={sharedStyles.forms.filtersGrid}>
            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Search
              </label>
              <div className={sharedStyles.forms.searchContainer}>
                <Search className={sharedStyles.forms.searchIcon} />
                <input
                  type="text"
                  placeholder="Search questions..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className={sharedStyles.forms.searchInput}
                />
              </div>
            </div>

            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Question Type
              </label>
              <select
                value={filters.questionType}
                onChange={(e) => handleFilterChange('questionType', e.target.value)}
                className={sharedStyles.forms.fieldSelect}
                disabled={questionTypesLoading}
              >
                <option value="">
                  {questionTypesLoading ? 'Loading types...' : 'All Types'}
                </option>
                {questionTypes?.map((type) => (
                  <option key={type.id} value={type.key}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>

            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Difficulty
              </label>
              <select
                value={filters.difficulty}
                onChange={(e) => handleFilterChange('difficulty', e.target.value)}
                className={sharedStyles.forms.fieldSelect}
              >
                <option value="">All Difficulties</option>
                <option value="1">Easy (1)</option>
                <option value="2">Easy-Medium (2)</option>
                <option value="3">Medium (3)</option>
                <option value="4">Medium-Hard (4)</option>
                <option value="5">Hard (5)</option>
              </select>
            </div>

            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className={sharedStyles.forms.fieldSelect}
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div className="flex items-end">
              <Button
                className={styles.clearButton}
                onClick={clearFilters}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Question List */}
      <Card className={sharedStyles.cards.cardBase}>
        <CardHeader className={sharedStyles.cards.cardHeader}>
          <CardTitle>Questions</CardTitle>
        </CardHeader>
        <CardContent className={sharedStyles.cards.cardContent}>
          {isInitialLoading || isRefetching ? (
            <div className={sharedStyles.states.loadingContainer}>
              {[...Array(5)].map((_, i) => (
                <div key={i} className={sharedStyles.states.loadingSkeletonItem}></div>
              ))}
            </div>
          ) : questions?.data && questions.data.length > 0 ? (
            <div className={styles.questionListContainer}>
              {questions.data.map((question) => (
              <div
                key={question.id}
                className={styles.questionItem}
              >
                <div className={styles.questionHeader}>
                  <div className={styles.questionContent}>
                    <div className={styles.questionBadges}>
                      <span className={styles.badgeQuiz}>
                        {question.quizzes?.[0]?.title}
                      </span>
                      <span className={styles.badgeQuestionType}>
                        {question.question_types?.name}
                      </span>
                      {question.explanations && question.explanations.length > 0 ? (
                        <CheckCircle className={styles.iconHasExplanation} />
                      ) : (
                        <XCircle className={styles.iconNoExplanation} />
                      )}
                    </div>
                    
                    <h3 className={styles.questionTitle}>
                      {question.question_text}
                    </h3>
                    
                    {question.question_options && question.question_options.length > 0 && (
                      <div className={styles.questionOptionsGrid}>
                        {question.question_options.map((option) => (
                          <div
                            key={option.id}
                            className={
                              option.is_correct
                                ? styles.optionCorrect
                                : styles.optionIncorrect
                            }
                          >
                            {option.option_key}: {option.option_text}
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {question.explanations && question.explanations.length > 0 && (
                      <div className={styles.questionExplanation}>
                        <strong>Explanation:</strong> {question.explanations[0].content}
                      </div>
                    )}
                    
                    <div className={styles.questionTimestamp}>
                      Updated {formatDate(question.updated_at || question.created_at || '')}
                    </div>
                  </div>
                  
                  <div className={styles.questionActions}>
                    <Button 
                      className={sharedStyles.buttons.buttonGhost}
                      onClick={() => handleEditQuestion(question.id)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      className={sharedStyles.buttons.buttonGhost}
                      onClick={() => handleDeleteQuestion(question.id)}
                      disabled={isDeleting}
                    >
                      {isDeleting ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>
              ))}
            </div>
          ) : (
            <div className={sharedStyles.states.emptyStateWrapper}>
              <FileText className={sharedStyles.states.emptyStateIcon} />
              <h3 className={sharedStyles.states.emptyStateTitle}>No Questions Found</h3>
              <p className={sharedStyles.states.emptyStateDescription}>
                {hasFilters
                  ? 'No questions match your current filters'
                  : 'Get started by creating your first question'}
              </p>
              <Button className={sharedStyles.buttons.buttonPrimary} onClick={handleAddQuestion}>
                <Plus className="h-4 w-4 mr-2" />
                Add Question
              </Button>
            </div>
          )}

          {/* Pagination */}
          {questions?.data && questions.data.length > 0 && (
            <Pagination
              currentPage={page}
              totalPages={questions?.totalPages || 0}
              onPageChange={handlePageChange}
            />
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <AlertDialog open={showDeleteModal} onOpenChange={cancelDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Question</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this question? This action cannot be undone.
              {selectedQuestion && (
                <div className="mt-4 p-3 bg-gray-50 rounded border">
                  <strong>Question:</strong> {selectedQuestion.question_text}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDelete} disabled={isDeleting}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteQuestion}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Question'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Create Question Modal */}
      {showCreateModal && (
        <QuestionFormModal
          onSave={handleSaveQuestion}
          onClose={handleCloseModals}
          isLoading={isCreating}
        />
      )}

      {/* Edit Question Modal */}
      {showEditModal && selectedQuestion && (
        <QuestionFormModal
          question={selectedQuestion}
          onSave={handleSaveQuestion}
          onClose={handleCloseModals}
          isLoading={isUpdating}
        />
      )}
    </div>
  )
}