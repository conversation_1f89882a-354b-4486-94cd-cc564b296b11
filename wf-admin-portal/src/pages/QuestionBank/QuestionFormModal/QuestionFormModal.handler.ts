import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { serviceContainer } from '@/services'
import type { QuestionWithRelations, DbQuestionOption, DbExplanation } from 'wf-shared/types'

// Types
export interface QuestionFormData {
  question_text: string
  quiz_id?: string // Optional - questions can be created independently
  question_type_id: string
  difficulty_level?: number // New field from updated schema
  is_active: boolean
  options: Partial<DbQuestionOption>[]
  explanation?: Partial<DbExplanation>
  // Quiz-specific fields (used when assigning to quiz)
  sort_order?: number // Optional - used for quiz assignment
  points?: number // Optional - points for this question in specific quiz
}

interface QuestionFormModalHandlerProps {
  question?: QuestionWithRelations
  onSave: (questionData: QuestionFormData) => void
}

export const useQuestionFormModalHandler = ({ question, onSave }: QuestionFormModalHandlerProps) => {
  const isEdit = !!question
  
  // Initialize form data
  const [formData, setFormData] = useState<QuestionFormData>({
    question_text: question?.question_text || '',
    quiz_id: '', // No longer pre-filled - questions are independent
    question_type_id: question?.question_type_id || '',
    difficulty_level: question?.difficulty_level || 1,
    is_active: question?.is_active ?? true,
    options: question?.question_options || [
      { option_key: 'A', option_text: '', is_correct: false, sort_order: 0 },
      { option_key: 'B', option_text: '', is_correct: false, sort_order: 1 },
      { option_key: 'C', option_text: '', is_correct: false, sort_order: 2 },
      { option_key: 'D', option_text: '', is_correct: false, sort_order: 3 },
    ],
    explanation: question?.explanations?.[0] ? {
      content: question.explanations[0].content,
      explanation_type: question.explanations[0].explanation_type,
      metadata: question.explanations[0].metadata as any,
    } : { content: '', explanation_type: 'basic' },
    sort_order: 1, // Default for quiz assignment
    points: 1, // Default points
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Data fetching
  const { data: quizzes } = useQuery({
    queryKey: ['quizzes-simple'],
    queryFn: async () => {
      const response = await serviceContainer.quizService.getQuizzes({ page: 1, pageSize: 1000 })
      return response.data?.data || []
    },
  })

  const { data: questionTypes } = useQuery({
    queryKey: ['question-types'],
    queryFn: () => serviceContainer.questionTypeService.getQuestionTypes(),
  })

  // Event handlers
  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleOptionChange = (index: number, field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((option, i) => 
        i === index ? { ...option, [field]: value } : option,
      ),
    }))
  }

  const handleCorrectAnswerChange = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((option, i) => ({
        ...option,
        is_correct: i === index,
      })),
    }))
  }

  const addOption = () => {
    const nextKey = String.fromCharCode(65 + formData.options.length) // A, B, C, D, E, F...
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, {
        option_key: nextKey,
        option_text: '',
        is_correct: false,
        sort_order: prev.options.length,
      }],
    }))
  }

  const removeOption = (index: number) => {
    if (formData.options.length <= 2) return // Minimum 2 options
    
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index).map((option, i) => ({
        ...option,
        option_key: String.fromCharCode(65 + i),
        sort_order: i,
      })),
    }))
  }

  const handleExplanationChange = (content: string) => {
    setFormData(prev => ({
      ...prev,
      explanation: { ...prev.explanation, content },
    }))
  }

  // Form validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.question_text.trim()) newErrors.question_text = 'Question text is required'
    // Quiz is now optional - questions can be created independently
    // if (!formData.quiz_id) newErrors.quiz_id = 'Quiz is required'
    if (!formData.question_type_id) newErrors.question_type_id = 'Question type is required'
    
    // Validate options
    const filledOptions = formData.options.filter(opt => opt.option_text?.trim())
    if (filledOptions.length < 2) newErrors.options = 'At least 2 options are required'
    
    const correctOptions = formData.options.filter(opt => opt.is_correct)
    if (correctOptions.length !== 1) newErrors.correct_answer = 'Exactly one correct answer is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onSave(formData)
    }
  }

  // Computed values
  const canAddOption = formData.options.length < 6
  const canRemoveOption = formData.options.length > 2

  // Difficulty level helper
  const getDifficultyLabel = (level: number) => {
    switch (level) {
      case 1: return 'Very Easy'
      case 2: return 'Easy'
      case 3: return 'Medium'
      case 4: return 'Hard'
      case 5: return 'Very Hard'
      default: return 'Medium'
    }
  }

  // Return handler interface
  return {
    // Data
    formData,
    errors,
    isEdit,
    quizzes: quizzes || [],
    questionTypes: questionTypes?.data || [],
    
    // Actions
    handleInputChange,
    handleOptionChange,
    handleCorrectAnswerChange,
    handleExplanationChange,
    addOption,
    removeOption,
    handleSubmit,
    
    // Computed values
    canAddOption,
    canRemoveOption,
    getDifficultyLabel,
  }
}