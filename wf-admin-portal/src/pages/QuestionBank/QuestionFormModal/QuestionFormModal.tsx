import { Button } from '@/components/ui/button'
import { X, Plus, Trash2 } from 'lucide-react'
import { useQuestionFormModalHandler, type QuestionFormData } from './QuestionFormModal.handler'
import styles from './QuestionFormModal.module.css'
import type { QuestionWithRelations } from 'wf-shared/types'

export interface QuestionFormModalProps {
  question?: QuestionWithRelations
  onSave: (questionData: QuestionFormData) => void
  onClose: () => void
  isLoading: boolean
}

export default function QuestionFormModal({ question, onSave, onClose, isLoading }: QuestionFormModalProps) {
  const {
    formData,
    errors,
    isEdit,
    quizzes,
    questionTypes,
    handleInputChange,
    handleOptionChange,
    handleCorrectAnswerChange,
    handleExplanationChange,
    addOption,
    removeOption,
    handleSubmit,
    canAddOption,
    canRemoveOption,
  } = useQuestionFormModalHandler({ question, onSave })

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2 className={styles.title}>
            {isEdit ? 'Edit Question' : 'Create New Question'}
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          {/* Basic Question Info */}
          <div className={styles.grid}>
            <div className={styles.fieldGroup}>
              <label className={styles.label}>
                Quiz <span className={styles.labelOptional}>(Optional - can be assigned later)</span>
              </label>
              <select
                value={formData.quiz_id}
                onChange={(e) => handleInputChange('quiz_id', e.target.value)}
                className={`${styles.select} ${errors.quiz_id ? styles.error : ''}`}
              >
                <option value="">No quiz selected (create independent question)</option>
                {quizzes.map((quiz: any) => (
                  <option key={quiz.id} value={quiz.id}>
                    {quiz.title}
                  </option>
                ))}
              </select>
              {errors.quiz_id && <p className={styles.errorMessage}>{errors.quiz_id}</p>}
              <p className={styles.helperText}>
                Questions can be created independently and assigned to quizzes later
              </p>
            </div>

            <div className={styles.fieldGroup}>
              <label className={styles.label}>
                Question Type *
              </label>
              <select
                value={formData.question_type_id}
                onChange={(e) => handleInputChange('question_type_id', e.target.value)}
                className={`${styles.select} ${errors.question_type_id ? styles.error : ''}`}
              >
                <option value="">Select Question Type</option>
                {questionTypes.map((type: any) => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </select>
              {errors.question_type_id && <p className={styles.errorMessage}>{errors.question_type_id}</p>}
            </div>

            {/* Difficulty Level */}
            <div className={styles.fieldGroup}>
              <label className={styles.label}>
                Difficulty Level
              </label>
              <select
                value={formData.difficulty_level || 1}
                onChange={(e) => handleInputChange('difficulty_level', Number(e.target.value))}
                className={styles.select}
              >
                <option value={1}>1 - Very Easy</option>
                <option value={2}>2 - Easy</option>
                <option value={3}>3 - Medium</option>
                <option value={4}>4 - Hard</option>
                <option value={5}>5 - Very Hard</option>
              </select>
            </div>
          </div>

          {/* Question Text */}
          <div className={styles.fieldGroup}>
            <label className={styles.label}>
              Question Text *
            </label>
            <textarea
              value={formData.question_text}
              onChange={(e) => handleInputChange('question_text', e.target.value)}
              rows={3}
              className={`${styles.textarea} ${errors.question_text ? styles.error : ''}`}
              placeholder="Enter the question text"
            />
            {errors.question_text && <p className={styles.errorMessage}>{errors.question_text}</p>}
          </div>

          {/* Options */}
          <div className={styles.fieldGroup}>
            <div className={styles.optionsHeader}>
              <label className={styles.label}>
                Answer Options *
              </label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addOption}
                disabled={!canAddOption}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Option
              </Button>
            </div>
            
            <div className={styles.optionsContainer}>
              {formData.options.map((option, index) => (
                <div key={index} className={styles.optionRow}>
                  <div className={styles.optionRadio}>
                    <input
                      type="radio"
                      name="correct_answer"
                      checked={option.is_correct}
                      onChange={() => handleCorrectAnswerChange(index)}
                      className={styles.optionRadioInput}
                    />
                    <span className={styles.optionKey}>{option.option_key}:</span>
                  </div>
                  
                  <input
                    type="text"
                    value={option.option_text || ''}
                    onChange={(e) => handleOptionChange(index, 'option_text', e.target.value)}
                    className={styles.optionInput}
                    placeholder={`Option ${option.option_key}`}
                    required
                  />
                  
                  {canRemoveOption && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeOption(index)}
                      className={styles.removeButton}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
            {errors.options && <p className={styles.errorMessage}>{errors.options}</p>}
            {errors.correct_answer && <p className={styles.errorMessage}>{errors.correct_answer}</p>}
          </div>

          {/* Explanation */}
          <div className={styles.fieldGroup}>
            <label className={styles.label}>
              Explanation
            </label>
            <textarea
              value={formData.explanation?.content || ''}
              onChange={(e) => handleExplanationChange(e.target.value)}
              rows={3}
              className={styles.textarea}
              placeholder="Provide an explanation for the correct answer (optional)"
            />
          </div>

          {/* Additional Settings */}
          <div className={styles.settingsGrid}>
            <div className={styles.fieldGroup}>
              <label className={styles.label}>
                Order Index
              </label>
              <input
                type="number"
                value={formData.sort_order || 1}
                onChange={(e) => handleInputChange('sort_order', parseInt(e.target.value) || 1)}
                className={styles.input}
                min="1"
              />
            </div>

            <div className={styles.checkboxContainer}>
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) => handleInputChange('is_active', e.target.checked)}
                className={styles.checkbox}
              />
              <label htmlFor="is_active" className={styles.checkboxLabel}>
                Active
              </label>
            </div>
          </div>

          {/* Form Actions */}
          <div className={styles.actions}>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : (isEdit ? 'Update Question' : 'Create Question')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}