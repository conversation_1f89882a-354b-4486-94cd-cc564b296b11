import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { serviceContainer } from '@/services'
import { X, Plus, Trash2 } from 'lucide-react'
import type { QuestionWithRelations, DbQuestionOption, DbExplanation } from 'wf-shared/types'

interface QuestionFormModalProps {
  question?: QuestionWithRelations
  onSave: (questionData: QuestionFormData) => void
  onClose: () => void
  isLoading: boolean
}

interface QuestionFormData {
  question_text: string
  quiz_id?: string // Optional - questions can be created independently
  question_type_id: string
  difficulty_level?: number // New field from updated schema
  is_active: boolean
  options: Partial<DbQuestionOption>[]
  explanation?: Partial<DbExplanation>
  // Quiz-specific fields (used when assigning to quiz)
  sort_order?: number // Optional - used for quiz assignment
  points?: number // Optional - points for this question in specific quiz
}

export default function QuestionFormModal({ question, onSave, onClose, isLoading }: QuestionFormModalProps) {
  const isEdit = !!question
  
  const [formData, setFormData] = useState<QuestionFormData>({
    question_text: question?.question_text || '',
    quiz_id: '', // No longer pre-filled - questions are independent
    question_type_id: question?.question_type_id || '',
    difficulty_level: question?.difficulty_level || 1,
    is_active: question?.is_active ?? true,
    options: question?.question_options || [
      { option_key: 'A', option_text: '', is_correct: false, sort_order: 0 },
      { option_key: 'B', option_text: '', is_correct: false, sort_order: 1 },
      { option_key: 'C', option_text: '', is_correct: false, sort_order: 2 },
      { option_key: 'D', option_text: '', is_correct: false, sort_order: 3 },
    ],
    explanation: question?.explanations?.[0] ? {
      content: question.explanations[0].content,
      explanation_type: question.explanations[0].explanation_type,
      metadata: question.explanations[0].metadata as any,
    } : { content: '', explanation_type: 'basic' },
    sort_order: 1, // Default for quiz assignment
    points: 1, // Default points
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Fetch reference data
  const { data: quizzes } = useQuery({
    queryKey: ['quizzes-simple'],
    queryFn: async () => {
      const response = await serviceContainer.quizService.getQuizzes({ page: 1, pageSize: 1000 })
      return response.data?.data || []
    },
  })

  const { data: questionTypes } = useQuery({
    queryKey: ['question-types'],
    queryFn: () => serviceContainer.questionTypeService.getQuestionTypes(),
  })

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleOptionChange = (index: number, field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((option, i) => 
        i === index ? { ...option, [field]: value } : option,
      ),
    }))
  }

  const handleCorrectAnswerChange = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((option, i) => ({
        ...option,
        is_correct: i === index,
      })),
    }))
  }

  const addOption = () => {
    const nextKey = String.fromCharCode(65 + formData.options.length) // A, B, C, D, E, F...
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, {
        option_key: nextKey,
        option_text: '',
        is_correct: false,
        sort_order: prev.options.length,
      }],
    }))
  }

  const removeOption = (index: number) => {
    if (formData.options.length <= 2) return // Minimum 2 options
    
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index).map((option, i) => ({
        ...option,
        option_key: String.fromCharCode(65 + i),
        sort_order: i,
      })),
    }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.question_text.trim()) newErrors.question_text = 'Question text is required'
    // Quiz is now optional - questions can be created independently
    // if (!formData.quiz_id) newErrors.quiz_id = 'Quiz is required'
    if (!formData.question_type_id) newErrors.question_type_id = 'Question type is required'
    
    // Validate options
    const filledOptions = formData.options.filter(opt => opt.option_text?.trim())
    if (filledOptions.length < 2) newErrors.options = 'At least 2 options are required'
    
    const correctOptions = formData.options.filter(opt => opt.is_correct)
    if (correctOptions.length !== 1) newErrors.correct_answer = 'Exactly one correct answer is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onSave(formData)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {isEdit ? 'Edit Question' : 'Create New Question'}
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Question Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quiz <span className="text-gray-500 text-xs">(Optional - can be assigned later)</span>
              </label>
              <select
                value={formData.quiz_id}
                onChange={(e) => handleInputChange('quiz_id', e.target.value)}
                className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.quiz_id ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">No quiz selected (create independent question)</option>
                {(quizzes || []).map((quiz: any) => (
                  <option key={quiz.id} value={quiz.id}>
                    {quiz.title}
                  </option>
                ))}
              </select>
              {errors.quiz_id && <p className="text-red-500 text-sm mt-1">{errors.quiz_id}</p>}
              <p className="text-gray-500 text-xs mt-1">
                Questions can be created independently and assigned to quizzes later
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Question Type *
              </label>
              <select
                value={formData.question_type_id}
                onChange={(e) => handleInputChange('question_type_id', e.target.value)}
                className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.question_type_id ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select Question Type</option>
                {(questionTypes?.data || []).map((type: any) => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </select>
              {errors.question_type_id && <p className="text-red-500 text-sm mt-1">{errors.question_type_id}</p>}
            </div>

            {/* Difficulty Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Difficulty Level
              </label>
              <select
                value={formData.difficulty_level || 1}
                onChange={(e) => handleInputChange('difficulty_level', Number(e.target.value))}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={1}>1 - Very Easy</option>
                <option value={2}>2 - Easy</option>
                <option value={3}>3 - Medium</option>
                <option value={4}>4 - Hard</option>
                <option value={5}>5 - Very Hard</option>
              </select>
            </div>
          </div>

          {/* Question Text */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Question Text *
            </label>
            <textarea
              value={formData.question_text}
              onChange={(e) => handleInputChange('question_text', e.target.value)}
              rows={3}
              className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.question_text ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter the question text"
            />
            {errors.question_text && <p className="text-red-500 text-sm mt-1">{errors.question_text}</p>}
          </div>

          {/* Options */}
          <div>
            <div className="flex justify-between items-center mb-3">
              <label className="block text-sm font-medium text-gray-700">
                Answer Options *
              </label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addOption}
                disabled={formData.options.length >= 6}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Option
              </Button>
            </div>
            
            <div className="space-y-3">
              {formData.options.map((option, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      name="correct_answer"
                      checked={option.is_correct}
                      onChange={() => handleCorrectAnswerChange(index)}
                      className="mr-2"
                    />
                    <span className="font-medium text-sm w-6">{option.option_key}:</span>
                  </div>
                  
                  <input
                    type="text"
                    value={option.option_text || ''}
                    onChange={(e) => handleOptionChange(index, 'option_text', e.target.value)}
                    className="flex-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder={`Option ${option.option_key}`}
                    required
                  />
                  
                  {formData.options.length > 2 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeOption(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
            {errors.options && <p className="text-red-500 text-sm mt-1">{errors.options}</p>}
            {errors.correct_answer && <p className="text-red-500 text-sm mt-1">{errors.correct_answer}</p>}
          </div>

          {/* Explanation */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Explanation
            </label>
            <textarea
              value={formData.explanation?.content || ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                explanation: { ...prev.explanation, content: e.target.value },
              }))}
              rows={3}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Provide an explanation for the correct answer (optional)"
            />
          </div>

          {/* Additional Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Order Index
              </label>
              <input
                type="number"
                value={formData.sort_order || 1}
                onChange={(e) => handleInputChange('sort_order', parseInt(e.target.value) || 1)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                min="1"
              />
            </div>

            <div className="flex items-center space-x-2 pt-8">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) => handleInputChange('is_active', e.target.checked)}
                className="rounded"
              />
              <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                Active
              </label>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : (isEdit ? 'Update Question' : 'Create Question')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
