import { Button } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { use<PERSON>ogin<PERSON>andler } from './Login.handler'
import styles from './Login.module.css'

export default function Login() {
  const {
    email,
    password,
    rememberMe,
    isLoading,
    setEmail,
    setPassword,
    setRememberMe,
    handleSubmit,
  } = useLoginHandler()

  return (
    <div className={styles.container}>
      <div className={styles.formWrapper}>
        <div className={styles.headerWrapper}>
          <h2 className={styles.headerTitle}>
            Admin Portal
          </h2>
          <p className={styles.headerSubtitle}>
            Sign in to access the admin dashboard
          </p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Sign In</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className={styles.formContainer}>
              <div className={styles.formField}>
                <label htmlFor="email" className={styles.formLabel}>
                  Email address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={styles.formInput}
                  placeholder="Enter your email"
                />
              </div>
              
              <div className={styles.formField}>
                <label htmlFor="password" className={styles.formLabel}>
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={styles.formInput}
                  placeholder="Enter your password"
                />
              </div>

              <div className={styles.formField}>
                <label className={styles.formCheckboxLabel}>
                  <input
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className={styles.formCheckbox}
                  />
                  Remember me
                </label>
              </div>

              <div>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className={styles.formButton}
                >
                  {isLoading ? 'Signing in...' : 'Sign in'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
        
        <div className={styles.footerWrapper}>
          <p className={styles.footerText}>
            Only admin users can access this portal
          </p>
        </div>
      </div>
    </div>
  )
}