.container {
  min-height: 100vh;
  background-color: theme('colors.gray.50');
  display: flex;
  align-items: center;
  justify-content: center;
  padding: theme('spacing.12') theme('spacing.4');
}

@media (min-width: theme('screens.sm')) {
  .container {
    padding: theme('spacing.12') theme('spacing.6');
  }
}

@media (min-width: theme('screens.lg')) {
  .container {
    padding: theme('spacing.12') theme('spacing.8');
  }
}

.formWrapper {
  max-width: 448px; /* 28rem */
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: theme('spacing.8');
}

/* Header styles */
.headerWrapper {
  text-align: center;
}

.headerTitle {
  margin-top: theme('spacing.6');
  font-size: theme('fontSize.3xl');
  font-weight: theme('fontWeight.extrabold');
  color: theme('colors.gray.900');
}

.headerSubtitle {
  margin-top: theme('spacing.2');
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
}

/* Form styles */
.formContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.6');
}

.formField {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.2');
}

.formLabel {
  display: block;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.700');
}

.formInput {
  margin-top: theme('spacing.1');
  appearance: none;
  position: relative;
  display: block;
  width: 100%;
  padding: theme('spacing.2') theme('spacing.3');
  border: 1px solid theme('colors.gray.300');
  color: theme('colors.gray.900');
  border-radius: theme('borderRadius.md');
  font-size: theme('fontSize.sm');
}

.formInput::placeholder {
  color: theme('colors.gray.500');
}

.formInput:focus {
  outline: none;
  ring: theme('ringWidth.2') theme('colors.blue.500');
  border-color: theme('colors.blue.500');
  z-index: 10;
}

.formButton {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  padding: theme('spacing.2') theme('spacing.4');
  border: 1px solid transparent;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  border-radius: theme('borderRadius.md');
  color: white;
  background-color: theme('colors.blue.600');
}

.formButton:hover {
  background-color: theme('colors.blue.700');
}

.formButton:focus {
  outline: none;
  ring: theme('ringWidth.2') theme('colors.blue.500');
  ring-offset: theme('ringOffsetWidth.2');
}

.formButton:disabled {
  opacity: 0.5;
}

.formCheckboxLabel {
  display: flex;
  align-items: center;
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.700');
}

.formCheckbox {
  margin-right: theme('spacing.2');
  border-radius: theme('borderRadius.DEFAULT');
  border-color: theme('colors.gray.300');
  color: theme('colors.blue.600');
}

.formCheckbox:focus {
  ring: theme('ringWidth.2') theme('colors.blue.500');
}

/* Footer styles */
.footerWrapper {
  text-align: center;
}

.footerText {
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.500');
}