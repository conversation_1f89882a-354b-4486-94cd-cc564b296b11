import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'
import { RepositoryContainer } from '@/repositories'

// Initialize repository container (no database injection needed)
const repositories = new RepositoryContainer()

export const useLoginHandler = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { signIn } = useAuth()
  const { toast } = useToast()

  // Load remembered credentials on component mount
  useEffect(() => {
    const loadCredentials = async () => {
      const credentials = await repositories.authStorage.loadCredentials()
      if (credentials.rememberMe && credentials.email) {
        setEmail(credentials.email)
        setRememberMe(credentials.rememberMe)
      }
    }
    loadCredentials()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const { error } = await signIn(email, password)
      
      if (error) {
        toast({
          title: 'Login Failed',
          description: error.message || 'Invalid credentials',
          variant: 'destructive',
        })
      } else {
        // Save credentials if remember me is checked
        await repositories.authStorage.saveCredentials(email, rememberMe)
        
        toast({
          title: 'Login Successful',
          description: 'Welcome to the admin portal',
          variant: 'default',
        })
      }
    } catch (error) {
      toast({
        title: 'Login Failed',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return {
    email,
    password,
    rememberMe,
    isLoading,
    setEmail,
    setPassword,
    setRememberMe,
    handleSubmit,
  }
}