/**
 * Dashboard CSS Module - Refactored with Shared Modules
 * Component-specific styles only, common patterns imported from shared modules
 */

/* Dashboard-specific styles only */

/* Welcome Section - Unique to Dashboard */
.welcomeCard {
  composes: cardPadded from '@styles/shared/cards.module.css';
  background: linear-gradient(135deg, theme('colors.blue.600'), theme('colors.purple.600'));
  color: white;
  border: none;
  margin-bottom: theme('spacing.6');
}

.welcomeMessage {
  font-size: theme('fontSize.2xl');
  font-weight: theme('fontWeight.bold');
  margin-bottom: theme('spacing.2');
}

.welcomeSubtext {
  opacity: 0.9;
  font-size: theme('fontSize.base');
  line-height: theme('lineHeight.relaxed');
}

/* Stats Cards - Dashboard specific styling */
.statCardTitle {
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.600');
}

.statCardIconWrapper {
  padding: theme('spacing.2');
  border-radius: theme('borderRadius.full');
}

.statCardValue {
  font-size: theme('fontSize.2xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.gray.900');
}

/* Quick Actions - Dashboard specific grid */
.quickActionsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: theme('spacing.4');
}

@media (min-width: theme('screens.lg')) {
  .quickActionsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.quickActionTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.1');
}

.quickActionDesc {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.500');
}

/* Activity Grid - Dashboard specific layout */
.activityGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: theme('spacing.4');
}

.activityItem {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

.activityDot {
  width: theme('spacing.2');
  height: theme('spacing.2');
  background-color: theme('colors.blue.500');
  border-radius: theme('borderRadius.full');
  flex-shrink: 0;
}

.activityText {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  flex-grow: 1;
}

.activityDate {
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.400');
  margin-left: auto;
  flex-shrink: 0;
}

/* Dashboard specific metrics */
.metricsHighlight {
  color: theme('colors.green.600');
  font-weight: theme('fontWeight.semibold');
}

.metricsWarning {
  color: theme('colors.yellow.600');
  font-weight: theme('fontWeight.semibold');
}

.metricsCritical {
  color: theme('colors.red.600');
  font-weight: theme('fontWeight.semibold');
}

/* Dark mode overrides for Dashboard-specific elements */
:global(.dark) .welcomeCard {
  background: linear-gradient(135deg, theme('colors.blue.700'), theme('colors.purple.700'));
}

:global(.dark) .statCardTitle {
  color: theme('colors.gray.400');
}

:global(.dark) .statCardValue {
  color: white;
}

:global(.dark) .quickActionTitle {
  color: white;
}

:global(.dark) .quickActionDesc {
  color: theme('colors.gray.400');
}

:global(.dark) .activityText {
  color: theme('colors.gray.300');
}

:global(.dark) .activityDate {
  color: theme('colors.gray.500');
}