import { useQuery } from '@tanstack/react-query'
import { serviceContainer } from '@/services'
import { useToast } from '@/hooks/use-toast'

export const useDashboardHandler = () => {
  const { toast } = useToast()

  const { data: statsResponse, isLoading, error } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: serviceContainer.dashboardService.getDashboardStats,
  })

  // Extract the actual stats data from the service response
  const stats = statsResponse?.data

  // Show error toast if there's an error
  if (statsResponse?.error && !isLoading) {
    toast({
      title: 'Error loading dashboard',
      description: statsResponse.error,
      variant: 'destructive',
    })
  }

  const statCards = [
    {
      title: 'Total Quizzes',
      value: stats?.totalQuizzes || 0,
      icon: 'FileText',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Total Questions',
      value: stats?.totalQuestions || 0,
      icon: 'HelpCircle',
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Total Users',
      value: stats?.totalUsers || 0,
      icon: 'Users',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'Recent Activity',
      value: stats?.recentActivity?.length || 0,
      icon: 'Activity',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ]

  const handleQuickAction = (action: string): void => {
    switch (action) {
      case 'add-quiz':
        // Navigate to quiz creation
        toast({
          title: 'Navigate to Quiz Creation',
          description: 'Redirecting to create new quiz form...',
        })
        // TODO: Add navigation when router is available
        // router.push('/quiz-management?action=create')
        break
        
      case 'add-question':
        // Navigate to question creation
        toast({
          title: 'Navigate to Question Creation',
          description: 'Redirecting to create new question form...',
        })
        // TODO: Add navigation when router is available
        // router.push('/question-bank?action=create')
        break
        
      case 'manage-users':
        // Navigate to user management
        toast({
          title: 'Navigate to User Management',
          description: 'Redirecting to user management page...',
        })
        // TODO: Add navigation when router is available
        // router.push('/user-management')
        break
        
      case 'view-reports':
        // Navigate to reports/analytics
        toast({
          title: 'Navigate to Reports',
          description: 'Analytics and reporting features coming soon...',
        })
        // TODO: Add navigation when router is available
        // router.push('/reports')
        break
        
      case 'validate-content':
        // Navigate to content validation
        toast({
          title: 'Navigate to Content Validation',
          description: 'Redirecting to content validation page...',
        })
        // TODO: Add navigation when router is available
        // router.push('/content-validation')
        break
        
      case 'bulk-import':
        // Navigate to bulk import
        toast({
          title: 'Navigate to Bulk Import',
          description: 'Redirecting to bulk import page...',
        })
        // TODO: Add navigation when router is available
        // router.push('/bulk-import')
        break
        
      default:
        toast({
          title: 'Action Not Implemented',
          description: `Quick action '${action}' will be implemented soon.`,
          variant: 'default',
        })
    }
  }

  return {
    stats,
    isLoading,
    error,
    statCards,
    handleQuickAction,
  }
}