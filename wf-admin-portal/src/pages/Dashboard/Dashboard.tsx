import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card'
import { formatNumber } from 'wf-shared/utils'
import { FileText, HelpCircle, Users, Activity } from 'lucide-react'
import { useDashboardHandler } from './Dashboard.handler'
import styles from './Dashboard.module.css'
import { sharedStyles } from '../../styles/shared'

const iconMap = {
  FileText,
  HelpCircle,
  Users,
  Activity,
}

export default function Dashboard() {
  const { stats, isLoading, statCards, handleQuickAction } = useDashboardHandler()

  if (isLoading) {
    return (
      <div className={sharedStyles.layouts.container}>
        <h1 className={sharedStyles.layouts.headerTitle}>Dashboard</h1>
        <div className={sharedStyles.layouts.statsGrid}>
          {[...Array(4)].map((_, i) => (
            <Card key={i} className={sharedStyles.cards.cardBase}>
              <CardContent className={sharedStyles.states.loadingSkeletonGrid}>
                <div className={sharedStyles.states.loadingSkeletonItem}></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={sharedStyles.layouts.container}>
      {/* Welcome Card */}
      <div className={styles.welcomeCard}>
        <h1 className={styles.welcomeMessage}>Welcome to Word Formation Admin</h1>
        <p className={styles.welcomeSubtext}>
          Overview of your Word Formation quiz platform
        </p>
      </div>

      {/* Stats Cards */}
      <div className={sharedStyles.layouts.statsGrid}>
        {statCards.map((stat) => {
          const IconComponent = iconMap[stat.icon as keyof typeof iconMap]
          return (
            <Card key={stat.title} className={sharedStyles.cards.statCard}>
              <CardHeader className={sharedStyles.cards.cardHeader}>
                <CardTitle className={sharedStyles.layouts.flexBetween}>
                  <span className={styles.statCardTitle}>{stat.title}</span>
                  <div className={`${styles.statCardIconWrapper} ${stat.bgColor}`}>
                    <IconComponent className={`h-4 w-4 ${stat.color}`} />
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className={sharedStyles.cards.cardContent}>
                <div className={styles.statCardValue}>
                  {formatNumber(stat.value)}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Quick Actions */}
      <div className={styles.quickActionsGrid}>
        <Card className={sharedStyles.cards.cardBase}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.activityGrid}>
              <button 
                className={sharedStyles.buttons.buttonSecondary}
                onClick={() => handleQuickAction('create-quiz')}
              >
                <div className={styles.quickActionTitle}>Create Quiz</div>
                <div className={styles.quickActionDesc}>Add a new quiz</div>
              </button>
              <button 
                className={sharedStyles.buttons.buttonSecondary}
                onClick={() => handleQuickAction('add-questions')}
              >
                <div className={styles.quickActionTitle}>Add Questions</div>
                <div className={styles.quickActionDesc}>Expand question bank</div>
              </button>
              <button 
                className={sharedStyles.buttons.buttonSecondary}
                onClick={() => handleQuickAction('validate-content')}
              >
                <div className={styles.quickActionTitle}>Validate Content</div>
                <div className={styles.quickActionDesc}>Check for issues</div>
              </button>
              <button 
                className={sharedStyles.buttons.buttonSecondary}
                onClick={() => handleQuickAction('import-data')}
              >
                <div className={styles.quickActionTitle}>Import Data</div>
                <div className={styles.quickActionDesc}>Bulk import content</div>
              </button>
            </div>
          </CardContent>
        </Card>

        <Card className={sharedStyles.cards.cardBase}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            {stats?.recentActivity && stats.recentActivity.length > 0 ? (
              <div className="space-y-3">
                {stats.recentActivity.slice(0, 5).map((activity, index) => (
                  <div key={index} className={styles.activityItem}>
                    <div className={styles.activityDot}></div>
                    <div className={styles.activityText}>
                      Quiz attempt completed
                    </div>
                    <div className={styles.activityDate}>
                      {new Date(activity.created_at).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={sharedStyles.states.emptyStateWrapper}>
                <div className={sharedStyles.states.emptyStateContent}>
                  No recent activity
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}