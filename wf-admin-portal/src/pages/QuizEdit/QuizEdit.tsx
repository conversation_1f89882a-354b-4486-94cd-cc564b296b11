import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Save, X } from 'lucide-react'
import { useQuizEditHandler } from './QuizEdit.handler'
import styles from './QuizEdit.module.css'
import { sharedStyles } from '../../styles/shared'

export default function QuizEdit() {
  const {
    isCreateMode,
    formData,
    quiz,
    categories,
    levels,
    quizTypes,
    isLoadingQuiz,
    quizError,
    quizResponse,
    saveQuizMutation,
    handleInputChange,
    handleSubmit,
    getBackPath,
    navigate,
  } = useQuizEditHandler()

  // Only show loading in edit mode
  if (!isCreateMode && isLoadingQuiz) {
    return (
      <div className={sharedStyles.states.loadingContainer}>
        <div className={sharedStyles.states.loadingContent}>
          <div className={sharedStyles.states.loadingSpinner}></div>
          <p className={sharedStyles.states.loadingText}>Loading quiz...</p>
        </div>
      </div>
    )
  }

  // Only show error in edit mode, not in create mode
  if (!isCreateMode && (quizError || !quiz)) {
    return (
      <div className={sharedStyles.layouts.container}>
        <Card className={sharedStyles.cards.cardBase}>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.states.errorContainer}>
              <X className={sharedStyles.states.errorIcon} />
              <h2 className={sharedStyles.states.errorTitle}>Quiz Not Found</h2>
              <p className={sharedStyles.states.errorDescription}>
                {quizResponse?.error || 'The quiz you are trying to edit does not exist.'}
              </p>
              <Button className={sharedStyles.buttons.buttonOutline} onClick={() => navigate('/quiz-management')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Quiz Management
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={sharedStyles.layouts.container}>
      {/* Header */}
      <Card className={styles.formSection}>
        <CardHeader>
          <div className={sharedStyles.layouts.header}>
            <div className={sharedStyles.layouts.headerContent}>
              <div className={styles.headerTitleContainer}>
                <Button
                  className={sharedStyles.buttons.buttonGhost}
                  onClick={() => navigate(getBackPath())}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  {getBackPath().includes('/quiz-management') ? 'Back to Quiz Management' : 'Back to Quiz'}
                </Button>
                <h1 className={sharedStyles.layouts.headerTitle}>
                  {isCreateMode ? 'Create Quiz' : 'Edit Quiz'}
                </h1>
                {isCreateMode && <span className={`${styles.headerBadge} ${styles.headerBadgeNew}`}>New</span>}
                {!isCreateMode && <span className={`${styles.headerBadge} ${styles.headerBadgeEdit}`}>Edit</span>}
              </div>
              <p className={sharedStyles.layouts.headerSubtitle}>
                {isCreateMode ? 'Create a new quiz with information and settings' : 'Update quiz information and settings'}
              </p>
            </div>
            <div className={sharedStyles.layouts.headerActions}>
              <Button 
                className={sharedStyles.buttons.buttonOutline}
                onClick={() => navigate(`/quiz/${formData.key}/view`)}
              >
                <X className={`${styles.saveButtonIcon} mr-2`} />
                Cancel
              </Button>
              <Button
                className={sharedStyles.buttons.buttonPrimary}
                onClick={handleSubmit}
                disabled={saveQuizMutation.isPending}
              >
                <Save className={`${styles.saveButtonIcon} mr-2`} />
                {saveQuizMutation.isPending ? 'Saving...' : (isCreateMode ? 'Create Quiz' : 'Save Changes')}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Edit Form */}
      <form onSubmit={handleSubmit}>
        <div className={styles.formContainer}>
          {/* Basic Information */}
          <Card className={styles.formSection}>
            <CardHeader>
              <CardTitle className={styles.formSectionTitle}>Basic Information</CardTitle>
              <p className={styles.formSectionDescription}>
                Provide the basic details about your quiz including title, key, and description.
              </p>
            </CardHeader>
            <CardContent className={styles.formFieldsContainer}>
              {/* Title */}
              <div className={sharedStyles.forms.formField}>
                <label htmlFor="title" className={`${sharedStyles.forms.fieldLabel} ${sharedStyles.forms.fieldLabelRequired}`}>Quiz Title</label>
                <input
                  id="title"
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter quiz title"
                  required
                  className={sharedStyles.forms.fieldInput}
                />
                <p className={sharedStyles.forms.fieldHint}>A clear, descriptive title for your quiz</p>
              </div>

              {/* Key */}
              <div className={sharedStyles.forms.formField}>
                <label htmlFor="key" className={`${sharedStyles.forms.fieldLabel} ${sharedStyles.forms.fieldLabelRequired}`}>Quiz Key</label>
                <div className={sharedStyles.forms.inputGroup}>
                  <div className={sharedStyles.forms.inputGroupAddon}>quiz_</div>
                  <input
                    id="key"
                    type="text"
                    value={formData.key}
                    onChange={(e) => handleInputChange('key', e.target.value)}
                    placeholder="Enter unique quiz key"
                    required
                    className={sharedStyles.forms.inputGroupField}
                  />
                </div>
                <p className={sharedStyles.forms.fieldHint}>Unique identifier for the quiz (letters, numbers, underscores only)</p>
              </div>

              {/* Description */}
              <div className={sharedStyles.forms.formField}>
                <label htmlFor="description" className={sharedStyles.forms.fieldLabel}>Description</label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter quiz description"
                  rows={3}
                  className={sharedStyles.forms.fieldTextarea}
                />
                <p className={sharedStyles.forms.fieldHint}>Brief description of what this quiz covers</p>
              </div>

              {/* Instructions */}
              <div className={sharedStyles.forms.formField}>
                <label htmlFor="instructions" className={sharedStyles.forms.fieldLabel}>Instructions</label>
                <textarea
                  id="instructions"
                  value={formData.instructions}
                  onChange={(e) => handleInputChange('instructions', e.target.value)}
                  placeholder="Enter quiz instructions"
                  rows={4}
                  className={sharedStyles.forms.fieldTextarea}
                />
                <p className={sharedStyles.forms.fieldHint}>Detailed instructions for quiz takers</p>
              </div>
            </CardContent>
          </Card>

          {/* Quiz Settings */}
          <Card className={styles.formSection}>
            <CardHeader>
              <CardTitle className={styles.formSectionTitle}>Quiz Settings</CardTitle>
              <p className={styles.formSectionDescription}>
                Configure the quiz parameters including category, difficulty, and time settings.
              </p>
            </CardHeader>
            <CardContent className={styles.formFieldsContainer}>
              <div className={styles.formFieldGrid}>
                {/* Category */}
                <div className={sharedStyles.forms.formField}>
                  <label htmlFor="category" className={sharedStyles.forms.fieldLabel}>Category</label>
                  <select
                    id="category"
                    value={formData.category_id}
                    onChange={(e) => handleInputChange('category_id', e.target.value)}
                    className={sharedStyles.forms.fieldSelect}
                  >
                    <option value="">Select category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Level */}
                <div className={sharedStyles.forms.formField}>
                  <label htmlFor="level" className={sharedStyles.forms.fieldLabel}>Level</label>
                  <select
                    id="level"
                    value={formData.level_id}
                    onChange={(e) => handleInputChange('level_id', e.target.value)}
                    className={sharedStyles.forms.fieldSelect}
                  >
                    <option value="">Select level</option>
                    {levels.map((level) => (
                      <option key={level.id} value={level.id}>
                        {level.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className={styles.formFieldGrid}>
                {/* Quiz Type */}
                <div className={sharedStyles.forms.formField}>
                  <label htmlFor="quiz_type" className={sharedStyles.forms.fieldLabel}>Quiz Type</label>
                  <select
                    id="quiz_type"
                    value={formData.quiz_type_id}
                    onChange={(e) => handleInputChange('quiz_type_id', e.target.value)}
                    className={sharedStyles.forms.fieldSelect}
                  >
                    <option value="">Select quiz type</option>
                    {quizTypes.map((type) => (
                      <option key={type.id} value={type.id}>
                        {type.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Difficulty Level */}
                <div className={sharedStyles.forms.formField}>
                  <label htmlFor="difficulty" className={sharedStyles.forms.fieldLabel}>Difficulty Level</label>
                  <select
                    id="difficulty"
                    value={formData.difficulty_level.toString()}
                    onChange={(e) => handleInputChange('difficulty_level', parseInt(e.target.value))}
                    className={sharedStyles.forms.fieldSelect}
                  >
                    {[1,2,3,4,5].map((level) => (
                      <option key={level} value={level}>
                        {level} - {level === 1 ? 'Very Easy' : level === 2 ? 'Easy' : level === 3 ? 'Medium' : level === 4 ? 'Hard' : 'Very Hard'}
                        <span className={`${styles.difficultyBadge} ${styles[`difficultyBadge${level}`]}`}></span>
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className={styles.formFieldGrid}>
                {/* Total Questions */}
                <div className={sharedStyles.forms.formField}>
                  <label htmlFor="total_questions" className={sharedStyles.forms.fieldLabel}>Total Questions</label>
                  <input
                    id="total_questions"
                    type="number"
                    min="1"
                    max="100"
                    value={formData.total_questions}
                    onChange={(e) => handleInputChange('total_questions', parseInt(e.target.value) || 1)}
                    className={sharedStyles.forms.fieldInput}
                  />
                  <p className={sharedStyles.forms.fieldHint}>Number of questions in this quiz (1-100)</p>
                </div>

                {/* Time Limit */}
                <div className={sharedStyles.forms.formField}>
                  <label htmlFor="time_limit" className={sharedStyles.forms.fieldLabel}>Time Limit (minutes)</label>
                  <input
                    id="time_limit"
                    type="number"
                    min="0"
                    value={formData.time_limit_minutes || ''}
                    onChange={(e) => handleInputChange('time_limit_minutes', e.target.value ? parseInt(e.target.value) : null)}
                    placeholder="No time limit"
                    className={sharedStyles.forms.fieldInput}
                  />
                  <p className={sharedStyles.forms.fieldHint}>Leave empty for no time limit</p>
                </div>
              </div>

              {/* Active Status */}
              <div className={sharedStyles.forms.formField}>
                <div className={sharedStyles.forms.fieldCheckboxContainer}>
                  <input
                    id="is_active"
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                    className={sharedStyles.forms.fieldCheckbox}
                  />
                  <label htmlFor="is_active" className={sharedStyles.forms.fieldCheckboxLabel}>Active Quiz</label>
                </div>
                <p className={sharedStyles.forms.fieldHint}>Only active quizzes are visible to users</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Save Actions */}
        <div className={styles.saveActions}>
          <Button 
            type="button"
            className={sharedStyles.buttons.buttonOutline}
            onClick={() => navigate(getBackPath())}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={saveQuizMutation.isPending}
            className={sharedStyles.buttons.buttonPrimary}
          >
            {saveQuizMutation.isPending && <div className={styles.saveButtonIcon}></div>}
            <Save className={styles.saveButtonIcon} />
            {saveQuizMutation.isPending ? 'Saving...' : (isCreateMode ? 'Create Quiz' : 'Save Changes')}
          </Button>
        </div>
      </form>
    </div>
  )
}