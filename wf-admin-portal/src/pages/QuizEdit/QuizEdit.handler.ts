import { useParams, useNavigate, useSearchParams } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useState, useEffect } from 'react'
import { serviceContainer } from '@/services'
import { useToast } from '@/hooks/use-toast'

export interface QuizFormData {
  title: string
  description: string
  instructions: string
  key: string
  category_id: string
  level_id: string
  quiz_type_id: string
  difficulty_level: number
  total_questions: number
  time_limit_minutes: number | null
  is_active: boolean
}

export const useQuizEditHandler = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Determine if this is create mode (no id) or edit mode (has id)
  const isCreateMode = !id

  // Determine where to go back based on URL params
  const getBackPath = () => {
    if (isCreateMode) {
      return '/quiz-management'
    }

    // Check if user came from quiz management via URL parameter
    const fromParam = searchParams.get('from')
    if (fromParam === 'quiz-management') {
      return '/quiz-management'
    }

    // Default to quiz view if came from elsewhere (like quiz detail page)
    return `/quiz/${id}/view`
  }

  // Fetch quiz data (only in edit mode)
  const { data: quizResponse, isLoading: isLoadingQuiz, error: quizError } = useQuery({
    queryKey: ['quiz-with-questions', id],
    queryFn: () => serviceContainer.quizService.getQuizWithQuestionsById(id!),
    enabled: !!id && !isCreateMode,
  })

  // Fetch reference data
  const { data: categoriesResponse } = useQuery({
    queryKey: ['categories'],
    queryFn: () => serviceContainer.categoryService.getCategories(),
  })

  const { data: levelsResponse } = useQuery({
    queryKey: ['levels'],
    queryFn: () => serviceContainer.levelService.getLevels(),
  })

  const { data: quizTypesResponse } = useQuery({
    queryKey: ['quiz-types'],
    queryFn: () => serviceContainer.quizTypeService.getQuizTypes(),
  })

  const quiz = quizResponse?.data
  const categories = categoriesResponse?.data || []
  const levels = levelsResponse?.data || []
  const quizTypes = quizTypesResponse?.data || []

  // Form state
  const [formData, setFormData] = useState<QuizFormData>({
    title: '',
    description: '',
    instructions: '',
    key: '',
    category_id: '',
    level_id: '',
    quiz_type_id: '',
    difficulty_level: 1,
    total_questions: 10,
    time_limit_minutes: 30,
    is_active: true,
  })

  // Update form data when quiz loads (edit mode only)
  useEffect(() => {
    if (quiz && !isCreateMode) {
      setFormData({
        title: quiz.title || '',
        description: quiz.description || '',
        instructions: quiz.instructions || '',
        key: quiz.key || '',
        category_id: quiz.category_id || '',
        level_id: quiz.level_id || '',
        quiz_type_id: quiz.quiz_type_id || '',
        difficulty_level: quiz.difficulty_level || 1,
        total_questions: quiz.total_questions || 10,
        time_limit_minutes: quiz.time_limit_minutes || 30,
        is_active: quiz.is_active ?? true,
      })
    }
  }, [quiz, isCreateMode])

  // Create/Update mutation
  const saveQuizMutation = useMutation({
    mutationFn: async (quizData: QuizFormData) => {
      if (isCreateMode) {
        const response = await serviceContainer.quizService.createQuiz(quizData)
        if (response.error) {
          throw new Error(response.error)
        }
        return response.data
      } else {
        const response = await serviceContainer.quizService.updateQuiz(id!, quizData)
        if (response.error) {
          throw new Error(response.error)
        }
        return response.data
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['quizzes'] })
      if (!isCreateMode) {
        queryClient.invalidateQueries({ queryKey: ['quiz-with-questions', id] })
      }
      toast({
        title: 'Success',
        description: isCreateMode ? 'Quiz created successfully' : 'Quiz updated successfully',
      })

      if (isCreateMode && data?.id) {
        navigate(`/quiz/${data.id}/view`)
      } else if (!isCreateMode) {
        navigate(`/quiz/${id}/view`)
      } else {
        navigate('/quiz-management')
      }
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : `Failed to ${isCreateMode ? 'create' : 'update'} quiz`,
        variant: 'destructive',
      })
    },
  })

  const handleInputChange = (field: keyof QuizFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const validateForm = (): boolean => {
    // Validate required fields
    if (!formData.title.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Quiz title is required',
        variant: 'destructive',
      })
      return false
    }

    if (!formData.key.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Quiz key is required',
        variant: 'destructive',
      })
      return false
    }

    if (!formData.category_id) {
      toast({
        title: 'Validation Error',
        description: 'Category is required',
        variant: 'destructive',
      })
      return false
    }

    if (!formData.level_id) {
      toast({
        title: 'Validation Error',
        description: 'Level is required',
        variant: 'destructive',
      })
      return false
    }

    return true
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    // Prepare data for submission
    const quizData = {
      ...formData,
      difficulty_level: Number(formData.difficulty_level),
      total_questions: Number(formData.total_questions),
      time_limit_minutes: formData.time_limit_minutes ? Number(formData.time_limit_minutes) : null,
    }

    // Submit the data
    saveQuizMutation.mutate(quizData)
  }

  return {
    // State
    isCreateMode,
    formData,
    quiz,
    categories,
    levels,
    quizTypes,
    
    // Loading states
    isLoadingQuiz,
    
    // Error states
    quizError,
    quizResponse,
    
    // Mutation states
    saveQuizMutation,
    
    // Actions
    handleInputChange,
    handleSubmit,
    getBackPath,
    navigate,
  }
}