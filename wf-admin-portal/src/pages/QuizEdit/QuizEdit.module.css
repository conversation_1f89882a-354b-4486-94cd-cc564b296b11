/**
 * QuizEdit CSS Module - Refactored with Shared Modules
 * Component-specific styles only, common patterns imported from shared modules
 */

/* QuizEdit-specific styles only */

/* Header Section - QuizEdit specific layout */
.headerTitleContainer {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
  margin-bottom: theme('spacing.2');
}

.headerBadge {
  composes: badgeBase from '@styles/shared/badges.module.css';
}

.headerBadgeNew {
  composes: headerBadge;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.headerBadgeEdit {
  composes: headerBadge;
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

.headerBadgeDraft {
  composes: headerBadge;
  background-color: theme('colors.yellow.100');
  color: theme('colors.yellow.800');
}

.headerBadgeActive {
  composes: headerBadge;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

/* Form Layout - QuizEdit specific complex form sections */
.formContainer {
  display: grid;
  grid-template-columns: 1fr;
  gap: theme('spacing.6');
}

@media (min-width: theme('screens.lg')) {
  .formContainer {
    grid-template-columns: 1fr 1fr;
  }
}

.formFieldsContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

/* Form Sections - QuizEdit specific section styling */
.formSection {
  composes: cardBase from '@styles/shared/cards.module.css';
  padding: theme('spacing.6');
}

.formSectionTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.4');
  padding-bottom: theme('spacing.2');
  border-bottom: 1px solid theme('colors.gray.200');
}

.formSectionDescription {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  margin-bottom: theme('spacing.4');
  line-height: theme('lineHeight.relaxed');
}

/* Form Field Grid - QuizEdit specific grid layout */
.formFieldGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: theme('spacing.4');
}

@media (min-width: theme('screens.md')) {
  .formFieldGrid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Difficulty Badge - QuizEdit specific difficulty indicators */
.difficultyBadge {
  composes: badgeBase from '@styles/shared/badges.module.css';
  margin-left: theme('spacing.2');
}

.difficultyBadge1,
.difficultyBadge2 {
  composes: difficultyBadge;
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.difficultyBadge3 {
  composes: difficultyBadge;
  background-color: theme('colors.yellow.100');
  color: theme('colors.yellow.800');
}

.difficultyBadge4,
.difficultyBadge5 {
  composes: difficultyBadge;
  background-color: theme('colors.red.100');
  color: theme('colors.red.800');
}

/* Preview Panel - QuizEdit specific preview functionality */
.previewPanel {
  background-color: theme('colors.gray.50');
  border: 1px solid theme('colors.gray.200');
  border-radius: theme('borderRadius.lg');
  padding: theme('spacing.6');
  position: sticky;
  top: theme('spacing.6');
}

.previewTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.4');
}

.previewCard {
  composes: cardBase from '@styles/shared/cards.module.css';
  padding: theme('spacing.4');
  box-shadow: theme('boxShadow.sm');
}

.previewCardTitle {
  font-size: theme('fontSize.base');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.2');
}

.previewCardDescription {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  line-height: theme('lineHeight.relaxed');
  margin-bottom: theme('spacing.3');
}

.previewCardMeta {
  display: flex;
  flex-wrap: wrap;
  gap: theme('spacing.2');
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.500');
}

.previewCardBadges {
  display: flex;
  flex-wrap: wrap;
  gap: theme('spacing.1');
  margin-top: theme('spacing.2');
}

.previewCardBadge {
  composes: badgeBase from '@styles/shared/badges.module.css';
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

/* Save Actions - QuizEdit specific action bar */
.saveActions {
  display: flex;
  justify-content: flex-end;
  gap: theme('spacing.3');
  padding: theme('spacing.4') theme('spacing.6');
  background-color: white;
  border-top: 1px solid theme('colors.gray.200');
  border-radius: 0 0 theme('borderRadius.lg') theme('borderRadius.lg');
  margin-top: theme('spacing.6');
}

.saveButtonIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
}

/* Validation Messages - QuizEdit specific validation styling */
.validationSummary {
  background-color: theme('colors.red.50');
  border: 1px solid theme('colors.red.200');
  border-radius: theme('borderRadius.md');
  padding: theme('spacing.4');
  margin-bottom: theme('spacing.4');
}

.validationSummaryTitle {
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.red.800');
  margin-bottom: theme('spacing.2');
}

.validationSummaryList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.validationSummaryItem {
  font-size: theme('fontSize.xs');
  color: theme('colors.red.700');
  padding: theme('spacing.1') 0;
}

/* Success Message - QuizEdit specific success styling */
.successMessage {
  background-color: theme('colors.green.50');
  border: 1px solid theme('colors.green.200');
  border-radius: theme('borderRadius.md');
  padding: theme('spacing.3') theme('spacing.4');
  margin-bottom: theme('spacing.4');
}

.successMessageText {
  font-size: theme('fontSize.sm');
  color: theme('colors.green.800');
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.successMessageIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
}

/* Progress Indicator - QuizEdit specific progress display */
.progressIndicator {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  padding: theme('spacing.2') theme('spacing.3');
  background-color: theme('colors.blue.50');
  border: 1px solid theme('colors.blue.200');
  border-radius: theme('borderRadius.md');
  font-size: theme('fontSize.sm');
  color: theme('colors.blue.800');
}

.progressIndicatorIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
  animation: spin 1s linear infinite;
}

/* Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(theme('spacing.2'));
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Responsive Adjustments - QuizEdit specific responsive behavior */
@media (max-width: theme('screens.sm')) {
  .formContainer {
    grid-template-columns: 1fr;
  }
  
  .formFieldGrid {
    grid-template-columns: 1fr;
  }
  
  .saveActions {
    flex-direction: column;
    gap: theme('spacing.2');
  }
}

/* Dark mode overrides for QuizEdit-specific elements */
:global(.dark) .formSection {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .formSectionTitle {
  color: white;
  border-bottom-color: theme('colors.gray.700');
}

:global(.dark) .formSectionDescription {
  color: theme('colors.gray.400');
}

:global(.dark) .previewPanel {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .previewTitle {
  color: white;
}

:global(.dark) .previewCard {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.600');
}

:global(.dark) .previewCardTitle {
  color: white;
}

:global(.dark) .previewCardDescription,
:global(.dark) .previewCardMeta {
  color: theme('colors.gray.300');
}

:global(.dark) .saveActions {
  background-color: theme('colors.gray.800');
  border-top-color: theme('colors.gray.700');
}

:global(.dark) .validationSummary {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: theme('colors.red.700');
}

:global(.dark) .validationSummaryTitle {
  color: theme('colors.red.300');
}

:global(.dark) .validationSummaryItem {
  color: theme('colors.red.400');
}

:global(.dark) .successMessage {
  background-color: rgba(34, 197, 94, 0.1);
  border-color: theme('colors.green.700');
}

:global(.dark) .successMessageText {
  color: theme('colors.green.300');
}

:global(.dark) .progressIndicator {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: theme('colors.blue.700');
  color: theme('colors.blue.300');
}

:global(.dark) .headerBadgeNew {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.300');
}

:global(.dark) .headerBadgeEdit {
  background-color: rgba(59, 130, 246, 0.2);
  color: theme('colors.blue.300');
}

:global(.dark) .headerBadgeDraft {
  background-color: rgba(245, 158, 11, 0.2);
  color: theme('colors.yellow.300');
}

:global(.dark) .headerBadgeActive {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.300');
}

:global(.dark) .difficultyBadge1,
:global(.dark) .difficultyBadge2 {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.300');
}

:global(.dark) .difficultyBadge3 {
  background-color: rgba(245, 158, 11, 0.2);
  color: theme('colors.yellow.300');
}

:global(.dark) .difficultyBadge4,
:global(.dark) .difficultyBadge5 {
  background-color: rgba(239, 68, 68, 0.2);
  color: theme('colors.red.300');
}

:global(.dark) .previewCardBadge {
  background-color: rgba(59, 130, 246, 0.2);
  color: theme('colors.blue.300');
}