import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Settings as SettingsIcon, User, Database, Shield } from 'lucide-react'
import { useSettingsHandler } from './Settings.handler'
import styles from './Settings.module.css'
import { sharedStyles } from '../../styles/shared'

export default function Settings() {
  const {
    userSettings,
    systemSettings,
    handleUserSettingsChange,
    handleSystemSettingsChange,
    handleSaveUserSettings,
    handleSaveSystemSettings,
    handleTestConnection,
    handleSecurityAction,
  } = useSettingsHandler()

  return (
    <div className={sharedStyles.layouts.container}>
      <div className={sharedStyles.layouts.header}>
        <h1 className={sharedStyles.layouts.headerTitle}>Settings</h1>
        <p className={sharedStyles.layouts.headerSubtitle}>
          Manage your admin portal configuration and preferences
        </p>
      </div>

      <div className={sharedStyles.layouts.gridResponsive}>
        {/* User Settings */}
        <Card className={sharedStyles.cards.cardBase}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={styles.cardTitleWithIcon}>
              <User className={styles.cardIcon} />
              <span>User Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.forms.formSection}>
            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Display Name
              </label>
              <input
                type="text"
                value={userSettings.displayName}
                onChange={(e) => handleUserSettingsChange('displayName', e.target.value)}
                className={sharedStyles.forms.fieldInput}
                placeholder="Enter your display name"
              />
            </div>
            
            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Email
              </label>
              <input
                type="email"
                value={userSettings.email}
                onChange={(e) => handleUserSettingsChange('email', e.target.value)}
                className={sharedStyles.forms.fieldInput}
                placeholder="<EMAIL>"
              />
            </div>
            
            <Button className={sharedStyles.buttons.buttonPrimary} onClick={handleSaveUserSettings}>Save Changes</Button>
          </CardContent>
        </Card>

        {/* Database Settings */}
        <Card className={sharedStyles.cards.cardBase}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={styles.cardTitleWithIcon}>
              <Database className={styles.cardIcon} />
              <span>Database Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.forms.formSection}>
            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Connection Status
              </label>
              <div className={styles.connectionStatus}>
                <div className={styles.statusDot}></div>
                <span className={styles.statusText}>Connected</span>
              </div>
            </div>
            
            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Database URL
              </label>
              <input
                type="text"
                className={sharedStyles.forms.fieldInput}
                value="https://wumsrmqsqtdwgzmxwpjn.supabase.co"
                readOnly
              />
            </div>
            
            <Button className={sharedStyles.buttons.buttonOutline} onClick={handleTestConnection}>
              Test Connection
            </Button>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card className={sharedStyles.cards.cardBase}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={styles.cardTitleWithIcon}>
              <Shield className={styles.cardIcon} />
              <span>Security Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.forms.formSection}>
            <div className={styles.securityItem}>
              <div className={styles.securityItemContent}>
                <h4 className={styles.securityItemTitle}>Two-Factor Authentication</h4>
                <p className={styles.securityItemDescription}>Add an extra layer of security</p>
              </div>
              <Button 
                className={sharedStyles.buttons.buttonOutline}
                onClick={() => handleSecurityAction('2FA')}
              >
                Enable
              </Button>
            </div>
            
            <div className={styles.securityItem}>
              <div className={styles.securityItemContent}>
                <h4 className={styles.securityItemTitle}>API Keys</h4>
                <p className={styles.securityItemDescription}>Manage API access keys</p>
              </div>
              <Button 
                className={sharedStyles.buttons.buttonOutline}
                onClick={() => handleSecurityAction('API Keys')}
              >
                Manage
              </Button>
            </div>
            
            <div className={styles.securityItem}>
              <div className={styles.securityItemContent}>
                <h4 className={styles.securityItemTitle}>Audit Logs</h4>
                <p className={styles.securityItemDescription}>View system activity logs</p>
              </div>
              <Button 
                className={sharedStyles.buttons.buttonOutline}
                onClick={() => handleSecurityAction('Audit Logs')}
              >
                View Logs
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* System Settings */}
        <Card className={sharedStyles.cards.cardBase}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={styles.cardTitleWithIcon}>
              <SettingsIcon className={styles.cardIcon} />
              <span>System Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.forms.formSection}>
            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Default Page Size
              </label>
              <select 
                className={sharedStyles.forms.fieldSelect}
                value={systemSettings.pageSize}
                onChange={(e) => handleSystemSettingsChange('pageSize', e.target.value)}
              >
                <option value="10">10 items per page</option>
                <option value="25">25 items per page</option>
                <option value="50">50 items per page</option>
                <option value="100">100 items per page</option>
              </select>
            </div>
            
            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Auto-validation
              </label>
              <select 
                className={sharedStyles.forms.fieldSelect}
                value={systemSettings.autoValidation}
                onChange={(e) => handleSystemSettingsChange('autoValidation', e.target.value)}
              >
                <option value="enabled">Enabled</option>
                <option value="disabled">Disabled</option>
              </select>
            </div>
            
            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Backup Frequency
              </label>
              <select 
                className={sharedStyles.forms.fieldSelect}
                value={systemSettings.backupFrequency}
                onChange={(e) => handleSystemSettingsChange('backupFrequency', e.target.value)}
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
            
            <Button className={sharedStyles.buttons.buttonPrimary} onClick={handleSaveSystemSettings}>Save Settings</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}