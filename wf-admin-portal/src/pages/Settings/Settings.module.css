/**
 * Settings CSS Module - Refactored with Shared Modules
 * Component-specific styles only, common patterns imported from shared modules
 */

/* Settings-specific styles only */

/* Card Title with Icon - Settings specific layout */
.cardTitleWithIcon {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.cardIcon {
  height: theme('spacing.5');
  width: theme('spacing.5');
}

/* Connection Status - Settings specific */
.connectionStatus {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.statusDot {
  width: theme('spacing.3');
  height: theme('spacing.3');
  background-color: theme('colors.green.500');
  border-radius: theme('borderRadius.full');
}

.statusText {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
}

/* Security Settings - Settings specific layout */
.securityItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.securityItemContent {
  flex-grow: 1;
}

.securityItemTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
}

.securityItemDescription {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
}

/* Dark mode overrides for Settings-specific elements */
:global(.dark) .statusText {
  color: theme('colors.gray.300');
}

:global(.dark) .securityItemTitle {
  color: white;
}

:global(.dark) .securityItemDescription {
  color: theme('colors.gray.300');
}