import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AlertTriangle, CheckCircle, XCircle, RefreshCw, Search } from 'lucide-react'
import { useContentValidationHandler } from './ContentValidation.handler'
import ValidationFixButton from './ValidationFixButton'
import ValidationIssueDetailsModal from './ValidationIssueDetailsModal/'
import QuestionFormModal from '../QuestionBank/QuestionFormModal'
import styles from './ContentValidation.module.css'
import { sharedStyles } from '../../styles/shared'

export default function ContentValidation() {
  const {
    // issues,
    // filteredIssues,
    highIssues,
    mediumIssues,
    lowIssues,
    currentTabIssues,
    stats,
    selectedIssue,
    selectedQuestion,
    isLoading,
    showDetailsModal,
    showQuestionEditor,
    fixingIssueId,
    activeTab,
    searchQuery,
    handleRevalidate,
    handleFixIssue,
    handleManualEdit,
    handleViewDetails,
    handleCloseDetailsModal,
    handleCloseQuestionEditor,
    handleSaveQuestion,
    handleTabChange,
    handleSearchChange,
  } = useContentValidationHandler()


  const severityIcons = {
    high: XCircle,
    medium: AlertTriangle,
    low: CheckCircle,
  }

  if (isLoading) {
    return (
      <div className={sharedStyles.layouts.container}>
        <div className={sharedStyles.layouts.header}>
          <h1 className={sharedStyles.layouts.headerTitle}>Content Validation</h1>
          <Button className={sharedStyles.buttons.buttonPrimary} disabled>
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            Validating...
          </Button>
        </div>
        <Card className={sharedStyles.cards.cardBase}>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.states.loadingContainer}>
              {[...Array(5)].map((_, i) => (
                <div key={i} className={sharedStyles.states.loadingSkeletonItem}></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={sharedStyles.layouts.container}>
      <div className={sharedStyles.layouts.header}>
        <div>
          <h1 className={sharedStyles.layouts.headerTitle}>Content Validation</h1>
          <p className={sharedStyles.layouts.headerSubtitle}>
            Automated checks for content quality and completeness
          </p>
        </div>
        <Button className={sharedStyles.buttons.buttonPrimary} onClick={handleRevalidate}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Re-validate
        </Button>
      </div>

      {/* Summary Cards */}
      <div className={sharedStyles.layouts.statsGrid}>
        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              Total Issues
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.cards.statCardValue}>{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              High Priority
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCardValueCritical}>
              {stats.high}
            </div>
          </CardContent>
        </Card>

        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              Medium Priority
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCardValueWarning}>
              {stats.medium}
            </div>
          </CardContent>
        </Card>

        <Card className={sharedStyles.cards.statCard}>
          <CardHeader className={sharedStyles.cards.cardHeader}>
            <CardTitle className={sharedStyles.cards.statCardTitle}>
              Low Priority
            </CardTitle>
          </CardHeader>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={styles.statCardValueInfo}>
              {stats.low}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter Section */}
      <Card className={sharedStyles.cards.cardBase}>
        <CardContent className={sharedStyles.cards.cardContent}>
          <div className={sharedStyles.forms.searchContainer}>
            <Search className={sharedStyles.forms.searchIcon} />
            <input
              type="text"
              placeholder="Search issues by message, type, or question ID..."
              value={searchQuery}
              onChange={handleSearchChange}
              className={sharedStyles.forms.searchInput}
            />
          </div>
        </CardContent>
      </Card>

      {/* Priority Tabs */}
      <Card className={sharedStyles.cards.cardBase}>
        <CardContent className={sharedStyles.cards.cardContent}>
          <div className={sharedStyles.layouts.toolbar}>
            <nav className={sharedStyles.layouts.navigation} aria-label="Tabs">
              <button
                onClick={() => handleTabChange('high')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'high'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className={styles.tabWithIcon}>
                  <XCircle className={styles.tabIcon} />
                  High Priority ({highIssues.length})
                </div>
              </button>
              <button
                onClick={() => handleTabChange('medium')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'medium'
                    ? 'border-yellow-500 text-yellow-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className={styles.tabWithIcon}>
                  <AlertTriangle className={styles.tabIcon} />
                  Medium Priority ({mediumIssues.length})
                </div>
              </button>
              <button
                onClick={() => handleTabChange('low')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'low'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className={styles.tabWithIcon}>
                  <CheckCircle className={styles.tabIcon} />
                  Low Priority ({lowIssues.length})
                </div>
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className={sharedStyles.cards.cardContent}>
            {currentTabIssues.length > 0 ? (
              <div className={styles.issuesListContainer}>
                {currentTabIssues.map((issue) => {
                  const SeverityIcon = severityIcons[issue.severity]
                  return (
                    <div
                      key={issue.id}
                      className={`${issue.severity === 'high' ? styles.issueItemCritical : issue.severity === 'medium' ? styles.issueItemWarning : styles.issueItemInfo}`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          <SeverityIcon className="h-5 w-5 mt-0.5" />
                          <div>
                            <h4 className="font-medium">{issue.message}</h4>
                            <p className="text-sm opacity-75 mt-1">
                              Type: {issue.type.replace('_', ' ')} • Entity: {issue.entity}
                            </p>
                            {issue.suggestions && issue.suggestions.length > 0 && (
                              <div className="mt-2">
                                <p className="text-sm font-medium">Suggestions:</p>
                                <ul className="text-sm opacity-75 list-disc list-inside">
                                  {issue.suggestions.map((suggestion, index) => (
                                    <li key={index}>{suggestion}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <ValidationFixButton
                            issue={issue}
                            onAutoFix={handleFixIssue}
                            onManualEdit={handleManualEdit}
                            onViewDetails={handleViewDetails}
                            isLoading={fixingIssueId === issue.id}
                          />
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className={sharedStyles.states.emptyStateWrapper}>
                <CheckCircle className={sharedStyles.states.emptyStateIcon} />
                <h3 className={sharedStyles.states.emptyStateTitle}>
                  {searchQuery ? 'No Matching Issues' : 'No Issues Found'}
                </h3>
                <p className={sharedStyles.states.emptyStateDescription}>
                  {searchQuery
                    ? 'Try adjusting your search terms or check other priority tabs.'
                    : `No ${activeTab} priority issues found.`
                  }
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>



      {/* Issue Details Modal */}
      {showDetailsModal && selectedIssue && (
        <ValidationIssueDetailsModal
          issue={selectedIssue}
          onClose={handleCloseDetailsModal}
          onAutoFix={handleFixIssue}
          onManualEdit={handleManualEdit}
          isLoading={fixingIssueId === selectedIssue.id}
        />
      )}

      {/* Question Editor Modal */}
      {showQuestionEditor && selectedQuestion && (
        <QuestionFormModal
          question={selectedQuestion}
          onSave={handleSaveQuestion}
          onClose={handleCloseQuestionEditor}
          isLoading={false}
        />
      )}
    </div>
  )
}