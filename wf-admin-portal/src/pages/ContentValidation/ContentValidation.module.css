/**
 * ContentValidation CSS Module - Refactored with Shared Modules
 * Component-specific styles only, common patterns imported from shared modules
 */

/* ContentValidation-specific styles only */

/* Stats Cards - specific styling for severity values */
.statCardValueCritical {
  composes: statCardValue from '@styles/shared/cards.module.css';
  color: theme('colors.red.600');
}

.statCardValueWarning {
  composes: statCardValue from '@styles/shared/cards.module.css';
  color: theme('colors.yellow.600');
}

.statCardValueInfo {
  composes: statCardValue from '@styles/shared/cards.module.css';
  color: theme('colors.blue.600');
}

.statCardValueSuccess {
  composes: statCardValue from '@styles/shared/cards.module.css';
  color: theme('colors.green.600');
}

/* Issue Items - ContentValidation specific issue styling */
.issueItem {
  composes: cardBase from '@styles/shared/cards.module.css';
  padding: theme('spacing.4');
  border: 1px solid;
}

.issueItemCritical {
  composes: issueItem;
  border-color: theme('colors.red.200');
  background-color: theme('colors.red.50');
}

.issueItemWarning {
  composes: issueItem;
  border-color: theme('colors.yellow.200');
  background-color: theme('colors.yellow.50');
}

.issueItemInfo {
  composes: issueItem;
  border-color: theme('colors.blue.200');
  background-color: theme('colors.blue.50');
}

/* Issues List Container */
.issuesListContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

/* Priority Tabs - ContentValidation specific tab styling */
.tabWithIcon {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.tabIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
}

/* Dark mode overrides for ContentValidation-specific elements */
:global(.dark) .statCardValueCritical {
  color: theme('colors.red.400');
}

:global(.dark) .statCardValueWarning {
  color: theme('colors.yellow.400');
}

:global(.dark) .statCardValueInfo {
  color: theme('colors.blue.400');
}

:global(.dark) .statCardValueSuccess {
  color: theme('colors.green.400');
}

:global(.dark) .issueItemCritical {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: theme('colors.red.800');
}

:global(.dark) .issueItemWarning {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: theme('colors.yellow.800');
}

:global(.dark) .issueItemInfo {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: theme('colors.blue.800');
}