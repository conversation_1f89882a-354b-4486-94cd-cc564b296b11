import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { serviceContainer } from '@/services'
import { useToast } from '@/hooks/use-toast'
import type { ValidationIssue } from '@/types'
import type { QuestionWithRelations } from 'wf-shared/types'

export const useContentValidationHandler = () => {
  const [selectedIssue, setSelectedIssue] = useState<ValidationIssue | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [showQuestionEditor, setShowQuestionEditor] = useState(false)
  const [selectedQuestion, setSelectedQuestion] = useState<QuestionWithRelations | null>(null)
  const [fixingIssueId, setFixingIssueId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'high' | 'medium' | 'low'>('high')
  const [searchQuery, setSearchQuery] = useState('')
  const { toast } = useToast()

  const { data: issuesResponse, isLoading, refetch } = useQuery({
    queryKey: ['validation-issues'],
    queryFn: () => serviceContainer.validationService.validateContent(),
  })

  // Extract the actual data from service response
  const issues = issuesResponse?.data

  // Filter issues based on search query
  const filteredIssues = issues?.filter(issue =>
    issue.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
    issue.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
    issue.entityId.toLowerCase().includes(searchQuery.toLowerCase()),
  ) || []

  // Separate issues by severity
  const highIssues = filteredIssues.filter(issue => issue.severity === 'high')
  const mediumIssues = filteredIssues.filter(issue => issue.severity === 'medium')
  const lowIssues = filteredIssues.filter(issue => issue.severity === 'low')

  // Get current tab issues
  const currentTabIssues = activeTab === 'high' ? highIssues :
                          activeTab === 'medium' ? mediumIssues : lowIssues

  // Calculate stats
  const stats = {
    total: issues?.length || 0,
    high: issues?.filter(issue => issue.severity === 'high').length || 0,
    medium: issues?.filter(issue => issue.severity === 'medium').length || 0,
    low: issues?.filter(issue => issue.severity === 'low').length || 0,
  }

  // Handle tab change
  const handleTabChange = (tab: 'high' | 'medium' | 'low') => {
    setActiveTab(tab)
  }

  // Handle search
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  const handleRevalidate = (): void => {
    refetch()
  }

  const handleFixIssue = async (issueId: string): Promise<void> => {
    setFixingIssueId(issueId)
    try {
      const response = await serviceContainer.validationService.fixIssue(issueId)
      if (response.data) {
        toast({
          title: 'Success',
          description: 'Issue fixed successfully',
        })
        refetch() // Refresh issues after fixing
      } else {
        toast({
          title: 'Info',
          description: 'This issue requires manual intervention',
          variant: 'default',
        })
      }
    } catch (error) {
      console.error('Failed to fix issue:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to fix issue',
        variant: 'destructive',
      })
    } finally {
      setFixingIssueId(null)
    }
  }

  const handleManualEdit = async (_issueId: string, entityId: string): Promise<void> => {
    try {
      // Get the question data for editing
      const response = await serviceContainer.questionService.getQuestions({
        page: 1,
        pageSize: 1000,
      })

      if (response.data) {
        const question = response.data.data.find(q => q.id === entityId)
        if (question) {
          setSelectedQuestion(question)
          setShowQuestionEditor(true)
        } else {
          toast({
            title: 'Error',
            description: 'Question not found',
            variant: 'destructive',
          })
        }
      }
    } catch (error) {
      console.error('Failed to load question for editing:', error)
      toast({
        title: 'Error',
        description: 'Failed to load question for editing',
        variant: 'destructive',
      })
    }
  }

  const handleViewDetails = (issue: ValidationIssue): void => {
    setSelectedIssue(issue)
    setShowDetailsModal(true)
  }

  const handleCloseDetailsModal = (): void => {
    setShowDetailsModal(false)
    setSelectedIssue(null)
  }

  const handleCloseQuestionEditor = (): void => {
    setShowQuestionEditor(false)
    setSelectedQuestion(null)
  }

  const handleSaveQuestion = async (questionData: any): Promise<void> => {
    try {
      if (selectedQuestion) {
        const response = await serviceContainer.questionService.updateQuestion(selectedQuestion.id, {
          question_text: questionData.question_text,
          question_type_id: questionData.question_type_id,
          difficulty_level: questionData.difficulty_level,
          is_active: questionData.is_active,
        })

        if (response.error) {
          throw new Error(response.error)
        }

        toast({
          title: 'Success',
          description: 'Question updated successfully',
        })

        handleCloseQuestionEditor()
        refetch() // Refresh validation issues
      }
    } catch (error) {
      console.error('Failed to save question:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save question',
        variant: 'destructive',
      })
    }
  }

  // Remove duplicate grouping - already done above

  return {
    // Data
    issues,
    filteredIssues,
    highIssues,
    mediumIssues,
    lowIssues,
    currentTabIssues,
    stats,
    selectedIssue,
    selectedQuestion,

    // State
    isLoading,
    showDetailsModal,
    showQuestionEditor,
    fixingIssueId,
    activeTab,
    searchQuery,

    // Actions
    handleRevalidate,
    handleFixIssue,
    handleManualEdit,
    handleViewDetails,
    handleCloseDetailsModal,
    handleCloseQuestionEditor,
    handleSaveQuestion,
    handleTabChange,
    handleSearchChange,
  }
}