import { Button } from '@/components/ui/button'
import { Edit, Info, CheckCircle } from 'lucide-react'
import type { ValidationIssue } from '@/types'

interface ValidationFixButtonProps {
  issue: ValidationIssue
  onAutoFix: (issueId: string) => Promise<void>
  onManualEdit: (issueId: string, entityId: string) => void
  onViewDetails: (issue: ValidationIssue) => void
  isLoading?: boolean
}

export default function ValidationFixButton({
  issue,
  onAutoFix,
  onManualEdit,
  onViewDetails,
  isLoading = false,
}: ValidationFixButtonProps) {
  const getAutoFixCapability = (issue: ValidationIssue) => {
    switch (issue.type) {
      case 'missing_explanation':
        return {
          canAutoFix: true,
          description: 'Create placeholder explanation',
        }
      case 'orphaned_data':
        return {
          canAutoFix: true,
          description: 'Remove orphaned data',
        }
      case 'invalid_option':
        return {
          canAutoFix: false,
          description: 'Requires manual review',
        }
      default:
        return {
          canAutoFix: false,
          description: 'Manual fix required',
        }
    }
  }

  const autoFixInfo = getAutoFixCapability(issue)

  const handleAutoFix = async () => {
    await onAutoFix(issue.id)
  }

  const handleManualEdit = () => {
    onManualEdit(issue.id, issue.entityId)
  }

  const handleViewDetails = () => {
    onViewDetails(issue)
  }

  // For simple auto-fixable issues, show a single auto-fix button
  if (autoFixInfo.canAutoFix && issue.type === 'orphaned_data') {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={handleAutoFix}
        disabled={isLoading}
        className="text-green-600 hover:text-green-700 border-green-200 hover:border-green-300"
      >
        {isLoading ? (
          <>
            <CheckCircle className="h-4 w-4 mr-1 animate-spin" />
            Fixing...
          </>
        ) : (
          <>
            <CheckCircle className="h-4 w-4 mr-1" />
            Auto-Fix
          </>
        )}
      </Button>
    )
  }

  // For complex issues, show multiple buttons
  return (
    <div className="flex space-x-2">
      {autoFixInfo.canAutoFix && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleAutoFix}
          disabled={isLoading}
          className="text-green-600 hover:text-green-700 border-green-200 hover:border-green-300"
        >
          {isLoading ? (
            <>
              <CheckCircle className="h-4 w-4 mr-1 animate-spin" />
              Fixing...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-1" />
              Auto-Fix
            </>
          )}
        </Button>
      )}

      <Button
        variant="outline"
        size="sm"
        onClick={handleManualEdit}
        disabled={isLoading}
        className="text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300"
      >
        <Edit className="h-4 w-4 mr-1" />
        Edit
      </Button>

      <Button
        variant="outline"
        size="sm"
        onClick={handleViewDetails}
        disabled={isLoading}
        className="text-gray-600 hover:text-gray-700 border-gray-200 hover:border-gray-300"
      >
        <Info className="h-4 w-4 mr-1" />
        Details
      </Button>
    </div>
  )
}
