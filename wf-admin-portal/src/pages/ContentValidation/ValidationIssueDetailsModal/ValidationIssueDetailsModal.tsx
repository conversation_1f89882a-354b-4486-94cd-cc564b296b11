import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { X, AlertTriangle, XCircle, CheckCircle, Lightbulb, Edit, Wrench } from 'lucide-react'
import { useValidationIssueDetailsModalHandler } from './ValidationIssueDetailsModal.handler'
import styles from './ValidationIssueDetailsModal.module.css'
import type { ValidationIssue } from '@/types'

export interface ValidationIssueDetailsModalProps {
  issue: ValidationIssue
  onClose: () => void
  onAutoFix?: (issueId: string) => Promise<void>
  onManualEdit?: (issueId: string, entityId: string) => void
  isLoading?: boolean
}

export default function ValidationIssueDetailsModal({ 
  issue, 
  onClose, 
  onAutoFix, 
  onManualEdit,
  isLoading = false, 
}: ValidationIssueDetailsModalProps) {
  const {
    Icon,
    typeInfo,
    hasSuggestions,
    canAutoFix,
    canManualEdit,
    handleAutoFix,
    handleManualEdit,
  } = useValidationIssueDetailsModalHandler({ 
    issue, 
    onAutoFix, 
    onManualEdit, 
    onClose, 
  })

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2 className={styles.title}>Issue Details</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Issue Summary */}
        <Card className={`${styles.issueCard} ${styles[`border${issue.severity.charAt(0).toUpperCase() + issue.severity.slice(1)}`]}`}>
          <CardHeader className={`${styles.issueHeader} ${styles[`bg${issue.severity.charAt(0).toUpperCase() + issue.severity.slice(1)}`]}`}>
            <CardTitle className={styles.issueTitle}>
              <Icon className={`h-6 w-6 ${styles[`color${issue.severity.charAt(0).toUpperCase() + issue.severity.slice(1)}`]}`} />
              <div>
                <div className={styles.issueTitleText}>{typeInfo.title}</div>
                <div className={styles.issueSubtitle}>
                  {issue.severity} Priority • {issue.entity}
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className={styles.issueContent}>
            <p className={styles.issueMessage}>{issue.message}</p>
            <div className={styles.entityId}>
              <span className={styles.entityIdLabel}>Entity ID:</span> {issue.entityId}
            </div>
          </CardContent>
        </Card>

        {/* Detailed Information */}
        <div className={styles.infoCardsContainer}>
          <Card className={styles.infoCard}>
            <CardHeader className={styles.infoCardHeader}>
              <CardTitle className={styles.infoCardTitle}>
                <AlertTriangle className={`${styles.infoCardIcon} ${styles.alertIcon}`} />
                Description
              </CardTitle>
            </CardHeader>
            <CardContent className={styles.infoCardContent}>
              <p className={styles.infoCardText}>{typeInfo.description}</p>
            </CardContent>
          </Card>

          <Card className={styles.infoCard}>
            <CardHeader className={styles.infoCardHeader}>
              <CardTitle className={styles.infoCardTitle}>
                <XCircle className={`${styles.infoCardIcon} ${styles.impactIcon}`} />
                Impact
              </CardTitle>
            </CardHeader>
            <CardContent className={styles.infoCardContent}>
              <p className={styles.infoCardText}>{typeInfo.impact}</p>
            </CardContent>
          </Card>

          {hasSuggestions && (
            <Card className={styles.infoCard}>
              <CardHeader className={styles.infoCardHeader}>
                <CardTitle className={styles.infoCardTitle}>
                  <Lightbulb className={`${styles.infoCardIcon} ${styles.suggestionIcon}`} />
                  Suggestions
                </CardTitle>
              </CardHeader>
              <CardContent className={styles.infoCardContent}>
                <ul className={styles.suggestionsList}>
                  {issue.suggestions!.map((suggestion, index) => (
                    <li key={index} className={styles.suggestionItem}>{suggestion}</li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Action Buttons */}
        <div className={styles.actions}>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          
          {canManualEdit && (
            <Button 
              variant="outline" 
              onClick={handleManualEdit}
              className={`${styles.actionButton} ${styles.manualEditButton}`}
            >
              <Edit className={styles.actionButtonIcon} />
              Edit Manually
            </Button>
          )}
          
          {canAutoFix && (
            <Button 
              onClick={handleAutoFix}
              disabled={isLoading}
              className={`${styles.actionButton} ${styles.autoFixButton}`}
            >
              {isLoading ? (
                <>
                  <Wrench className={`${styles.actionButtonIcon} ${styles.loadingIcon}`} />
                  Applying Fix...
                </>
              ) : (
                <>
                  <Wrench className={styles.actionButtonIcon} />
                  Apply Auto-Fix
                </>
              )}
            </Button>
          )}
        </div>

        {/* Auto-fix Information */}
        {typeInfo.autoFixable && (
          <div className={styles.autoFixInfo}>
            <div className={styles.autoFixInfoContent}>
              <CheckCircle className={styles.autoFixInfoIcon} />
              <div>
                <div className={styles.autoFixInfoTitle}>Auto-Fix Available</div>
                <div className={styles.autoFixInfoDescription}>{typeInfo.autoFixDescription}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}