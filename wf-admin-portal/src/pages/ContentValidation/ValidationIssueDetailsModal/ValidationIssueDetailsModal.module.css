/* Shared modal styles */
@import '@styles/shared/modal.module.css';

/* Use shared styles */
.overlay {
  composes: overlay from '@styles/shared/modal.module.css';
}

.modal {
  composes: modalLarge from '@styles/shared/modal.module.css';
}

.header {
  composes: header from '@styles/shared/modal.module.css';
}

.title {
  composes: title from '@styles/shared/modal.module.css';
}

.actions {
  composes: actions from '@styles/shared/modal.module.css';
}

/* Issue-specific styles */
.issueCard {
  margin-bottom: 1.5rem;
}

.issueHeader {
  padding: 1rem;
}

.issueTitle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.issueTitleText {
  font-size: 1.125rem;
}

.issueSubtitle {
  font-size: 0.875rem;
  font-weight: 400;
  color: rgb(75, 85, 99);
  text-transform: capitalize;
}

.issueContent {
  padding-top: 1rem;
}

.issueMessage {
  color: rgb(55, 65, 81);
  margin-bottom: 0.75rem;
}

.entityId {
  font-size: 0.875rem;
  color: rgb(107, 114, 128);
}

.entityIdLabel {
  font-weight: 600;
}

/* Information cards */
.infoCardsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.infoCard {
  border: 1px solid rgb(229, 231, 235);
  border-radius: 0.5rem;
  background-color: white;
}

.infoCardHeader {
  padding: 1rem 1rem 0 1rem;
}

.infoCardTitle {
  font-size: 1.125rem;
  display: flex;
  align-items: center;
}

.infoCardIcon {
  height: 1.25rem;
  width: 1.25rem;
  margin-right: 0.5rem;
}

.infoCardContent {
  padding: 0 1rem 1rem 1rem;
}

.infoCardText {
  color: rgb(55, 65, 81);
}

/* Suggestions list */
.suggestionsList {
  list-style-type: disc;
  list-style-position: inside;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.suggestionItem {
  color: rgb(55, 65, 81);
}

/* Action buttons */
.actionButton {
  display: flex;
  align-items: center;
}

.actionButtonIcon {
  height: 1rem;
  width: 1rem;
  margin-right: 0.5rem;
}

.autoFixButton {
  background-color: rgb(22, 163, 74);
  color: white;
}

.autoFixButton:hover {
  background-color: rgb(21, 128, 61);
}

.manualEditButton {
  color: rgb(37, 99, 235);
  border-color: rgb(191, 219, 254);
}

.manualEditButton:hover {
  color: rgb(29, 78, 216);
  border-color: rgb(147, 197, 253);
}

.loadingIcon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Auto-fix information */
.autoFixInfo {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: rgb(240, 253, 244);
  border: 1px solid rgb(187, 247, 208);
  border-radius: 0.5rem;
}

.autoFixInfoContent {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.autoFixInfoIcon {
  height: 1.25rem;
  width: 1.25rem;
  color: rgb(22, 163, 74);
  margin-top: 0.125rem;
}

.autoFixInfoTitle {
  font-weight: 500;
  color: rgb(20, 83, 45);
}

.autoFixInfoDescription {
  font-size: 0.875rem;
  color: rgb(21, 128, 61);
}

/* Severity-specific border colors */
.borderHigh {
  border-color: rgb(254, 202, 202);
}

.borderMedium {
  border-color: rgb(254, 240, 138);
}

.borderLow {
  border-color: rgb(191, 219, 254);
}

/* Severity-specific background colors */
.bgHigh {
  background-color: rgb(254, 242, 242);
}

.bgMedium {
  background-color: rgb(254, 249, 195);
}

.bgLow {
  background-color: rgb(239, 246, 255);
}

/* Severity-specific text colors */
.colorHigh {
  color: rgb(220, 38, 38);
}

.colorMedium {
  color: rgb(217, 119, 6);
}

.colorLow {
  color: rgb(37, 99, 235);
}

/* Icon colors for info cards */
.alertIcon {
  color: rgb(249, 115, 22);
}

.impactIcon {
  color: rgb(239, 68, 68);
}

.suggestionIcon {
  color: rgb(234, 179, 8);
}

/* Dark mode support - component-specific styles */
:global(.dark) .infoCard {
  background-color: rgb(55, 65, 81);
  border-color: rgb(75, 85, 99);
}

:global(.dark) .issueMessage {
  color: rgb(209, 213, 219);
}

:global(.dark) .entityId {
  color: rgb(156, 163, 175);
}

:global(.dark) .infoCardText {
  color: rgb(209, 213, 219);
}

:global(.dark) .suggestionItem {
  color: rgb(209, 213, 219);
}

:global(.dark) .issueSubtitle {
  color: rgb(156, 163, 175);
}

:global(.dark) .autoFixInfo {
  background-color: rgb(20, 83, 45);
  border-color: rgb(34, 197, 94);
}

:global(.dark) .autoFixInfoTitle {
  color: rgb(187, 247, 208);
}

:global(.dark) .autoFixInfoDescription {
  color: rgb(134, 239, 172);
}