import type { ValidationIssue } from '@/types'
import { XCir<PERSON>, AlertTriangle, CheckCircle } from 'lucide-react'

// Types
export interface ValidationIssueDetailsModalHandlerProps {
  issue: ValidationIssue
  onAutoFix?: (issueId: string) => Promise<void>
  onManualEdit?: (issueId: string, entityId: string) => void
  onClose: () => void
}

// Configuration types
interface SeverityConfig {
  icon: any
  color: string
  bgColor: string
  borderColor: string
}

interface IssueTypeInfo {
  title: string
  description: string
  impact: string
  autoFixable: boolean
  autoFixDescription: string
}

export const useValidationIssueDetailsModalHandler = ({ 
  issue, 
  onAutoFix, 
  onManualEdit, 
  onClose, 
}: ValidationIssueDetailsModalHandlerProps) => {
  
  // Severity configuration
  const severityConfig: Record<string, SeverityConfig> = {
    high: {
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
    },
    medium: {
      icon: AlertTriangle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
    },
    low: {
      icon: CheckCircle,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
    },
  }

  // Issue type descriptions
  const getIssueTypeDescription = (type: string): IssueTypeInfo => {
    switch (type) {
      case 'missing_explanation':
        return {
          title: 'Missing Explanation',
          description: 'This question does not have an explanation to help students understand the correct answer.',
          impact: 'Students may not understand why their answer was wrong, reducing learning effectiveness.',
          autoFixable: true,
          autoFixDescription: 'Creates a placeholder explanation that can be edited later.',
        }
      case 'invalid_option':
        return {
          title: 'Invalid Options',
          description: 'This question has too few answer options or invalid option configuration.',
          impact: 'Questions with insufficient options may not provide adequate challenge or may confuse students.',
          autoFixable: false,
          autoFixDescription: 'Requires manual review to add appropriate options.',
        }
      case 'orphaned_data':
        return {
          title: 'Orphaned Data',
          description: 'This data exists without proper relationships to other entities.',
          impact: 'Orphaned data can cause display issues and database inconsistencies.',
          autoFixable: true,
          autoFixDescription: 'Safely removes the orphaned data from the database.',
        }
      default:
        return {
          title: 'Data Issue',
          description: 'A data quality issue has been detected.',
          impact: 'This may affect the user experience or system functionality.',
          autoFixable: false,
          autoFixDescription: 'Requires manual investigation and resolution.',
        }
    }
  }

  // Computed values
  const config = severityConfig[issue.severity]
  const Icon = config.icon
  const typeInfo = getIssueTypeDescription(issue.type)

  // Event handlers
  const handleAutoFix = async () => {
    if (onAutoFix) {
      await onAutoFix(issue.id)
      onClose()
    }
  }

  const handleManualEdit = () => {
    if (onManualEdit) {
      onManualEdit(issue.id, issue.entityId)
      onClose()
    }
  }

  // Return handler interface
  return {
    // Data
    config,
    Icon,
    typeInfo,
    issue,
    
    // Computed values
    hasSuggestions: issue.suggestions && issue.suggestions.length > 0,
    canAutoFix: typeInfo.autoFixable && !!onAutoFix,
    canManualEdit: !!onManualEdit,
    
    // Actions
    handleAutoFix,
    handleManualEdit,
    onClose,
  }
}