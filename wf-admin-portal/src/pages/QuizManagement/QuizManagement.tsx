import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { formatDate } from 'wf-shared/utils'
import { Plus, Edit, Trash2, Eye, FileText } from 'lucide-react'
import { useQuizManagementHandler } from './QuizManagement.handler'
import styles from './QuizManagement.module.css'
import { sharedStyles } from '../../styles/shared'
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog'
import { Pagination } from '@/components/ui/pagination'


export default function QuizManagement() {
  const {
    quizzes,
    categories,
    levels,
    page,
    filters,
    isInitialLoading,
    isRefetching,
    error,
    hasFilters,
    deleteConfirmation,
    isDeleting,
    categoriesLoading,
    levelsLoading,
    handleFilterChange,
    clearFilters,
    handlePageChange,
    handleCreateQuiz,
    handleViewQuiz,
    handleEditQuiz,
    handleDeleteQuiz,
    handleConfirmDelete,
    handleCloseDeleteConfirmation,
  } = useQuizManagementHandler()




  if (error) {
    return (
      <div className={sharedStyles.layouts.container}>
        <div className={sharedStyles.layouts.header}>
          <h1 className={sharedStyles.layouts.headerTitle}>Quiz Management</h1>
          <Button className={sharedStyles.buttons.buttonPrimary} onClick={handleCreateQuiz}>
            <Plus className="h-4 w-4 mr-2" />
            Create Quiz
          </Button>
        </div>
        <Card className={sharedStyles.cards.cardBase}>
          <CardContent className={sharedStyles.cards.cardContent}>
            <div className={sharedStyles.states.errorContainer}>
              <div className={sharedStyles.states.errorIconWrapper}>
                <Trash2 className={sharedStyles.states.errorIcon} />
              </div>
              <h3 className={sharedStyles.states.errorTitle}>Error Loading Quizzes</h3>
              <p className={sharedStyles.states.errorDescription}>
                {error instanceof Error ? error.message : 'An unexpected error occurred'}
              </p>
              <Button 
                className={sharedStyles.buttons.buttonOutline}
                onClick={() => window.location.reload()}
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={sharedStyles.layouts.container}>
      <div className={sharedStyles.layouts.header}>
        <div>
          <h1 className={sharedStyles.layouts.headerTitle}>Quiz Management</h1>
          <p className={sharedStyles.layouts.headerSubtitle}>
            Manage your quiz content and settings
          </p>
        </div>
        <div className={styles.headerActions}>
          <Button className={sharedStyles.buttons.buttonPrimary} onClick={handleCreateQuiz}>
            <Plus className="h-4 w-4 mr-2" />
            Create Quiz
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className={sharedStyles.cards.cardBase}>
        <CardHeader className={sharedStyles.cards.cardHeader}>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent className={sharedStyles.cards.cardContent}>
          <div className={sharedStyles.forms.filtersGrid}>
            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Category
              </label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className={sharedStyles.forms.fieldSelect}
                disabled={categoriesLoading}
              >
                <option value="">
                  {categoriesLoading ? 'Loading categories...' : 'All Categories'}
                </option>
                {categories?.map((category) => (
                  <option key={category.id} value={category.key}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Level
              </label>
              <select
                value={filters.level}
                onChange={(e) => handleFilterChange('level', e.target.value)}
                className={sharedStyles.forms.fieldSelect}
                disabled={levelsLoading}
              >
                <option value="">
                  {levelsLoading ? 'Loading levels...' : 'All Levels'}
                </option>
                {levels?.map((level) => (
                  <option key={level.id} value={level.key}>
                    {level.name}
                  </option>
                ))}
              </select>
            </div>

            <div className={sharedStyles.forms.formField}>
              <label className={sharedStyles.forms.fieldLabel}>
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className={sharedStyles.forms.fieldSelect}
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div className="flex items-end">
              <Button
                className={styles.clearButton}
                onClick={clearFilters}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quiz List */}
      <Card className={sharedStyles.cards.cardBase}>
        <CardHeader className={sharedStyles.cards.cardHeader}>
          <CardTitle>
            Quizzes ({quizzes?.count || 0})
          </CardTitle>
        </CardHeader>
        <CardContent className={sharedStyles.cards.cardContent}>
          {isInitialLoading || isRefetching ? (
            <div className={sharedStyles.states.loadingContainer}>
              {[...Array(5)].map((_, i) => (
                <div key={i} className={sharedStyles.states.loadingSkeletonItem}></div>
              ))}
            </div>
          ) : quizzes?.data && quizzes.data.length > 0 ? (
            <div className={styles.quizListContainer}>
              {quizzes.data.map((quiz) => (
                <div key={quiz.id} className={styles.quizItem}>
                  <div className={styles.quizContent}>
                    <h3 className={styles.quizTitle}>{quiz.title}</h3>
                    <div className={styles.quizMetadata}>
                      <span className={styles.badgeCategory}>
                        {Array.isArray(quiz.categories) ? quiz.categories[0]?.name : quiz.categories?.name}
                      </span>
                      <span className={styles.badgeLevel}>
                        {Array.isArray(quiz.levels) ? quiz.levels[0]?.name : quiz.levels?.name}
                      </span>
                      {quiz.quiz_types && (
                        <span className={styles.badgeQuizType}>
                          {Array.isArray(quiz.quiz_types) ? quiz.quiz_types[0]?.name : quiz.quiz_types?.name}
                        </span>
                      )}
                      <span className={
                        quiz.is_active 
                          ? styles.badgeStatusActive
                          : styles.badgeStatusInactive
                      }>
                        {quiz.is_active ? 'Active' : 'Inactive'}
                      </span>
                      <span className="text-gray-400">
                        {quiz.total_questions} questions • Updated {formatDate(quiz.updated_at || quiz.created_at || '')}
                      </span>
                    </div>
                  </div>
                  
                  <div className={styles.quizActions}>
                    <Button
                      className={styles.quizActionButton}
                      onClick={() => handleViewQuiz(quiz.id)}
                      title="View quiz details"
                      aria-label={`View quiz: ${quiz.title}`}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      className={styles.quizActionButton}
                      onClick={() => handleEditQuiz(quiz)}
                      title="Edit quiz"
                      aria-label={`Edit quiz: ${quiz.title}`}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      className={`${styles.quizActionButton} ${styles.quizActionButtonDanger}`}
                      onClick={() => handleDeleteQuiz(quiz)}
                      disabled={isDeleting}
                      title="Delete quiz"
                      aria-label={`Delete quiz: ${quiz.title}`}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className={sharedStyles.states.emptyStateWrapper}>
              <FileText className={sharedStyles.states.emptyStateIcon} />
              <h3 className={sharedStyles.states.emptyStateTitle}>No Quizzes Found</h3>
              <p className={sharedStyles.states.emptyStateDescription}>
                {hasFilters 
                  ? 'No quizzes match your current filters' 
                  : 'Get started by creating your first quiz'}
              </p>
              <Button className={sharedStyles.buttons.buttonPrimary} onClick={handleCreateQuiz}>
                <Plus className="h-4 w-4 mr-2" />
                Create Quiz
              </Button>
            </div>
          )}

          {/* Pagination */}
          <Pagination
            currentPage={page}
            totalPages={quizzes?.totalPages || 0}
            onPageChange={handlePageChange}
          />
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={handleCloseDeleteConfirmation}
        onConfirm={handleConfirmDelete}
        title="Delete Quiz"
        description={`Are you sure you want to delete "${deleteConfirmation.quizTitle}"? This action cannot be undone and will permanently remove the quiz and all its questions.`}
        confirmText="Delete Quiz"
        cancelText="Cancel"
        variant="danger"
        isLoading={isDeleting}
      />
    </div>
  )
}