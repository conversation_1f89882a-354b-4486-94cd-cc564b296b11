import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { serviceContainer } from '@/services'
import { X, Wand2, Info } from 'lucide-react'

interface QuizFormModalProps {
  quiz?: any
  onSave: (quizData: any) => void
  onClose: () => void
  isLoading: boolean
}

export default function QuizFormModal({ quiz, onSave, onClose, isLoading }: QuizFormModalProps) {
  const isEdit = !!quiz
  
  const [formData, setFormData] = useState({
    title: quiz?.title || '',
    description: quiz?.description || '',
    instructions: quiz?.instructions || '',
    key: quiz?.key || '',
    category_id: quiz?.category_id || '',
    level_id: quiz?.level_id || '',
    quiz_type_id: quiz?.quiz_type_id || '',
    difficulty_level: quiz?.difficulty_level || 3, // Default to medium difficulty
    total_questions: quiz?.total_questions || 15, // Default to 15 questions
    time_limit_minutes: quiz?.time_limit_minutes || 30,
    is_active: quiz?.is_active ?? true,
    subcategory_id: quiz?.subcategory_id || '',
  })

  // Track if fields have been modified by the user
  const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({})

  // Track if we should show the key generation helper
  const [showKeyHelper, setShowKeyHelper] = useState(!isEdit && !formData.key)

  const [errors, setErrors] = useState<Record<string, string>>({})

  const { data: categories } = useQuery({
    queryKey: ['categories'],
    queryFn: () => serviceContainer.categoryService.getCategories(),
  })

  const { data: levels } = useQuery({
    queryKey: ['levels'],
    queryFn: () => serviceContainer.levelService.getLevels(),
  })

  const { data: quizTypes } = useQuery({
    queryKey: ['quiz-types'],
    queryFn: () => serviceContainer.quizTypeService.getQuizTypes(),
  })

  // Temporarily disabled subcategories
  // const { data: subcategories } = useQuery({
  //   queryKey: ['subcategories', formData.category_id],
  //   queryFn: () => serviceContainer.subcategoryService.getSubcategoriesByCategory(formData.category_id),
  //   enabled: !!formData.category_id,
  // })

  // Auto-generate quiz key when category, level, or title changes
  useEffect(() => {
    if (!isEdit && !touchedFields.key && formData.category_id && formData.level_id) {
      const category = categories?.data?.find((c: any) => c.id === formData.category_id)
      const level = levels?.data?.find((l: any) => l.id === formData.level_id)

      if (category && level) {
        // Generate a key like: wf_quiz_a1_001, vocab_quiz_b2_001, etc.
        const categoryPrefix = category.key === 'word_formation' ? 'wf' : category.key
        const levelKey = level.key.toLowerCase()

        // Generate a simple incremental number
        const timestamp = Date.now().toString().slice(-3)
        const generatedKey = `${categoryPrefix}_quiz_${levelKey}_${timestamp}`

        setFormData(prev => ({ ...prev, key: generatedKey }))
        setShowKeyHelper(false)
      }
    }
  }, [formData.category_id, formData.level_id, categories, levels, isEdit, touchedFields.key])

  const handleInputChange = (field: string, value: string | number | boolean | null) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setTouchedFields(prev => ({ ...prev, [field]: true }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }

    // Reset subcategory when category changes
    if (field === 'category_id' && formData.subcategory_id) {
      setFormData(prev => ({ ...prev, subcategory_id: '' }))
    }
  }

  const generateQuizKey = () => {
    const category = categories?.data?.find((c: any) => c.id === formData.category_id)
    const level = levels?.data?.find((l: any) => l.id === formData.level_id)

    if (category && level) {
      const categoryPrefix = category.key === 'word_formation' ? 'wf' : category.key
      const levelKey = level.key.toLowerCase()
      const timestamp = Date.now().toString().slice(-3)
      const generatedKey = `${categoryPrefix}_quiz_${levelKey}_${timestamp}`

      setFormData(prev => ({ ...prev, key: generatedKey }))
      setTouchedFields(prev => ({ ...prev, key: true }))
      setShowKeyHelper(false)
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Required field validation
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    } else if (formData.title.length < 3) {
      newErrors.title = 'Title must be at least 3 characters'
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters'
    }

    if (!formData.key.trim()) {
      newErrors.key = 'Key is required'
    } else if (!/^[a-z0-9_]+$/.test(formData.key)) {
      newErrors.key = 'Key must contain only lowercase letters, numbers, and underscores'
    } else if (formData.key.length < 3) {
      newErrors.key = 'Key must be at least 3 characters'
    }

    if (!formData.category_id) newErrors.category_id = 'Category is required'
    if (!formData.level_id) newErrors.level_id = 'Level is required'

    if (formData.difficulty_level < 1 || formData.difficulty_level > 5) {
      newErrors.difficulty_level = 'Difficulty must be between 1 and 5'
    }

    if (formData.total_questions < 1) {
      newErrors.total_questions = 'Must have at least 1 question'
    } else if (formData.total_questions > 100) {
      newErrors.total_questions = 'Cannot exceed 100 questions'
    }

    if (formData.time_limit_minutes && (formData.time_limit_minutes < 1 || formData.time_limit_minutes > 300)) {
      newErrors.time_limit_minutes = 'Time limit must be between 1 and 300 minutes'
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters'
    }

    if (formData.instructions && formData.instructions.length > 1000) {
      newErrors.instructions = 'Instructions must be less than 1000 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      // Scroll to the first error
      const firstErrorField = Object.keys(errors)[0]
      if (firstErrorField) {
        const element = document.getElementById(firstErrorField)
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' })
          element.focus()
        }
      }
      return
    }

    // Normalize and clean data before submission
    const quizData = {
      ...formData,
      title: formData.title.trim(),
      key: formData.key.trim(),
      description: formData.description.trim(),
      instructions: formData.instructions.trim(),
      difficulty_level: Number(formData.difficulty_level),
      total_questions: Number(formData.total_questions),
      time_limit_minutes: formData.time_limit_minutes ? Number(formData.time_limit_minutes) : null,
      // Only include subcategory_id if it's set
      subcategory_id: formData.subcategory_id || undefined,
    }

    onSave(quizData)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {isEdit ? 'Edit Quiz' : 'Create New Quiz'}
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                Title *
              </label>
              <input
                id="title"
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.title ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter quiz title"
              />
              {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label htmlFor="key" className="block text-sm font-medium text-gray-700">
                  Quiz Key *
                </label>
                {!isEdit && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={generateQuizKey}
                    className="text-xs flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <Wand2 className="h-3 w-3 mr-1" />
                    Auto-generate
                  </Button>
                )}
              </div>
              <div className="relative">
                <input
                  id="key"
                  type="text"
                  value={formData.key}
                  onChange={(e) => {
                    handleInputChange('key', e.target.value.toLowerCase().replace(/[^a-z0-9_]/g, ''))
                    setShowKeyHelper(false)
                  }}
                  className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.key ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="e.g., wf_quiz_a1_001"
                />
                {showKeyHelper && (
                  <div className="absolute right-2 top-2 text-gray-400">
                    <Info className="h-4 w-4" />
                  </div>
                )}
              </div>
              {errors.key && <p className="text-red-500 text-sm mt-1">{errors.key}</p>}
              {showKeyHelper && (
                <p className="text-gray-500 text-xs mt-1">
                  Select a category and level to auto-generate a key, or create your own using lowercase letters, numbers, and underscores.
                </p>
              )}
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <span className="text-xs text-gray-500">
                {formData.description.length}/500
              </span>
            </div>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              maxLength={500}
              className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.description ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter a brief description of the quiz content and objectives"
            />
            {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <label htmlFor="instructions" className="block text-sm font-medium text-gray-700">
                Instructions
              </label>
              <span className="text-xs text-gray-500">
                {formData.instructions.length}/1000
              </span>
            </div>
            <textarea
              id="instructions"
              value={formData.instructions}
              onChange={(e) => handleInputChange('instructions', e.target.value)}
              rows={4}
              maxLength={1000}
              className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.instructions ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter detailed instructions for students taking this quiz"
            />
            {errors.instructions && <p className="text-red-500 text-sm mt-1">{errors.instructions}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                id="category_id"
                value={formData.category_id}
                onChange={(e) => handleInputChange('category_id', e.target.value)}
                className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.category_id ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select Category</option>
                {(categories?.data || []).map((category: any) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.category_id && <p className="text-red-500 text-sm mt-1">{errors.category_id}</p>}
            </div>

            {/* Temporarily disabled subcategory field */}
            {/* <div>
              <label htmlFor="subcategory_id" className="block text-sm font-medium text-gray-700 mb-2">
                Subcategory
              </label>
              <select
                id="subcategory_id"
                value={formData.subcategory_id}
                onChange={(e) => handleInputChange('subcategory_id', e.target.value)}
                disabled={!formData.category_id}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
              >
                <option value="">Select Subcategory</option>
                {(subcategories?.data || []).map((subcategory: any) => (
                  <option key={subcategory.id} value={subcategory.id}>
                    {subcategory.name}
                  </option>
                ))}
              </select>
              {!formData.category_id && (
                <p className="text-gray-500 text-xs mt-1">Select a category first</p>
              )}
            </div> */}

            <div>
              <label htmlFor="level_id" className="block text-sm font-medium text-gray-700 mb-2">
                Level *
              </label>
              <select
                id="level_id"
                value={formData.level_id}
                onChange={(e) => handleInputChange('level_id', e.target.value)}
                className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.level_id ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select Level</option>
                {(levels?.data || []).map((level: any) => (
                  <option key={level.id} value={level.id}>
                    {level.name} ({level.key.toUpperCase()})
                  </option>
                ))}
              </select>
              {errors.level_id && <p className="text-red-500 text-sm mt-1">{errors.level_id}</p>}
            </div>

            <div>
              <label htmlFor="quiz_type_id" className="block text-sm font-medium text-gray-700 mb-2">
                Quiz Type
              </label>
              <select
                id="quiz_type_id"
                value={formData.quiz_type_id}
                onChange={(e) => handleInputChange('quiz_type_id', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Quiz Type</option>
                {(quizTypes?.data || []).map((type: any) => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="difficulty_level" className="block text-sm font-medium text-gray-700 mb-2">
                Difficulty Level *
              </label>
              <select
                id="difficulty_level"
                value={formData.difficulty_level}
                onChange={(e) => handleInputChange('difficulty_level', Number(e.target.value))}
                className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.difficulty_level ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value={1}>1 - Very Easy</option>
                <option value={2}>2 - Easy</option>
                <option value={3}>3 - Medium</option>
                <option value={4}>4 - Hard</option>
                <option value={5}>5 - Very Hard</option>
              </select>
              {errors.difficulty_level && <p className="text-red-500 text-sm mt-1">{errors.difficulty_level}</p>}
              <p className="text-gray-500 text-xs mt-1">
                {formData.difficulty_level <= 2 ? 'Easy' : formData.difficulty_level === 3 ? 'Medium' : 'Hard'}
              </p>
            </div>

            <div>
              <label htmlFor="total_questions" className="block text-sm font-medium text-gray-700 mb-2">
                Total Questions *
              </label>
              <input
                id="total_questions"
                type="number"
                min="1"
                max="100"
                value={formData.total_questions}
                onChange={(e) => handleInputChange('total_questions', Number(e.target.value))}
                className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.total_questions ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.total_questions && <p className="text-red-500 text-sm mt-1">{errors.total_questions}</p>}
              <p className="text-gray-500 text-xs mt-1">
                Recommended: 10-20 questions
              </p>
            </div>

            <div>
              <label htmlFor="time_limit_minutes" className="block text-sm font-medium text-gray-700 mb-2">
                Time Limit (minutes)
              </label>
              <input
                id="time_limit_minutes"
                type="number"
                min="1"
                max="300"
                value={formData.time_limit_minutes || ''}
                onChange={(e) => handleInputChange('time_limit_minutes', e.target.value ? Number(e.target.value) : null)}
                className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.time_limit_minutes ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Optional (no time limit)"
              />
              {errors.time_limit_minutes && <p className="text-red-500 text-sm mt-1">{errors.time_limit_minutes}</p>}
              <p className="text-gray-500 text-xs mt-1">
                Leave empty for no time limit
              </p>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                  Quiz Status
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  {formData.is_active ? 'Quiz is active and visible to students' : 'Quiz is inactive and hidden from students'}
                </p>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) => handleInputChange('is_active', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-2 text-sm text-gray-700">
                  {formData.is_active ? 'Active' : 'Inactive'}
                </label>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center pt-6 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              {!isEdit && (
                <span>
                  💡 Tip: You can add questions to this quiz after creating it
                </span>
              )}
            </div>
            <div className="flex space-x-3">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !formData.title.trim() || !formData.category_id || !formData.level_id}
                className="min-w-[120px]"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {isEdit ? 'Updating...' : 'Creating...'}
                  </div>
                ) : (
                  isEdit ? 'Update Quiz' : 'Create Quiz'
                )}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}