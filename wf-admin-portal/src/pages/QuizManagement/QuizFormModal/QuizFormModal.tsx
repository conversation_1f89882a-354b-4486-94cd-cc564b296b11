import { Button } from '@/components/ui/button'
import { X, Wand2, Info } from 'lucide-react'
import { useQuizFormModalHandler } from './QuizFormModal.handler'
import styles from './QuizFormModal.module.css'

export interface QuizFormModalProps {
  quiz?: any
  onSave: (quizData: any) => void
  onClose: () => void
  isLoading: boolean
}

export default function QuizFormModal({ quiz, onSave, onClose, isLoading }: QuizFormModalProps) {
  const {
    formData,
    errors,
    showKeyHelper,
    isEdit,
    categories,
    levels,
    quizTypes,
    handleInputChange,
    handleKeyChange,
    generateQuizKey,
    handleSubmit,
    descriptionLength,
    instructionsLength,
  } = useQuizFormModalHandler({ quiz, onSave })

  const getDifficultyLabel = (level: number) => {
    return level <= 2 ? 'Easy' : level === 3 ? 'Medium' : 'Hard'
  }

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2 className={styles.title}>
            {isEdit ? 'Edit Quiz' : 'Create New Quiz'}
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.grid}>
            <div className={styles.fieldGroup}>
              <label htmlFor="title" className={styles.label}>
                Title *
              </label>
              <input
                id="title"
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className={`${styles.input} ${errors.title ? styles.error : ''}`}
                placeholder="Enter quiz title"
              />
              {errors.title && <p className={styles.errorMessage}>{errors.title}</p>}
            </div>

            <div className={styles.fieldGroup}>
              <div className={styles.labelRow}>
                <label htmlFor="key" className={styles.label}>
                  Quiz Key *
                </label>
                {!isEdit && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={generateQuizKey}
                    className={styles.autoGenerateButton}
                  >
                    <Wand2 className={styles.autoGenerateIcon} />
                    Auto-generate
                  </Button>
                )}
              </div>
              <div className={styles.inputWrapper}>
                <input
                  id="key"
                  type="text"
                  value={formData.key}
                  onChange={(e) => handleKeyChange(e.target.value)}
                  className={`${styles.input} ${errors.key ? styles.error : ''}`}
                  placeholder="e.g., wf_quiz_a1_001"
                />
                {showKeyHelper && (
                  <div className={styles.helperIcon}>
                    <Info className="h-4 w-4" />
                  </div>
                )}
              </div>
              {errors.key && <p className={styles.errorMessage}>{errors.key}</p>}
              {showKeyHelper && (
                <p className={styles.helperText}>
                  Select a category and level to auto-generate a key, or create your own using lowercase letters, numbers, and underscores.
                </p>
              )}
            </div>
          </div>

          <div className={styles.fieldGroup}>
            <div className={styles.labelRow}>
              <label htmlFor="description" className={styles.label}>
                Description
              </label>
              <span className={styles.charCounter}>
                {descriptionLength}/500
              </span>
            </div>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              maxLength={500}
              className={`${styles.textarea} ${errors.description ? styles.error : ''}`}
              placeholder="Enter a brief description of the quiz content and objectives"
            />
            {errors.description && <p className={styles.errorMessage}>{errors.description}</p>}
          </div>

          <div className={styles.fieldGroup}>
            <div className={styles.labelRow}>
              <label htmlFor="instructions" className={styles.label}>
                Instructions
              </label>
              <span className={styles.charCounter}>
                {instructionsLength}/1000
              </span>
            </div>
            <textarea
              id="instructions"
              value={formData.instructions}
              onChange={(e) => handleInputChange('instructions', e.target.value)}
              rows={4}
              maxLength={1000}
              className={`${styles.textarea} ${errors.instructions ? styles.error : ''}`}
              placeholder="Enter detailed instructions for students taking this quiz"
            />
            {errors.instructions && <p className={styles.errorMessage}>{errors.instructions}</p>}
          </div>

          <div className={styles.threeColumnGrid}>
            <div className={styles.fieldGroup}>
              <label htmlFor="category_id" className={styles.label}>
                Category *
              </label>
              <select
                id="category_id"
                value={formData.category_id}
                onChange={(e) => handleInputChange('category_id', e.target.value)}
                className={`${styles.select} ${errors.category_id ? styles.error : ''}`}
              >
                <option value="">Select Category</option>
                {categories.map((category: any) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.category_id && <p className={styles.errorMessage}>{errors.category_id}</p>}
            </div>

            <div className={styles.fieldGroup}>
              <label htmlFor="level_id" className={styles.label}>
                Level *
              </label>
              <select
                id="level_id"
                value={formData.level_id}
                onChange={(e) => handleInputChange('level_id', e.target.value)}
                className={`${styles.select} ${errors.level_id ? styles.error : ''}`}
              >
                <option value="">Select Level</option>
                {levels.map((level: any) => (
                  <option key={level.id} value={level.id}>
                    {level.name} ({level.key.toUpperCase()})
                  </option>
                ))}
              </select>
              {errors.level_id && <p className={styles.errorMessage}>{errors.level_id}</p>}
            </div>

            <div className={styles.fieldGroup}>
              <label htmlFor="quiz_type_id" className={styles.label}>
                Quiz Type
              </label>
              <select
                id="quiz_type_id"
                value={formData.quiz_type_id}
                onChange={(e) => handleInputChange('quiz_type_id', e.target.value)}
                className={styles.select}
              >
                <option value="">Select Quiz Type</option>
                {quizTypes.map((type: any) => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className={styles.threeColumnGrid}>
            <div className={styles.fieldGroup}>
              <label htmlFor="difficulty_level" className={styles.label}>
                Difficulty Level *
              </label>
              <select
                id="difficulty_level"
                value={formData.difficulty_level}
                onChange={(e) => handleInputChange('difficulty_level', Number(e.target.value))}
                className={`${styles.select} ${errors.difficulty_level ? styles.error : ''}`}
              >
                <option value={1}>1 - Very Easy</option>
                <option value={2}>2 - Easy</option>
                <option value={3}>3 - Medium</option>
                <option value={4}>4 - Hard</option>
                <option value={5}>5 - Very Hard</option>
              </select>
              {errors.difficulty_level && (
                <p className={styles.errorMessage}>{errors.difficulty_level}</p>
              )}
              <p className={styles.helperText}>
                {getDifficultyLabel(formData.difficulty_level)}
              </p>
            </div>

            <div className={styles.fieldGroup}>
              <label htmlFor="total_questions" className={styles.label}>
                Total Questions *
              </label>
              <input
                id="total_questions"
                type="number"
                min="1"
                max="100"
                value={formData.total_questions}
                onChange={(e) => handleInputChange('total_questions', Number(e.target.value))}
                className={`${styles.input} ${errors.total_questions ? styles.error : ''}`}
              />
              {errors.total_questions && (
                <p className={styles.errorMessage}>{errors.total_questions}</p>
              )}
              <p className={styles.helperText}>
                Recommended: 10-20 questions
              </p>
            </div>

            <div className={styles.fieldGroup}>
              <label htmlFor="time_limit_minutes" className={styles.label}>
                Time Limit (minutes)
              </label>
              <input
                id="time_limit_minutes"
                type="number"
                min="1"
                max="300"
                value={formData.time_limit_minutes || ''}
                onChange={(e) => handleInputChange('time_limit_minutes', e.target.value ? Number(e.target.value) : null)}
                className={`${styles.input} ${errors.time_limit_minutes ? styles.error : ''}`}
                placeholder="Optional (no time limit)"
              />
              {errors.time_limit_minutes && (
                <p className={styles.errorMessage}>{errors.time_limit_minutes}</p>
              )}
              <p className={styles.helperText}>
                Leave empty for no time limit
              </p>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="is_active" className={styles.label}>
                  Quiz Status
                </label>
                <p className={styles.helperText}>
                  {formData.is_active ? 'Quiz is active and visible to students' : 'Quiz is inactive and hidden from students'}
                </p>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) => handleInputChange('is_active', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-2 text-sm text-gray-700">
                  {formData.is_active ? 'Active' : 'Inactive'}
                </label>
              </div>
            </div>
          </div>

          <div className={styles.actions}>
            <div className="flex-1">
              {!isEdit && (
                <span className={styles.helperText}>
                  💡 Tip: You can add questions to this quiz after creating it
                </span>
              )}
            </div>
            <div className="flex space-x-3">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !formData.title.trim() || !formData.category_id || !formData.level_id}
                className="min-w-[120px]"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {isEdit ? 'Updating...' : 'Creating...'}
                  </div>
                ) : (
                  isEdit ? 'Update Quiz' : 'Create Quiz'
                )}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}