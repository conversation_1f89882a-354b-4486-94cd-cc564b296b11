import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { serviceContainer } from '@/services'

// Types
interface QuizFormData {
  title: string
  description: string
  instructions: string
  key: string
  category_id: string
  level_id: string
  quiz_type_id: string
  difficulty_level: number
  total_questions: number
  time_limit_minutes: number | null
  is_active: boolean
  subcategory_id: string
}

interface QuizFormModalHandlerProps {
  quiz?: any
  onSave: (quizData: any) => void
}

export const useQuizFormModalHandler = ({ quiz, onSave }: QuizFormModalHandlerProps) => {
  const isEdit = !!quiz
  
  // Form state
  const [formData, setFormData] = useState<QuizFormData>({
    title: quiz?.title || '',
    description: quiz?.description || '',
    instructions: quiz?.instructions || '',
    key: quiz?.key || '',
    category_id: quiz?.category_id || '',
    level_id: quiz?.level_id || '',
    quiz_type_id: quiz?.quiz_type_id || '',
    difficulty_level: quiz?.difficulty_level || 3, // Default to medium difficulty
    total_questions: quiz?.total_questions || 15, // Default to 15 questions
    time_limit_minutes: quiz?.time_limit_minutes || 30,
    is_active: quiz?.is_active ?? true,
    subcategory_id: quiz?.subcategory_id || '',
  })

  // Track if fields have been modified by the user
  const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({})

  // Track if we should show the key generation helper
  const [showKeyHelper, setShowKeyHelper] = useState(!isEdit && !formData.key)

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Data fetching
  const { data: categories } = useQuery({
    queryKey: ['categories'],
    queryFn: () => serviceContainer.categoryService.getCategories(),
  })

  const { data: levels } = useQuery({
    queryKey: ['levels'],
    queryFn: () => serviceContainer.levelService.getLevels(),
  })

  const { data: quizTypes } = useQuery({
    queryKey: ['quiz-types'],
    queryFn: () => serviceContainer.quizTypeService.getQuizTypes(),
  })

  // Auto-generate quiz key when category, level, or title changes
  useEffect(() => {
    if (!isEdit && !touchedFields.key && formData.category_id && formData.level_id) {
      const category = categories?.data?.find((c: any) => c.id === formData.category_id)
      const level = levels?.data?.find((l: any) => l.id === formData.level_id)

      if (category && level) {
        // Generate a key like: wf_quiz_a1_001, vocab_quiz_b2_001, etc.
        const categoryPrefix = category.key === 'word_formation' ? 'wf' : category.key
        const levelKey = level.key.toLowerCase()

        // Generate a simple incremental number
        const timestamp = Date.now().toString().slice(-3)
        const generatedKey = `${categoryPrefix}_quiz_${levelKey}_${timestamp}`

        setFormData(prev => ({ ...prev, key: generatedKey }))
        setShowKeyHelper(false)
      }
    }
  }, [formData.category_id, formData.level_id, categories, levels, isEdit, touchedFields.key])

  // Form validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Required field validation
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    } else if (formData.title.length < 3) {
      newErrors.title = 'Title must be at least 3 characters'
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters'
    }

    if (!formData.key.trim()) {
      newErrors.key = 'Key is required'
    } else if (!/^[a-z0-9_]+$/.test(formData.key)) {
      newErrors.key = 'Key must contain only lowercase letters, numbers, and underscores'
    } else if (formData.key.length < 3) {
      newErrors.key = 'Key must be at least 3 characters'
    }

    if (!formData.category_id) newErrors.category_id = 'Category is required'
    if (!formData.level_id) newErrors.level_id = 'Level is required'

    if (formData.difficulty_level < 1 || formData.difficulty_level > 5) {
      newErrors.difficulty_level = 'Difficulty must be between 1 and 5'
    }

    if (formData.total_questions < 1) {
      newErrors.total_questions = 'Must have at least 1 question'
    } else if (formData.total_questions > 100) {
      newErrors.total_questions = 'Cannot exceed 100 questions'
    }

    if (formData.time_limit_minutes && (formData.time_limit_minutes < 1 || formData.time_limit_minutes > 300)) {
      newErrors.time_limit_minutes = 'Time limit must be between 1 and 300 minutes'
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters'
    }

    if (formData.instructions && formData.instructions.length > 1000) {
      newErrors.instructions = 'Instructions must be less than 1000 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Event handlers
  const handleInputChange = (field: string, value: string | number | boolean | null) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setTouchedFields(prev => ({ ...prev, [field]: true }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }

    // Reset subcategory when category changes
    if (field === 'category_id' && formData.subcategory_id) {
      setFormData(prev => ({ ...prev, subcategory_id: '' }))
    }
  }

  const generateQuizKey = () => {
    const category = categories?.data?.find((c: any) => c.id === formData.category_id)
    const level = levels?.data?.find((l: any) => l.id === formData.level_id)

    if (category && level) {
      const categoryPrefix = category.key === 'word_formation' ? 'wf' : category.key
      const levelKey = level.key.toLowerCase()
      const timestamp = Date.now().toString().slice(-3)
      const generatedKey = `${categoryPrefix}_quiz_${levelKey}_${timestamp}`

      setFormData(prev => ({ ...prev, key: generatedKey }))
      setTouchedFields(prev => ({ ...prev, key: true }))
      setShowKeyHelper(false)
    }
  }

  const handleKeyChange = (value: string) => {
    const cleanedValue = value.toLowerCase().replace(/[^a-z0-9_]/g, '')
    handleInputChange('key', cleanedValue)
    setShowKeyHelper(false)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      // Scroll to the first error
      const firstErrorField = Object.keys(errors)[0]
      if (firstErrorField) {
        const element = document.getElementById(firstErrorField)
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' })
          element.focus()
        }
      }
      return
    }

    // Normalize and clean data before submission
    const quizData = {
      ...formData,
      title: formData.title.trim(),
      key: formData.key.trim(),
      description: formData.description.trim(),
      instructions: formData.instructions.trim(),
      difficulty_level: Number(formData.difficulty_level),
      total_questions: Number(formData.total_questions),
      time_limit_minutes: formData.time_limit_minutes ? Number(formData.time_limit_minutes) : null,
      // Only include subcategory_id if it's set
      subcategory_id: formData.subcategory_id || undefined,
    }

    onSave(quizData)
  }

  // Return handler interface
  return {
    // Data
    formData,
    errors,
    showKeyHelper,
    isEdit,
    categories: categories?.data || [],
    levels: levels?.data || [],
    quizTypes: quizTypes?.data || [],
    
    // Actions
    handleInputChange,
    handleKeyChange,
    generateQuizKey,
    handleSubmit,
    
    // Computed values
    descriptionLength: formData.description.length,
    instructionsLength: formData.instructions.length,
  }
}