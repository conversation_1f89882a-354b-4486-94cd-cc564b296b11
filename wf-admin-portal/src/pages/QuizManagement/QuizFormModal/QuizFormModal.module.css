/* Shared modal styles */
@import '@styles/shared/modal.module.css';

/* Use shared styles */
.overlay {
  composes: overlay from '@styles/shared/modal.module.css';
}

.modal {
  composes: modalMedium from '@styles/shared/modal.module.css';
}

.header {
  composes: header from '@styles/shared/modal.module.css';
}

.title {
  composes: title from '@styles/shared/modal.module.css';
}

.form {
  composes: form from '@styles/shared/modal.module.css';
}

.grid {
  composes: grid from '@styles/shared/modal.module.css';
}

.fieldGroup {
  composes: fieldGroup from '@styles/shared/modal.module.css';
}

.labelRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.label {
  composes: label from '@styles/shared/modal.module.css';
}

.autoGenerateButton {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  color: rgb(37, 99, 235);
}

.autoGenerateButton:hover {
  color: rgb(30, 64, 175);
}

.autoGenerateIcon {
  height: 0.75rem;
  width: 0.75rem;
  margin-right: 0.25rem;
}

/* Input fields */
.inputWrapper {
  position: relative;
}

.input {
  composes: input from '@styles/shared/modal.module.css';
}

.textarea {
  composes: textarea from '@styles/shared/modal.module.css';
}

.select {
  composes: select from '@styles/shared/modal.module.css';
}

/* Character counters */
.charCounter {
  composes: charCounter from '@styles/shared/modal.module.css';
}

/* Helper text */
.helperIcon {
  position: absolute;
  right: 0.5rem;
  top: 0.5rem;
  color: rgb(156, 163, 175);
  height: 1rem;
  width: 1rem;
}

.helperText {
  composes: helperText from '@styles/shared/modal.module.css';
}

/* Error messages */
.errorMessage {
  composes: errorMessage from '@styles/shared/modal.module.css';
}

/* Three column grid for smaller fields */
.threeColumnGrid {
  composes: threeColumnGrid from '@styles/shared/modal.module.css';
}

/* Form actions */
.actions {
  composes: actions from '@styles/shared/modal.module.css';
}

/* Dark mode support is handled by shared module */