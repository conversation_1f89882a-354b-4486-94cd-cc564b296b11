import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient, keepPreviousData } from '@tanstack/react-query'
import { serviceContainer } from '@/services'
import { useToast } from '@/hooks/use-toast'
import type { QuizWithRelations } from 'wf-shared/types'

export const useQuizManagementHandler = () => {
  const [page, setPage] = useState(1)
  const [filters, setFilters] = useState({
    category: '',
    level: '',
    status: '',
  })
  const [isVeryFirstLoad, setIsVeryFirstLoad] = useState(true)
  // Confirmation dialog state
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean
    quizId: string | null
    quizTitle: string
  }>({
    isOpen: false,
    quizId: null,
    quizTitle: '',
  })
  
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // First, load categories and levels
  const { data: categoriesResponse, isLoading: categoriesLoading } = useQuery({
    queryKey: ['categories'],
    queryFn: serviceContainer.categoryService.getCategories,
  })

  const { data: levelsResponse, isLoading: levelsLoading } = useQuery({
    queryKey: ['levels'],
    queryFn: serviceContainer.levelService.getLevels,
  })

  // Extract the actual data from service responses
  const categories = categoriesResponse?.data
  const levels = levelsResponse?.data

  // Then load quizzes with proper dependencies
  const { data: quizzesResponse, isLoading, isFetching, error, refetch } = useQuery({
    queryKey: ['quizzes', page, filters],
    queryFn: () => {
      // Transform UI filter format to repository format
      const repositoryFilters: Record<string, any> = {}

      // Map category key to category_id
      if (filters.category && categories) {
        const category = categories.find(cat => cat.key === filters.category)
        if (category) {
          repositoryFilters.category_id = category.id
        }
      }

      // Map level key to level_id
      if (filters.level && levels) {
        const level = levels.find(lvl => lvl.key === filters.level)
        if (level) {
          repositoryFilters.level_id = level.id
        }
      }

      // Map status to is_active boolean
      if (filters.status) {
        repositoryFilters.is_active = filters.status === 'active'
      }

      return serviceContainer.quizService.getQuizzes({
        page,
        pageSize: 10,
        ...repositoryFilters,
      })
    },
    enabled: true, // Always enabled, filters will just be ignored if categories/levels not loaded
    placeholderData: keepPreviousData, // Keep previous data while loading new data
  })

  const quizzes = quizzesResponse?.data

  // Track when we've made the first query attempt (successful or failed)
  useEffect(() => {
    if (!isLoading && isVeryFirstLoad) {
      setIsVeryFirstLoad(false)
    }
  }, [isLoading, isVeryFirstLoad])

  // Also mark first load as done if there's an error (to prevent full page skeleton on subsequent filter changes)
  useEffect(() => {
    if (error && isVeryFirstLoad) {
      setIsVeryFirstLoad(false)
    }
  }, [error, isVeryFirstLoad])

  // Determine loading states:
  // - isInitialLoading: true ONLY for the very first load of the page (before any data has been loaded)
  // - isRefetching: true for ANY loading after the first successful load (filters, pagination, etc.)
  const isInitialLoading = isLoading && isVeryFirstLoad
  const isRefetching = isFetching && !isVeryFirstLoad

  // Show error toast if there are service errors - using useEffect to avoid setState during render
  useEffect(() => {
    if (quizzesResponse?.error && !isLoading) {
      toast({
        title: 'Error loading quizzes',
        description: quizzesResponse.error,
        variant: 'destructive',
      })
    }
  }, [quizzesResponse?.error, isLoading, toast])

  useEffect(() => {
    if (categoriesResponse?.error) {
      toast({
        title: 'Error loading categories',
        description: categoriesResponse.error,
        variant: 'destructive',
      })
    }
  }, [categoriesResponse?.error, toast])

  useEffect(() => {
    if (levelsResponse?.error) {
      toast({
        title: 'Error loading levels',
        description: levelsResponse.error,
        variant: 'destructive',
      })
    }
  }, [levelsResponse?.error, toast])

  const handleFilterChange = (filterType: string, value: string): void => {
    setFilters(prev => ({ ...prev, [filterType]: value }))
    setPage(1) // Reset to first page when filters change
  }

  const clearFilters = (): void => {
    setFilters({ category: '', level: '', status: '' })
    setPage(1)
  }

  const handlePageChange = (newPage: number): void => {
    setPage(newPage)
  }

  // Mutations removed - now handled in the QuizEdit component

  const deleteQuizMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await serviceContainer.quizService.deleteQuiz(id)
      if (response.error) {
        throw new Error(response.error)
      }
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['quizzes'] })
      toast({
        title: 'Success',
        description: 'Quiz deleted successfully',
      })
      handleCloseDeleteConfirmation()
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete quiz',
        variant: 'destructive',
      })
    },
  })

  const handleCreateQuiz = (): void => {
    window.location.href = '/create-quiz'
  }

  const handleViewQuiz = (quizId: string): void => {
    // Navigate to quiz view page
    window.open(`/quiz/${quizId}/view`, '_blank')
  }

  const handleEditQuiz = (quiz: QuizWithRelations): void => {
    window.location.href = `/quiz/${quiz.id}/edit?from=quiz-management`
  }

  const handleDeleteQuiz = (quiz: QuizWithRelations): void => {
    setDeleteConfirmation({
      isOpen: true,
      quizId: quiz.id,
      quizTitle: quiz.title || 'Untitled Quiz',
    })
  }

  const handleConfirmDelete = (): void => {
    if (deleteConfirmation.quizId) {
      deleteQuizMutation.mutate(deleteConfirmation.quizId)
    }
  }

  const handleCloseDeleteConfirmation = (): void => {
    setDeleteConfirmation({
      isOpen: false,
      quizId: null,
      quizTitle: '',
    })
  }

  // Modal handlers removed - now using page navigation

  const hasFilters = Object.values(filters).some(v => v)

  return {
    // Data
    quizzes,
    categories,
    levels,

    // State
    page,
    filters,
    isLoading,
    isInitialLoading,
    isRefetching,
    error,
    hasFilters,
    deleteConfirmation,

    // Loading states
    categoriesLoading,
    levelsLoading,
    isDeleting: deleteQuizMutation.isPending,

    // Actions
    handleFilterChange,
    clearFilters,
    handlePageChange,
    handleCreateQuiz,
    handleViewQuiz,
    handleEditQuiz,
    handleDeleteQuiz,
    handleConfirmDelete,
    handleCloseDeleteConfirmation,
    refetch,
  }
}