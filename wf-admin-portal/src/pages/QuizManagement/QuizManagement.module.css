/**
 * QuizManagement CSS Module - Refactored with Shared Modules
 * Component-specific styles only, common patterns imported from shared modules
 */

/* QuizManagement-specific styles only */

/* Header Actions - QuizManagement specific layout */
.headerActions {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

/* Quiz List - QuizManagement specific quiz item styling */
.quizListContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

.quizItem {
  composes: cardHover from '@styles/shared/cards.module.css';
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: theme('spacing.4');
}

.quizContent {
  flex: 1;
}

.quizTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.1');
}

.quizMetadata {
  margin-top: theme('spacing.1');
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.500');
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.quizActions {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
}

.quizActionButton {
  padding: theme('spacing.1');
  border-radius: theme('borderRadius.md');
  color: theme('colors.gray.500');
  transition: all 0.2s ease;
}

.quizActionButton:hover {
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.700');
}

.quizActionButtonDanger:hover {
  background-color: theme('colors.red.50');
  color: theme('colors.red.600');
}

/* Badges - QuizManagement specific badge variants */
.badgeCategory {
  composes: badgeBase from '@styles/shared/badges.module.css';
  background-color: theme('colors.blue.100');
  color: theme('colors.blue.800');
}

.badgeLevel {
  composes: badgeBase from '@styles/shared/badges.module.css';
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
}

.badgeQuizType {
  composes: badgeBase from '@styles/shared/badges.module.css';
  background-color: theme('colors.purple.100');
  color: theme('colors.purple.800');
}

.badgeStatusActive {
  composes: badgeStatusActive from '@styles/shared/badges.module.css';
}

.badgeStatusInactive {
  composes: badgeStatusInactive from '@styles/shared/badges.module.css';
}

/* Enhanced Quiz Card Layout - QuizManagement specific */
.quizCard {
  composes: cardHover from '@styles/shared/cards.module.css';
  padding: theme('spacing.4');
}

.quizCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: theme('spacing.3');
}

.quizCardTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.1');
}

.quizCardDescription {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
  line-height: theme('lineHeight.relaxed');
}

.quizCardStats {
  display: flex;
  align-items: center;
  gap: theme('spacing.4');
  margin-top: theme('spacing.3');
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.500');
}

.quizCardStat {
  display: flex;
  align-items: center;
  gap: theme('spacing.1');
}

.quizCardStatIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
}

.quizCardBadges {
  display: flex;
  flex-wrap: wrap;
  gap: theme('spacing.2');
  margin-top: theme('spacing.3');
}

.quizCardFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: theme('spacing.4');
  padding-top: theme('spacing.3');
  border-top: 1px solid theme('colors.gray.100');
}

.quizCardLastUpdated {
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.500');
}

/* Bulk Actions - QuizManagement specific */
.bulkActionsWrapper {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  padding: theme('spacing.3') theme('spacing.4');
  background-color: theme('colors.blue.50');
  border: 1px solid theme('colors.blue.200');
  border-radius: theme('borderRadius.md');
  margin-bottom: theme('spacing.4');
}

.bulkActionsText {
  font-size: theme('fontSize.sm');
  color: theme('colors.blue.800');
  flex: 1;
}

.bulkActionsButtons {
  display: flex;
  gap: theme('spacing.2');
}

/* Clear Button - QuizManagement specific styling */
.clearButton {
  composes: buttonOutline from '@styles/shared/buttons.module.css';
  width: 100%;
}

/* Dark mode overrides for QuizManagement-specific elements */
:global(.dark) .quizItem {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

:global(.dark) .quizItem:hover {
  background-color: theme('colors.gray.700');
}

:global(.dark) .quizTitle {
  color: white;
}

:global(.dark) .quizMetadata {
  color: theme('colors.gray.400');
}

:global(.dark) .quizActionButton:hover {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
}

:global(.dark) .quizActionButtonDanger:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: theme('colors.red.400');
}

:global(.dark) .badgeCategory {
  background-color: rgba(59, 130, 246, 0.2);
  color: theme('colors.blue.300');
}

:global(.dark) .badgeLevel {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.300');
}

:global(.dark) .badgeQuizType {
  background-color: rgba(147, 51, 234, 0.2);
  color: theme('colors.purple.300');
}

:global(.dark) .quizCardTitle {
  color: white;
}

:global(.dark) .quizCardDescription {
  color: theme('colors.gray.300');
}

:global(.dark) .quizCardStats {
  color: theme('colors.gray.400');
}

:global(.dark) .quizCardFooter {
  border-top-color: theme('colors.gray.700');
}

:global(.dark) .quizCardLastUpdated {
  color: theme('colors.gray.400');
}

:global(.dark) .bulkActionsWrapper {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: theme('colors.blue.800');
}

:global(.dark) .bulkActionsText {
  color: theme('colors.blue.300');
}