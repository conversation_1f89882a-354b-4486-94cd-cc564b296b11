
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for admin-portal/src/pages/QuestionBank/QuestionBank.style.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">admin-portal/src/pages/QuestionBank</a> QuestionBank.style.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/52</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/52</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >export const questionBankStyles = {<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >  container: "space-y-6",</span>
<span class="cstat-no" title="statement not covered" >  header: {</span>
<span class="cstat-no" title="statement not covered" >    wrapper: "flex justify-between items-center",</span>
<span class="cstat-no" title="statement not covered" >    content: "",</span>
<span class="cstat-no" title="statement not covered" >    title: "text-3xl font-bold text-gray-900",</span>
<span class="cstat-no" title="statement not covered" >    subtitle: "mt-2 text-gray-600",</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  statsGrid: "grid grid-cols-1 md:grid-cols-4 gap-4",</span>
<span class="cstat-no" title="statement not covered" >  statCard: {</span>
<span class="cstat-no" title="statement not covered" >    header: "pb-2",</span>
<span class="cstat-no" title="statement not covered" >    title: "text-sm font-medium text-gray-600",</span>
<span class="cstat-no" title="statement not covered" >    value: {</span>
<span class="cstat-no" title="statement not covered" >      default: "text-2xl font-bold",</span>
<span class="cstat-no" title="statement not covered" >      green: "text-2xl font-bold text-green-600",</span>
<span class="cstat-no" title="statement not covered" >      orange: "text-2xl font-bold text-orange-600",</span>
<span class="cstat-no" title="statement not covered" >      blue: "text-2xl font-bold text-blue-600",</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  questionList: {</span>
<span class="cstat-no" title="statement not covered" >    container: "space-y-4",</span>
<span class="cstat-no" title="statement not covered" >    item: "border border-gray-200 rounded-lg p-4 hover:bg-gray-50",</span>
<span class="cstat-no" title="statement not covered" >    header: "flex items-start justify-between",</span>
<span class="cstat-no" title="statement not covered" >    content: "flex-1",</span>
<span class="cstat-no" title="statement not covered" >    badges: "flex items-center space-x-2 mb-2",</span>
<span class="cstat-no" title="statement not covered" >    title: "font-medium text-gray-900 mb-2",</span>
<span class="cstat-no" title="statement not covered" >    optionsGrid: "grid grid-cols-2 gap-2 mb-2",</span>
<span class="cstat-no" title="statement not covered" >    explanation: "mt-2 p-2 bg-blue-50 rounded text-sm text-blue-800",</span>
<span class="cstat-no" title="statement not covered" >    timestamp: "mt-2 text-xs text-gray-500",</span>
<span class="cstat-no" title="statement not covered" >    actions: "flex items-center space-x-2 ml-4",</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  badges: {</span>
<span class="cstat-no" title="statement not covered" >    quiz: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",</span>
<span class="cstat-no" title="statement not covered" >    questionType: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800",</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  option: {</span>
<span class="cstat-no" title="statement not covered" >    correct: "text-sm p-2 rounded bg-green-100 text-green-800 font-medium",</span>
<span class="cstat-no" title="statement not covered" >    incorrect: "text-sm p-2 rounded bg-gray-100 text-gray-600",</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  icons: {</span>
<span class="cstat-no" title="statement not covered" >    hasExplanation: "h-4 w-4 text-green-500",</span>
<span class="cstat-no" title="statement not covered" >    noExplanation: "h-4 w-4 text-red-500",</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  pagination: {</span>
<span class="cstat-no" title="statement not covered" >    wrapper: "mt-6 flex justify-center space-x-2",</span>
<span class="cstat-no" title="statement not covered" >    info: "px-4 py-2 text-sm text-gray-600",</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  loadingSkeleton: {</span>
<span class="cstat-no" title="statement not covered" >    item: "h-24 bg-gray-200 rounded",</span>
<span class="cstat-no" title="statement not covered" >    grid: "animate-pulse space-y-4",</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-13T07:12:08.601Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    