
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for admin-portal/src/pages/Dashboard/Dashboard.style.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">admin-portal/src/pages/Dashboard</a> Dashboard.style.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/30</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/30</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >export const dashboardStyles = {<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >  container: "space-y-6",</span>
<span class="cstat-no" title="statement not covered" >  header: {</span>
<span class="cstat-no" title="statement not covered" >    wrapper: "",</span>
<span class="cstat-no" title="statement not covered" >    title: "text-3xl font-bold text-gray-900",</span>
<span class="cstat-no" title="statement not covered" >    subtitle: "mt-2 text-gray-600",</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  statsGrid: "grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",</span>
<span class="cstat-no" title="statement not covered" >  statCard: {</span>
<span class="cstat-no" title="statement not covered" >    header: "flex flex-row items-center justify-between space-y-0 pb-2",</span>
<span class="cstat-no" title="statement not covered" >    title: "text-sm font-medium text-gray-600",</span>
<span class="cstat-no" title="statement not covered" >    iconWrapper: "p-2 rounded-full",</span>
<span class="cstat-no" title="statement not covered" >    value: "text-2xl font-bold text-gray-900",</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  quickActionsGrid: "grid grid-cols-1 gap-6 lg:grid-cols-2",</span>
<span class="cstat-no" title="statement not covered" >  quickActionButton: "p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",</span>
<span class="cstat-no" title="statement not covered" >  quickActionTitle: "font-medium text-gray-900",</span>
<span class="cstat-no" title="statement not covered" >  quickActionDesc: "text-sm text-gray-500",</span>
<span class="cstat-no" title="statement not covered" >  activityGrid: "grid grid-cols-2 gap-4",</span>
<span class="cstat-no" title="statement not covered" >  activityItem: "flex items-center space-x-3",</span>
<span class="cstat-no" title="statement not covered" >  activityDot: "w-2 h-2 bg-blue-500 rounded-full",</span>
<span class="cstat-no" title="statement not covered" >  activityText: "text-sm text-gray-600",</span>
<span class="cstat-no" title="statement not covered" >  activityDate: "text-xs text-gray-400 ml-auto",</span>
<span class="cstat-no" title="statement not covered" >  emptyActivity: "text-center py-6 text-gray-500",</span>
<span class="cstat-no" title="statement not covered" >  loadingSkeleton: {</span>
<span class="cstat-no" title="statement not covered" >    header: "animate-pulse",</span>
<span class="cstat-no" title="statement not covered" >    bar: "h-4 bg-gray-200 rounded w-3/4",</span>
<span class="cstat-no" title="statement not covered" >    value: "h-8 bg-gray-200 rounded w-1/2",</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-13T07:12:08.601Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    