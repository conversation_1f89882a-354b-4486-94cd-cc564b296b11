{"name": "wf-admin-portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.1.5", "@supabase/supabase-js": "^2.51.0", "@tanstack/react-query": "^5.17.15", "@tanstack/react-table": "^8.11.8", "class-variance-authority": "^0.7.1", "lucide-react": "^0.316.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-router-dom": "^6.21.3", "tailwind-merge": "^2.6.0", "wf-shared": "file:../wf-shared", "zod": "^3.22.4"}, "devDependencies": {"@types/css-modules": "^1.0.5", "@types/jest": "^30.0.0", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.6.1", "autoprefixer": "^10.4.17", "clsx": "^2.1.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^26.1.0", "postcss": "^8.4.33", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.0", "vitest": "^1.2.2"}}