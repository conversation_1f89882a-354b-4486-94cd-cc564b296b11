{"name": "wf-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:coverage:watch": "vitest --coverage", "test:coverage:open": "vitest run --coverage && open coverage/index.html"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/react": "^9.39.0", "@sentry/tracing": "^7.120.3", "@supabase/supabase-js": "^2.50.5", "@tanstack/react-query": "^5.81.0", "chart.js": "^4.2.1", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.522.0", "next-themes": "^0.4.6", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-hook-form": "^7.58.1", "react-resizable-panels": "^3.0.3", "react-router-dom": "^6.11.2", "recharts": "^2.15.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "web-vitals": "^5.0.3", "wf-shared": "file:../wf-shared", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/css-modules": "^1.0.5", "@types/node": "^24.0.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "clsx": "^2.1.1", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jest-environment-jsdom": "^30.0.0", "postcss": "^8.4.27", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.3.3", "typescript": "^5.8.3", "vite": "^4.4.5", "vitest": "^3.2.3"}}