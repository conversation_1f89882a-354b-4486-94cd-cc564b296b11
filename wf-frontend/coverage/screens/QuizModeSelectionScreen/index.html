
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for screens/QuizModeSelectionScreen</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> screens/QuizModeSelectionScreen</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">73.55% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>217/295</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">97.72% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>43/44</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">80% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>4/5</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">73.55% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>217/295</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="QuizModeSelectionScreen.handler.ts"><a href="QuizModeSelectionScreen.handler.ts.html">QuizModeSelectionScreen.handler.ts</a></td>
	<td data-value="8.33" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 8%"></div><div class="cover-empty" style="width: 92%"></div></div>
	</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="84" class="abs low">7/84</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="84" class="abs low">7/84</td>
	</tr>

<tr>
	<td class="file high" data-value="QuizModeSelectionScreen.style.ts"><a href="QuizModeSelectionScreen.style.ts.html">QuizModeSelectionScreen.style.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="51" class="abs high">51/51</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="51" class="abs high">51/51</td>
	</tr>

<tr>
	<td class="file high" data-value="QuizModeSelectionScreen.tsx"><a href="QuizModeSelectionScreen.tsx.html">QuizModeSelectionScreen.tsx</a></td>
	<td data-value="99.37" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 99%"></div><div class="cover-empty" style="width: 1%"></div></div>
	</td>
	<td data-value="99.37" class="pct high">99.37%</td>
	<td data-value="160" class="abs high">159/160</td>
	<td data-value="97.72" class="pct high">97.72%</td>
	<td data-value="44" class="abs high">43/44</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="99.37" class="pct high">99.37%</td>
	<td data-value="160" class="abs high">159/160</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-10T02:31:05.870Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    