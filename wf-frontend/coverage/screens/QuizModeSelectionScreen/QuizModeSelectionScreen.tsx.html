
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for screens/QuizModeSelectionScreen/QuizModeSelectionScreen.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">screens/QuizModeSelectionScreen</a> QuizModeSelectionScreen.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">99.37% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>159/160</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">97.72% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>43/44</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>4/4</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">99.37% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>159/160</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">130x</span>
<span class="cline-any cline-yes">130x</span>
<span class="cline-any cline-yes">130x</span>
<span class="cline-any cline-yes">130x</span>
<span class="cline-any cline-yes">130x</span>
<span class="cline-any cline-yes">130x</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">48x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-yes">26x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">156x</span>
<span class="cline-any cline-yes">156x</span>
<span class="cline-any cline-yes">156x</span>
<span class="cline-any cline-yes">156x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">52x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span></td><td class="text"><pre class="prettyprint lang-js">import React from 'react';
import { Clock, BookOpen, Star, PlayCircle, CheckCircle } from 'lucide-react';
import { PageWrapper, ResponsiveCard } from '@/components/Layout/ResponsiveContainer';
import { useQuizModeSelectionScreenHandler } from './QuizModeSelectionScreen.handler';
import { styles } from './QuizModeSelectionScreen.style';
import { useLanguage } from '@/context/LanguageContext';
import type { QuizModeSelectionProps } from '@/types';
&nbsp;
/**
 * QuizModeSelectionScreen component for choosing between Practice and Test modes.
 * Pure UI component - all business logic handled by the handler
 * Updated with new pre-quiz UI design and enhanced responsive layout
 * Can be used as a route component (no props) or as a child component (with props)
 */
const QuizModeSelectionScreen: React.FC&lt;QuizModeSelectionProps&gt; = (props = {}) =&gt; {
  const { quiz, onModeSelect, onBack } = props;
  const { t } = useLanguage();
  const {
    selectedMode,
    modes,
    handleModeSelect,
    handleStartQuiz,
    handleBack,
    canStartQuiz,
    quiz: fetchedQuiz,
    isLoading,
    error
  } = useQuizModeSelectionScreenHandler(props);
&nbsp;
  const activeQuiz = quiz || fetchedQuiz;
&nbsp;
  // Loading state
  if (isLoading) {
    return (
      &lt;PageWrapper&gt;
        &lt;div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8"&gt;
          &lt;div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 sm:p-8 text-center"&gt;
            &lt;div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"&gt;&lt;/div&gt;
            &lt;p className="text-gray-600 dark:text-gray-300"&gt;{t('common.loading')}&lt;/p&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/PageWrapper&gt;
    );
  }
&nbsp;
  // Error state
  if (error) {
    return (
      &lt;PageWrapper&gt;
        &lt;div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8"&gt;
          &lt;div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 sm:p-8 text-center"&gt;
            &lt;div className="bg-red-100 dark:bg-red-900/20 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center"&gt;
              &lt;span className="text-red-600 text-2xl"&gt;⚠️&lt;/span&gt;
            &lt;/div&gt;
            &lt;h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2"&gt;{t('common.error')}&lt;/h3&gt;
            &lt;p className="text-gray-600 dark:text-gray-300 mb-6"&gt;{error.message}&lt;/p&gt;
            &lt;button
              onClick={handleBack}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
            &gt;
              {t('common.back')}
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/PageWrapper&gt;
    );
  }
&nbsp;
  const getDifficultyLabel = (level: number): string =&gt; {
    if (level &lt;= 2) return t('modeSelection.easy');
    if (level === 3) return t('modeSelection.medium');
    if (level &gt;= 4) return t('modeSelection.hard'<span class="branch-0 cbranch-no" title="branch not covered" >);</span>
<span class="cstat-no" title="statement not covered" >    return t('modeSelection.easy'); // fallback</span>
  };
&nbsp;
  const getDifficultyStars = (difficulty: number = 0) =&gt; {
    return Array.from({ length: 5 }, (_, index) =&gt; (
      &lt;Star
        key={index}
        className={`h-4 w-4 ${
          index &lt; difficulty ? 'text-yellow-400 fill-current' : 'text-gray-300 dark:text-gray-600'
        }`}
      /&gt;
    ));
  };
&nbsp;
  return (
    &lt;PageWrapper&gt;
      &lt;div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8"&gt;
        &lt;div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 sm:p-8"&gt;
          {/* Quiz Header */}
          &lt;div className="text-center mb-8"&gt;
            &lt;span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium"&gt;
              {activeQuiz?.level?.key || ''}
            &lt;/span&gt;
            &lt;h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mt-4 mb-2"&gt;
              {activeQuiz?.title || 'Word Formation Quiz'}
            &lt;/h1&gt;
            &lt;p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-sm sm:text-base"&gt;
              {activeQuiz?.description || 'Master word formation patterns and improve your vocabulary skills with practical examples and exercises.'}
            &lt;/p&gt;
          &lt;/div&gt;
&nbsp;
          {/* Quiz Statistics Grid - Enhanced for mobile */}
          &lt;div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-8"&gt;
            &lt;div className="bg-gray-50 dark:bg-gray-700 p-4 sm:p-6 rounded-lg text-center"&gt;
              &lt;BookOpen className="h-6 w-6 sm:h-8 sm:w-8 text-gray-600 dark:text-gray-300 mx-auto mb-2" /&gt;
              &lt;div className="font-semibold text-gray-900 dark:text-white text-lg sm:text-xl"&gt;{activeQuiz?.total_questions ?? '...'}&lt;/div&gt;
              &lt;div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400"&gt;{t('modeSelection.questions')}&lt;/div&gt;
            &lt;/div&gt;
            &lt;div className="bg-gray-50 dark:bg-gray-700 p-4 sm:p-6 rounded-lg text-center"&gt;
              &lt;Clock className="h-6 w-6 sm:h-8 sm:w-8 text-gray-600 dark:text-gray-300 mx-auto mb-2" /&gt;
              &lt;div className="font-semibold text-gray-900 dark:text-white text-lg sm:text-xl"&gt;{activeQuiz?.time_limit_minutes ?? '...'} min&lt;/div&gt;
              &lt;div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400"&gt;{t('modeSelection.duration')}&lt;/div&gt;
            &lt;/div&gt;
            &lt;div className="bg-gray-50 dark:bg-gray-700 p-4 sm:p-6 rounded-lg text-center"&gt;
              &lt;div className="flex justify-center mb-2"&gt;
                {getDifficultyStars(activeQuiz?.difficulty_level ?? 1)}
              &lt;/div&gt;
              &lt;div className="font-semibold text-gray-900 dark:text-white"&gt;{t('modeSelection.level')} {activeQuiz?.difficulty_level ?? '...'}&lt;/div&gt;
              &lt;div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400"&gt;
                {activeQuiz?.difficulty_level ? getDifficultyLabel(activeQuiz.difficulty_level) : '...'}
              &lt;/div&gt;
            &lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          {/* Mode Selection - Enhanced for mobile */}
          &lt;div className="mb-8"&gt;
            &lt;h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-4 sm:mb-6 text-center"&gt;{t('modeSelection.chooseMode')}&lt;/h3&gt;
            &lt;div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6"&gt;
              {modes.map((mode) =&gt; (
                &lt;div
                  key={mode.id}
                  onClick={() =&gt; handleModeSelect(mode)}
                  className={`cursor-pointer rounded-lg border-2 p-4 sm:p-6 transition-all duration-200 ${
                    selectedMode === mode.id
                      ? mode.selectedColor
                      : mode.color
                  }`}
                &gt;
                  &lt;div className="relative"&gt;
                    &lt;div className="flex items-start justify-between mb-4"&gt;
                      &lt;div className="flex items-center"&gt;
                        &lt;span className="text-xl sm:text-2xl mr-3"&gt;{mode.icon}&lt;/span&gt;
                        &lt;div&gt;
                          &lt;h4 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white"&gt;{mode.name}&lt;/h4&gt;
                          {mode.recommended &amp;&amp; (
                            &lt;span className="inline-block bg-green-500 text-white text-xs px-2 py-1 rounded-full mt-1"&gt;
                              {t('modeSelection.recommended')}
                            &lt;/span&gt;
                          )}
                        &lt;/div&gt;
                      &lt;/div&gt;
                      {selectedMode === mode.id &amp;&amp; (
                        &lt;CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600 dark:text-blue-400 flex-shrink-0" /&gt;
                      )}
                    &lt;/div&gt;
&nbsp;
                    &lt;p className="text-gray-600 dark:text-gray-300 mb-4 text-sm sm:text-base"&gt;{mode.description}&lt;/p&gt;
&nbsp;
                    &lt;ul className="space-y-2"&gt;
                      {mode.features.map((feature, index) =&gt; (
                        &lt;li key={index} className="flex items-start"&gt;
                          &lt;CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" /&gt;
                          &lt;span className="text-gray-700 dark:text-gray-300 text-xs sm:text-sm"&gt;{feature}&lt;/span&gt;
                        &lt;/li&gt;
                      ))}
                    &lt;/ul&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              ))}
            &lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          {/* Action Buttons - Enhanced for mobile */}
          &lt;div className="flex justify-center"&gt;
            &lt;button
              onClick={handleStartQuiz}
              disabled={!canStartQuiz()}
              className={`${
                canStartQuiz()
                  ? 'bg-blue-600 text-white hover:bg-blue-700 transform hover:scale-105'
                  : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              } px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-medium text-base sm:text-lg transition-all duration-200 inline-flex items-center justify-center`}
            &gt;
              &lt;PlayCircle className="h-5 w-5 sm:h-6 sm:w-6 mr-2" /&gt;
              {canStartQuiz() ? t('modeSelection.startQuiz') : t('modeSelection.selectMode')}
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/PageWrapper&gt;
  );
};
&nbsp;
export default QuizModeSelectionScreen; </pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-10T02:31:05.870Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    