
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for screens/QuizScreen/QuizScreen.style.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">screens/QuizScreen</a> QuizScreen.style.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/133</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/133</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * QuizScreen Styling Constants
 * Defines all styling classes and theme integration for the Quiz screen
 */
&nbsp;
// Container and Layout Styles
<span class="cstat-no" title="statement not covered" >export const containerStyles = {</span>
<span class="cstat-no" title="statement not covered" >  wrapper: 'min-h-screen bg-gray-50 dark:bg-gray-900',</span>
<span class="cstat-no" title="statement not covered" >  loadingWrapper: 'flex items-center justify-center min-h-96',</span>
<span class="cstat-no" title="statement not covered" >  errorCard: 'text-center',</span>
<span class="cstat-no" title="statement not covered" >  header: 'mb-6',</span>
<span class="cstat-no" title="statement not covered" >  headerTop: 'flex items-center justify-between mb-4',</span>
<span class="cstat-no" title="statement not covered" >  headerLeft: 'flex items-center gap-3',</span>
<span class="cstat-no" title="statement not covered" >  headerRight: 'text-right',</span>
<span class="cstat-no" title="statement not covered" >  title: 'text-xl font-semibold text-gray-900 dark:text-white',</span>
<span class="cstat-no" title="statement not covered" >  subtitle: 'text-sm text-gray-600 dark:text-gray-300',</span>
<span class="cstat-no" title="statement not covered" >  questionInfo: 'text-sm text-gray-600 dark:text-gray-300',</span>
<span class="cstat-no" title="statement not covered" >  answeredInfo: 'text-xs text-gray-500 dark:text-gray-400',</span>
<span class="cstat-no" title="statement not covered" >  progressBar: 'w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2',</span>
<span class="cstat-no" title="statement not covered" >  progressFill: 'bg-blue-600 h-2 rounded-full transition-all duration-300'</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// Loading and Error Styles
<span class="cstat-no" title="statement not covered" >export const loadingStyles = {</span>
<span class="cstat-no" title="statement not covered" >  container: 'text-center',</span>
<span class="cstat-no" title="statement not covered" >  spinner: 'animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4',</span>
<span class="cstat-no" title="statement not covered" >  text: 'text-gray-600 dark:text-gray-300'</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const errorStyles = {</span>
<span class="cstat-no" title="statement not covered" >  icon: 'w-12 h-12 mx-auto mb-2',</span>
<span class="cstat-no" title="statement not covered" >  iconContainer: 'text-red-600 mb-4',</span>
<span class="cstat-no" title="statement not covered" >  title: 'text-xl font-semibold mb-2 text-gray-900 dark:text-white',</span>
<span class="cstat-no" title="statement not covered" >  message: 'text-gray-600 dark:text-gray-300 mb-4'</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// Button Styles
<span class="cstat-no" title="statement not covered" >export const buttonStyles = {</span>
<span class="cstat-no" title="statement not covered" >  quit: 'text-gray-600 hover:text-gray-800 transition-colors',</span>
<span class="cstat-no" title="statement not covered" >  previous: 'btn-responsive bg-gray-600 text-white hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed',</span>
<span class="cstat-no" title="statement not covered" >  checkAnswer: 'btn-responsive bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed',</span>
<span class="cstat-no" title="statement not covered" >  next: 'btn-responsive bg-green-600 text-white hover:bg-green-700',</span>
<span class="cstat-no" title="statement not covered" >  finish: 'btn-responsive bg-green-600 text-white hover:bg-green-700'</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// Question Card Styles
<span class="cstat-no" title="statement not covered" >export const questionStyles = {</span>
<span class="cstat-no" title="statement not covered" >  card: 'mb-6',</span>
<span class="cstat-no" title="statement not covered" >  header: 'mb-6',</span>
<span class="cstat-no" title="statement not covered" >  questionRow: 'flex items-start gap-3 mb-4',</span>
<span class="cstat-no" title="statement not covered" >  questionNumber: 'flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center',</span>
<span class="cstat-no" title="statement not covered" >  questionNumberText: 'text-blue-600 font-semibold text-sm',</span>
<span class="cstat-no" title="statement not covered" >  questionContent: 'flex-grow',</span>
<span class="cstat-no" title="statement not covered" >  questionText: 'text-lg font-medium text-gray-900 mb-2',</span>
<span class="cstat-no" title="statement not covered" >  exampleBox: 'bg-gray-50 p-3 rounded-lg mb-4',</span>
<span class="cstat-no" title="statement not covered" >  exampleText: 'text-gray-700 italic',</span>
<span class="cstat-no" title="statement not covered" >  optionsContainer: 'space-y-3'</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// Answer Option Styles
<span class="cstat-no" title="statement not covered" >export const getOptionClassName = (</span>
<span class="cstat-no" title="statement not covered" >  isSelected: boolean,</span>
<span class="cstat-no" title="statement not covered" >  isCorrectOption: boolean,</span>
<span class="cstat-no" title="statement not covered" >  showFeedback: boolean,</span>
<span class="cstat-no" title="statement not covered" >  mode: 'practice' | 'test'</span>
<span class="cstat-no" title="statement not covered" >): string =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  let baseClass = 'p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (showFeedback &amp;&amp; mode === 'practice') {</span>
<span class="cstat-no" title="statement not covered" >    if (isCorrectOption) {</span>
<span class="cstat-no" title="statement not covered" >      baseClass += 'border-green-500 bg-green-50 dark:bg-green-900/20 ';</span>
<span class="cstat-no" title="statement not covered" >    } else if (isSelected &amp;&amp; !isCorrectOption) {</span>
<span class="cstat-no" title="statement not covered" >      baseClass += 'border-red-500 bg-red-50 dark:bg-red-900/20 ';</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      baseClass += 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 ';</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  } else if (isSelected) {</span>
<span class="cstat-no" title="statement not covered" >    baseClass += 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 ';</span>
<span class="cstat-no" title="statement not covered" >  } else {</span>
<span class="cstat-no" title="statement not covered" >    baseClass += 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 ';</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return baseClass;</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const optionStyles = {</span>
<span class="cstat-no" title="statement not covered" >  container: 'flex items-center gap-3',</span>
<span class="cstat-no" title="statement not covered" >  circle: 'w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-medium',</span>
<span class="cstat-no" title="statement not covered" >  text: 'text-gray-900',</span>
<span class="cstat-no" title="statement not covered" >  checkIcon: 'w-5 h-5 text-green-500 ml-auto',</span>
<span class="cstat-no" title="statement not covered" >  xIcon: 'w-5 h-5 text-red-500 ml-auto'</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const getOptionCircleClassName = (isSelected: boolean): string =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  return isSelected </span>
<span class="cstat-no" title="statement not covered" >    ? 'border-blue-500 bg-blue-500 text-white' </span>
<span class="cstat-no" title="statement not covered" >    : 'border-gray-300';</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// Feedback Styles
<span class="cstat-no" title="statement not covered" >export const feedbackStyles = {</span>
<span class="cstat-no" title="statement not covered" >  container: 'mt-6 p-4 rounded-lg border-l-4',</span>
<span class="cstat-no" title="statement not covered" >  correct: 'bg-green-50 border-green-500',</span>
<span class="cstat-no" title="statement not covered" >  incorrect: 'bg-red-50 border-red-500',</span>
<span class="cstat-no" title="statement not covered" >  content: 'flex items-start gap-3',</span>
<span class="cstat-no" title="statement not covered" >  icon: 'flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center',</span>
<span class="cstat-no" title="statement not covered" >  correctIcon: 'bg-green-500',</span>
<span class="cstat-no" title="statement not covered" >  incorrectIcon: 'bg-red-500',</span>
<span class="cstat-no" title="statement not covered" >  iconSvg: 'w-4 h-4 text-white',</span>
<span class="cstat-no" title="statement not covered" >  textContainer: 'flex-grow',</span>
<span class="cstat-no" title="statement not covered" >  title: 'font-medium mb-2',</span>
<span class="cstat-no" title="statement not covered" >  correctTitle: 'text-green-800',</span>
<span class="cstat-no" title="statement not covered" >  incorrectTitle: 'text-red-800',</span>
<span class="cstat-no" title="statement not covered" >  explanation: 'text-sm',</span>
<span class="cstat-no" title="statement not covered" >  correctExplanation: 'text-green-700',</span>
<span class="cstat-no" title="statement not covered" >  incorrectExplanation: 'text-red-700',</span>
<span class="cstat-no" title="statement not covered" >  correctAnswer: 'text-sm text-red-700 mt-2'</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// Navigation Styles
<span class="cstat-no" title="statement not covered" >export const navigationStyles = {</span>
<span class="cstat-no" title="statement not covered" >  container: 'flex flex-col sm:flex-row gap-4 justify-between',</span>
<span class="cstat-no" title="statement not covered" >  leftSection: 'flex gap-2',</span>
<span class="cstat-no" title="statement not covered" >  rightSection: 'flex gap-2'</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// Question Overview Styles (Mobile)
<span class="cstat-no" title="statement not covered" >export const overviewStyles = {</span>
<span class="cstat-no" title="statement not covered" >  container: 'mt-6 sm:hidden',</span>
<span class="cstat-no" title="statement not covered" >  title: 'font-medium text-gray-900 mb-3',</span>
<span class="cstat-no" title="statement not covered" >  grid: 'grid grid-cols-5 gap-2',</span>
<span class="cstat-no" title="statement not covered" >  questionButton: 'w-8 h-8 rounded text-sm font-medium transition-colors'</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const getQuestionButtonClassName = (</span>
<span class="cstat-no" title="statement not covered" >  isCurrent: boolean,</span>
<span class="cstat-no" title="statement not covered" >  isAnswered: boolean</span>
<span class="cstat-no" title="statement not covered" >): string =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  if (isCurrent) {</span>
<span class="cstat-no" title="statement not covered" >    return 'bg-blue-600 text-white';</span>
<span class="cstat-no" title="statement not covered" >  } else if (isAnswered) {</span>
<span class="cstat-no" title="statement not covered" >    return 'bg-green-100 text-green-800 border border-green-300';</span>
<span class="cstat-no" title="statement not covered" >  } else {</span>
<span class="cstat-no" title="statement not covered" >    return 'bg-gray-100 text-gray-600 border border-gray-300';</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// Icon paths for SVG icons
<span class="cstat-no" title="statement not covered" >export const iconPaths = {</span>
<span class="cstat-no" title="statement not covered" >  close: 'M6 18L18 6M6 6l12 12',</span>
<span class="cstat-no" title="statement not covered" >  warning: 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',</span>
<span class="cstat-no" title="statement not covered" >  chevronLeft: 'M15 19l-7-7 7-7',</span>
<span class="cstat-no" title="statement not covered" >  chevronRight: 'M9 5l7 7-7 7',</span>
<span class="cstat-no" title="statement not covered" >  check: 'M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z',</span>
<span class="cstat-no" title="statement not covered" >  x: 'M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z',</span>
<span class="cstat-no" title="statement not covered" >  checkCircle: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// Mode specific styles
<span class="cstat-no" title="statement not covered" >export const modeStyles = {</span>
<span class="cstat-no" title="statement not covered" >  practice: '🎯 Practice Mode',</span>
<span class="cstat-no" title="statement not covered" >  test: '📝 Test Mode'</span>
<span class="cstat-no" title="statement not covered" >}; </span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-10T02:31:05.870Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    