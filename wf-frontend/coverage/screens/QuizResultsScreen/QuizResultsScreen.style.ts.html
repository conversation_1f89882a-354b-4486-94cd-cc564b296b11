
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for screens/QuizResultsScreen/QuizResultsScreen.style.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">screens/QuizResultsScreen</a> QuizResultsScreen.style.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/66</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/66</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * QuizResultsScreen Styles
 * Centralized styling constants and theme integration
 */
&nbsp;
// Component styling constants
<span class="cstat-no" title="statement not covered" >export const styles = {</span>
  // Page layout
<span class="cstat-no" title="statement not covered" >  pageContainer: 'py-8',</span>
  
  // Header styles
<span class="cstat-no" title="statement not covered" >  headerContainer: 'text-center mb-8',</span>
<span class="cstat-no" title="statement not covered" >  successIcon: 'w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4',</span>
<span class="cstat-no" title="statement not covered" >  successIconInner: 'w-8 h-8 text-green-600',</span>
<span class="cstat-no" title="statement not covered" >  failIcon: 'w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4',</span>
<span class="cstat-no" title="statement not covered" >  failIconInner: 'w-8 h-8 text-red-600',</span>
<span class="cstat-no" title="statement not covered" >  title: 'text-3xl font-bold text-gray-900 dark:text-white mb-2',</span>
<span class="cstat-no" title="statement not covered" >  subtitle: 'text-gray-600 dark:text-gray-300',</span>
  
  // Score card styles
<span class="cstat-no" title="statement not covered" >  scoreCard: 'mb-8 text-center',</span>
<span class="cstat-no" title="statement not covered" >  scoreNumber: 'text-6xl font-bold mb-2 transition-all duration-1000',</span>
<span class="cstat-no" title="statement not covered" >  performanceBadge: 'inline-flex items-center px-4 py-2 rounded-full text-sm font-medium',</span>
<span class="cstat-no" title="statement not covered" >  statsGrid: 'grid grid-cols-1 sm:grid-cols-3 gap-6 text-center',</span>
<span class="cstat-no" title="statement not covered" >  statValue: 'text-2xl font-bold text-gray-900 dark:text-white',</span>
<span class="cstat-no" title="statement not covered" >  statLabel: 'text-sm text-gray-600 dark:text-gray-300',</span>
  
  // Performance analysis
<span class="cstat-no" title="statement not covered" >  analysisCard: 'mb-8',</span>
<span class="cstat-no" title="statement not covered" >  analysisTitle: 'text-xl font-semibold text-gray-900 dark:text-white mb-4',</span>
<span class="cstat-no" title="statement not covered" >  progressSection: 'space-y-4',</span>
<span class="cstat-no" title="statement not covered" >  progressHeader: 'flex justify-between items-center mb-2',</span>
<span class="cstat-no" title="statement not covered" >  progressLabel: 'text-sm font-medium text-gray-700 dark:text-gray-300',</span>
<span class="cstat-no" title="statement not covered" >  progressValue: 'text-sm text-gray-600 dark:text-gray-400',</span>
<span class="cstat-no" title="statement not covered" >  progressBar: 'w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2',</span>
<span class="cstat-no" title="statement not covered" >  progressFill: 'h-2 rounded-full transition-all duration-1000',</span>
  
  // Detailed results
<span class="cstat-no" title="statement not covered" >  detailedToggle: 'w-full text-left px-4 py-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center justify-between',</span>
<span class="cstat-no" title="statement not covered" >  detailedTitle: 'font-medium text-gray-900 dark:text-white',</span>
<span class="cstat-no" title="statement not covered" >  expandIcon: 'w-5 h-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200',</span>
<span class="cstat-no" title="statement not covered" >  questionList: 'mt-4 space-y-4',</span>
<span class="cstat-no" title="statement not covered" >  questionItem: 'border border-gray-200 dark:border-gray-700 rounded-lg p-4',</span>
<span class="cstat-no" title="statement not covered" >  questionHeader: 'flex items-center justify-between mb-2',</span>
<span class="cstat-no" title="statement not covered" >  questionTitle: 'font-medium text-gray-900 dark:text-white',</span>
<span class="cstat-no" title="statement not covered" >  questionBadge: 'px-2 py-1 rounded text-xs font-medium',</span>
<span class="cstat-no" title="statement not covered" >  correctBadge: 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200',</span>
<span class="cstat-no" title="statement not covered" >  incorrectBadge: 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200',</span>
<span class="cstat-no" title="statement not covered" >  questionText: 'text-gray-700 dark:text-gray-300 mb-2',</span>
<span class="cstat-no" title="statement not covered" >  answerSection: 'grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm',</span>
<span class="cstat-no" title="statement not covered" >  answerLabel: 'font-medium text-gray-700 dark:text-gray-300',</span>
<span class="cstat-no" title="statement not covered" >  userAnswer: 'text-red-600',</span>
<span class="cstat-no" title="statement not covered" >  correctAnswer: 'text-green-600',</span>
  
  // Action buttons
<span class="cstat-no" title="statement not covered" >  actionGrid: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4',</span>
<span class="cstat-no" title="statement not covered" >  primaryButton: 'btn-responsive text-white',</span>
<span class="cstat-no" title="statement not covered" >  secondaryButton: 'btn-responsive bg-gray-600 text-white hover:bg-gray-700',</span>
  
  // Loading state
<span class="cstat-no" title="statement not covered" >  loadingContainer: 'flex items-center justify-center min-h-96',</span>
<span class="cstat-no" title="statement not covered" >  loadingContent: 'text-center',</span>
<span class="cstat-no" title="statement not covered" >  loadingSpinner: 'animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4',</span>
<span class="cstat-no" title="statement not covered" >  loadingText: 'text-gray-600 dark:text-gray-300'</span>
<span class="cstat-no" title="statement not covered" >} as const;</span>
&nbsp;
// Score color mappings
<span class="cstat-no" title="statement not covered" >export const scoreColors = {</span>
<span class="cstat-no" title="statement not covered" >  excellent: 'text-green-600',</span>
<span class="cstat-no" title="statement not covered" >  veryGood: 'text-blue-600', </span>
<span class="cstat-no" title="statement not covered" >  good: 'text-yellow-600',</span>
<span class="cstat-no" title="statement not covered" >  fair: 'text-orange-600',</span>
<span class="cstat-no" title="statement not covered" >  needsImprovement: 'text-red-600'</span>
<span class="cstat-no" title="statement not covered" >} as const;</span>
&nbsp;
// Progress bar colors
<span class="cstat-no" title="statement not covered" >export const progressColors = {</span>
<span class="cstat-no" title="statement not covered" >  high: 'bg-green-500',</span>
<span class="cstat-no" title="statement not covered" >  medium: 'bg-blue-500',</span>
<span class="cstat-no" title="statement not covered" >  low: 'bg-yellow-500',</span>
<span class="cstat-no" title="statement not covered" >  poor: 'bg-red-500'</span>
<span class="cstat-no" title="statement not covered" >} as const;</span>
&nbsp;
// Performance badge styles
<span class="cstat-no" title="statement not covered" >export const performanceBadges = {</span>
<span class="cstat-no" title="statement not covered" >  excellent: 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200',</span>
<span class="cstat-no" title="statement not covered" >  veryGood: 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200',</span>
<span class="cstat-no" title="statement not covered" >  good: 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200',</span>
<span class="cstat-no" title="statement not covered" >  fair: 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200',</span>
<span class="cstat-no" title="statement not covered" >  needsImprovement: 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200'</span>
<span class="cstat-no" title="statement not covered" >} as const;</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-10T02:31:05.870Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    