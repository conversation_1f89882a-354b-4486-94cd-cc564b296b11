
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">45.73% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>482/1054</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.18% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>74/119</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.14% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>24/42</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">45.73% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>482/1054</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="authService.ts"><a href="authService.ts.html">authService.ts</a></td>
	<td data-value="87.06" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 87%"></div><div class="cover-empty" style="width: 13%"></div></div>
	</td>
	<td data-value="87.06" class="pct high">87.06%</td>
	<td data-value="116" class="abs high">101/116</td>
	<td data-value="54.83" class="pct medium">54.83%</td>
	<td data-value="31" class="abs medium">17/31</td>
	<td data-value="85.71" class="pct high">85.71%</td>
	<td data-value="7" class="abs high">6/7</td>
	<td data-value="87.06" class="pct high">87.06%</td>
	<td data-value="116" class="abs high">101/116</td>
	</tr>

<tr>
	<td class="file low" data-value="quizService.ts"><a href="quizService.ts.html">quizService.ts</a></td>
	<td data-value="37.17" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 37%"></div><div class="cover-empty" style="width: 63%"></div></div>
	</td>
	<td data-value="37.17" class="pct low">37.17%</td>
	<td data-value="764" class="abs low">284/764</td>
	<td data-value="60.34" class="pct medium">60.34%</td>
	<td data-value="58" class="abs medium">35/58</td>
	<td data-value="44.44" class="pct low">44.44%</td>
	<td data-value="18" class="abs low">8/18</td>
	<td data-value="37.17" class="pct low">37.17%</td>
	<td data-value="764" class="abs low">284/764</td>
	</tr>

<tr>
	<td class="file low" data-value="supabaseClient.ts"><a href="supabaseClient.ts.html">supabaseClient.ts</a></td>
	<td data-value="42.85" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 42%"></div><div class="cover-empty" style="width: 58%"></div></div>
	</td>
	<td data-value="42.85" class="pct low">42.85%</td>
	<td data-value="42" class="abs low">18/42</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="42.85" class="pct low">42.85%</td>
	<td data-value="42" class="abs low">18/42</td>
	</tr>

<tr>
	<td class="file medium" data-value="translationService.ts"><a href="translationService.ts.html">translationService.ts</a></td>
	<td data-value="59.84" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 59%"></div><div class="cover-empty" style="width: 41%"></div></div>
	</td>
	<td data-value="59.84" class="pct medium">59.84%</td>
	<td data-value="132" class="abs medium">79/132</td>
	<td data-value="75.86" class="pct medium">75.86%</td>
	<td data-value="29" class="abs medium">22/29</td>
	<td data-value="71.42" class="pct medium">71.42%</td>
	<td data-value="14" class="abs medium">10/14</td>
	<td data-value="59.84" class="pct medium">59.84%</td>
	<td data-value="132" class="abs medium">79/132</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-10T02:31:05.870Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    