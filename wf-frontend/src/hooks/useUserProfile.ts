import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/context/AuthContext';
import { QuizService } from '@/services/quizService';
import type { User } from '@/types';

interface UseUserProfileReturn {
  userProfile: User | null;
  loading: boolean;
  error: string | null;
  fullName: string;
}

export const useUserProfile = (): UseUserProfileReturn => {
  const { user } = useAuth();

  // Use React Query for better caching and state management
  const {
    data: userProfile,
    isLoading: loading,
    error: queryError,
  } = useQuery({
    queryKey: ['userProfile', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      const { data: userData, error: profileError } = await QuizService.getUserProfile(user.id);

      if (profileError) {
        throw new Error(profileError);
      }

      return userData;
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
  });

  const error = queryError ? (queryError as Error).message : null;

  // Generate full name from available data
  const getFullName = (): string => {
    // Try to get from database profile first (when loaded)
    if (userProfile?.first_name && userProfile?.last_name) {
      return `${userProfile.first_name} ${userProfile.last_name}`.trim();
    }

    // Fallback to auth user (custom User type)
    if (user?.first_name && user?.last_name) {
      return `${user.first_name} ${user.last_name}`.trim();
    }

    // Final fallback to email
    return user?.email || 'User';
  };

  return {
    userProfile: userProfile ?? null,
    loading,
    error,
    fullName: getFullName(),
  };
};
