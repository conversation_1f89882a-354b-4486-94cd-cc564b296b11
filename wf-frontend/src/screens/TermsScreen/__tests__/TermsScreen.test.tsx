import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import TermsScreen from '../TermsScreen';
import { useLanguage } from '@/context/LanguageContext';

// Mock dependencies
vi.mock('@/context/LanguageContext');
vi.mock('@/components/ConditionalLayout', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="conditional-layout">{children}</div>
}));
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props} data-testid="card">{children}</div>,
  CardContent: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props} data-testid="card-content">{children}</div>,
  CardHeader: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props} data-testid="card-header">{children}</div>,
  CardTitle: ({ children, ...props }: React.ComponentProps<'h1'>) => <h1 {...props} data-testid="card-title">{children}</h1>
}));

// Mock translation function
const mockT = vi.fn((key: string) => {
  const translations: Record<string, string> = {
    'terms.title': 'Terms of Service',
    'terms.lastUpdated': 'Last updated: January 1, 2024',
    'terms.section1': 'Acceptance of Terms',
    'terms.section2': 'Use of Service',
    'terms.section3': 'User Accounts',
    'terms.section4': 'Content and Conduct',
    'terms.section5': 'Privacy Policy',
    'terms.section6': 'Termination',
    'terms.section7': 'Disclaimers',
    'terms.section8': 'Contact Information',
    'terms.content.section1Text': 'By using our service, you agree to these terms.',
    'terms.content.section2Text': 'You may use our service for lawful purposes only.',
    'terms.content.section3Text': 'You are responsible for maintaining account security.',
    'terms.content.section4Text': 'You must not post inappropriate content.',
    'terms.content.section5Text': 'Your privacy is important to us.',
    'terms.content.section6Text': 'We may terminate accounts for violations.',
    'terms.content.section7Text': 'Service is provided as-is without warranties.',
    'terms.content.section8Text': 'Contact us with any questions about these terms.'
  };
  return translations[key] || key;
});

// Mock getRaw function for list items
const mockGetRaw = vi.fn((key: string) => {
  const rawData: Record<string, string[]> = {
    'terms.content.section2List': [
      'Follow all applicable laws',
      'Respect other users',
      'Do not misuse the service'
    ],
    'terms.content.section5List': [
      'We collect minimal data',
      'Data is used to improve service',
      'We do not sell personal information'
    ]
  };
  return rawData[key] || [];
});

describe('TermsScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    vi.mocked(useLanguage).mockReturnValue({
      t: mockT,
      getRaw: mockGetRaw,
      language: 'en',
      setLanguage: vi.fn(),
      isLoading: false,
      availableLanguages: ['en', 'vi'] as const,
      getLanguageDisplayName: vi.fn((lang) => lang === 'en' ? 'English' : 'Vietnamese'),
      getLanguageFlag: vi.fn((lang) => lang === 'en' ? '🇺🇸' : '🇻🇳')
    });
  });

  const renderTermsScreen = () => {
    return render(<TermsScreen />);
  };

  describe('Layout and Structure', () => {
    it('renders within conditional layout', () => {
      renderTermsScreen();

      expect(screen.getByTestId('conditional-layout')).toBeInTheDocument();
    });

    it('renders within card component', () => {
      renderTermsScreen();

      expect(screen.getByTestId('card')).toBeInTheDocument();
      expect(screen.getByTestId('card-header')).toBeInTheDocument();
      expect(screen.getByTestId('card-content')).toBeInTheDocument();
    });

    it('displays main title with file text icon', () => {
      renderTermsScreen();

      expect(screen.getByText('Terms of Service')).toBeInTheDocument();
      expect(screen.getByTestId('card-title')).toBeInTheDocument();
    });

    it('displays last updated information', () => {
      renderTermsScreen();

      expect(screen.getByText('Last updated: January 1, 2024')).toBeInTheDocument();
    });
  });

  describe('Terms of Service Sections', () => {
    it('displays section 1 - Acceptance of Terms', () => {
      renderTermsScreen();

      expect(screen.getByText('1. Acceptance of Terms')).toBeInTheDocument();
      expect(screen.getByText('By using our service, you agree to these terms.')).toBeInTheDocument();
    });

    it('displays section 2 - Use of Service', () => {
      renderTermsScreen();

      expect(screen.getByText('2. Use of Service')).toBeInTheDocument();
      expect(screen.getByText('You may use our service for lawful purposes only.')).toBeInTheDocument();
    });

    it('displays section 2 list items', () => {
      renderTermsScreen();

      expect(screen.getByText('Follow all applicable laws')).toBeInTheDocument();
      expect(screen.getByText('Respect other users')).toBeInTheDocument();
      expect(screen.getByText('Do not misuse the service')).toBeInTheDocument();
    });

    it('displays section 3 - User Accounts', () => {
      renderTermsScreen();

      expect(screen.getByText('3. User Accounts')).toBeInTheDocument();
      expect(screen.getByText('You are responsible for maintaining account security.')).toBeInTheDocument();
    });

    it('displays section 4 - Content and Conduct', () => {
      renderTermsScreen();

      expect(screen.getByText('4. Content and Conduct')).toBeInTheDocument();
      expect(screen.getByText('You must not post inappropriate content.')).toBeInTheDocument();
    });

    it('displays section 5 - Privacy Policy', () => {
      renderTermsScreen();

      expect(screen.getByText('5. Privacy Policy')).toBeInTheDocument();
      expect(screen.getByText('Your privacy is important to us.')).toBeInTheDocument();
    });

    it('displays section 5 list items', () => {
      renderTermsScreen();

      expect(screen.getByText('We collect minimal data')).toBeInTheDocument();
      expect(screen.getByText('Data is used to improve service')).toBeInTheDocument();
      expect(screen.getByText('We do not sell personal information')).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('uses language context for translations', () => {
      renderTermsScreen();

      expect(useLanguage).toHaveBeenCalled();
      expect(mockT).toHaveBeenCalledWith('terms.title');
      expect(mockT).toHaveBeenCalledWith('terms.lastUpdated');
      expect(mockT).toHaveBeenCalledWith('terms.section1');
    });

    it('uses getRaw for list content', () => {
      renderTermsScreen();

      expect(mockGetRaw).toHaveBeenCalledWith('terms.content.section2List');
      expect(mockGetRaw).toHaveBeenCalledWith('terms.content.section5List');
    });
  });

  describe('Responsive Design', () => {
    it('applies responsive container classes', () => {
      renderTermsScreen();

      const container = screen.getByText('Terms of Service').closest('.max-w-4xl');
      expect(container).toHaveClass('max-w-4xl', 'mx-auto', 'px-4', 'sm:px-6', 'lg:px-8', 'py-8');
    });

    it('applies prose classes for content formatting', () => {
      renderTermsScreen();

      const content = screen.getByTestId('card-content');
      expect(content).toHaveClass('prose', 'prose-gray', 'dark:prose-invert', 'max-w-none');
    });
  });

  describe('Dark Mode Support', () => {
    it('applies dark mode classes to headings', () => {
      renderTermsScreen();

      const section1Heading = screen.getByText('1. Acceptance of Terms');
      expect(section1Heading).toHaveClass('dark:text-white');

      const section2Heading = screen.getByText('2. Use of Service');
      expect(section2Heading).toHaveClass('dark:text-white');
    });

    it('applies dark mode classes to text content', () => {
      renderTermsScreen();

      const lastUpdated = screen.getByText('Last updated: January 1, 2024');
      expect(lastUpdated).toHaveClass('dark:text-gray-400');

      const section1Text = screen.getByText('By using our service, you agree to these terms.');
      expect(section1Text).toHaveClass('dark:text-gray-300');
    });

    it('applies dark mode classes to list items', () => {
      renderTermsScreen();

      const listItems = screen.getAllByText(/Follow all applicable|Respect other|Do not misuse/);
      listItems.forEach(item => {
        const listElement = item.closest('ul');
        expect(listElement).toHaveClass('dark:text-gray-300');
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper heading hierarchy', () => {
      renderTermsScreen();

      const mainTitle = screen.getByText('Terms of Service');
      expect(mainTitle.closest('[data-testid="card-title"]')).toBeInTheDocument();

      const sectionHeadings = screen.getAllByText(/^\d+\./);
      sectionHeadings.forEach(heading => {
        expect(heading.tagName).toBe('H2');
      });
    });

    it('has proper list structure', () => {
      renderTermsScreen();

      const lists = screen.getAllByRole('list');
      expect(lists.length).toBeGreaterThan(0);

      lists.forEach(list => {
        expect(list.tagName).toBe('UL');
        expect(list).toHaveClass('list-disc', 'list-inside');
      });
    });
  });

  describe('Content Completeness', () => {
    it('renders all expected sections', () => {
      renderTermsScreen();

      // Check that all main sections are present
      expect(screen.getByText('1. Acceptance of Terms')).toBeInTheDocument();
      expect(screen.getByText('2. Use of Service')).toBeInTheDocument();
      expect(screen.getByText('3. User Accounts')).toBeInTheDocument();
      expect(screen.getByText('4. Content and Conduct')).toBeInTheDocument();
      expect(screen.getByText('5. Privacy Policy')).toBeInTheDocument();
    });

    it('handles empty list data gracefully', () => {
      vi.mocked(useLanguage).mockReturnValue({
        t: mockT,
        getRaw: vi.fn(() => []),
        language: 'en',
        setLanguage: vi.fn(),
        isLoading: false,
        availableLanguages: ['en', 'vi'] as const,
        getLanguageDisplayName: vi.fn((lang) => lang === 'en' ? 'English' : 'Vietnamese'),
        getLanguageFlag: vi.fn((lang) => lang === 'en' ? '🇺🇸' : '🇻🇳')
      });

      renderTermsScreen();

      // Should still render sections even with empty lists
      expect(screen.getByText('1. Acceptance of Terms')).toBeInTheDocument();
      expect(screen.getByText('By using our service, you agree to these terms.')).toBeInTheDocument();
    });
  });

  describe('Translation Support', () => {
    it('handles missing translations gracefully', () => {
      const mockTWithMissing = vi.fn((key: string) => key);
      vi.mocked(useLanguage).mockReturnValue({
        t: mockTWithMissing,
        getRaw: mockGetRaw,
        language: 'en',
        setLanguage: vi.fn(),
        isLoading: false,
        availableLanguages: ['en', 'vi'] as const,
        getLanguageDisplayName: vi.fn((lang) => lang === 'en' ? 'English' : 'Vietnamese'),
        getLanguageFlag: vi.fn((lang) => lang === 'en' ? '🇺🇸' : '🇻🇳')
      });

      renderTermsScreen();

      expect(screen.getByText('terms.title')).toBeInTheDocument();
      expect(screen.getByText('terms.lastUpdated')).toBeInTheDocument();
    });
  });

  describe('Legal Content Structure', () => {
    it('displays terms in logical order', () => {
      renderTermsScreen();

      const sections = screen.getAllByText(/^\d+\./);
      expect(sections[0]).toHaveTextContent('1. Acceptance of Terms');
      expect(sections[1]).toHaveTextContent('2. Use of Service');
      expect(sections[2]).toHaveTextContent('3. User Accounts');
      expect(sections[3]).toHaveTextContent('4. Content and Conduct');
      expect(sections[4]).toHaveTextContent('5. Privacy Policy');
    });

    it('provides comprehensive coverage of terms topics', () => {
      renderTermsScreen();

      // Verify key legal topics are covered
      expect(screen.getByText(/Acceptance of Terms/)).toBeInTheDocument();
      expect(screen.getByText(/Use of Service/)).toBeInTheDocument();
      expect(screen.getByText(/User Accounts/)).toBeInTheDocument();
      expect(screen.getByText(/Content and Conduct/)).toBeInTheDocument();
      expect(screen.getByText(/Privacy Policy/)).toBeInTheDocument();
    });
  });
});
