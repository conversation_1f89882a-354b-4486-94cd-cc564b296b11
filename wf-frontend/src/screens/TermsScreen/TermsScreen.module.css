/**
 * TermsScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 33 lines | AFTER: ~20 lines | REDUCTION: ~39%
 * 
 * Uses shared modules for:
 * - Container layouts (pageContainer)
 */

/* Container using shared layout */
.container {
  composes: pageContainer from '@/styles/shared/layouts/containers.module.css';
}

/* Component-specific styles */
.cardTitle {
  display: flex;
  align-items: center;
  font-size: theme('fontSize.3xl');
}

.cardContent {
  max-width: none;
  line-height: theme('lineHeight.7');
  color: theme('colors.gray.700');
}

:global(.dark) .cardContent {
  color: theme('colors.gray.300');
}