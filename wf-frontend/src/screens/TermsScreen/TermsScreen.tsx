import React from 'react';
import { FileText } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import ConditionalLayout from "@/components/ConditionalLayout";
import { useLanguage } from '@/context/LanguageContext';

const TermsScreen: React.FC = () => {
  const { t, getRaw } = useLanguage();

  return (
    <ConditionalLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-3xl">
              <FileText className="h-8 w-8 mr-3" />
              {t('terms.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="prose prose-gray dark:prose-invert max-w-none">
            <p className="text-gray-600 dark:text-gray-400 mb-6">{t('terms.lastUpdated')}</p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">1. {t('terms.section1')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('terms.content.section1Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">2. {t('terms.section2')}</h2>
            <p className="mb-4 text-gray-700 dark:text-gray-300">
              {t('terms.content.section2Text')}
            </p>
            <ul className="list-disc list-inside mb-6 space-y-2 text-gray-700 dark:text-gray-300">
              {getRaw('terms.content.section2List')?.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">3. {t('terms.section3')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('terms.content.section3Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">4. {t('terms.section4')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('terms.content.section4Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">5. {t('terms.section5')}</h2>
            <p className="mb-4 text-gray-700 dark:text-gray-300">{t('terms.content.section5Text')}</p>
            <ul className="list-disc list-inside mb-6 space-y-2 text-gray-700 dark:text-gray-300">
              {getRaw('terms.content.section5List')?.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">6. {t('terms.section6')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('terms.content.section6Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">7. {t('terms.section7')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('terms.content.section7Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">8. {t('terms.section8')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('terms.content.section8Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">9. {t('terms.section9')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('terms.content.section9Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">10. {t('terms.section10')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('terms.content.section10Text')}
            </p>
          </CardContent>
        </Card>
      </div>
    </ConditionalLayout>
  );
};

export default TermsScreen; 