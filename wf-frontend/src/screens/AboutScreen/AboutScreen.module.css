/**
 * AboutScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 210 lines | AFTER: ~75 lines | REDUCTION: ~64%
 * 
 * Uses shared modules for:
 * - Container layouts (pageContainer)
 * - Typography patterns (pageTitle, sectionTitle, description, etc.)
 */

/* Container using shared layout */
.container {
  composes: pageContainer from '@/styles/shared/layouts/containers.module.css';
}

/* Typography using shared components */
.title {
  composes: pageTitleLarge from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.4xl'); /* Override for about page */
}

.sectionTitle {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
}

.prose {
  composes: bodyText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.lg');
  margin-bottom: theme('spacing.8');
}

.welcomeText {
  composes: subtitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.xl');
  margin-bottom: theme('spacing.6');
}

.description {
  composes: description from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.6');
}

/* Component-specific styles not in shared modules yet */
.section {
  margin-bottom: theme('spacing.12');
}

.featureList {
  list-style-type: disc;
  list-style-position: inside;
  margin-bottom: theme('spacing.6');
}

.featureList li {
  margin-bottom: theme('spacing.2');
}

.cardTitle {
  display: flex;
  align-items: center;
  font-size: theme('fontSize.2xl');
}

.cardIcon {
  height: theme('spacing.6');
  width: theme('spacing.6');
  margin-right: theme('spacing.3');
}

/* Form styles - will be moved to shared modules in Phase 2 */
.form {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.6');
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: theme('spacing.6');
}

@media (min-width: theme('screens.md')) {
  .formGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.formField {
  /* Container for form field */
}

.label {
  display: block;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.700');
  margin-bottom: theme('spacing.2');
}

:global(.dark) .label {
  color: theme('colors.gray.300');
}

.labelWithIcon {
  display: flex;
  align-items: center;
}

.labelIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
  margin-right: theme('spacing.1');
}

.input {
  width: 100%;
  padding: theme('spacing.2') theme('spacing.3');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.lg');
  background-color: white;
  color: theme('colors.gray.900');
}

.input:focus {
  outline: none;
  ring: theme('ringWidth.2') theme('colors.blue.500');
  border-color: transparent;
}

:global(.dark) .input {
  border-color: theme('colors.gray.600');
  background-color: theme('colors.gray.700');
  color: white;
}

.textarea {
  width: 100%;
  padding: theme('spacing.2') theme('spacing.3');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.lg');
  background-color: white;
  color: theme('colors.gray.900');
  resize: vertical;
}

.textarea:focus {
  outline: none;
  ring: theme('ringWidth.2') theme('colors.blue.500');
  border-color: transparent;
}

:global(.dark) .textarea {
  border-color: theme('colors.gray.600');
  background-color: theme('colors.gray.700');
  color: white;
}

.select {
  width: 100%;
  padding: theme('spacing.2') theme('spacing.3');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.lg');
  background-color: white;
  color: theme('colors.gray.900');
}

.select:focus {
  outline: none;
  ring: theme('ringWidth.2') theme('colors.blue.500');
  border-color: transparent;
}

:global(.dark) .select {
  border-color: theme('colors.gray.600');
  background-color: theme('colors.gray.700');
  color: white;
}

.submitContainer {
  display: flex;
  justify-content: flex-end;
}

.submitButton {
  display: flex;
  align-items: center;
  padding: theme('spacing.2') theme('spacing.6');
}

.submitIcon {
  height: theme('spacing.4');
  width: theme('spacing.4');
  margin-right: theme('spacing.2');
}