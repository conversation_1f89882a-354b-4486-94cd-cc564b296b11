import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import AboutScreen from '../AboutScreen';
import { useAboutScreen } from '../AboutScreen.handler';
import { useLanguage } from '@/context/LanguageContext';

// Mock dependencies
vi.mock('../AboutScreen.handler');
vi.mock('@/context/LanguageContext');
vi.mock('@/components/ConditionalLayout', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="conditional-layout">{children}</div>
}));
vi.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: React.ComponentProps<'button'>) => <button {...props}>{children}</button>
}));
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props} data-testid="card">{children}</div>,
  CardContent: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props} data-testid="card-content">{children}</div>,
  CardDescription: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props} data-testid="card-description">{children}</div>,
  CardHeader: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props} data-testid="card-header">{children}</div>,
  CardTitle: ({ children, ...props }: React.ComponentProps<'h2'>) => <h2 {...props} data-testid="card-title">{children}</h2>
}));

// Mock translation function
const mockT = vi.fn((key: string) => {
  const translations: Record<string, string> = {
    'about.title': 'About Us',
    'about.welcome': 'Welcome to our English learning platform!',
    'about.mission.title': 'Our Mission',
    'about.mission.description': 'To provide quality English education for everyone.',
    'about.whatWeOffer.title': 'What We Offer',
    'about.whatWeOffer.grammar.title': 'Grammar',
    'about.whatWeOffer.grammar.description': 'Comprehensive grammar lessons',
    'about.whatWeOffer.vocabulary.title': 'Vocabulary',
    'about.whatWeOffer.vocabulary.description': 'Extensive vocabulary building',
    'about.whatWeOffer.reading.title': 'Reading',
    'about.whatWeOffer.reading.description': 'Reading comprehension exercises',
    'about.whatWeOffer.listening.title': 'Listening',
    'about.whatWeOffer.listening.description': 'Listening skills development',
    'about.whatWeOffer.writing.title': 'Writing',
    'about.whatWeOffer.writing.description': 'Writing practice and improvement',
    'about.whatWeOffer.progress.title': 'Progress Tracking',
    'about.whatWeOffer.progress.description': 'Track your learning progress',
    'about.whyChooseUs.title': 'Why Choose Us',
    'about.whyChooseUs.description': 'We offer the best learning experience.',
    'about.feedback.title': 'Send Feedback',
    'about.feedback.description': 'We value your feedback and suggestions.',
    'about.feedback.form.name': 'Name',
    'about.feedback.form.namePlaceholder': 'Enter your name',
    'about.feedback.form.email': 'Email',
    'about.feedback.form.emailPlaceholder': 'Enter your email',
    'about.feedback.form.subject': 'Subject',
    'about.feedback.form.selectSubject': 'Select a subject',
    'about.feedback.form.subjects.general': 'General Inquiry',
    'about.feedback.form.subjects.bug': 'Bug Report',
    'about.feedback.form.subjects.feature': 'Feature Request',
    'about.feedback.form.subjects.content': 'Content Feedback',
    'about.feedback.form.subjects.technical': 'Technical Support',
    'about.feedback.form.subjects.other': 'Other',
    'about.feedback.form.message': 'Message',
    'about.feedback.form.messagePlaceholder': 'Enter your message',
    'about.feedback.form.sendButton': 'Send Feedback'
  };
  return translations[key] || key;
});

// Mock handler return values
const mockHandlerReturn = {
  feedback: {
    name: '',
    email: '',
    subject: '',
    message: ''
  },
  handleInputChange: vi.fn(),
  handleSubmit: vi.fn()
};

describe('AboutScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    vi.mocked(useLanguage).mockReturnValue({
      t: mockT,
      language: 'en',
      setLanguage: vi.fn(),
      getRaw: vi.fn(),
      isLoading: false,
      availableLanguages: ['en', 'vi'] as const,
      getLanguageDisplayName: vi.fn((lang) => lang === 'en' ? 'English' : 'Vietnamese'),
      getLanguageFlag: vi.fn((lang) => lang === 'en' ? '🇺🇸' : '🇻🇳')
    });
    
    vi.mocked(useAboutScreen).mockReturnValue(mockHandlerReturn);
  });

  const renderAboutScreen = () => {
    return render(<AboutScreen />);
  };

  describe('Layout and Structure', () => {
    it('renders within conditional layout', () => {
      renderAboutScreen();

      expect(screen.getByTestId('conditional-layout')).toBeInTheDocument();
    });

    it('displays main title', () => {
      renderAboutScreen();

      expect(screen.getByText('About Us')).toBeInTheDocument();
    });

    it('displays welcome message', () => {
      renderAboutScreen();

      expect(screen.getByText('Welcome to our English learning platform!')).toBeInTheDocument();
    });
  });

  describe('About Content', () => {
    it('displays mission section', () => {
      renderAboutScreen();

      expect(screen.getByText('Our Mission')).toBeInTheDocument();
      expect(screen.getByText('To provide quality English education for everyone.')).toBeInTheDocument();
    });

    it('displays what we offer section', () => {
      renderAboutScreen();

      expect(screen.getByText('What We Offer')).toBeInTheDocument();
      expect(screen.getByText('Grammar:')).toBeInTheDocument();
      expect(screen.getByText('Vocabulary:')).toBeInTheDocument();
      expect(screen.getByText('Reading:')).toBeInTheDocument();
      expect(screen.getByText('Listening:')).toBeInTheDocument();
      expect(screen.getByText('Writing:')).toBeInTheDocument();
      expect(screen.getByText('Progress Tracking:')).toBeInTheDocument();
    });

    it('displays why choose us section', () => {
      renderAboutScreen();

      expect(screen.getByText('Why Choose Us')).toBeInTheDocument();
      expect(screen.getByText('We offer the best learning experience.')).toBeInTheDocument();
    });
  });

  describe('Feedback Form', () => {
    it('displays feedback form card', () => {
      renderAboutScreen();

      expect(screen.getByTestId('card')).toBeInTheDocument();
      expect(screen.getByTestId('card-title')).toHaveTextContent('Send Feedback');
      expect(screen.getByText('We value your feedback and suggestions.')).toBeInTheDocument();
    });

    it('displays name input field', () => {
      renderAboutScreen();

      const nameInput = screen.getByLabelText(/name/i);
      expect(nameInput).toBeInTheDocument();
      expect(nameInput).toHaveAttribute('type', 'text');
      expect(nameInput).toHaveAttribute('required');
      expect(nameInput).toHaveAttribute('placeholder', 'Enter your name');
    });

    it('displays email input field', () => {
      renderAboutScreen();

      const emailInput = screen.getByLabelText(/email/i);
      expect(emailInput).toBeInTheDocument();
      expect(emailInput).toHaveAttribute('type', 'email');
      expect(emailInput).toHaveAttribute('required');
      expect(emailInput).toHaveAttribute('placeholder', 'Enter your email');
    });

    it('displays subject select field', () => {
      renderAboutScreen();

      const subjectSelect = screen.getByLabelText(/subject/i);
      expect(subjectSelect).toBeInTheDocument();
      expect(subjectSelect.tagName).toBe('SELECT');
      expect(subjectSelect).toHaveAttribute('required');
    });

    it('displays subject options', () => {
      renderAboutScreen();

      expect(screen.getByText('Select a subject')).toBeInTheDocument();
      expect(screen.getByText('General Inquiry')).toBeInTheDocument();
      expect(screen.getByText('Bug Report')).toBeInTheDocument();
      expect(screen.getByText('Feature Request')).toBeInTheDocument();
      expect(screen.getByText('Content Feedback')).toBeInTheDocument();
      expect(screen.getByText('Technical Support')).toBeInTheDocument();
      expect(screen.getByText('Other')).toBeInTheDocument();
    });

    it('displays message textarea field', () => {
      renderAboutScreen();

      const messageTextarea = screen.getByLabelText(/message/i);
      expect(messageTextarea).toBeInTheDocument();
      expect(messageTextarea.tagName).toBe('TEXTAREA');
      expect(messageTextarea).toHaveAttribute('required');
      expect(messageTextarea).toHaveAttribute('placeholder', 'Enter your message');
    });

    it('displays send feedback button', () => {
      renderAboutScreen();

      const sendButton = screen.getByRole('button', { name: /send feedback/i });
      expect(sendButton).toBeInTheDocument();
      expect(sendButton).toHaveAttribute('type', 'submit');
    });
  });

  describe('User Interactions', () => {
    it('calls handleInputChange when name input changes', () => {
      renderAboutScreen();

      const nameInput = screen.getByLabelText(/name/i);
      fireEvent.change(nameInput, { target: { value: 'John Doe' } });

      expect(mockHandlerReturn.handleInputChange).toHaveBeenCalled();
    });

    it('calls handleInputChange when email input changes', () => {
      renderAboutScreen();

      const emailInput = screen.getByLabelText(/email/i);
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      expect(mockHandlerReturn.handleInputChange).toHaveBeenCalled();
    });

    it('calls handleInputChange when subject select changes', () => {
      renderAboutScreen();

      const subjectSelect = screen.getByLabelText(/subject/i);
      fireEvent.change(subjectSelect, { target: { value: 'general' } });

      expect(mockHandlerReturn.handleInputChange).toHaveBeenCalled();
    });

    it('calls handleInputChange when message textarea changes', () => {
      renderAboutScreen();

      const messageTextarea = screen.getByLabelText(/message/i);
      fireEvent.change(messageTextarea, { target: { value: 'Test message' } });

      expect(mockHandlerReturn.handleInputChange).toHaveBeenCalled();
    });

    it('calls handleSubmit when form is submitted', () => {
      renderAboutScreen();

      const form = screen.getByRole('button', { name: /send feedback/i }).closest('form');
      fireEvent.submit(form!);

      expect(mockHandlerReturn.handleSubmit).toHaveBeenCalled();
    });
  });

  describe('Component Integration', () => {
    it('uses language context for translations', () => {
      renderAboutScreen();

      expect(useLanguage).toHaveBeenCalled();
      expect(mockT).toHaveBeenCalledWith('about.title');
      expect(mockT).toHaveBeenCalledWith('about.welcome');
    });

    it('calls about screen handler hook', () => {
      renderAboutScreen();

      expect(useAboutScreen).toHaveBeenCalled();
    });
  });

  describe('Form Values', () => {
    it('displays form values from handler state', () => {
      vi.mocked(useAboutScreen).mockReturnValue({
        ...mockHandlerReturn,
        feedback: {
          name: 'John Doe',
          email: '<EMAIL>',
          subject: 'general',
          message: 'Test message'
        }
      });

      renderAboutScreen();

      expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();

      const selectElement = screen.getByRole('combobox');
      expect(selectElement).toHaveValue('general');

      expect(screen.getByDisplayValue('Test message')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('applies responsive container classes', () => {
      renderAboutScreen();

      const container = screen.getByText('About Us').closest('.max-w-4xl');
      expect(container).toHaveClass('max-w-4xl', 'mx-auto', 'px-4', 'sm:px-6', 'lg:px-8', 'py-8');
    });

    it('applies responsive grid classes to form', () => {
      renderAboutScreen();

      const gridContainer = screen.getByLabelText(/name/i).closest('.grid');
      expect(gridContainer).toHaveClass('grid', 'grid-cols-1', 'md:grid-cols-2', 'gap-6');
    });
  });

  describe('Dark Mode Support', () => {
    it('applies dark mode classes to text elements', () => {
      renderAboutScreen();

      const title = screen.getByText('About Us');
      expect(title).toHaveClass('dark:text-white');

      const missionTitle = screen.getByText('Our Mission');
      expect(missionTitle).toHaveClass('dark:text-white');
    });

    it('applies dark mode classes to form elements', () => {
      renderAboutScreen();

      const nameInput = screen.getByLabelText(/name/i);
      expect(nameInput).toHaveClass('dark:bg-gray-700', 'dark:text-white', 'dark:border-gray-600');
    });
  });
});
