import { useState } from 'react';

export interface FeedbackData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface UseAboutScreenReturn {
  feedback: FeedbackData;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
}

export const useAboutScreen = (): UseAboutScreenReturn => {
  const [feedback, setFeedback] = useState<FeedbackData>({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFeedback(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Feedback submitted:', feedback);
    alert('Thank you for your feedback! We will get back to you soon.');
    setFeedback({ name: '', email: '', subject: '', message: '' });
  };

  return {
    feedback,
    handleInputChange,
    handleSubmit
  };
}; 