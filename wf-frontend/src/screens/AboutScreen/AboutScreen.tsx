import React from 'react';
import { Mail, MessageSquare, Send } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import ConditionalLayout from "@/components/ConditionalLayout";
import { useAboutScreen } from './AboutScreen.handler';
import { useLanguage } from '@/context/LanguageContext';
import styles from './AboutScreen.module.css';

const AboutScreen: React.FC = () => {
  const { feedback, handleInputChange, handleSubmit } = useAboutScreen();
  const { t } = useLanguage();

  return (
    <ConditionalLayout>
      <div className={styles.container}>
        {/* About Section */}
        <div className={styles.section}>
          <h1 className={styles.title}>{t('about.title')}</h1>
          <div className={styles.prose}>
            <p className={styles.welcomeText}>
              {t('about.welcome')}
            </p>

            <h2 className={styles.sectionTitle}>{t('about.mission.title')}</h2>
            <p className={styles.description}>
              {t('about.mission.description')}
            </p>

            <h2 className={styles.sectionTitle}>{t('about.whatWeOffer.title')}</h2>
            <ul className={styles.featureList}>
              <li><strong>{t('about.whatWeOffer.grammar.title')}:</strong> {t('about.whatWeOffer.grammar.description')}</li>
              <li><strong>{t('about.whatWeOffer.vocabulary.title')}:</strong> {t('about.whatWeOffer.vocabulary.description')}</li>
              <li><strong>{t('about.whatWeOffer.reading.title')}:</strong> {t('about.whatWeOffer.reading.description')}</li>
              <li><strong>{t('about.whatWeOffer.listening.title')}:</strong> {t('about.whatWeOffer.listening.description')}</li>
              <li><strong>{t('about.whatWeOffer.writing.title')}:</strong> {t('about.whatWeOffer.writing.description')}</li>
              <li><strong>{t('about.whatWeOffer.progress.title')}:</strong> {t('about.whatWeOffer.progress.description')}</li>
            </ul>

            <h2 className={styles.sectionTitle}>{t('about.whyChooseUs.title')}</h2>
            <p className={styles.description}>
              {t('about.whyChooseUs.description')}
            </p>
          </div>
        </div>

        {/* Feedback Form */}
        <Card>
          <CardHeader>
            <CardTitle className={styles.cardTitle}>
              <MessageSquare className={styles.cardIcon} />
              {t('about.feedback.title')}
            </CardTitle>
            <CardDescription>
              {t('about.feedback.description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className={styles.form}>
              <div className={styles.formGrid}>
                <div className={styles.formField}>
                  <label htmlFor="name" className={styles.label}>
                    {t('about.feedback.form.name')} *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={feedback.name}
                    onChange={handleInputChange}
                    required
                    className={styles.input}
                    placeholder={t('about.feedback.form.namePlaceholder')}
                  />
                </div>

                <div className={styles.formField}>
                  <label htmlFor="email" className={styles.labelWithIcon}>
                    <Mail className={styles.labelIcon} />
                    {t('about.feedback.form.email')} *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={feedback.email}
                    onChange={handleInputChange}
                    required
                    className={styles.input}
                    placeholder={t('about.feedback.form.emailPlaceholder')}
                  />
                </div>
              </div>

              <div className={styles.formField}>
                <label htmlFor="subject" className={styles.label}>
                  {t('about.feedback.form.subject')} *
                </label>
                <select
                  id="subject"
                  name="subject"
                  value={feedback.subject}
                  onChange={handleInputChange}
                  required
                  className={styles.select}
                >
                  <option value="">{t('about.feedback.form.selectSubject')}</option>
                  <option value="general">{t('about.feedback.form.subjects.general')}</option>
                  <option value="bug">{t('about.feedback.form.subjects.bug')}</option>
                  <option value="feature">{t('about.feedback.form.subjects.feature')}</option>
                  <option value="content">{t('about.feedback.form.subjects.content')}</option>
                  <option value="technical">{t('about.feedback.form.subjects.technical')}</option>
                  <option value="other">{t('about.feedback.form.subjects.other')}</option>
                </select>
              </div>

              <div className={styles.formField}>
                <label htmlFor="message" className={styles.label}>
                  {t('about.feedback.form.message')} *
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={feedback.message}
                  onChange={handleInputChange}
                  required
                  rows={5}
                  className={styles.textarea}
                  placeholder={t('about.feedback.form.messagePlaceholder')}
                />
              </div>

              <div className={styles.submitContainer}>
                <Button type="submit" className={styles.submitButton}>
                  <Send className={styles.submitIcon} />
                  {t('about.feedback.form.sendButton')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </ConditionalLayout>
  );
};

export default AboutScreen; 