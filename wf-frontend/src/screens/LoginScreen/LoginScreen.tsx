import React, { useState } from 'react';
import { <PERSON>, EyeOff, BookOpen } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import { useLoginScreenHandler } from './LoginScreen.handler';
import Footer from '@/components/Footer';
import styles from './LoginScreen.module.css';

/**
 * Login Screen Component
 * Pure UI component that renders the authentication form
 * All business logic is handled by useLoginScreenHandler
 * Updated with modern SignIn.tsx template design
 */
const LoginScreen: React.FC = () => {
  const { loading } = useAuth();
  const { t } = useLanguage();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const {
    // State
    isSignUp,
    formData,
    errors,
    isLoading,
    message,
    
    // Handlers
    handleInputChange,
    handleSelectChange,
    handleSubmit,
    toggleMode,
    handleForgotPassword
  } = useLoginScreenHandler();

  const cefrLevelOptions = [
    { value: '', label: t('auth.selectYourLevel') },
    { value: 'A1', label: t('auth.cefrLevels.a1') },
    { value: 'A2', label: t('auth.cefrLevels.a2') },
    { value: 'B1', label: t('auth.cefrLevels.b1') },
    { value: 'B2', label: t('auth.cefrLevels.b2') },
    { value: 'C1', label: t('auth.cefrLevels.c1') },
    { value: 'C2', label: t('auth.cefrLevels.c2') }
  ];

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingCard}>
          <div className={styles.loadingSpinner}></div>
          <p className={styles.loadingText}>{t('auth.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.card}>
        {/* Header */}
        <div className={styles.header}>
          <div className={styles.iconContainer}>
            <div className={styles.iconWrapper}>
              <BookOpen className={styles.icon} />
            </div>
          </div>
          <h1 className={styles.title}>
            {isSignUp ? t('auth.createAccount') : t('auth.welcomeBack')}
          </h1>
          <p className={styles.subtitle}>
            {isSignUp
              ? t('auth.signUpSubtitle')
              : t('auth.signInSubtitle')
            }
          </p>
        </div>

        {/* Success/Error Messages */}
        {message && (
          <div className={`${styles.message} ${styles.messageSuccess}`}>
            {message}
          </div>
        )}

        {errors.submit && (
          <div className={`${styles.message} ${styles.messageError}`}>
            {errors.submit}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className={styles.form}>
          {/* Name Field (Sign Up Only) */}
          {isSignUp && (
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('auth.fullName')}
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                  errors.name ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'
                } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                placeholder={t('auth.fullNamePlaceholder')}
                disabled={isLoading}
                required={isSignUp}
              />
              {errors.name && (
                <p className="text-red-600 dark:text-red-400 text-sm mt-1">{errors.name}</p>
              )}
            </div>
          )}

          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('auth.emailAddress')}
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                errors.email ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'
              } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              placeholder={t('auth.emailPlaceholder')}
              disabled={isLoading}
              required
            />
            {errors.email && (
              <p className="text-red-600 dark:text-red-400 text-sm mt-1">{errors.email}</p>
            )}
          </div>

          {/* Password Field */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('auth.password')}
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                  errors.password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'
                } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                placeholder={t('auth.passwordPlaceholder')}
                disabled={isLoading}
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                ) : (
                  <Eye className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-600 dark:text-red-400 text-sm mt-1">{errors.password}</p>
            )}
          </div>

          {/* Confirm Password Field (Sign Up Only) */}
          {isSignUp && (
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('auth.confirmPassword')}
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                    errors.confirmPassword ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'
                  } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  placeholder={t('auth.confirmPasswordPlaceholder')}
                  disabled={isLoading}
                  required={isSignUp}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  disabled={isLoading}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-600 dark:text-red-400 text-sm mt-1">{errors.confirmPassword}</p>
              )}
            </div>
          )}

          {/* CEFR Level Field (Sign Up Only) */}
          {isSignUp && (
            <div>
              <label htmlFor="cefrLevel" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('auth.currentEnglishLevel')}
              </label>
              <select
                id="level_id"
                name="level_id"
                value={formData.level_id}
                onChange={handleSelectChange}
                className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                disabled={isLoading}
                required={isSignUp}
              >
                {cefrLevelOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Remember Me / Forgot Password (Sign In Only) */}
          {!isSignUp && (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  {t('auth.rememberMe')}
                </label>
              </div>
              <button
                type="button"
                onClick={handleForgotPassword}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
              >
                {t('auth.forgotPassword')}
              </button>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className={`w-full py-3 px-4 rounded-lg font-medium focus:ring-4 focus:ring-blue-500 focus:ring-opacity-50 transition-all duration-200 transform ${
              isLoading 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-blue-600 hover:bg-blue-700 hover:scale-105'
            } text-white`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {isSignUp ? t('auth.creatingAccount') : t('auth.signingIn')}
              </div>
            ) : (
              isSignUp ? t('auth.createAccount') : t('auth.signIn')
            )}
          </button>

          {/* Toggle Mode */}
          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-300">
              {isSignUp ? t('auth.alreadyHaveAccount') : t('auth.dontHaveAccount')}{' '}
              <button
                type="button"
                onClick={toggleMode}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
                disabled={isLoading}
              >
                {isSignUp ? t('auth.signIn') : t('auth.signUp')}
              </button>
            </p>
          </div>
        </form>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default LoginScreen; 