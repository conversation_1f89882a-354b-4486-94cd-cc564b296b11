import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';


// Form data interface
interface FormData {
  email: string;
  password: string;
  confirmPassword: string;
  name: string;
  level_id: string;
}

// Form errors interface
interface FormErrors {
  email?: string;
  password?: string;
  confirmPassword?: string;
  name?: string;
  submit?: string;
}

// Handler return type
interface LoginScreenHandler {
  // State
  isSignUp: boolean;
  formData: FormData;
  errors: FormErrors;
  isLoading: boolean;
  message: string;
  
  // Handlers
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSelectChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  toggleMode: () => void;
  handleForgotPassword: () => void;
  
  // Utilities
  validateForm: () => boolean;
}

/**
 * Custom hook for managing Login screen business logic
 * Handles form state, validation, and authentication
 */
export const useLoginScreenHandler = (): LoginScreenHandler => {
  const location = useLocation();
  const [isSignUp, setIsSignUp] = useState<boolean>(location.pathname.includes('signup'));
  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
    level_id: 'A2' // Default CEFR level
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<string>('');

  const { signIn, signUp, user, loading } = useAuth();
  const navigate = useNavigate();

  // Redirect if already authenticated
  useEffect(() => {
    if (user && !loading) {
      const from = (location.state as { from?: { pathname?: string } })?.from?.pathname || '/home';
      navigate(from, { replace: true });
    }
  }, [user, loading, navigate, location]);

  // Update signup mode based on current URL path
  useEffect(() => {
    setIsSignUp(location.pathname.includes('signup'));
  }, [location.pathname]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    // Sign up specific validations
    if (isSignUp) {
      if (!formData.name.trim()) {
        newErrors.name = 'Name is required';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>): void => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Only clear errors and set loading if validation passes
    setIsLoading(true);
    setMessage('');
    setErrors({});

    try {
      if (isSignUp) {
        // Split name into first and last name
        const nameParts = formData.name.trim().split(' ');
        const first_name = nameParts[0] || '';
        const last_name = nameParts.slice(1).join(' ') || '';

        const userData = {
          first_name,
          last_name,
          level_id: formData.level_id
        };

        const result = await signUp(formData.email, formData.password, userData);
        
        if (result.error) {
          setErrors({ submit: result.error });
        } else {
          // Check if email confirmation is required
          if (result.user && !result.user.email_confirmed_at) {
            setMessage('Account created successfully! Please check your email to verify your account before signing in.');
            // Switch to sign-in mode after successful registration
            setTimeout(() => {
              setIsSignUp(false);
              setFormData(prev => ({
                ...prev,
                password: '',
                confirmPassword: '',
                name: ''
              }));
            }, 3000);
          } else {
            // User is automatically signed in
            setMessage('Account created and signed in successfully!');
            // Navigation will be handled by the useEffect hook
          }
        }
      } else {
        const result = await signIn(formData.email, formData.password);
        
        if (result.error) {
          setErrors({ submit: result.error });
        } else {
          setMessage('Signed in successfully!');
          // Navigation will be handled by the useEffect hook
        }
      }
    } catch {
      setErrors({ submit: 'An unexpected error occurred. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMode = (): void => {
    // Clear form data and errors
    setFormData({
      email: '',
      password: '',
      confirmPassword: '',
      name: '',
      level_id: 'A2'
    });
    setErrors({});
    setMessage('');
    
    // Navigate to the appropriate route instead of just toggling state
    if (isSignUp) {
      navigate('/signin');
    } else {
      navigate('/signup');
    }
  };

  const handleForgotPassword = (): void => {
    navigate('/forgot-password');
  };

  return {
    // State
    isSignUp,
    formData,
    errors,
    isLoading,
    message,
    
    // Handlers
    handleInputChange,
    handleSelectChange,
    handleSubmit,
    toggleMode,
    handleForgotPassword,
    
    // Utilities
    validateForm
  };
}; 