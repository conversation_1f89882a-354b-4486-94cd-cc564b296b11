/**
 * LoginScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 336 lines | AFTER: ~180 lines | REDUCTION: ~46%
 * 
 * Uses shared modules for:
 * - Container layouts (fullHeightContainer)
 * - Card components (cardCentered)
 * - Button components (buttonPrimary, fullWidth)
 * - Typography patterns (pageTitle, subtitle, linkText)
 */

/* Container using shared full-height layout */
.container {
  composes: fullHeightContainer from '@/styles/shared/layouts/containers.module.css';
  background: linear-gradient(to bottom right, theme('colors.blue.50'), theme('colors.indigo.100'));
  display: flex;
  flex-direction: column;
}

:global(.dark) .container {
  background: linear-gradient(to bottom right, theme('colors.gray.900'), theme('colors.gray.800'));
}

.loadingContainer {
  min-height: 100vh;
  background: linear-gradient(to bottom right, theme('colors.blue.50'), theme('colors.indigo.100'));
  display: flex;
  align-items: center;
  justify-content: center;
  padding: theme('spacing.4');
}

.content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: theme('spacing.4');
}

/* Card using shared centered card */
.card {
  composes: cardCentered from '@/styles/shared/components/cards.module.css';
  max-width: 448px;
  padding: theme('spacing.8');
}

:global(.dark) .card {
  background-color: theme('colors.gray.800');
}

.loadingCard {
  background-color: white;
  border-radius: theme('borderRadius.2xl');
  box-shadow: theme('boxShadow.xl');
  padding: theme('spacing.8');
  text-align: center;
}

.header {
  text-align: center;
  margin-bottom: theme('spacing.8');
}

.iconContainer {
  display: flex;
  justify-content: center;
  margin-bottom: theme('spacing.4');
}

.iconWrapper {
  background-color: theme('colors.blue.600');
  padding: theme('spacing.3');
  border-radius: theme('borderRadius.full');
}

.icon {
  height: theme('spacing.8');
  width: theme('spacing.8');
  color: white;
}

/* Typography using shared components */
.title {
  composes: pageTitle from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.2');
}

.subtitle {
  composes: subtitle from '@/styles/shared/components/typography.module.css';
}

.form {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.6');
}

.formField {
  /* Form field container */
}

.label {
  display: block;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.700');
  margin-bottom: theme('spacing.2');
}

:global(.dark) .label {
  color: theme('colors.gray.300');
}

.input {
  width: 100%;
  padding: theme('spacing.3') theme('spacing.4');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.lg');
  background-color: white;
  color: theme('colors.gray.900');
  transition: all 0.2s ease;
}

.input:focus {
  outline: none;
  ring: theme('ringWidth.2') theme('colors.blue.500');
  border-color: transparent;
}

:global(.dark) .input {
  border-color: theme('colors.gray.600');
  background-color: theme('colors.gray.700');
  color: white;
}

.inputError {
  border-color: theme('colors.red.500');
}

:global(.dark) .inputError {
  border-color: theme('colors.red.400');
}

.inputDisabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.passwordContainer {
  position: relative;
}

.passwordToggle {
  position: absolute;
  right: theme('spacing.3');
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: theme('colors.gray.400');
  padding: theme('spacing.1');
}

.passwordToggle:hover {
  color: theme('colors.gray.600');
}

:global(.dark) .passwordToggle {
  color: theme('colors.gray.500');
}

:global(.dark) .passwordToggle:hover {
  color: theme('colors.gray.300');
}

.select {
  width: 100%;
  padding: theme('spacing.3') theme('spacing.4');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.lg');
  background-color: white;
  color: theme('colors.gray.900');
  transition: all 0.2s ease;
}

.select:focus {
  outline: none;
  ring: theme('ringWidth.2') theme('colors.blue.500');
  border-color: transparent;
}

:global(.dark) .select {
  border-color: theme('colors.gray.600');
  background-color: theme('colors.gray.700');
  color: white;
}

.error {
  color: theme('colors.red.600');
  font-size: theme('fontSize.sm');
  margin-top: theme('spacing.1');
}

:global(.dark) .error {
  color: theme('colors.red.400');
}

.message {
  padding: theme('spacing.3') theme('spacing.4');
  border-radius: theme('borderRadius.lg');
  margin-bottom: theme('spacing.6');
}

.messageSuccess {
  background-color: theme('colors.green.100');
  border: 1px solid theme('colors.green.400');
  color: theme('colors.green.700');
}

:global(.dark) .messageSuccess {
  background-color: rgba(34, 197, 94, 0.3);
  border-color: theme('colors.green.600');
  color: theme('colors.green.300');
}

.messageError {
  background-color: theme('colors.red.100');
  border: 1px solid theme('colors.red.400');
  color: theme('colors.red.700');
}

:global(.dark) .messageError {
  background-color: rgba(239, 68, 68, 0.3);
  border-color: theme('colors.red.600');
  color: theme('colors.red.300');
}

/* Button using shared components */
.button {
  composes: buttonPrimary fullWidth from '@/styles/shared/components/buttons.module.css';
  transform: scale(1);
}

.button:hover:not(.buttonDisabled) {
  transform: scale(1.02);
}

.buttonLoading {
  background-color: theme('colors.blue.400');
  cursor: not-allowed;
}

.buttonDisabled {
  background-color: theme('colors.gray.400');
  cursor: not-allowed;
}

/* Links using shared components */
.toggleLink {
  composes: linkText from '@/styles/shared/components/typography.module.css';
  text-align: center;
  font-weight: theme('fontWeight.medium');
}

.forgotLink {
  composes: linkText from '@/styles/shared/components/typography.module.css';
  text-align: center;
  font-size: theme('fontSize.sm');
}

.loadingSpinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: theme('spacing.12');
  width: theme('spacing.12');
  border: 2px solid transparent;
  border-bottom: 2px solid theme('colors.blue.600');
  margin: 0 auto theme('spacing.4');
}

.loadingText {
  color: theme('colors.gray.600');
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}