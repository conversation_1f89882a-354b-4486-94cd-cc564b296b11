import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import LoginScreen from '../LoginScreen';

// Mock the AuthContext
vi.mock('../../../context/AuthContext', () => ({
  useAuth: vi.fn(() => ({
    user: null,
    loading: false,
    isAuthenticated: false,
    signIn: vi.fn(),
    signUp: vi.fn(),
    signOut: vi.fn(),
  }))
}));

// Mock the LanguageContext
vi.mock('../../../context/LanguageContext', () => ({
  useLanguage: vi.fn(() => ({
    language: 'en',
    setLanguage: vi.fn(),
    t: (key: string) => {
      const translations: Record<string, string> = {
        'auth.welcomeBack': 'Welcome Back',
        'auth.createAccount': 'Create Account',
        'auth.signIn': 'Sign In',
        'auth.signUp': 'Sign Up',
        'auth.email': 'Email',
        'auth.emailAddress': 'Email',
        'auth.password': 'Password',
        'auth.fullName': 'Full Name',
        'auth.confirmPassword': 'Confirm Password',
        'auth.signInSubtitle': 'Sign in to your account',
        'auth.signUpSubtitle': 'Create your account',
        'auth.dontHaveAccount': "Don't have an account?",
        'auth.alreadyHaveAccount': 'Already have an account?',
        'auth.signingIn': 'Signing in...',
        'auth.creatingAccount': 'Creating account...',
        'auth.fullNamePlaceholder': 'Enter your full name',
        'auth.emailPlaceholder': 'Enter your email',
        'auth.passwordPlaceholder': 'Enter your password',
        'auth.confirmPasswordPlaceholder': 'Confirm your password'
      };
      return translations[key] || key;
    }
  }))
}));

// Mock the LoginScreen handler
const mockHandler = {
  isSignUp: false,
  formData: {
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
    level_id: 'A2'
  },
  errors: {},
  isLoading: false,
  message: '',
  handleInputChange: vi.fn(),
  handleSelectChange: vi.fn(),
  handleSubmit: vi.fn(e => e.preventDefault()),
  toggleMode: vi.fn(),
  handleForgotPassword: vi.fn(),
  validateForm: vi.fn(() => true)
};

vi.mock('../LoginScreen.handler', () => ({
  useLoginScreenHandler: vi.fn(() => mockHandler)
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ pathname: '/signin', state: null }),
  };
});

// Mock Footer component
vi.mock('../../../components/Footer', () => ({
  default: () => <div data-testid="footer">Footer</div>
}));



describe('LoginScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockNavigate.mockClear();

    // Reset handler mock to default sign in state
    Object.assign(mockHandler, {
      isSignUp: false,
      formData: {
        email: '',
        password: '',
        confirmPassword: '',
        name: '',
        level_id: 'A2'
      },
      errors: {},
      isLoading: false,
      message: '',
      handleInputChange: vi.fn(),
      handleSelectChange: vi.fn(),
      handleSubmit: vi.fn(e => e.preventDefault()),
      toggleMode: vi.fn(),
      handleForgotPassword: vi.fn(),
      validateForm: vi.fn(() => true)
    });
  });

  it('renders sign in form by default', () => {
    render(<LoginScreen />);

    // Check for sign in form elements (using English text from translations)
    expect(screen.getByText('Welcome Back')).toBeInTheDocument();
    expect(screen.getByLabelText('Email')).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('renders sign up form when in signup mode', () => {
    // Set handler to sign up state
    Object.assign(mockHandler, {
      isSignUp: true
    });

    render(<LoginScreen />);

    // Check for sign up form elements
    expect(screen.getByRole('heading', { name: 'Create Account' })).toBeInTheDocument();
    expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Confirm Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
  });

  it('calls toggleMode when switching between sign in and sign up', () => {
    const mockToggleMode = vi.fn();
    Object.assign(mockHandler, {
      toggleMode: mockToggleMode
    });

    render(<LoginScreen />);

    // Find and click the sign up link
    const signUpButton = screen.getByText('Sign Up');
    fireEvent.click(signUpButton);

    // Verify toggleMode was called
    expect(mockToggleMode).toHaveBeenCalled();
  });

  it('calls handleSubmit when sign in form is submitted', () => {
    const mockHandleSubmit = vi.fn(e => e.preventDefault());
    Object.assign(mockHandler, {
      handleSubmit: mockHandleSubmit
    });

    render(<LoginScreen />);

    const form = document.querySelector('form');
    fireEvent.submit(form!);

    expect(mockHandleSubmit).toHaveBeenCalledTimes(1);
  });

  it('calls handleSubmit when sign up form is submitted', () => {
    const mockHandleSubmit = vi.fn(e => e.preventDefault());
    Object.assign(mockHandler, {
      isSignUp: true,
      handleSubmit: mockHandleSubmit
    });

    render(<LoginScreen />);

    const form = document.querySelector('form');
    fireEvent.submit(form!);

    expect(mockHandleSubmit).toHaveBeenCalledTimes(1);
  });

  it('renders without crashing', () => {
    render(<LoginScreen />);

    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('shows loading state during authentication', () => {
    Object.assign(mockHandler, {
      isLoading: true
    });

    render(<LoginScreen />);

    const signInButton = screen.getByRole('button', { name: /signing in/i });
    expect(signInButton).toBeInTheDocument();
  });
});
