import React, { useState } from 'react';
import { HelpCircle, Info, CheckCircle, X } from 'lucide-react';
import { PageWrapper } from '@/components/Layout/ResponsiveContainer';
import { useQuizScreenHandler } from './QuizScreen.handler';
import { useLanguage } from '@/context/LanguageContext';
import styles from './QuizScreen.module.css';
import { cn } from 'wf-shared/utils';

/**
 * QuizScreen Component
 * Pure UI component that renders the quiz interface
 * All business logic is handled by useQuizScreenHandler
 * Updated with new quiz UI design
 */
const QuizScreen: React.FC = () => {
  const { t } = useLanguage();
  const {
    // Core state
    quiz,
    questions,
    currentQuestionIndex,
    mode,
    loading,
    error,
    
    // UI state
    showFeedback,
    selectedAnswer,
    isAnswerSubmitted,
    
    // Computed properties
    currentQuestion,
    progress,
    
    // Handlers
    handleAnswerSelect,
    handleNextQuestion,
    handlePreviousQuestion,
    handleQuizComplete,
    handleQuit,
  } = useQuizScreenHandler();

  // Local state for hint and explanation visibility
  const [showHint, setShowHint] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);

  // Auto-expand explanation in practice mode when answer is selected
  React.useEffect(() => {
    if (mode === 'practice' && showFeedback && currentQuestion?.explanations?.length) {
      setShowExplanation(true);
      setShowHint(false); // Close hint if open
    }
  }, [showFeedback, mode, currentQuestion?.explanations?.length]);

  // Collapse explanation when moving to next question
  React.useEffect(() => {
    setShowExplanation(false);
    setShowHint(false);
  }, [currentQuestionIndex]);


  const handleHintClick = () => {
    setShowHint(!showHint);
    setShowExplanation(false);
  };

  const handleExplanationClick = () => {
    setShowExplanation(!showExplanation);
    setShowHint(false);
  };

  // Reset hint and explanation when question changes (now handled by useEffect)
  const resetHelpStates = () => {
    setShowHint(false);
    setShowExplanation(false);
  };

  // Wrapper functions to reset help states on navigation
  const handleNextQuestionWithReset = () => {
    resetHelpStates();
    handleNextQuestion();
  };

  const handlePreviousQuestionWithReset = () => {
    resetHelpStates();
    handlePreviousQuestion();
  };

  // Loading state
  if (loading) {
    return (
      <PageWrapper>
        <div className={styles.wrapper}>
          <div className={styles.loadingWrapper}>
            <div className={styles.errorCard}>
              <div className={styles.loadingSpinner}></div>
              <p className={styles.loadingText}>{t('quiz.loadingQuiz')}</p>
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  // Error state
  if (error) {
    return (
      <PageWrapper>
        <div className={styles.wrapper}>
          <div className={styles.loadingWrapper}>
            <div className={styles.errorCard}>
              <div className={styles.errorIconContainer}>
                <X className={styles.errorIcon} />
              </div>
              <h2 className={styles.errorTitle}>{t('quiz.quizLoadingError')}</h2>
              <p className={styles.errorMessage}>{error}</p>
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  // No questions state
  if (!questions.length || !currentQuestion) {
    return (
      <PageWrapper>
        <div className={styles.wrapper}>
          <div className={styles.loadingWrapper}>
            <div className={styles.errorCard}>
              <h2 className={styles.errorTitle}>{t('quiz.noQuestionsAvailable')}</h2>
              <p className={styles.errorMessage}>{t('quiz.noQuestionsText')}</p>
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      {/* Quiz Header with Progress */}
      <div className={styles.header}>
        <div className={styles.headerTop}>
          <div className={styles.headerLeft}>
            <button
              onClick={handleQuit}
              className={styles.quitButton}
            >
              <X className="h-6 w-6" />
            </button>
            <div>
              <h1 className={cn(styles.title, mode === 'practice' ? styles.modePractice : styles.modeTest)}>
                {quiz?.title || 'Word Formation Quiz'}
              </h1>
              <p className={styles.subtitle}>
                {mode === 'practice' ? t('quiz.practiceMode') : t('quiz.testMode')}
              </p>
            </div>
          </div>
          <div className={styles.headerRight}>
            <div className={styles.questionInfo}>
              {t('quiz.question')} {currentQuestionIndex + 1} {t('quiz.of')} {questions.length}
            </div>
          </div>
        </div>

        {/* Improved Progress Bar */}
        <div>
          <div className={styles.answeredInfo}>
            <span>{t('quiz.progress')}</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className={styles.progressBar}>
            <div 
              className={styles.progressFill}
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      </div>

      <div className={styles.questionCard}>
        <div className={styles.questionHeader}>
          <div className={styles.questionRow}>
            <div className={styles.questionNumber}>
              <span className={styles.questionNumberText}>{currentQuestionIndex + 1}</span>
            </div>
            <div className={styles.questionContent}>
              <h2 className={styles.questionText}>
                {currentQuestion.question_text}
              </h2>
            </div>
          </div>
          {currentQuestion.metadata?.example_usage && (
            <div className={styles.exampleBox}>
              <p className={styles.exampleText}>
                <strong>{t('quiz.example')}:</strong> &ldquo;{currentQuestion.metadata.example_usage}&rdquo;
              </p>
            </div>
          )}
        </div>

        <div className={styles.optionsContainer}>
          {(currentQuestion.options || []).map((option) => {
            const optionText = option.option_text || '';
            const isSelected = selectedAnswer === optionText;
            const isCorrectOption = optionText === currentQuestion.correct_answer;
            const showAnswerFeedback = showFeedback && mode === 'practice';
            
            // Get option class based on state
            let optionClass = styles.optionBase;
            if (showAnswerFeedback) {
              if (isCorrectOption) {
                optionClass = cn(styles.optionBase, styles.optionCorrect);
              } else if (isSelected && !isCorrectOption) {
                optionClass = cn(styles.optionBase, styles.optionIncorrect);
              } else {
                optionClass = cn(styles.optionBase, styles.optionNeutral);
              }
            } else if (isSelected) {
              optionClass = cn(styles.optionBase, styles.optionSelected);
            } else {
              optionClass = cn(styles.optionBase, styles.optionNormal);
            }

            // Get circle class based on selection
            const circleClass = cn(
              styles.optionCircle,
              isSelected ? styles.optionCircleSelected : styles.optionCircleNormal
            );

            return (
              <button
                key={option.id}
                onClick={() => handleAnswerSelect(optionText)}
                className={optionClass}
                disabled={isAnswerSubmitted && mode === 'test'}
              >
                <div className={styles.optionContainer}>
                  <div className={circleClass}>
                    {isSelected && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                  <span className={styles.optionText}>{optionText}</span>
                  {showAnswerFeedback && isCorrectOption && (
                    <CheckCircle className={styles.optionCheckIcon} />
                  )}
                  {showAnswerFeedback && isSelected && !isCorrectOption && (
                    <X className={styles.optionXIcon} />
                  )}
                </div>
              </button>
            );
          })}
        </div>

        {/* Hint and Explanation Buttons - Only in Practice Mode */}
        {mode === 'practice' && (
          <div className="flex space-x-4 mb-6">
            {currentQuestion.metadata?.grammar_rule && (
              <button
                onClick={handleHintClick}
                className={`flex items-center px-4 py-2 rounded-lg border transition-colors duration-200 ${
                  showHint
                    ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-700 text-yellow-700 dark:text-yellow-300'
                    : 'border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <HelpCircle className="h-4 w-4 mr-2" />
                {t('quiz.hint')}
              </button>
            )}
            {currentQuestion.explanations?.length && (
              <button
                onClick={handleExplanationClick}
                className={`flex items-center px-4 py-2 rounded-lg border transition-colors duration-200 ${
                  showExplanation
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-300'
                    : 'border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <Info className="h-4 w-4 mr-2" />
                {t('quiz.explanation')}
              </button>
            )}
          </div>
        )}

        {/* Hint Content */}
        {showHint && currentQuestion.metadata?.grammar_rule && (
          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
            <h4 className="font-semibold text-yellow-800 dark:text-yellow-300 mb-2">{t('quiz.hintTitle')}</h4>
            <p className="text-yellow-700 dark:text-yellow-300">{currentQuestion.metadata.grammar_rule}</p>
          </div>
        )}

        {/* Explanation Content */}
        {showExplanation && currentQuestion.explanations?.length && (
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
            <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-3">{t('quiz.explanationTitle')}</h4>

            {/* Show structured explanations */}
            <div className="space-y-3">
              {currentQuestion.explanations.map((explanation, index) => (
                <div key={explanation.id || index} className="text-blue-700 dark:text-blue-300">
                  <div className="font-medium text-blue-800 dark:text-blue-300 mb-1">
                    {explanation.explanation_type === 'correct_answer' ? t('quiz.correctAnswerLabel') : t('quiz.wrongAnswerLabel')}
                  </div>
                  <div className="text-sm">{explanation.content}</div>
                  {explanation.metadata?.rule && (
                    <div className="text-xs text-blue-600 dark:text-blue-400 mt-1 italic">
                      {t('quiz.rule')}: {explanation.metadata.rule}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        <div className={styles.navigationContainer}>
          <div className={styles.navigationLeft}>
            <button
              onClick={handlePreviousQuestionWithReset}
              disabled={currentQuestionIndex === 0}
              className={cn(
                styles.buttonBase,
                styles.previousButton,
                currentQuestionIndex === 0 && styles.buttonDisabled
              )}
            >
              {t('quiz.previous')}
            </button>
          </div>

          <div className={styles.navigationRight}>
            {currentQuestionIndex === questions.length - 1 ? (
              <button
                onClick={handleQuizComplete}
                disabled={!selectedAnswer}
                className={cn(
                  styles.buttonBase,
                  styles.finishButton,
                  !selectedAnswer && styles.buttonDisabled
                )}
              >
                {t('quiz.submitQuiz')}
              </button>
            ) : (
              <button
                onClick={handleNextQuestionWithReset}
                disabled={!selectedAnswer}
                className={cn(
                  styles.buttonBase,
                  styles.nextButton,
                  !selectedAnswer && styles.buttonDisabled
                )}
              >
                {t('quiz.next')}
              </button>
            )}
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default QuizScreen; 