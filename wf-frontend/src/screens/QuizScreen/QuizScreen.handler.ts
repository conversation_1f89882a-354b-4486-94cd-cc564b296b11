import { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { QuizService } from '@/services/quizService';
import { APP_CONSTANTS } from 'wf-shared/constants';
import type { 
  Quiz as QuizType, 
  QuizQuestion, 
  QuizMode, 
  QuizResults,
  QuizAnswer
} from '@/types';

// Handler return type interface
interface QuizScreenHandler {
  // Core state
  quiz: QuizType | null;
  questions: QuizQuestion[];
  currentQuestionIndex: number;
  userAnswers: Record<string, string>;
  mode: QuizMode;
  loading: boolean;
  error: string | null;
  
  // UI state
  showFeedback: boolean;
  selectedAnswer: string;
  isAnswerSubmitted: boolean;
  quizStartTime: Date | null;
  
  // Computed properties
  currentQuestion: QuizQuestion | null;
  progress: number;
  isCorrect: boolean;
  canProceed: boolean;
  
  // Handlers
  handleAnswerSelect: (answer: string) => void;
  handleAnswerSubmit: () => void;
  handleNextQuestion: () => void;
  handlePreviousQuestion: () => void;
  handleQuizComplete: () => Promise<void>;
  handleQuit: () => void;
  setCurrentQuestionIndex: (index: number) => void;
}

/**
 * Custom hook for managing QuizScreen business logic
 * Handles quiz data loading, question navigation, answer management, 
 * and results calculation
 */
export const useQuizScreenHandler = (): QuizScreenHandler => {
  const { quizId } = useParams<{ quizId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  // Core state
  const [quiz, setQuiz] = useState<QuizType | null>(null);
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [userAnswers, setUserAnswers] = useState<Record<string, string>>({});
  const [mode, setMode] = useState<QuizMode>('practice');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // UI state
  const [showFeedback, setShowFeedback] = useState<boolean>(false);
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');
  const [isAnswerSubmitted, setIsAnswerSubmitted] = useState<boolean>(false);
  const [quizStartTime, setQuizStartTime] = useState<Date | null>(null);

  // Ref to track if data is being loaded to prevent duplicate calls
  const isLoadingRef = useRef<boolean>(false);

  /**
   * Load quiz data from service layer
   */
  const loadQuizData = useCallback(async (): Promise<void> => {
    try {
      // Prevent duplicate calls if already loading
      if (isLoadingRef.current) {
        console.log('Quiz data already loading, skipping duplicate call');
        return;
      }
      
      isLoadingRef.current = true;
      setLoading(true);
      setError(null);

      console.log('Loading quiz data with quizId:', quizId);

      let quizDataRes;
      if (quizId) {
        console.log('Fetching quiz by ID:', quizId);
        quizDataRes = await QuizService.getQuizById(quizId);
      } else {
        console.log('No quizId provided, using fallback to A2/word_formation');
        // Fallback to default A2 Word Formation quiz
        quizDataRes = await QuizService.getQuizByLevelAndCategory('A2', 'word_formation');
      }

      const { data: quizData, error: quizError } = quizDataRes;
      
      // Enhanced logging to debug the optimization
      console.log('Quiz data response:', { 
        hasQuizData: !!quizData, 
        hasQuestionsProperty: quizData?.questions !== undefined,
        questionsCount: quizData?.questions?.length || 0,
        quizError 
      });

      if (quizError || !quizData) {
        throw new Error(quizError || 'No A2 Word Formation quiz found');
      }

      // Check if quiz data already includes questions (from getUserQuizById)
      let questionsData: QuizQuestion[] = [];
      if (quizData.questions && Array.isArray(quizData.questions)) {
        // Questions are already included in the quiz response - no need for separate call!
        questionsData = quizData.questions;
        console.log('Using questions from quiz data:', questionsData.length, 'questions found');
        // Debug: Check if options are included
        console.log('Sample question structure:', {
          hasOptions: questionsData[0]?.options !== undefined,
          optionsCount: questionsData[0]?.options?.length || 0,
          firstQuestion: questionsData[0]
        });
      } else {
        // Fallback: get questions separately (should rarely be needed)
        console.log('Questions not included in quiz data, fetching separately...');
        const questionsRes = await QuizService.getQuizQuestions(quizData.id);
        if (questionsRes.error || !questionsRes.data) {
          throw new Error(questionsRes.error || 'No questions found for this quiz');
        }
        questionsData = questionsRes.data;
      }

      if (!questionsData || questionsData.length === 0) {
        throw new Error('No questions found for this quiz');
      }

      setQuiz(quizData);
      setQuestions(questionsData);
      
      // Get mode from URL params or localStorage
      const urlParams = new URLSearchParams(window.location.search);
      const quizMode = (urlParams.get('mode') as QuizMode) ||
                      (localStorage.getItem(APP_CONSTANTS.STORAGE_KEYS.QUIZ_MODE) as QuizMode) ||
                      'practice';
      setMode(quizMode);
      
    } catch (err) {
      console.error('Error loading quiz:', err);
      setError(err instanceof Error ? err.message : 'An error occurred loading the quiz');
    } finally {
      isLoadingRef.current = false;
      setLoading(false);
    }
  }, [quizId]);

  // Load quiz data on component mount
  useEffect(() => {
    loadQuizData();
  }, [quizId]);

  // Initialize quiz start time when questions are loaded
  useEffect(() => {
    if (questions.length > 0 && !quizStartTime) {
      setQuizStartTime(new Date());
    }
  }, [questions, quizStartTime]);

  // Update UI state when current question changes
  useEffect(() => {
    const currentQuestion = questions[currentQuestionIndex];
    if (currentQuestion) {
      const answer = userAnswers[currentQuestion.id];
      setSelectedAnswer(answer || '');
      setIsAnswerSubmitted(!!answer);
      setShowFeedback(mode === 'practice' && !!answer);
    }
  }, [currentQuestionIndex, userAnswers, questions, mode]);

  /**
   * Handle answer selection
   */
  const handleAnswerSelect = (answer: string): void => {
    if (isAnswerSubmitted && mode === 'test') return; // Prevent changing answers in test mode

    setSelectedAnswer(answer);

    const currentQuestion = questions[currentQuestionIndex];
    const newAnswers = {
      ...userAnswers,
      [currentQuestion.id]: answer
    };
    setUserAnswers(newAnswers);
    setIsAnswerSubmitted(true);

    // Show immediate feedback in practice mode
    if (mode === 'practice') {
      setShowFeedback(true);
    }
  };

  /**
   * Handle answer submission (practice mode)
   */
  const handleAnswerSubmit = (): void => {
    if (!selectedAnswer) return;

    const currentQuestion = questions[currentQuestionIndex];
    const newAnswers = {
      ...userAnswers,
      [currentQuestion.id]: selectedAnswer
    };
    
    setUserAnswers(newAnswers);
    setIsAnswerSubmitted(true);
    
    if (mode === 'practice') {
      setShowFeedback(true);
    }
  };

  /**
   * Navigate to next question or complete quiz
   */
  const handleNextQuestion = (): void => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      // Reset feedback state when moving to next question
      setShowFeedback(false);
    } else {
      // Quiz completed
      handleQuizComplete();
    }
  };

  /**
   * Navigate to previous question
   */
  const handlePreviousQuestion = (): void => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      // Reset feedback state when moving to previous question
      setShowFeedback(false);
    }
  };

  /**
   * Complete quiz and navigate to results
   */
  const handleQuizComplete = async (): Promise<void> => {
    try {
      console.log('=== QUIZ COMPLETION STARTED ===');
      console.log('User:', user);
      console.log('User ID:', user?.id);
      console.log('User Email:', user?.email);
      console.log('Quiz:', quiz);
      console.log('Quiz ID:', quiz?.id);

      // Calculate results
      const results = calculateResults();
      console.log('Calculated results:', results);

      // Save quiz attempt to database if user is logged in
      if (user && quiz) {
        console.log('Saving quiz attempt for user:', user.id, 'quiz:', quiz.id);
        const saveResult = await QuizService.saveQuizAttempt(user.id, quiz.id, results);
        console.log('Save result:', saveResult);
        if (!saveResult.success) {
          console.error('Failed to save quiz attempt:', saveResult.error);
          // Continue anyway - don't block the user from seeing results
        }
      } else {
        console.log('Not saving quiz attempt - user or quiz missing:', { user: !!user, quiz: !!quiz });
      }
      
      // Navigate to results page with data
      navigate('/quiz-results', { 
        state: { 
          results, 
          quiz, 
          mode,
          userAnswers,
          questions 
        } 
      });

      console.log('=== QUIZ COMPLETION FINISHED SUCCESSFULLY ===');
    } catch (err) {
      console.error('=== ERROR COMPLETING QUIZ ===', err);
      // Still navigate to results even if saving fails
      const results = calculateResults();
      navigate('/quiz-results', { 
        state: { 
          results, 
          quiz, 
          mode,
          userAnswers,
          questions 
        } 
      });
    }
  };

  /**
   * Calculate quiz results
   */
  const calculateResults = (): QuizResults => {
    let correctCount = 0;
    let totalAnswered = 0;
    const answers: QuizAnswer[] = [];
    
    // Debug logging
    console.log('Calculating results...');
    console.log('User answers:', userAnswers);
    console.log('Questions count:', questions.length);
    
    questions.forEach(question => {
      const userAnswer = userAnswers[question.id];
      if (userAnswer) {
        totalAnswered++;
        const isCorrect = userAnswer === question.correct_answer;
        if (isCorrect) {
          correctCount++;
        }
        
        answers.push({
          questionId: question.id,
          userAnswer,
          correctAnswer: question.correct_answer || '',
          isCorrect,
          timeSpent: 0 // TODO: Track individual question time
        });
        
        console.log(`Q${question.order_index}: "${userAnswer}" vs "${question.correct_answer}" = ${isCorrect}`);
      } else {
        console.log(`Q${question.order_index}: No answer provided`);
        
        // Add unanswered question to results
        answers.push({
          questionId: question.id,
          userAnswer: '',
          correctAnswer: question.correct_answer || '',
          isCorrect: false,
          timeSpent: 0
        });
      }
    });

    const percentage = totalAnswered > 0 ? Math.round((correctCount / totalAnswered) * 100) : 0;
    const timeSpent = quizStartTime ? Math.round((new Date().getTime() - quizStartTime.getTime()) / 1000) : 0;

    console.log(`Results: ${correctCount}/${totalAnswered} correct (${percentage}%)`);

    return {
      correct: correctCount,
      total: questions.length,
      answered: totalAnswered,
      percentage,
      timeSpent,
      passed: percentage >= 70, // 70% pass threshold
      mode,
      answers
    };
  };

  /**
   * Handle quiz quit with confirmation
   */
  const handleQuit = (): void => {
    if (window.confirm('Are you sure you want to quit? Your progress will be lost.')) {
      navigate('/home');
    }
  };

  // Computed properties
  const currentQuestion = questions[currentQuestionIndex] || null;
  const progress = questions.length > 0 ? ((currentQuestionIndex + 1) / questions.length) * 100 : 0;
  const isCorrect = currentQuestion ? selectedAnswer === currentQuestion.correct_answer : false;
  const canProceed = !!selectedAnswer;

  return {
    // Core state
    quiz,
    questions,
    currentQuestionIndex,
    userAnswers,
    mode,
    loading,
    error,
    
    // UI state
    showFeedback,
    selectedAnswer,
    isAnswerSubmitted,
    quizStartTime,
    
    // Computed properties
    currentQuestion,
    progress,
    isCorrect,
    canProceed,
    
    // Handlers
    handleAnswerSelect,
    handleAnswerSubmit,
    handleNextQuestion,
    handlePreviousQuestion,
    handleQuizComplete,
    handleQuit,
    setCurrentQuestionIndex
  };
}; 