/**
 * QuizScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 664 lines | AFTER: ~400 lines | REDUCTION: ~40%
 * 
 * Uses shared modules for:
 * - Container layouts (fullHeightContainer, pageContainer)
 * - Card components (cardBase, cardCentered)
 * - Button components (buttonPrimary, buttonSecondary, fullWidth)
 * - Typography patterns (pageTitle, subtitle, description, mutedText)
 */

/* Container using shared full-height layout */
.wrapper {
  composes: fullHeightContainer from '@/styles/shared/layouts/containers.module.css';
  background-color: theme('colors.gray.50');
}

:global(.dark) .wrapper {
  background-color: theme('colors.gray.900');
}

.loadingWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 384px; /* 24rem */
}

.errorCard {
  text-align: center;
}

.header {
  margin-bottom: theme('spacing.6');
}

.headerTop {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: theme('spacing.4');
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

.headerRight {
  text-align: right;
}

/* Typography using shared components */
.title {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.xl');
}

.subtitle {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.sm');
}

.questionInfo {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.sm');
}

.answeredInfo {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.500');
}

:global(.dark) .answeredInfo {
  color: theme('colors.gray.400');
}

.progressBar {
  width: 100%;
  background-color: theme('colors.gray.200');
  border-radius: theme('borderRadius.full');
  height: theme('spacing.2');
}

:global(.dark) .progressBar {
  background-color: theme('colors.gray.700');
}

.progressFill {
  background-color: theme('colors.blue.600');
  height: theme('spacing.2');
  border-radius: theme('borderRadius.full');
  transition: all 0.3s ease;
}

/* Loading and Error Styles */
.loadingContainer {
  text-align: center;
}

.loadingSpinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: theme('spacing.12');
  width: theme('spacing.12');
  border: 2px solid transparent;
  border-bottom: 2px solid theme('colors.blue.600');
  margin: 0 auto theme('spacing.4');
}

.loadingText {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
}

.errorIcon {
  width: theme('spacing.12');
  height: theme('spacing.12');
  margin: 0 auto theme('spacing.2');
}

.errorIconContainer {
  color: theme('colors.red.600');
  margin-bottom: theme('spacing.4');
}

.errorTitle {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.2');
}

.errorMessage {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.4');
}

/* Button Styles using shared components */
.buttonBase {
  composes: buttonSecondary from '@/styles/shared/components/buttons.module.css';
}

.quitButton {
  color: theme('colors.gray.600');
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

.quitButton:hover {
  color: theme('colors.gray.800');
}

.previousButton {
  composes: buttonSecondary from '@/styles/shared/components/buttons.module.css';
}

.previousButton:hover:not(.buttonDisabled) {
  background-color: theme('colors.gray.700');
}

.checkAnswerButton {
  background-color: theme('colors.blue.600');
  color: white;
}

.checkAnswerButton:hover:not(.buttonDisabled) {
  background-color: theme('colors.blue.700');
}

.nextButton {
  composes: buttonPrimary from '@/styles/shared/components/buttons.module.css';
  background-color: theme('colors.green.600');
}

.nextButton:hover {
  background-color: theme('colors.green.700');
}

.finishButton {
  composes: buttonPrimary from '@/styles/shared/components/buttons.module.css';
  background-color: theme('colors.green.600');
}

.finishButton:hover {
  background-color: theme('colors.green.700');
}

.buttonDisabled {
  background-color: theme('colors.gray.400') !important;
  cursor: not-allowed !important;
}

/* Question Card Styles */
.questionCard {
  margin-bottom: theme('spacing.6');
}

.questionHeader {
  margin-bottom: theme('spacing.6');
}

.questionRow {
  display: flex;
  align-items: flex-start;
  gap: theme('spacing.3');
  margin-bottom: theme('spacing.4');
}

.questionNumber {
  flex-shrink: 0;
  width: theme('spacing.8');
  height: theme('spacing.8');
  background-color: theme('colors.blue.100');
  border-radius: theme('borderRadius.full');
  display: flex;
  align-items: center;
  justify-content: center;
}

.questionNumberText {
  color: theme('colors.blue.600');
  font-weight: theme('fontWeight.semibold');
  font-size: theme('fontSize.sm');
}

.questionContent {
  flex-grow: 1;
}

.questionText {
  composes: bodyText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.medium');
  margin-bottom: theme('spacing.2');
}

.exampleBox {
  background-color: theme('colors.gray.50');
  padding: theme('spacing.3');
  border-radius: theme('borderRadius.lg');
  margin-bottom: theme('spacing.4');
}

:global(.dark) .exampleBox {
  background-color: theme('colors.gray.800');
}

.exampleText {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  font-style: italic;
}

.optionsContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.3');
}

/* Answer Option Base Styles using shared components */
.optionBase {
  composes: cardBase from '@/styles/shared/components/cards.module.css';
  padding: theme('spacing.4');
  border: 2px solid;
  cursor: pointer;
}

/* Option States - Normal (no feedback) */
.optionNormal {
  border-color: theme('colors.gray.200');
}

.optionNormal:hover {
  border-color: theme('colors.gray.300');
}

:global(.dark) .optionNormal {
  border-color: theme('colors.gray.700');
}

:global(.dark) .optionNormal:hover {
  border-color: theme('colors.gray.600');
}

.optionSelected {
  border-color: theme('colors.blue.500');
  background-color: theme('colors.blue.50');
}

:global(.dark) .optionSelected {
  background-color: rgba(59, 130, 246, 0.2);
}

/* Feedback States (Practice Mode) */
.optionCorrect {
  border-color: theme('colors.green.500');
  background-color: theme('colors.green.50');
}

:global(.dark) .optionCorrect {
  background-color: rgba(34, 197, 94, 0.2);
}

.optionIncorrect {
  border-color: theme('colors.red.500');
  background-color: theme('colors.red.50');
}

:global(.dark) .optionIncorrect {
  background-color: rgba(239, 68, 68, 0.2);
}

.optionNeutral {
  border-color: theme('colors.gray.200');
  background-color: theme('colors.gray.50');
}

:global(.dark) .optionNeutral {
  border-color: theme('colors.gray.700');
  background-color: theme('colors.gray.800');
}

/* Option Content */
.optionContainer {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

.optionCircle {
  width: theme('spacing.6');
  height: theme('spacing.6');
  border-radius: theme('borderRadius.full');
  border: 2px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
}

.optionCircleNormal {
  border-color: theme('colors.gray.300');
}

.optionCircleSelected {
  border-color: theme('colors.blue.500');
  background-color: theme('colors.blue.500');
  color: white;
}

.optionText {
  color: theme('colors.gray.900');
  flex-grow: 1;
}

:global(.dark) .optionText {
  color: white;
}

.optionCheckIcon {
  width: theme('spacing.5');
  height: theme('spacing.5');
  color: theme('colors.green.500');
  margin-left: auto;
}

.optionXIcon {
  width: theme('spacing.5');
  height: theme('spacing.5');
  color: theme('colors.red.500');
  margin-left: auto;
}

/* Feedback Styles */
.feedbackContainer {
  margin-top: theme('spacing.6');
  padding: theme('spacing.4');
  border-radius: theme('borderRadius.lg');
  border-left: 4px solid;
}

.feedbackCorrect {
  background-color: theme('colors.green.50');
  border-color: theme('colors.green.500');
}

:global(.dark) .feedbackCorrect {
  background-color: rgba(34, 197, 94, 0.1);
}

.feedbackIncorrect {
  background-color: theme('colors.red.50');
  border-color: theme('colors.red.500');
}

:global(.dark) .feedbackIncorrect {
  background-color: rgba(239, 68, 68, 0.1);
}

.feedbackContent {
  display: flex;
  align-items: flex-start;
  gap: theme('spacing.3');
}

.feedbackIcon {
  flex-shrink: 0;
  width: theme('spacing.6');
  height: theme('spacing.6');
  border-radius: theme('borderRadius.full');
  display: flex;
  align-items: center;
  justify-content: center;
}

.feedbackIconCorrect {
  background-color: theme('colors.green.500');
}

.feedbackIconIncorrect {
  background-color: theme('colors.red.500');
}

.feedbackIconSvg {
  width: theme('spacing.4');
  height: theme('spacing.4');
  color: white;
}

.feedbackTextContainer {
  flex-grow: 1;
}

.feedbackTitle {
  font-weight: theme('fontWeight.medium');
  margin-bottom: theme('spacing.2');
}

.feedbackTitleCorrect {
  color: theme('colors.green.800');
}

:global(.dark) .feedbackTitleCorrect {
  color: theme('colors.green.300');
}

.feedbackTitleIncorrect {
  color: theme('colors.red.800');
}

:global(.dark) .feedbackTitleIncorrect {
  color: theme('colors.red.300');
}

.feedbackExplanation {
  font-size: theme('fontSize.sm');
}

.feedbackExplanationCorrect {
  color: theme('colors.green.700');
}

:global(.dark) .feedbackExplanationCorrect {
  color: theme('colors.green.200');
}

.feedbackExplanationIncorrect {
  color: theme('colors.red.700');
}

:global(.dark) .feedbackExplanationIncorrect {
  color: theme('colors.red.200');
}

.feedbackCorrectAnswer {
  font-size: theme('fontSize.sm');
  color: theme('colors.red.700');
  margin-top: theme('spacing.2');
}

:global(.dark) .feedbackCorrectAnswer {
  color: theme('colors.red.300');
}

/* Navigation Styles */
.navigationContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
  justify-content: space-between;
}

@media (min-width: theme('screens.sm')) {
  .navigationContainer {
    flex-direction: row;
  }
}

.navigationLeft {
  display: flex;
  gap: theme('spacing.2');
}

.navigationRight {
  display: flex;
  gap: theme('spacing.2');
}

/* Question Overview (Mobile) */
.overviewContainer {
  margin-top: theme('spacing.6');
}

@media (min-width: theme('screens.sm')) {
  .overviewContainer {
    display: none;
  }
}

.overviewTitle {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.3');
}

.overviewGrid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: theme('spacing.2');
}

.questionButton {
  composes: buttonSecondary from '@/styles/shared/components/buttons.module.css';
  width: theme('spacing.8');
  height: theme('spacing.8');
  font-size: theme('fontSize.sm');
  border: 1px solid;
}

.questionButtonCurrent {
  background-color: theme('colors.blue.600');
  color: white;
  border-color: theme('colors.blue.600');
}

.questionButtonAnswered {
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
  border-color: theme('colors.green.300');
}

.questionButtonUnanswered {
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.600');
  border-color: theme('colors.gray.300');
}

/* Mode Styles */
.modePractice::before {
  content: "🎯 ";
}

.modeTest::before {
  content: "📝 ";
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.optionBase {
  animation: fadeIn 0.3s ease-out;
}

.feedbackContainer {
  animation: slideIn 0.2s ease-out;
}

/* Responsive Design */
@media (max-width: theme('screens.sm')) {
  .headerTop {
    flex-direction: column;
    align-items: flex-start;
    gap: theme('spacing.2');
  }
  
  .navigationContainer {
    gap: theme('spacing.3');
  }
  
  .questionRow {
    flex-direction: column;
    gap: theme('spacing.2');
  }
  
  .questionNumber {
    align-self: flex-start;
  }
}