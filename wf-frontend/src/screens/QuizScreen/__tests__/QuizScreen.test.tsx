import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { <PERSON>rowserRouter } from 'react-router-dom';
import QuizScreen from '../QuizScreen';
import { useQuizScreenHandler } from '../QuizScreen.handler';
import { useLanguage } from '@/context/LanguageContext';

// Mock the handler
vi.mock('../QuizScreen.handler');
const mockUseQuizScreenHandler = vi.mocked(useQuizScreenHandler);

// Mock the language context
vi.mock('@/context/LanguageContext');
const mockUseLanguage = vi.mocked(useLanguage);

// Mock the layout components
vi.mock('@/components/Layout/ResponsiveContainer', () => ({
  PageWrapper: ({ children }: { children: React.ReactNode }) => <div data-testid="page-wrapper">{children}</div>,
  ResponsiveCard: ({ children }: { children: React.ReactNode }) => <div data-testid="responsive-card">{children}</div>
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  HelpCircle: () => <div data-testid="help-circle-icon" />,
  Info: () => <div data-testid="info-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  X: () => <div data-testid="x-icon" />
}));

// Translation mock
const translations: Record<string, string> = {
  'quiz.loadingQuiz': 'Loading quiz...',
  'quiz.quizLoadingError': 'Quiz Loading Error',
  'quiz.noQuestionsAvailable': 'No Questions Available',
  'quiz.noQuestionsText': 'No questions found for this quiz',
  'quiz.practiceMode': 'Practice Mode',
  'quiz.testMode': 'Test Mode',
  'quiz.question': 'Question',
  'quiz.of': 'of',
  'quiz.progress': 'Progress',
  'quiz.example': 'Example',
  'quiz.hint': 'Hint',
  'quiz.explanation': 'Explanation',
  'quiz.hintTitle': 'Grammar Hint',
  'quiz.explanationTitle': 'Explanation',
  'quiz.correctAnswerLabel': 'Correct Answer',
  'quiz.wrongAnswerLabel': 'Wrong Answer',
  'quiz.rule': 'Rule',
  'quiz.previous': 'Previous',
  'quiz.next': 'Next',
  'quiz.submitQuiz': 'Submit Quiz'
};

// Mock quiz data
const mockQuiz = {
  id: 'quiz-1',
  key: 'test-quiz',
  title: 'Test Quiz',
  description: 'A test quiz',
  category_id: 'cat-1',
  level_id: 'level-1',
  difficulty_level: 3,
  total_questions: 2,
  time_limit_minutes: 30,
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockQuestions = [
  {
    id: 'q1',
    quiz_id: 'quiz-1',
    question_type_id: 'type-1',
    question_text: 'What is the correct form?',
    difficulty_level: 3,
    order_index: 0,
    is_active: true,
    correct_answer: 'Option A',
    metadata: {
      example_usage: 'Example sentence here',
      grammar_rule: 'This is a grammar rule'
    },
    options: [
      { id: 'opt1', question_id: 'q1', option_key: 'A', option_text: 'Option A', is_correct: true, sort_order: 1, created_at: '2025-01-01' },
      { id: 'opt2', question_id: 'q1', option_key: 'B', option_text: 'Option B', is_correct: false, sort_order: 2, created_at: '2025-01-01' }
    ],
    explanations: [
      {
        id: 'exp1',
        question_id: 'q1',
        explanation_type: 'correct_answer',
        content: 'This is the correct explanation',
        metadata: { rule: 'Grammar rule explanation' },
        created_at: '2024-01-01T00:00:00Z'
      }
    ],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'q2',
    quiz_id: 'quiz-1',
    question_type_id: 'type-1',
    question_text: 'Second question?',
    difficulty_level: 3,
    order_index: 1,
    is_active: true,
    correct_answer: 'Option C',
    metadata: {},
    options: [
      { id: 'opt3', question_id: 'q2', option_key: 'A', option_text: 'Option C', is_correct: true, sort_order: 1, created_at: '2025-01-01' },
      { id: 'opt4', question_id: 'q2', option_key: 'B', option_text: 'Option D', is_correct: false, sort_order: 2, created_at: '2025-01-01' }
    ],
    explanations: [],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

// Default handler mock return
const defaultHandlerReturn = {
  quiz: mockQuiz,
  questions: mockQuestions,
  currentQuestionIndex: 0,
  userAnswers: {},
  mode: 'practice' as const,
  loading: false,
  error: null,
  showFeedback: false,
  selectedAnswer: '',
  isAnswerSubmitted: false,
  quizStartTime: new Date(),
  currentQuestion: mockQuestions[0],
  progress: 50,
  isCorrect: false,
  canProceed: false,
  handleAnswerSelect: vi.fn(),
  handleAnswerSubmit: vi.fn(),
  handleNextQuestion: vi.fn(),
  handlePreviousQuestion: vi.fn(),
  handleQuizComplete: vi.fn(),
  handleQuit: vi.fn(),
  setCurrentQuestionIndex: vi.fn()
};

const renderQuizScreen = () => {
  return render(
    <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
      <QuizScreen />
    </BrowserRouter>
  );
};

describe('QuizScreen', () => {
  beforeEach(() => {
    mockUseLanguage.mockReturnValue({
      t: (key: string) => translations[key] || key,
      language: 'en',
      setLanguage: vi.fn(),
      getRaw: vi.fn(),
      isLoading: false,
      availableLanguages: ['en', 'vi'],
      getLanguageDisplayName: vi.fn((_lang: string) => 'English'),
      getLanguageFlag: vi.fn((_lang: string) => '🇺🇸')
    });
    
    mockUseQuizScreenHandler.mockReturnValue(defaultHandlerReturn);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Loading State', () => {
    it('displays loading state when loading is true', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        loading: true
      });

      renderQuizScreen();

      expect(screen.getByText('Loading quiz...')).toBeInTheDocument();
      expect(screen.getByTestId('page-wrapper')).toBeInTheDocument();
    });
  });

  describe('Error State', () => {
    it('displays error state when error exists', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        loading: false,
        error: 'Failed to load quiz'
      });

      renderQuizScreen();

      expect(screen.getByText('Quiz Loading Error')).toBeInTheDocument();
      expect(screen.getByText('Failed to load quiz')).toBeInTheDocument();
      expect(screen.getByTestId('x-icon')).toBeInTheDocument();
    });
  });

  describe('No Questions State', () => {
    it('displays no questions state when questions array is empty', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        questions: [],
        currentQuestion: null
      });

      renderQuizScreen();

      expect(screen.getByText('No Questions Available')).toBeInTheDocument();
      expect(screen.getByText('No questions found for this quiz')).toBeInTheDocument();
    });

    it('displays no questions state when currentQuestion is null', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        currentQuestion: null
      });

      renderQuizScreen();

      expect(screen.getByText('No Questions Available')).toBeInTheDocument();
    });
  });

  describe('Quiz Header', () => {
    it('displays quiz title and mode correctly', () => {
      renderQuizScreen();

      expect(screen.getByText('Test Quiz')).toBeInTheDocument();
      expect(screen.getByText('Practice Mode')).toBeInTheDocument();
    });

    it('displays test mode when mode is test', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        mode: 'test'
      });

      renderQuizScreen();

      expect(screen.getByText('Test Mode')).toBeInTheDocument();
    });

    it('displays question progress correctly', () => {
      renderQuizScreen();

      // Use getAllByText to get all matching elements and select the most specific one
      const progressElements = screen.getAllByText((_content, element) => {
        const hasText = element?.textContent?.includes('Question') &&
                       element?.textContent?.includes('1') &&
                       element?.textContent?.includes('of') &&
                       element?.textContent?.includes('2');
        return hasText || false;
      });

      // The most specific element should be the last one (the actual div with the text)
      expect(progressElements.length).toBeGreaterThan(0);
      expect(progressElements[progressElements.length - 1]).toBeInTheDocument();
    });

    it('displays progress bar with correct percentage', () => {
      renderQuizScreen();

      expect(screen.getByText('Progress')).toBeInTheDocument();
      expect(screen.getByText('50%')).toBeInTheDocument();
    });

    it('calls handleQuit when quit button is clicked', () => {
      const mockHandleQuit = vi.fn();
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        handleQuit: mockHandleQuit
      });

      renderQuizScreen();

      const quitButton = screen.getByTestId('x-icon').closest('button');
      fireEvent.click(quitButton!);

      expect(mockHandleQuit).toHaveBeenCalledTimes(1);
    });
  });

  describe('Question Display', () => {
    it('displays current question text', () => {
      renderQuizScreen();

      expect(screen.getByText('What is the correct form?')).toBeInTheDocument();
    });

    it('displays example usage when available', () => {
      renderQuizScreen();

      expect(screen.getByText('Example:')).toBeInTheDocument();
      expect(screen.getByText('"Example sentence here"')).toBeInTheDocument();
    });

    it('does not display example when not available', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        currentQuestion: {
          ...mockQuestions[0],
          metadata: {}
        }
      });

      renderQuizScreen();

      expect(screen.queryByText('Example:')).not.toBeInTheDocument();
    });
  });

  describe('Answer Options', () => {
    it('displays all answer options', () => {
      renderQuizScreen();

      expect(screen.getByText('Option A')).toBeInTheDocument();
      expect(screen.getByText('Option B')).toBeInTheDocument();
    });

    it('calls handleAnswerSelect when option is clicked', () => {
      const mockHandleAnswerSelect = vi.fn();
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        handleAnswerSelect: mockHandleAnswerSelect
      });

      renderQuizScreen();

      const optionA = screen.getByText('Option A');
      fireEvent.click(optionA);

      expect(mockHandleAnswerSelect).toHaveBeenCalledWith('Option A');
    });

    it('shows selected state for selected answer', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        selectedAnswer: 'Option A'
      });

      renderQuizScreen();

      const optionA = screen.getByText('Option A').closest('button');
      expect(optionA).toHaveClass('border-blue-600');
    });
  });

  describe('Practice Mode Features', () => {
    it('shows hint button when grammar rule is available in practice mode', () => {
      renderQuizScreen();

      expect(screen.getByText('Hint')).toBeInTheDocument();
      expect(screen.getByTestId('help-circle-icon')).toBeInTheDocument();
    });

    it('shows explanation button when explanations are available in practice mode', () => {
      renderQuizScreen();

      expect(screen.getByText('Explanation')).toBeInTheDocument();
      expect(screen.getByTestId('info-icon')).toBeInTheDocument();
    });

    it('does not show hint/explanation buttons in test mode', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        mode: 'test'
      });

      renderQuizScreen();

      expect(screen.queryByText('Hint')).not.toBeInTheDocument();
      expect(screen.queryByText('Explanation')).not.toBeInTheDocument();
    });

    it('shows feedback icons for correct/incorrect answers in practice mode', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        selectedAnswer: 'Option A',
        showFeedback: true,
        mode: 'practice'
      });

      renderQuizScreen();

      expect(screen.getByTestId('check-circle-icon')).toBeInTheDocument();
    });

    it('shows feedback for incorrect answer in practice mode', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        selectedAnswer: 'Option B',
        showFeedback: true,
        isCorrect: false,
        mode: 'practice'
      });

      renderQuizScreen();

      // Use getAllByTestId since there are multiple x-icons (quit button + incorrect answer feedback)
      const xIcons = screen.getAllByTestId('x-icon');
      expect(xIcons.length).toBe(2); // One for quit button, one for incorrect feedback
    });
  });

  describe('Navigation', () => {
    it('displays previous button', () => {
      renderQuizScreen();

      expect(screen.getByText('Previous')).toBeInTheDocument();
    });

    it('disables previous button on first question', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        currentQuestionIndex: 0
      });

      renderQuizScreen();

      const previousButton = screen.getByText('Previous');
      expect(previousButton).toBeDisabled();
    });

    it('enables previous button when not on first question', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        currentQuestionIndex: 1
      });

      renderQuizScreen();

      const previousButton = screen.getByText('Previous');
      expect(previousButton).not.toBeDisabled();
    });

    it('calls handlePreviousQuestion when previous button is clicked', () => {
      const mockHandlePrevious = vi.fn();
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        currentQuestionIndex: 1,
        handlePreviousQuestion: mockHandlePrevious
      });

      renderQuizScreen();

      const previousButton = screen.getByText('Previous');
      fireEvent.click(previousButton);

      expect(mockHandlePrevious).toHaveBeenCalledTimes(1);
    });

    it('displays next button when not on last question', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        currentQuestionIndex: 0
      });

      renderQuizScreen();

      expect(screen.getByText('Next')).toBeInTheDocument();
    });

    it('displays submit button on last question', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        currentQuestionIndex: 1 // Last question (0-indexed)
      });

      renderQuizScreen();

      expect(screen.getByText('Submit Quiz')).toBeInTheDocument();
      expect(screen.queryByText('Next')).not.toBeInTheDocument();
    });

    it('disables next button when no answer is selected', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        selectedAnswer: '',
        canProceed: false
      });

      renderQuizScreen();

      const nextButton = screen.getByText('Next');
      expect(nextButton).toBeDisabled();
    });

    it('enables next button when answer is selected', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        selectedAnswer: 'Option A',
        canProceed: true
      });

      renderQuizScreen();

      const nextButton = screen.getByText('Next');
      expect(nextButton).not.toBeDisabled();
    });

    it('calls handleNextQuestion when next button is clicked', () => {
      const mockHandleNext = vi.fn();
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        selectedAnswer: 'Option A',
        canProceed: true,
        handleNextQuestion: mockHandleNext
      });

      renderQuizScreen();

      const nextButton = screen.getByText('Next');
      fireEvent.click(nextButton);

      expect(mockHandleNext).toHaveBeenCalledTimes(1);
    });

    it('calls handleQuizComplete when submit button is clicked', () => {
      const mockHandleComplete = vi.fn();
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        currentQuestionIndex: 1,
        selectedAnswer: 'Option C',
        canProceed: true,
        handleQuizComplete: mockHandleComplete
      });

      renderQuizScreen();

      const submitButton = screen.getByText('Submit Quiz');
      fireEvent.click(submitButton);

      expect(mockHandleComplete).toHaveBeenCalledTimes(1);
    });
  });

  describe('Interactive Features', () => {
    it('toggles hint display when hint button is clicked', () => {
      const { rerender } = renderQuizScreen();

      const hintButton = screen.getByText('Hint');
      fireEvent.click(hintButton);

      // Re-render with hint shown
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        // Note: The actual hint display is controlled by local state in the component
      });

      rerender(
        <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
          <QuizScreen />
        </BrowserRouter>
      );

      expect(hintButton).toBeInTheDocument();
    });

    it('toggles explanation display when explanation button is clicked', () => {
      const { rerender } = renderQuizScreen();

      const explanationButton = screen.getByText('Explanation');
      fireEvent.click(explanationButton);

      // Re-render with explanation shown
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        // Note: The actual explanation display is controlled by local state in the component
      });

      rerender(
        <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
          <QuizScreen />
        </BrowserRouter>
      );

      expect(explanationButton).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles missing quiz title gracefully', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        quiz: {
          ...mockQuiz,
          title: null
        }
      });

      renderQuizScreen();

      expect(screen.getByText('Word Formation Quiz')).toBeInTheDocument();
    });

    it('handles empty options array', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        currentQuestion: {
          ...mockQuestions[0],
          options: []
        }
      });

      renderQuizScreen();

      expect(screen.getByText('What is the correct form?')).toBeInTheDocument();
      expect(screen.queryByText('Option A')).not.toBeInTheDocument();
    });

    it('handles missing metadata gracefully', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        currentQuestion: {
          ...mockQuestions[0],
          metadata: null
        }
      });

      renderQuizScreen();

      expect(screen.getByText('What is the correct form?')).toBeInTheDocument();
      expect(screen.queryByText('Example:')).not.toBeInTheDocument();
    });

    it('handles zero progress correctly', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        progress: 0
      });

      renderQuizScreen();

      expect(screen.getByText('0%')).toBeInTheDocument();
    });

    it('handles 100% progress correctly', () => {
      mockUseQuizScreenHandler.mockReturnValue({
        ...defaultHandlerReturn,
        progress: 100
      });

      renderQuizScreen();

      expect(screen.getByText('100%')).toBeInTheDocument();
    });
  });
});
