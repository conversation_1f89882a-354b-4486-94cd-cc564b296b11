import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import '@testing-library/jest-dom';
import HomeScreen from '../HomeScreen';

// Mock the dependencies
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: vi.fn(() => ({
    language: 'en',
    setLanguage: vi.fn(),
    t: (key: string) => {
      const translations: Record<string, string> = {
        'home.welcomeBack': 'Welcome Back',
        'home.subtitle': 'Continue your learning journey',
        'home.findPerfectQuiz': 'Find the Perfect Quiz',
        'home.searchPlaceholder': 'Search quizzes...',
        'home.category': 'Category',
        'home.allCategories': 'All Categories',
        'home.subCategory': 'Sub Category',
        'home.allSubCategories': 'All Sub Categories',
        'home.level': 'Level',
        'home.allLevels': 'All Levels',
        'home.viewMode': 'View Mode',
        'home.gridView': 'Grid View',
        'home.listView': 'List View',
        'home.noQuizzesFound': 'No quizzes found',
        'home.clearFilters': 'Clear Filters',
        'home.startQuiz': 'Start Quiz',
        'home.questions': 'questions',
        'home.min': 'min',
        'home.selectCategory': 'home.selectCategory',
        'home.selectSubCategory': 'home.selectSubCategory',
        'home.selectLevel': 'home.selectLevel',
        'home.view': 'home.view',
        'home.grid': 'home.grid',
        'home.list': 'home.list',
        'home.quizzesFound': 'home.quizzesFound',
        'home.loading': 'home.loading',
        'home.errorLoadingQuizzes': 'home.errorLoadingQuizzes',
        'home.noQuizzesAvailable': 'home.noQuizzesAvailable',
        'home.selectCategoryFirst': 'home.selectCategoryFirst',
        'common.loading': 'Loading...',
        'common.error': 'Error',
        'common.previous': 'Previous',
        'common.next': 'Next'
      };
      return translations[key] || key;
    }
  }))
}));

vi.mock('@/components/Layout', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="layout">{children}</div>
}));

vi.mock('@/components/Pagination', () => ({
  Pagination: ({ currentPage, totalPages, onPageChange }: { currentPage: number; totalPages: number; onPageChange: (page: number) => void }) => (
    <div data-testid="pagination">
      <button 
        onClick={() => onPageChange(currentPage - 1)} 
        disabled={currentPage === 1}
        data-testid="prev-page"
      >
        Previous
      </button>
      <span data-testid="page-info">{currentPage} of {totalPages}</span>
      <button 
        onClick={() => onPageChange(currentPage + 1)} 
        disabled={currentPage === totalPages}
        data-testid="next-page"
      >
        Next
      </button>
    </div>
  )
}));

// Mock the HomeScreen handler
const mockHandler = {
  searchTerm: '',
  setSearchTerm: vi.fn(),
  selectedCategory: 'all',
  setSelectedCategory: vi.fn(),
  selectedSubCategory: 'all',
  setSelectedSubCategory: vi.fn(),
  selectedLevel: 'all',
  setSelectedLevel: vi.fn(),
  viewMode: 'grid' as const,
  setViewMode: vi.fn(),
  quizzes: [],
  categories: [],
  subCategories: [],
  levels: [],
  isLoading: false,
  isLoadingCategories: false,
  isLoadingSubCategories: false,
  isLoadingLevels: false,
  error: null,
  refetch: vi.fn(),
  currentPage: 1,
  setCurrentPage: vi.fn(),
  totalPages: 1,
  totalCount: 0,
  pageSize: 12
};

vi.mock('../HomeScreen.handler', () => ({
  useHomeScreen: vi.fn(() => mockHandler)
}));

describe('HomeScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset handler mock to default state
    Object.assign(mockHandler, {
      searchTerm: '',
      selectedCategory: 'all',
      selectedSubCategory: 'all',
      selectedLevel: 'all',
      viewMode: 'grid',
      quizzes: [],
      categories: [],
      subCategories: [],
      levels: [],
      isLoading: false,
      isLoadingCategories: false,
      isLoadingSubCategories: false,
      isLoadingLevels: false,
      error: null,
      currentPage: 1,
      totalPages: 1,
      totalCount: 0,
      pageSize: 12,
      setSearchTerm: vi.fn(),
      setSelectedCategory: vi.fn(),
      setSelectedSubCategory: vi.fn(),
      setSelectedLevel: vi.fn(),
      setViewMode: vi.fn(),
      refetch: vi.fn(),
      setCurrentPage: vi.fn()
    });
  });

  it('renders without crashing', () => {
    render(<HomeScreen />);
    expect(screen.getByTestId('layout')).toBeInTheDocument();
  });

  it('displays welcome section with correct text', () => {
    render(<HomeScreen />);
    
    expect(screen.getByText('Welcome Back')).toBeInTheDocument();
    expect(screen.getByText('Continue your learning journey')).toBeInTheDocument();
  });

  it('displays search and filter section', () => {
    render(<HomeScreen />);
    
    expect(screen.getByText('Find the Perfect Quiz')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search quizzes...')).toBeInTheDocument();
  });

  it('handles search input changes', () => {
    const mockSetSearchTerm = vi.fn();
    Object.assign(mockHandler, { setSearchTerm: mockSetSearchTerm });
    
    render(<HomeScreen />);
    
    const searchInput = screen.getByPlaceholderText('Search quizzes...');
    fireEvent.change(searchInput, { target: { value: 'test search' } });
    
    expect(mockSetSearchTerm).toHaveBeenCalledWith('test search');
  });

  it('displays category filter dropdown', () => {
    const mockCategories = [
      { id: '1', key: 'grammar', name: 'Grammar', description: 'Grammar quizzes' },
      { id: '2', key: 'vocabulary', name: 'Vocabulary', description: 'Vocabulary quizzes' }
    ];

    Object.assign(mockHandler, { categories: mockCategories });

    render(<HomeScreen />);

    // Check for the translated label key
    expect(screen.getByText('home.selectCategory')).toBeInTheDocument();
    // The select should contain the categories
    const categorySelect = screen.getByDisplayValue('All Categories');
    expect(categorySelect).toBeInTheDocument();
  });

  it('handles category selection changes', () => {
    const mockSetSelectedCategory = vi.fn();
    const mockCategories = [
      { id: '1', key: 'grammar', name: 'Grammar', description: 'Grammar quizzes' }
    ];

    Object.assign(mockHandler, {
      setSelectedCategory: mockSetSelectedCategory,
      categories: mockCategories
    });

    render(<HomeScreen />);

    const categorySelect = screen.getByDisplayValue('All Categories');
    fireEvent.change(categorySelect, { target: { value: 'grammar' } });

    expect(mockSetSelectedCategory).toHaveBeenCalledWith('grammar');
  });

  it('displays level filter dropdown', () => {
    const mockLevels = [
      { id: '1', key: 'A1', name: 'Beginner', description: 'Beginner level' },
      { id: '2', key: 'B1', name: 'Intermediate', description: 'Intermediate level' }
    ];

    Object.assign(mockHandler, { levels: mockLevels });

    render(<HomeScreen />);

    // Check for the translated label key
    expect(screen.getByText('home.selectLevel')).toBeInTheDocument();
    const levelSelect = screen.getByDisplayValue('All Levels');
    expect(levelSelect).toBeInTheDocument();
  });

  it('handles level selection changes', () => {
    const mockSetSelectedLevel = vi.fn();
    const mockLevels = [
      { id: '1', key: 'A1', name: 'Beginner', description: 'Beginner level' }
    ];

    Object.assign(mockHandler, {
      setSelectedLevel: mockSetSelectedLevel,
      levels: mockLevels
    });

    render(<HomeScreen />);

    const levelSelect = screen.getByDisplayValue('All Levels');
    fireEvent.change(levelSelect, { target: { value: 'A1' } });

    expect(mockSetSelectedLevel).toHaveBeenCalledWith('A1');
  });

  it('displays view mode toggle buttons', () => {
    render(<HomeScreen />);

    // Check for the view mode buttons directly
    expect(screen.getByText('home.grid')).toBeInTheDocument();
    expect(screen.getByText('home.list')).toBeInTheDocument();
  });

  it('handles view mode changes', () => {
    const mockSetViewMode = vi.fn();
    Object.assign(mockHandler, { setViewMode: mockSetViewMode });

    render(<HomeScreen />);

    const listViewButton = screen.getByText('home.list');
    fireEvent.click(listViewButton);

    expect(mockSetViewMode).toHaveBeenCalledWith('list');
  });

  it('shows loading state', () => {
    Object.assign(mockHandler, { isLoading: true });

    render(<HomeScreen />);

    // Check for loading text using common.loading instead
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows error state', () => {
    Object.assign(mockHandler, { error: 'Failed to load quizzes' });

    render(<HomeScreen />);

    // Check for the translated error message
    expect(screen.getByText('home.errorLoadingQuizzes')).toBeInTheDocument();
    expect(screen.getByText('Failed to load quizzes')).toBeInTheDocument();
  });

  it('displays no quizzes found message when no quizzes available', () => {
    Object.assign(mockHandler, {
      quizzes: [],
      isLoading: false,
      error: null
    });

    render(<HomeScreen />);

    expect(screen.getByText('No quizzes found')).toBeInTheDocument();
    // Use getAllByText to handle multiple "Clear Filters" buttons
    const clearFilterButtons = screen.getAllByText('Clear Filters');
    expect(clearFilterButtons.length).toBeGreaterThan(0);
  });

  it('displays quizzes in grid view', () => {
    const mockQuizzes = [
      {
        id: '1',
        title: 'Grammar Quiz 1',
        description: 'Test your grammar skills',
        difficulty_level: 3,
        estimated_duration: 15,
        question_count: 10,
        level: { key: 'B1', name: 'Intermediate' },
        category: { key: 'grammar', name: 'Grammar' }
      },
      {
        id: '2',
        title: 'Vocabulary Quiz 1',
        description: 'Test your vocabulary',
        difficulty_level: 2,
        estimated_duration: 20,
        question_count: 15,
        level: { key: 'A2', name: 'Elementary' },
        category: { key: 'vocabulary', name: 'Vocabulary' }
      }
    ];

    Object.assign(mockHandler, {
      quizzes: mockQuizzes,
      viewMode: 'grid',
      isLoading: false,
      error: null
    });

    render(<HomeScreen />);

    expect(screen.getByText('Grammar Quiz 1')).toBeInTheDocument();
    expect(screen.getByText('Vocabulary Quiz 1')).toBeInTheDocument();
    expect(screen.getByText('Test your grammar skills')).toBeInTheDocument();
    expect(screen.getByText('Test your vocabulary')).toBeInTheDocument();
  });

  it('displays pagination when there are multiple pages', () => {
    Object.assign(mockHandler, {
      totalPages: 3,
      currentPage: 2,
      totalCount: 30
    });

    render(<HomeScreen />);

    expect(screen.getByTestId('pagination')).toBeInTheDocument();
    expect(screen.getByTestId('page-info')).toHaveTextContent('2 of 3');
  });

  it('handles pagination page changes', () => {
    const mockSetCurrentPage = vi.fn();
    Object.assign(mockHandler, {
      setCurrentPage: mockSetCurrentPage,
      totalPages: 3,
      currentPage: 1
    });

    render(<HomeScreen />);

    const nextButton = screen.getByTestId('next-page');
    fireEvent.click(nextButton);

    expect(mockSetCurrentPage).toHaveBeenCalledWith(2);
  });
});
