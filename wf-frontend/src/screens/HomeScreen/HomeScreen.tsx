import React from 'react';
import { Filter, Search, Star, Clock, ArrowRight, Grid3X3, List } from 'lucide-react';
import Layout from '@/components/Layout';
import { Pagination } from '@/components/Pagination';
import { useHomeScreen } from './HomeScreen.handler';
import { useLanguage } from '@/context/LanguageContext';

const HomeScreen: React.FC = () => {
  const { t } = useLanguage();
  const {
    searchTerm,
    setSearchTerm,
    selectedCategory,
    setSelectedCategory,
    selectedSubCategory,
    setSelectedSubCategory,
    selectedLevel,
    setSelectedLevel,
    viewMode,
    setViewMode,
    quizzes,
    categories,
    subCategories,
    levels,
    isLoading,
    isLoadingCategories,
    isLoadingSubCategories,
    isLoadingLevels,
    error,
    // Pagination
    currentPage,
    setCurrentPage,
    totalPages,
    totalCount,
    pageSize
  } = useHomeScreen();

  // Since we're now filtering on the backend, we can use quizzes directly
  const filteredQuizzes = quizzes;

  const getDifficultyStars = (difficulty: number = 1) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${index < difficulty ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const getDifficultyLabel = (level: number): string => {
    if (level <= 2) return t('home.easy');
    if (level === 3) return t('home.medium');
    if (level >= 4) return t('home.hard');
    return t('home.easy'); // fallback
  };

  const getLevelColor = (level: string) => {
    const colors: Record<string, string> = {
      A1: 'bg-green-100 text-green-800',
      A2: 'bg-blue-100 text-blue-800',
      B1: 'bg-yellow-100 text-yellow-800',
      B2: 'bg-orange-100 text-orange-800',
      C1: 'bg-red-100 text-red-800',
      C2: 'bg-purple-100 text-purple-800',
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  // Show error state if there's an error
  if (error) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-red-800 mb-2">{t('home.errorLoadingQuizzes')}</h3>
            <p className="text-red-600">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200"
            >
              Retry
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{t('home.welcomeBack')}</h2>
          <p className="text-gray-600 dark:text-gray-300">{t('home.subtitle')}</p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-8">
          <div className="flex items-center mb-6">
            <Filter className="h-5 w-5 text-gray-400 dark:text-gray-500 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('home.findPerfectQuiz')}</h3>
          </div>
          
          {/* Search Bar */}
          <div className="relative mb-6">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 dark:text-gray-500" />
            <input
              type="text"
              placeholder={t('home.searchPlaceholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('home.selectCategory')}</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isLoadingCategories}
              >
                <option value="all">{t('home.allCategories')}</option>
                {categories.map(category => (
                  <option key={category.key} value={category.key}>
                    {category.name}
                  </option>
                ))}
              </select>
              {isLoadingCategories && (
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{t('common.loading')}</div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('home.selectSubCategory')}</label>
              <select
                value={selectedSubCategory}
                onChange={(e) => setSelectedSubCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={selectedCategory === 'all' || isLoadingSubCategories}
              >
                <option value="all">
                  {selectedCategory === 'all' ? t('home.selectCategoryFirst') : t('home.allSubCategories')}
                </option>
                {subCategories.map(subCategory => (
                  <option key={subCategory.key} value={subCategory.key}>
                    {subCategory.name}
                  </option>
                ))}
              </select>
              {isLoadingSubCategories && (
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{t('common.loading')}</div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('home.selectLevel')}</label>
              <select
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isLoadingLevels}
              >
                <option value="all">{t('home.allLevels')}</option>
                {levels.map(level => (
                  <option key={level.key} value={level.key}>
                    {level.name} ({level.key})
                  </option>
                ))}
              </select>
              {isLoadingLevels && (
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{t('common.loading')}</div>
              )}
            </div>

            <div className="flex items-end">
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('all');
                  setSelectedSubCategory('all');
                  setSelectedLevel('all');
                }}
                className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                {t('home.clearFilters')}
              </button>
            </div>
          </div>
        </div>

        {/* View Toggle and Results Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-300">
              {isLoading ? t('common.loading') : (
                totalCount > 0 ? (
                  <>
                    {t('home.showing')} {((currentPage - 1) * pageSize) + 1}-{Math.min(currentPage * pageSize, totalCount)} {t('home.of')} {totalCount} {totalCount === 1 ? t('home.quiz') : t('home.quizzesFound')}
                  </>
                ) : (
                  `0 ${t('home.quizzesFound')}`
                )
              )}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-300 mr-2">{t('home.view')}:</span>
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 ${
                  viewMode === 'grid'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Grid3X3 className="h-4 w-4 mr-1.5" />
                {t('home.grid')}
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 ${
                  viewMode === 'list'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <List className="h-4 w-4 mr-1.5" />
                {t('home.list')}
              </button>
            </div>
          </div>
        </div>

        {/* Quizzes Display */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 animate-pulse">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                <div className="flex justify-between">
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredQuizzes.length === 0 ? (
          <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
            <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Search className="h-8 w-8 text-gray-400 dark:text-gray-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('home.noQuizzesFound')}</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              {quizzes.length === 0
                ? t('home.noQuizzesAvailable')
                : t('home.adjustFilters')
              }
            </p>
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
                setSelectedSubCategory('all');
                setSelectedLevel('all');
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              {t('home.clearFilters')}
            </button>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredQuizzes.map(quiz => (
              <div key={quiz.id} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-100 dark:border-gray-700">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getLevelColor(quiz.level?.key || '')}`}>
                      {quiz.level?.key || ''}
                    </span>
                    <div className="flex items-center">
                      {getDifficultyStars(quiz.difficulty_level ?? 1)}
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                    {quiz.title}
                  </h3>

                  <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2 text-sm">
                    {quiz.description || 'Improve your English skills with this comprehensive quiz.'}
                  </p>

                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4 space-x-4">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {quiz.time_limit_minutes ?? '...'} {t('home.minutes')}
                    </div>
                    <div className="flex items-center">
                      <span className="text-gray-600 dark:text-gray-300">{quiz.total_questions ?? '...'} {t('home.questions')}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-sm">
                      <span className="text-gray-600 dark:text-gray-300">
                        {quiz.category?.parent ? quiz.category.parent.name : quiz.category?.name || ''}
                      </span>
                      {quiz.category?.parent && (
                        <span className="text-gray-400 dark:text-gray-500"> • {quiz.category.name}</span>
                      )}
                    </div>
                    <a
                      href={`/quiz/${quiz.id}?mode=practice`}
                      className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
                    >
                      {t('home.startPractice', 'Start Practice')}
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* List View */
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('home.tableQuiz')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('home.tableLevel')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('home.tableCategory')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('home.tableDifficulty')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('home.tableDuration')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('home.tableQuestions')}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('home.tableAction')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredQuizzes.map(quiz => (
                    <tr key={quiz.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                      <td className="px-6 py-4">
                        <div className="max-w-xs">
                          <div className="text-sm font-medium text-gray-900 dark:text-white line-clamp-1">
                            {quiz.title}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 line-clamp-3 mt-1">
                            {quiz.description || 'Improve your English skills with this comprehensive quiz.'}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getLevelColor(quiz.level?.key || '')}`}>
                          {quiz.level?.key || ''}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {quiz.category?.parent ? quiz.category.parent.name : quiz.category?.name || ''}
                        </div>
                        {quiz.category?.parent && (
                          <div className="text-sm text-gray-500 dark:text-gray-400">{quiz.category.name}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex mr-2">
                            {getDifficultyStars(quiz.difficulty_level ?? 1)}
                          </div>
                          <span className="text-sm text-gray-600 dark:text-gray-300">
                            {getDifficultyLabel(quiz.difficulty_level ?? 1)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-900 dark:text-white">
                          <Clock className="h-4 w-4 mr-1 text-gray-400 dark:text-gray-500" />
                          {quiz.time_limit_minutes ?? '...'} min
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {quiz.total_questions ?? '...'} {t('home.questions')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <a
                          href={`/quiz/${quiz.id}?mode=practice`}
                          className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
                        >
                          {t('home.startPractice', 'Start Practice')}
                          <ArrowRight className="h-4 w-4 ml-1" />
                        </a>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Pagination */}
        {!isLoading && totalPages > 1 && (
          <div className="mt-8">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
        )}
      </div>
    </Layout>
  );
};

export default HomeScreen; 