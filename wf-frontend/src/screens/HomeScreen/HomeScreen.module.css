/**
 * HomeScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 38 lines | AFTER: ~20 lines | REDUCTION: ~47%
 * 
 * Uses shared modules for:
 * - Card components (cardBase)
 * - Typography patterns (pageTitle, description, sectionTitle)
 */

/* Typography using shared components */
.welcomeTitle {
  composes: pageTitle from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.2');
}

.description {
  composes: description from '@/styles/shared/components/typography.module.css';
}

.filterTitle {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
}

/* Card using shared components */
.card {
  composes: cardBase from '@/styles/shared/components/cards.module.css';
  padding: theme('spacing.6');
}