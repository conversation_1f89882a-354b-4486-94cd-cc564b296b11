import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { QuizService } from '@/services/quizService';
import { Category, Level, Quiz } from 'wf-shared/types';
import { APP_CONSTANTS } from 'wf-shared/constants';

export type ViewMode = 'grid' | 'list';

export interface UseHomeScreenReturn {
  searchTerm: string;
  setSearchTerm: (v: string) => void;
  selectedCategory: string;
  setSelectedCategory: (v: string) => void;
  selectedSubCategory: string;
  setSelectedSubCategory: (v: string) => void;
  selectedLevel: string;
  setSelectedLevel: (v: string) => void;
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
  quizzes: Quiz[];
  categories: Category[];
  subCategories: Category[];
  levels: Level[];
  isLoading: boolean;
  isLoadingCategories: boolean;
  isLoadingSubCategories: boolean;
  isLoadingLevels: boolean;
  error: string | null;
  refetch: () => void;
  // Pagination
  currentPage: number;
  setCurrentPage: (page: number) => void;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

export const useHomeScreen = (): UseHomeScreenReturn => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedSubCategory, setSelectedSubCategory] = useState('all');
  const [selectedLevel, setSelectedLevel] = useState('all');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = APP_CONSTANTS.PAGINATION.DEFAULT_PAGE_SIZE;

  // Fetch main categories
  const {
    data: categories = [],
    isLoading: isLoadingCategories
  } = useQuery({
    queryKey: ['main-categories'],
    queryFn: async () => {
      const { data, error } = await QuizService.getMainCategories();
      if (error) throw new Error(error);
      return data ?? [];
    }
  });

  // Fetch subcategories when a category is selected
  const {
    data: subCategories = [],
    isLoading: isLoadingSubCategories
  } = useQuery({
    queryKey: ['subcategories', selectedCategory],
    queryFn: async () => {
      if (selectedCategory === 'all') return [];
      const { data, error } = await QuizService.getSubCategories(selectedCategory);
      if (error) throw new Error(error);
      return data ?? [];
    },
    enabled: selectedCategory !== 'all'
  });

  // Fetch levels
  const {
    data: levels = [],
    isLoading: isLoadingLevels
  } = useQuery({
    queryKey: ['levels'],
    queryFn: async () => {
      const { data, error } = await QuizService.getLevels();
      if (error) throw new Error(error);
      return data ?? [];
    }
  });

  // Fetch practice quizzes based on filters with pagination
  const {
    data: quizResponse,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['practice-quizzes', { searchTerm, selectedCategory, selectedSubCategory, selectedLevel, currentPage }],
    queryFn: async () => {
      // Use the main category if no subcategory is selected
      const categoryKey = selectedSubCategory !== 'all' ? selectedSubCategory :
                         selectedCategory !== 'all' ? selectedCategory : undefined;

      const response = await QuizService.getPracticeQuizzes({
        search: searchTerm || undefined,
        categoryKey,
        levelKey: selectedLevel !== 'all' ? selectedLevel : undefined,
        page: currentPage,
        pageSize
      });
      if (response.error) throw new Error(response.error);
      return response;
    }
  });

  // Extract data from response
  const quizzes = quizResponse?.data ?? [];
  const totalCount = quizResponse?.totalCount ?? 0;
  const totalPages = quizResponse?.totalPages ?? 0;

  return {
    searchTerm,
    setSearchTerm: (term: string) => {
      setSearchTerm(term);
      setCurrentPage(1); // Reset to first page when search changes
    },
    selectedCategory,
    setSelectedCategory: (category: string) => {
      setSelectedCategory(category);
      // Reset subcategory when category changes
      setSelectedSubCategory('all');
      setCurrentPage(1); // Reset to first page when category changes
    },
    selectedSubCategory,
    setSelectedSubCategory: (subCategory: string) => {
      setSelectedSubCategory(subCategory);
      setCurrentPage(1); // Reset to first page when subcategory changes
    },
    selectedLevel,
    setSelectedLevel: (level: string) => {
      setSelectedLevel(level);
      setCurrentPage(1); // Reset to first page when level changes
    },
    viewMode,
    setViewMode,
    quizzes,
    categories,
    subCategories,
    levels,
    isLoading,
    isLoadingCategories,
    isLoadingSubCategories,
    isLoadingLevels,
    error: error ? error.message : null,
    refetch,
    // Pagination
    currentPage,
    setCurrentPage,
    totalPages,
    totalCount,
    pageSize
  };
};