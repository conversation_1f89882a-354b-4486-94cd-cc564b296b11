import React from 'react';
import { Shield } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import ConditionalLayout from "@/components/ConditionalLayout";
import { useLanguage } from '@/context/LanguageContext';
import styles from './PrivacyScreen.module.css';

const PrivacyScreen: React.FC = () => {
  const { t, getRaw } = useLanguage();

  return (
    <ConditionalLayout>
      <div className={styles.container}>
        <Card>
          <CardHeader>
            <CardTitle className={styles.cardTitle}>
              <Shield className="h-8 w-8 mr-3" />
              {t('privacy.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className={styles.cardContent}>
            <p className="text-gray-600 dark:text-gray-400 mb-6">{t('privacy.lastUpdated')}</p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">1. {t('privacy.section1')}</h2>
            <p className="mb-4 text-gray-700 dark:text-gray-300">{t('privacy.content.section1Text')}</p>
            <ul className="list-disc list-inside mb-6 space-y-2 text-gray-700 dark:text-gray-300">
              {getRaw('privacy.content.section1List')?.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">2. {t('privacy.section2')}</h2>
            <p className="mb-4 text-gray-700 dark:text-gray-300">{t('privacy.content.section2Text')}</p>
            <ul className="list-disc list-inside mb-6 space-y-2 text-gray-700 dark:text-gray-300">
              {getRaw('privacy.content.section2List')?.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">3. {t('privacy.section3')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('privacy.content.section3Text')}
            </p>
            <ul className="list-disc list-inside mb-6 space-y-2 text-gray-700 dark:text-gray-300">
              {getRaw('privacy.content.section3List')?.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">4. {t('privacy.section4')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('privacy.content.section4Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">5. {t('privacy.section5')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('privacy.content.section5Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">6. {t('privacy.section6')}</h2>
            <p className="mb-4 text-gray-700 dark:text-gray-300">{t('privacy.content.section6Text')}</p>
            <ul className="list-disc list-inside mb-6 space-y-2 text-gray-700 dark:text-gray-300">
              {getRaw('privacy.content.section6List')?.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">7. {t('privacy.section7')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('privacy.content.section7Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">8. {t('privacy.section8')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('privacy.content.section8Text')}
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">9. {t('privacy.section9')}</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {t('privacy.content.section9Text')}
            </p>
          </CardContent>
        </Card>
      </div>
    </ConditionalLayout>
  );
};

export default PrivacyScreen; 