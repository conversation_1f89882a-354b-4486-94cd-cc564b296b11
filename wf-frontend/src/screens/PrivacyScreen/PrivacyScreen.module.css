/**
 * PrivacyScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 54 lines | AFTER: ~25 lines | REDUCTION: ~54%
 * 
 * Uses shared modules for:
 * - Container layouts (pageContainer)
 * - Typography patterns (sectionTitle, mutedText)
 */

/* Container using shared layout */
.container {
  composes: pageContainer from '@/styles/shared/layouts/containers.module.css';
}

/* Typography using shared components */
.sectionTitle {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
}

.lastUpdated {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.6');
}

/* Component-specific styles */
.cardTitle {
  display: flex;
  align-items: center;
  font-size: theme('fontSize.3xl');
}

.cardContent {
  max-width: none;
  line-height: theme('lineHeight.7');
  color: theme('colors.gray.700');
}

:global(.dark) .cardContent {
  color: theme('colors.gray.300');
}