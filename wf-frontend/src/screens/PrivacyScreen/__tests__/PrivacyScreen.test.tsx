import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import PrivacyScreen from '../PrivacyScreen';
import { useLanguage } from '@/context/LanguageContext';

// Mock dependencies
vi.mock('@/context/LanguageContext');
vi.mock('@/components/ConditionalLayout', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="conditional-layout">{children}</div>
}));
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props} data-testid="card">{children}</div>,
  CardContent: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props} data-testid="card-content">{children}</div>,
  CardHeader: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props} data-testid="card-header">{children}</div>,
  CardTitle: ({ children, ...props }: React.ComponentProps<'h1'>) => <h1 {...props} data-testid="card-title">{children}</h1>
}));

// Mock translation function
const mockT = vi.fn((key: string) => {
  const translations: Record<string, string> = {
    'privacy.title': 'Privacy Policy',
    'privacy.lastUpdated': 'Last updated: January 1, 2024',
    'privacy.section1': 'Information We Collect',
    'privacy.section2': 'How We Use Your Information',
    'privacy.section3': 'Information Sharing',
    'privacy.section4': 'Data Security',
    'privacy.section5': 'Your Rights',
    'privacy.section6': 'Contact Us',
    'privacy.content.section1Text': 'We collect information you provide directly to us.',
    'privacy.content.section2Text': 'We use the information we collect to provide our services.',
    'privacy.content.section3Text': 'We do not sell or rent your personal information.',
    'privacy.content.section4Text': 'We implement security measures to protect your data.',
    'privacy.content.section5Text': 'You have certain rights regarding your personal information.',
    'privacy.content.section6Text': 'If you have questions, please contact us.'
  };
  return translations[key] || key;
});

// Mock getRaw function for list items
const mockGetRaw = vi.fn((key: string) => {
  const rawData: Record<string, string[]> = {
    'privacy.content.section1List': [
      'Account information (name, email)',
      'Usage data and analytics',
      'Device and browser information'
    ],
    'privacy.content.section2List': [
      'Provide and maintain our services',
      'Improve user experience',
      'Send important notifications'
    ],
    'privacy.content.section3List': [
      'With your consent',
      'For legal compliance',
      'To protect our rights'
    ]
  };
  return rawData[key] || [];
});

describe('PrivacyScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    vi.mocked(useLanguage).mockReturnValue({
      t: mockT,
      getRaw: mockGetRaw,
      language: 'en',
      setLanguage: vi.fn(),
      isLoading: false,
      availableLanguages: ['en', 'vi', 'fr'] as const,
      getLanguageDisplayName: vi.fn((lang) => lang === 'en' ? 'English' : lang === 'vi' ? 'Vietnamese' : 'French'),
      getLanguageFlag: vi.fn((lang) => lang === 'en' ? '🇺🇸' : lang === 'vi' ? '🇻🇳' : '🇫🇷')
    });
  });

  const renderPrivacyScreen = () => {
    return render(<PrivacyScreen />);
  };

  describe('Layout and Structure', () => {
    it('renders within conditional layout', () => {
      renderPrivacyScreen();

      expect(screen.getByTestId('conditional-layout')).toBeInTheDocument();
    });

    it('renders within card component', () => {
      renderPrivacyScreen();

      expect(screen.getByTestId('card')).toBeInTheDocument();
      expect(screen.getByTestId('card-header')).toBeInTheDocument();
      expect(screen.getByTestId('card-content')).toBeInTheDocument();
    });

    it('displays main title with shield icon', () => {
      renderPrivacyScreen();

      expect(screen.getByText('Privacy Policy')).toBeInTheDocument();
      expect(screen.getByTestId('card-title')).toBeInTheDocument();
    });

    it('displays last updated information', () => {
      renderPrivacyScreen();

      expect(screen.getByText('Last updated: January 1, 2024')).toBeInTheDocument();
    });
  });

  describe('Privacy Policy Sections', () => {
    it('displays section 1 - Information We Collect', () => {
      renderPrivacyScreen();

      expect(screen.getByText('1. Information We Collect')).toBeInTheDocument();
      expect(screen.getByText('We collect information you provide directly to us.')).toBeInTheDocument();
    });

    it('displays section 1 list items', () => {
      renderPrivacyScreen();

      expect(screen.getByText('Account information (name, email)')).toBeInTheDocument();
      expect(screen.getByText('Usage data and analytics')).toBeInTheDocument();
      expect(screen.getByText('Device and browser information')).toBeInTheDocument();
    });

    it('displays section 2 - How We Use Your Information', () => {
      renderPrivacyScreen();

      expect(screen.getByText('2. How We Use Your Information')).toBeInTheDocument();
      expect(screen.getByText('We use the information we collect to provide our services.')).toBeInTheDocument();
    });

    it('displays section 2 list items', () => {
      renderPrivacyScreen();

      expect(screen.getByText('Provide and maintain our services')).toBeInTheDocument();
      expect(screen.getByText('Improve user experience')).toBeInTheDocument();
      expect(screen.getByText('Send important notifications')).toBeInTheDocument();
    });

    it('displays section 3 - Information Sharing', () => {
      renderPrivacyScreen();

      expect(screen.getByText('3. Information Sharing')).toBeInTheDocument();
      expect(screen.getByText('We do not sell or rent your personal information.')).toBeInTheDocument();
    });

    it('displays section 3 list items', () => {
      renderPrivacyScreen();

      expect(screen.getByText('With your consent')).toBeInTheDocument();
      expect(screen.getByText('For legal compliance')).toBeInTheDocument();
      expect(screen.getByText('To protect our rights')).toBeInTheDocument();
    });

    it('displays section 4 - Data Security', () => {
      renderPrivacyScreen();

      expect(screen.getByText('4. Data Security')).toBeInTheDocument();
      expect(screen.getByText('We implement security measures to protect your data.')).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('uses language context for translations', () => {
      renderPrivacyScreen();

      expect(useLanguage).toHaveBeenCalled();
      expect(mockT).toHaveBeenCalledWith('privacy.title');
      expect(mockT).toHaveBeenCalledWith('privacy.lastUpdated');
      expect(mockT).toHaveBeenCalledWith('privacy.section1');
    });

    it('uses getRaw for list content', () => {
      renderPrivacyScreen();

      expect(mockGetRaw).toHaveBeenCalledWith('privacy.content.section1List');
      expect(mockGetRaw).toHaveBeenCalledWith('privacy.content.section2List');
      expect(mockGetRaw).toHaveBeenCalledWith('privacy.content.section3List');
    });
  });

  describe('Responsive Design', () => {
    it('applies responsive container classes', () => {
      renderPrivacyScreen();

      const container = screen.getByText('Privacy Policy').closest('.max-w-4xl');
      expect(container).toHaveClass('max-w-4xl', 'mx-auto', 'px-4', 'sm:px-6', 'lg:px-8', 'py-8');
    });

    it('applies prose classes for content formatting', () => {
      renderPrivacyScreen();

      const content = screen.getByTestId('card-content');
      expect(content).toHaveClass('prose', 'prose-gray', 'dark:prose-invert', 'max-w-none');
    });
  });

  describe('Dark Mode Support', () => {
    it('applies dark mode classes to headings', () => {
      renderPrivacyScreen();

      const section1Heading = screen.getByText('1. Information We Collect');
      expect(section1Heading).toHaveClass('dark:text-white');

      const section2Heading = screen.getByText('2. How We Use Your Information');
      expect(section2Heading).toHaveClass('dark:text-white');
    });

    it('applies dark mode classes to text content', () => {
      renderPrivacyScreen();

      const lastUpdated = screen.getByText('Last updated: January 1, 2024');
      expect(lastUpdated).toHaveClass('dark:text-gray-400');

      const section1Text = screen.getByText('We collect information you provide directly to us.');
      expect(section1Text).toHaveClass('dark:text-gray-300');
    });

    it('applies dark mode classes to list items', () => {
      renderPrivacyScreen();

      const listItems = screen.getAllByText(/Account information|Usage data|Device and browser/);
      listItems.forEach(item => {
        const listElement = item.closest('ul');
        expect(listElement).toHaveClass('dark:text-gray-300');
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper heading hierarchy', () => {
      renderPrivacyScreen();

      const mainTitle = screen.getByText('Privacy Policy');
      expect(mainTitle.closest('[data-testid="card-title"]')).toBeInTheDocument();

      const sectionHeadings = screen.getAllByText(/^\d+\./);
      sectionHeadings.forEach(heading => {
        expect(heading.tagName).toBe('H2');
      });
    });

    it('has proper list structure', () => {
      renderPrivacyScreen();

      const lists = screen.getAllByRole('list');
      expect(lists.length).toBeGreaterThan(0);

      lists.forEach(list => {
        expect(list.tagName).toBe('UL');
        expect(list).toHaveClass('list-disc', 'list-inside');
      });
    });
  });

  describe('Content Completeness', () => {
    it('renders all expected sections', () => {
      renderPrivacyScreen();

      // Check that all main sections are present
      expect(screen.getByText('1. Information We Collect')).toBeInTheDocument();
      expect(screen.getByText('2. How We Use Your Information')).toBeInTheDocument();
      expect(screen.getByText('3. Information Sharing')).toBeInTheDocument();
      expect(screen.getByText('4. Data Security')).toBeInTheDocument();
    });

    it('handles empty list data gracefully', () => {
      vi.mocked(useLanguage).mockReturnValue({
        t: mockT,
        getRaw: vi.fn(() => []),
        language: 'en',
        setLanguage: vi.fn(),
        isLoading: false,
        availableLanguages: ['en', 'vi', 'fr'] as const,
        getLanguageDisplayName: vi.fn((lang) => lang === 'en' ? 'English' : lang === 'vi' ? 'Vietnamese' : 'French'),
        getLanguageFlag: vi.fn((lang) => lang === 'en' ? '🇺🇸' : lang === 'vi' ? '🇻🇳' : '🇫🇷')
      });

      renderPrivacyScreen();

      // Should still render sections even with empty lists
      expect(screen.getByText('1. Information We Collect')).toBeInTheDocument();
      expect(screen.getByText('We collect information you provide directly to us.')).toBeInTheDocument();
    });
  });

  describe('Translation Support', () => {
    it('handles missing translations gracefully', () => {
      const mockTWithMissing = vi.fn((key: string) => key);
      vi.mocked(useLanguage).mockReturnValue({
        t: mockTWithMissing,
        getRaw: mockGetRaw,
        language: 'en',
        setLanguage: vi.fn(),
        isLoading: false,
        availableLanguages: ['en', 'vi', 'fr'] as const,
        getLanguageDisplayName: vi.fn((lang) => lang === 'en' ? 'English' : lang === 'vi' ? 'Vietnamese' : 'French'),
        getLanguageFlag: vi.fn((lang) => lang === 'en' ? '🇺🇸' : lang === 'vi' ? '🇻🇳' : '🇫🇷')
      });

      renderPrivacyScreen();

      expect(screen.getByText('privacy.title')).toBeInTheDocument();
      expect(screen.getByText('privacy.lastUpdated')).toBeInTheDocument();
    });
  });
});
