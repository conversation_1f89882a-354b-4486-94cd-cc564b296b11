import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import ProfileScreen from '../ProfileScreen';

// Mock dependencies
vi.mock('@/context/AuthContext', () => ({
  useAuth: vi.fn()
}));

vi.mock('@/context/LanguageContext', () => ({
  useLanguage: vi.fn()
}));

vi.mock('@/context/ThemeContext', () => ({
  useTheme: vi.fn()
}));

vi.mock('../ProfileScreen.handler', () => ({
  useProfileHandler: vi.fn()
}));

vi.mock('@/components/Layout', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="layout">{children}</div>
}));

vi.mock('lucide-react', () => ({
  User: () => <svg data-testid="user-icon" />,
  Mail: () => <svg data-testid="mail-icon" />,
  Save: () => <svg data-testid="save-icon" />,
  AlertCircle: () => <svg data-testid="alert-circle-icon" />,
  RefreshCw: () => <svg data-testid="refresh-icon" />,
  Wifi: () => <svg data-testid="wifi-icon" />,
  WifiOff: () => <svg data-testid="wifi-off-icon" />,
  Loader2: () => <svg data-testid="loader2-icon" />
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, variant, className, disabled, ...props }: React.ComponentProps<'button'> & { variant?: string }) => (
    <button
      onClick={onClick}
      className={className}
      disabled={disabled}
      data-variant={variant}
      data-testid="button"
      {...props}
    >
      {children}
    </button>
  )
}));

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: React.ComponentProps<'div'>) => (
    <div className={className} data-testid="card">{children}</div>
  ),
  CardContent: ({ children, className }: React.ComponentProps<'div'>) => (
    <div className={className} data-testid="card-content">{children}</div>
  ),
  CardHeader: ({ children, className }: React.ComponentProps<'div'>) => (
    <div className={className} data-testid="card-header">{children}</div>
  ),
  CardTitle: ({ children, className }: React.ComponentProps<'h3'>) => (
    <h3 className={className} data-testid="card-title">{children}</h3>
  )
}));

// Import mocked modules
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import { useTheme } from '@/context/ThemeContext';
import { useProfileHandler } from '../ProfileScreen.handler';

// Mock data
const mockTranslations = {
  'profile.loadingProfile': 'Loading profile...',
  'profile.title': 'Profile Settings',
  'profile.subtitle': 'Manage your account information and preferences',
  'profile.level': 'Level',
  'profile.personalInfo': 'Personal Information',
  'profile.firstName': 'First Name',
  'profile.lastName': 'Last Name',
  'profile.email': 'Email',
  'profile.emailNote': 'Email cannot be changed',
  'profile.selectLevel': 'Select your level',
  'profile.saving': 'Saving...',
  'profile.saveChanges': 'Save Changes',
  'common.cancel': 'Cancel',
  'common.edit': 'Edit',
  'nav.profile': 'Profile'
};

const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  first_name: 'John',
  last_name: 'Doe',
  level_id: 'level-1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockFormData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  level: 'level-1',
  levelName: 'Beginner'
};

const mockLevels = [
  { id: 'level-1', key: 'A1', name: 'Beginner', system: 'CEFR', description: 'Beginner level', sort_order: 1, is_active: true },
  { id: 'level-2', key: 'A2', name: 'Elementary', system: 'CEFR', description: 'Elementary level', sort_order: 2, is_active: true },
  { id: 'level-3', key: 'B1', name: 'Intermediate', system: 'CEFR', description: 'Intermediate level', sort_order: 3, is_active: true }
];

const mockAuthContext = {
  user: mockUser,
  loading: false,
  isAuthenticated: true,
  signIn: vi.fn(),
  signUp: vi.fn(),
  signOut: vi.fn()
};

const mockLanguageContext = {
  t: vi.fn((key: string) => mockTranslations[key as keyof typeof mockTranslations] || key),
  language: 'en' as const,
  setLanguage: vi.fn(),
  getRaw: vi.fn(),
  isLoading: false,
  availableLanguages: ['en', 'vi', 'fr'] as ('en' | 'vi' | 'fr')[],
  getLanguageDisplayName: vi.fn((_lang: string) => 'English'),
  getLanguageFlag: vi.fn((_lang: string) => '🇺🇸')
};

const mockThemeContext = {
  theme: 'light' as const,
  setTheme: vi.fn()
};

const mockProfileHandler = {
  formData: mockFormData,
  isEditing: false,
  loading: false,
  saving: false,
  error: null,
  levels: mockLevels,
  hasChanges: true,
  handleInputChange: vi.fn(),
  handleSave: vi.fn(),
  handleCancel: vi.fn(),
  toggleEditing: vi.fn(),
  retryLoad: vi.fn()
};

describe('ProfileScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset the mock function
    mockLanguageContext.t.mockImplementation((key: string) => mockTranslations[key as keyof typeof mockTranslations] || key);
    vi.mocked(useAuth).mockReturnValue(mockAuthContext);
    vi.mocked(useLanguage).mockReturnValue(mockLanguageContext);
    vi.mocked(useTheme).mockReturnValue(mockThemeContext);
    vi.mocked(useProfileHandler).mockReturnValue(mockProfileHandler);
  });

  const renderProfileScreen = () => {
    return render(
      <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        <ProfileScreen />
      </BrowserRouter>
    );
  };

  describe('Loading State', () => {
    it('displays loading spinner when loading', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        loading: true
      });

      renderProfileScreen();

      expect(screen.getByText('Loading profile...')).toBeInTheDocument();
      expect(screen.getByTestId('layout')).toBeInTheDocument();
    });

    it('displays loading animation', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        loading: true
      });

      renderProfileScreen();

      const loadingSpinner = screen.getByText('Loading profile...').previousElementSibling;
      expect(loadingSpinner).toHaveClass('animate-spin');
    });
  });

  describe('Profile Header', () => {
    it('displays page title and subtitle', () => {
      renderProfileScreen();

      expect(screen.getByText('Profile Settings')).toBeInTheDocument();
      expect(screen.getByText('Manage your account information and preferences')).toBeInTheDocument();
    });

    it('displays user avatar and name', () => {
      renderProfileScreen();

      const userIcons = screen.getAllByTestId('user-icon');
      expect(userIcons.length).toBeGreaterThan(0);
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('displays user level', () => {
      renderProfileScreen();

      expect(screen.getByText('Level: Beginner')).toBeInTheDocument();
    });

    it('displays edit button in view mode', () => {
      renderProfileScreen();

      const editButton = screen.getByText('Edit Profile');
      expect(editButton).toBeInTheDocument();
      expect(editButton).toHaveAttribute('data-variant', 'default');
    });

    it('displays cancel button in edit mode', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        isEditing: true
      });

      renderProfileScreen();

      const cancelButtons = screen.getAllByText('Cancel');
      expect(cancelButtons.length).toBeGreaterThan(0);
      // Check the header cancel button (first one)
      expect(cancelButtons[0]).toHaveAttribute('data-variant', 'outline');
    });
  });

  describe('Error Handling', () => {
    it('displays error message when error exists', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        error: 'Failed to load profile'
      });

      renderProfileScreen();

      expect(screen.getByText('Failed to load profile')).toBeInTheDocument();
    });

    it('applies error styling to error message', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        error: 'Failed to load profile'
      });

      renderProfileScreen();

      // AsyncErrorBoundary shows the error message for unknown error types
      const errorMessage = screen.getByText('Failed to load profile');
      const errorContainer = errorMessage.closest('div[class*="bg-amber-50"]');
      expect(errorContainer).toHaveClass('bg-amber-50', 'dark:bg-amber-900/10');
    });
  });

  describe('Personal Information Section', () => {
    it('displays section title with icon', () => {
      renderProfileScreen();

      expect(screen.getByText('Personal Information')).toBeInTheDocument();
      expect(screen.getAllByTestId('user-icon')).toHaveLength(2); // One in header, one in section
    });

    it('displays first name field in view mode', () => {
      renderProfileScreen();

      expect(screen.getByText('First Name')).toBeInTheDocument();
      expect(screen.getByText('John')).toBeInTheDocument();
    });

    it('displays last name field in view mode', () => {
      renderProfileScreen();

      expect(screen.getByText('Last Name')).toBeInTheDocument();
      expect(screen.getByText('Doe')).toBeInTheDocument();
    });

    it('displays email field with icon and note', () => {
      renderProfileScreen();

      expect(screen.getByText('Email')).toBeInTheDocument();
      expect(screen.getByTestId('mail-icon')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('Email cannot be changed')).toBeInTheDocument();
    });

    it('displays level field in view mode', () => {
      renderProfileScreen();

      expect(screen.getByText('Level')).toBeInTheDocument();
      expect(screen.getByText('Beginner')).toBeInTheDocument();
    });
  });

  describe('Edit Mode', () => {
    beforeEach(() => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        isEditing: true
      });
    });

    it('displays input fields in edit mode', () => {
      renderProfileScreen();

      const firstNameInput = screen.getByDisplayValue('John');
      const lastNameInput = screen.getByDisplayValue('Doe');

      expect(firstNameInput).toBeInTheDocument();
      expect(lastNameInput).toBeInTheDocument();
      expect(firstNameInput.tagName).toBe('INPUT');
      expect(lastNameInput.tagName).toBe('INPUT');
    });

    it('displays level select dropdown in edit mode', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        isEditing: true,
        formData: {
          ...mockFormData,
          level: 'level-1'
        }
      });

      renderProfileScreen();

      const levelSelect = screen.getByRole('combobox');
      expect(levelSelect).toBeInTheDocument();
      expect(levelSelect.tagName).toBe('SELECT');
      expect(levelSelect).toHaveValue('level-1');
    });

    it('displays level options in select dropdown', () => {
      renderProfileScreen();

      expect(screen.getByText('Select your level')).toBeInTheDocument();
      expect(screen.getByText('A1 - Beginner')).toBeInTheDocument();
      expect(screen.getByText('A2 - Elementary')).toBeInTheDocument();
      expect(screen.getByText('B1 - Intermediate')).toBeInTheDocument();
    });

    it('displays save and cancel buttons in edit mode', () => {
      renderProfileScreen();

      expect(screen.getByText('Save Changes')).toBeInTheDocument();
      expect(screen.getByTestId('save-icon')).toBeInTheDocument();
      expect(screen.getAllByText('Cancel')).toHaveLength(2); // One in header, one in form
    });

    it('displays saving state when loading', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        isEditing: true,
        saving: true
      });

      renderProfileScreen();

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('calls toggleEditing when edit button is clicked', () => {
      renderProfileScreen();

      const editButton = screen.getByText('Edit Profile');
      fireEvent.click(editButton);

      expect(mockProfileHandler.toggleEditing).toHaveBeenCalledTimes(1);
    });

    it('calls handleInputChange when first name input changes', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        isEditing: true
      });

      renderProfileScreen();

      const firstNameInput = screen.getByDisplayValue('John');
      fireEvent.change(firstNameInput, { target: { value: 'Jane' } });

      expect(mockProfileHandler.handleInputChange).toHaveBeenCalledTimes(1);
    });

    it('calls handleInputChange when last name input changes', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        isEditing: true
      });

      renderProfileScreen();

      const lastNameInput = screen.getByDisplayValue('Doe');
      fireEvent.change(lastNameInput, { target: { value: 'Smith' } });

      expect(mockProfileHandler.handleInputChange).toHaveBeenCalledTimes(1);
    });

    it('calls handleInputChange when level select changes', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        isEditing: true,
        formData: {
          ...mockFormData,
          level: 'level-1'
        }
      });

      renderProfileScreen();

      const levelSelect = screen.getByRole('combobox');
      fireEvent.change(levelSelect, { target: { value: 'level-2' } });

      expect(mockProfileHandler.handleInputChange).toHaveBeenCalledTimes(1);
    });

    it('calls handleSave when save button is clicked', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        isEditing: true
      });

      renderProfileScreen();

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      expect(mockProfileHandler.handleSave).toHaveBeenCalledTimes(1);
    });

    it('calls handleCancel when cancel button is clicked', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        isEditing: true
      });

      renderProfileScreen();

      const cancelButtons = screen.getAllByText('Cancel');
      fireEvent.click(cancelButtons[1]); // Click the form cancel button

      expect(mockProfileHandler.handleCancel).toHaveBeenCalledTimes(1);
    });
  });

  describe('Component Integration', () => {
    it('uses language context for translations', () => {
      renderProfileScreen();

      expect(mockLanguageContext.t).toHaveBeenCalledWith('profile.title');
      expect(mockLanguageContext.t).toHaveBeenCalledWith('profile.subtitle');
      expect(mockLanguageContext.t).toHaveBeenCalledWith('profile.personalInfo');
    });

    it('calls profile handler hook', () => {
      renderProfileScreen();

      expect(useProfileHandler).toHaveBeenCalledTimes(1);
    });

    it('renders within layout component', () => {
      renderProfileScreen();

      expect(screen.getByTestId('layout')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('applies responsive grid classes to form', () => {
      renderProfileScreen();

      const formGrid = screen.getByText('First Name').closest('.grid');
      expect(formGrid).toHaveClass('grid-cols-1', 'md:grid-cols-2');
    });

    it('applies responsive container classes', () => {
      renderProfileScreen();

      const container = screen.getByText('Profile Settings').closest('.max-w-4xl');
      expect(container).toHaveClass('mx-auto', 'px-4', 'sm:px-6', 'lg:px-8');
    });
  });

  describe('Dark Mode Support', () => {
    it('applies dark mode classes to text elements', () => {
      renderProfileScreen();

      const title = screen.getByText('Profile Settings');
      expect(title).toHaveClass('dark:text-white');

      const subtitle = screen.getByText('Manage your account information and preferences');
      expect(subtitle).toHaveClass('dark:text-gray-300');
    });

    it('applies dark mode classes to form elements in edit mode', () => {
      vi.mocked(useProfileHandler).mockReturnValue({
        ...mockProfileHandler,
        isEditing: true
      });

      renderProfileScreen();

      const firstNameInput = screen.getByDisplayValue('John');
      expect(firstNameInput).toHaveClass('dark:bg-gray-700', 'dark:text-white');
    });
  });
});
