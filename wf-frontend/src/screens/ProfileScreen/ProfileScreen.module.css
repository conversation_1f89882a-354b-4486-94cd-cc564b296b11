/**
 * ProfileScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 32 lines | AFTER: ~15 lines | REDUCTION: ~53%
 * 
 * Uses shared modules for:
 * - Container layouts (pageContainer)
 * - Typography patterns (pageTitle)
 */

/* Container using shared layout */
.container {
  composes: pageContainer from '@/styles/shared/layouts/containers.module.css';
}

/* Typography using shared components */
.title {
  composes: pageTitle from '@/styles/shared/components/typography.module.css';
}

/* Component-specific styles */
.section {
  margin-bottom: theme('spacing.8');
}