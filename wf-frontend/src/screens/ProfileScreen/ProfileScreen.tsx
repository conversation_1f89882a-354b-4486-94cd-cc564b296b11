import React from 'react';
import { User, Mail, Save } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Layout from "@/components/Layout";
import { useLanguage } from '@/context/LanguageContext';
import { useProfileHandler } from './ProfileScreen.handler';
import { LoadingState, LoadingButton } from '@/components/LoadingStates';
import AsyncErrorBoundary from '@/components/AsyncErrorBoundary';

const ProfileScreen: React.FC = () => {
  const { t } = useLanguage();
  const {
    formData,
    isEditing,
    loading,
    saving,
    error,
    levels,
    handleInputChange,
    handleSave,
    handleCancel,
    toggleEditing,
    retryLoad,
    hasChanges
  } = useProfileHandler();

  return (
    <Layout>
      <div className={containerStyles.wrapper}>
        {/* Page Title */}
        <div className={containerStyles.header}>
          <h2 className={containerStyles.title}>{t('profile.title')}</h2>
          <p className={containerStyles.subtitle}>{t('profile.subtitle')}</p>
        </div>

        <LoadingState
          loading={loading}
          error={error}
          onRetry={retryLoad}
          loadingComponent={
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-300">{t('profile.loadingProfile')}</p>
              </div>
            </div>
          }
          errorComponent={
            <AsyncErrorBoundary
              error={error}
              onRetry={retryLoad}
              onDismiss={() => {}}
            >
              <div></div>
            </AsyncErrorBoundary>
          }
        >
          {/* Error Message for inline errors */}
          {error && !loading && (
            <AsyncErrorBoundary
              error={error}
              onRetry={retryLoad}
              showErrorDetails={false}
            >
              <div></div>
            </AsyncErrorBoundary>
        )}

        {/* Profile Card */}
        <Card className={cardStyles.profileCard}>
          <CardHeader className={cardStyles.profileHeader}>
            <div className={cardStyles.profileInfo}>
              <div className={cardStyles.avatar}>
                <User className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className={cardStyles.profileName}>
                  {formData.firstName} {formData.lastName}
                </CardTitle>
                <p className={cardStyles.profileLevel}>{t('profile.level')}: {formData.levelName}</p>
              </div>
            </div>
            <Button
              onClick={toggleEditing}
              variant={isEditing ? "outline" : "default"}
              className={buttonStyles.editButton}
            >
              {isEditing ? t('common.cancel') : `${t('common.edit')} ${t('nav.profile')}`}
            </Button>
          </CardHeader>
        </Card>

        {/* Personal Information */}
        <Card className={cardStyles.infoCard}>
          <CardHeader>
            <CardTitle className={cardStyles.sectionTitle}>
              <User className="h-5 w-5 mr-2" />
              {t('profile.personalInfo')}
            </CardTitle>
          </CardHeader>
          <CardContent className={cardStyles.formContent}>
            <div className={cardStyles.formGrid}>
              <div>
                <label className={labelStyles.fieldLabel}>{t('profile.firstName')}</label>
                {isEditing ? (
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className={inputStyles.textInput}
                  />
                ) : (
                  <p className={inputStyles.displayText}>{formData.firstName}</p>
                )}
              </div>

              <div>
                <label className={labelStyles.fieldLabel}>{t('profile.lastName')}</label>
                {isEditing ? (
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className={inputStyles.textInput}
                  />
                ) : (
                  <p className={inputStyles.displayText}>{formData.lastName}</p>
                )}
              </div>

              <div>
                <label className={labelStyles.fieldLabelWithIcon}>
                  <Mail className="h-4 w-4 inline mr-1" />
                  {t('profile.email')}
                </label>
                <p className={inputStyles.displayText}>{formData.email}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('profile.emailNote')}</p>
              </div>

              <div>
                <label className={labelStyles.fieldLabel}>{t('profile.level')}</label>
                {isEditing ? (
                  <select
                    name="level"
                    value={formData.level}
                    onChange={handleInputChange}
                    className={inputStyles.selectInput}
                  >
                    <option value="">{t('profile.selectLevel')}</option>
                    {levels.map(level => (
                      <option key={level.id} value={level.id}>
                        {level.key} - {level.name}
                      </option>
                    ))}
                  </select>
                ) : (
                  <p className={inputStyles.displayText}>{formData.levelName}</p>
                )}
              </div>
            </div>

            {isEditing && (
              <div className={buttonStyles.actionButtonsContainer}>
                <LoadingButton
                  loading={saving}
                  onClick={handleSave}
                  className={buttonStyles.saveButton}
                  disabled={!hasChanges}
                  variant="primary"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {t('profile.saveChanges')}
                </LoadingButton>
                <Button 
                  variant="outline" 
                  onClick={handleCancel} 
                  disabled={saving}
                >
                  {t('common.cancel')}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
        </LoadingState>
      </div>
    </Layout>
  );
};

export default ProfileScreen; 