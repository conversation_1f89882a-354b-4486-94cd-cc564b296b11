import { useState, useCallback, useEffect, useRef } from 'react';
import { useAuth } from '@/context/AuthContext';
import { QuizService } from '@/services/quizService';
import type { Level } from '@/types';
import { useAsyncError } from '@/components/AsyncErrorBoundary';
import { toast } from '@/components/ui/use-toast';

// Types for Profile form data - simplified to match database schema
export interface ProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  level: string;
  levelName: string;
}

// Handler interface for type safety
export interface ProfileHandler {
  formData: ProfileFormData;
  isEditing: boolean;
  loading: boolean;
  saving: boolean;
  error: string | null;
  levels: Level[];
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleSave: () => Promise<void>;
  handleCancel: () => void;
  toggleEditing: () => void;
  retryLoad: () => void;
  hasChanges: boolean;
}

// Initial form data
const initialFormData: ProfileFormData = {
  firstName: '',
  lastName: '',
  email: '',
  level: '',
  levelName: ''
};

export const useProfileHandler = (): ProfileHandler => {
  const { user } = useAuth();
  const { createNetworkError, createServerError, createValidationError } = useAsyncError();

  // Form state management
  const [formData, setFormData] = useState<ProfileFormData>(initialFormData);
  const [isEditing, setIsEditing] = useState(false);
  const [originalData, setOriginalData] = useState<ProfileFormData>(initialFormData);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [levels, setLevels] = useState<Level[]>([]);

  // Ref to track if data is being loaded to prevent duplicate calls
  const isLoadingRef = useRef<boolean>(false);

  // Computed properties
  const hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData);

  /**
   * Load user profile data from database
   */
  const loadUserProfile = useCallback(async (): Promise<void> => {
    try {
      // Prevent duplicate calls if already loading
      if (isLoadingRef.current) {
        console.log('Profile data already loading, skipping duplicate call');
        return;
      }
      
      isLoadingRef.current = true;
      setLoading(true);
      setError(null);

      // Fetch user profile data and all levels in parallel for better performance
      const [userProfileResult, levelsResult] = await Promise.all([
        QuizService.getUserProfile(user!.id),
        QuizService.getLevels()
      ]);

      const { data: userData, error: profileError } = userProfileResult;
      const { data: allLevels, error: levelsError } = levelsResult;

      if (profileError || !userData) {
        const errorMessage = profileError || 'Failed to load profile data';
        setError(errorMessage);
        
        // Show appropriate error type
        if (profileError?.includes('network') || profileError?.includes('fetch')) {
          setError(createNetworkError(errorMessage).message);
        } else {
          setError(createServerError(errorMessage).message);
        }
        return;
      }

      const profileData: ProfileFormData = {
        firstName: userData.first_name,
        lastName: userData.last_name,
        email: userData.email,
        level: userData.level_id,
        levelName: userData.level?.name || ''
      };

      setFormData(profileData);
      setOriginalData(profileData);
      
      // Set levels data (don't fail if levels loading fails)
      if (!levelsError && allLevels) {
        setLevels(allLevels);
      } else {
        console.warn('Failed to load levels for dropdown:', levelsError);
      }
    } catch (err) {
      console.error('Error loading user profile:', err);
      const errorMsg = err instanceof Error ? err.message : 'Failed to load profile data';
      setError(createNetworkError(errorMsg).message);
    } finally {
      isLoadingRef.current = false;
      setLoading(false);
    }
  }, [user, createNetworkError, createServerError]);

  // Load user profile data on mount
  useEffect(() => {
    if (user) {
      loadUserProfile();
    }
  }, [user]);


  // Handle input changes for all form fields
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'level') {
      // Update level name when level changes
      const selectedLevel = levels.find(level => level.id === value);
      setFormData(prev => ({
        ...prev,
        level: value,
        levelName: selectedLevel?.name || ''
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  }, [levels]);

  // Save profile changes
  const handleSave = useCallback(async (): Promise<void> => {
    try {
      setSaving(true);
      setError(null);

      if (!user) {
        const authError = createValidationError('User not authenticated');
        setError(authError.message);
        return;
      }

      // Basic validation
      if (!formData.firstName.trim() || !formData.lastName.trim()) {
        const validationError = createValidationError('First name and last name are required');
        setError(validationError.message);
        return;
      }

      const updateData = {
        first_name: formData.firstName.trim(),
        last_name: formData.lastName.trim(),
        level_id: formData.level
      };

      const { data: updatedUser, error: updateError } = await QuizService.updateUserProfile(user.id, updateData);

      if (updateError || !updatedUser) {
        const errorMessage = updateError || 'Failed to update profile';
        
        // Show appropriate error type
        if (updateError?.includes('network') || updateError?.includes('fetch')) {
          setError(createNetworkError(errorMessage).message);
        } else if (updateError?.includes('validation') || updateError?.includes('invalid')) {
          setError(createValidationError(errorMessage).message);
        } else {
          setError(createServerError(errorMessage).message);
        }
        return;
      }

      // Update the original data to reflect saved changes
      setOriginalData(formData);
      setIsEditing(false);

      // Show success notification
      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
        variant: "default",
      });

      console.log('Profile updated successfully');
    } catch (err) {
      console.error('Error saving profile:', err);
      const errorMsg = err instanceof Error ? err.message : 'Failed to save profile changes';
      setError(createNetworkError(errorMsg).message);
      
      // Show error toast
      toast({
        title: "Save Failed",
        description: "Failed to save your profile changes. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  }, [formData, user, createNetworkError, createServerError, createValidationError]);

  // Cancel editing and revert changes
  const handleCancel = useCallback(() => {
    setFormData(originalData);
    setIsEditing(false);
  }, [originalData]);

  // Toggle editing mode
  const toggleEditing = useCallback(() => {
    if (isEditing) {
      // If currently editing, treat as cancel
      handleCancel();
    } else {
      // If not editing, enter edit mode
      setIsEditing(true);
      setError(null); // Clear any existing errors when starting to edit
    }
  }, [isEditing, handleCancel]);

  // Retry loading profile data
  const retryLoad = useCallback(() => {
    if (user) {
      loadUserProfile();
    }
  }, [user]);

  return {
    formData,
    isEditing,
    loading,
    saving,
    error,
    levels,
    handleInputChange,
    handleSave,
    handleCancel,
    toggleEditing,
    retryLoad,
    hasChanges
  };
};