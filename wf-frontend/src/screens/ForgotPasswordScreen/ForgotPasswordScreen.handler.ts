import { useState } from 'react';

export interface UseForgotPasswordScreenReturn {
  email: string;
  setEmail: (email: string) => void;
  isSubmitted: boolean;
  handleSubmit: (e: React.FormEvent) => void;
}

export const useForgotPasswordScreen = (): UseForgotPasswordScreenReturn => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitted(true);
  };

  return {
    email,
    setEmail,
    isSubmitted,
    handleSubmit
  };
}; 