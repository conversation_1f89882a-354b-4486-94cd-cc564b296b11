import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, ArrowLeft, Mail } from 'lucide-react';
import { useForgotPasswordScreen } from './ForgotPasswordScreen.handler';
import { useLanguage } from '@/context/LanguageContext';
import Footer from '@/components/Footer';
import styles from './ForgotPasswordScreen.module.css';

const ForgotPasswordScreen: React.FC = () => {
  const { t } = useLanguage();
  const { email, setEmail, isSubmitted, handleSubmit } = useForgotPasswordScreen();

  if (isSubmitted) {
    return (
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.card}>
          <div className={styles.iconContainer}>
            <div className={styles.iconWrapperSuccess}>
              <Mail className={styles.iconSuccess} data-testid="mail-icon" />
            </div>
          </div>
          <h1 className={styles.titleSuccess}>{t('forgotPassword.checkEmail.title')}</h1>
          <p className={styles.successMessage}>
            {t('forgotPassword.checkEmail.message')} <strong className={styles.userEmail}>{email}</strong>
          </p>
          <p className={styles.successHint}>
            {t('forgotPassword.checkEmail.hint')}
          </p>
          <Link
            to="/signin"
            className={styles.backLink}
          >
            <ArrowLeft className={styles.backIcon} />
            {t('forgotPassword.backToSignIn')}
          </Link>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.card}>
        <div className={styles.header}>
          <div className={styles.iconContainer}>
            <div className={styles.iconWrapper}>
              <BookOpen className={styles.icon} data-testid="book-icon" />
            </div>
          </div>
          <h1 className={styles.title}>{t('forgotPassword.title')}</h1>
          <p className={styles.subtitle}>{t('forgotPassword.subtitle')}</p>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formField}>
            <label htmlFor="email" className={styles.label}>
              {t('forgotPassword.emailLabel')}
            </label>
            <input
              id="email"
              name="email"
              type="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className={styles.input}
              placeholder={t('forgotPassword.emailPlaceholder')}
            />
            <p className={styles.hint}>
              {t('forgotPassword.emailHint')}
            </p>
          </div>

          <button
            type="submit"
            className={styles.button}
          >
            {t('forgotPassword.sendResetLink')}
          </button>

          <div className={styles.linkContainer}>
            <Link
              to="/signin"
              className={styles.backLink}
            >
              <ArrowLeft className={styles.backIcon} />
              {t('forgotPassword.backToSignIn')}
            </Link>
          </div>
        </form>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ForgotPasswordScreen; 