import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import ForgotPasswordScreen from '../ForgotPasswordScreen';
import { useForgotPasswordScreen } from '../ForgotPasswordScreen.handler';
import { useLanguage } from '@/context/LanguageContext';

// Mock dependencies
vi.mock('../ForgotPasswordScreen.handler');
vi.mock('@/context/LanguageContext');
vi.mock('@/components/Footer', () => ({
  default: () => <div data-testid="footer">Footer</div>
}));

// Mock translation function
const mockT = vi.fn((key: string) => {
  const translations: Record<string, string> = {
    'forgotPassword.title': 'Reset Your Password',
    'forgotPassword.subtitle': 'Enter your email address and we\'ll send you a link to reset your password.',
    'forgotPassword.emailLabel': 'Email Address',
    'forgotPassword.emailPlaceholder': 'Enter your email address',
    'forgotPassword.emailHint': 'We\'ll send a password reset link to this email address.',
    'forgotPassword.sendResetLink': 'Send Reset Link',
    'forgotPassword.backToSignIn': 'Back to Sign In',
    'forgotPassword.checkEmail.title': 'Check Your Email',
    'forgotPassword.checkEmail.message': 'We\'ve sent a password reset link to',
    'forgotPassword.checkEmail.hint': 'If you don\'t see the email, check your spam folder.'
  };
  return translations[key] || key;
});

// Mock handler return values
const mockHandlerReturn = {
  email: '',
  setEmail: vi.fn(),
  isSubmitted: false,
  handleSubmit: vi.fn()
};

describe('ForgotPasswordScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    vi.mocked(useLanguage).mockReturnValue({
      t: mockT,
      language: 'en',
      setLanguage: vi.fn(),
      getRaw: vi.fn(),
      isLoading: false,
      availableLanguages: ['en', 'vi'] as const,
      getLanguageDisplayName: vi.fn((lang) => lang === 'en' ? 'English' : 'Vietnamese'),
      getLanguageFlag: vi.fn((lang) => lang === 'en' ? '🇺🇸' : '🇻🇳')
    });
    
    vi.mocked(useForgotPasswordScreen).mockReturnValue(mockHandlerReturn);
  });

  const renderForgotPasswordScreen = () => {
    return render(
      <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        <ForgotPasswordScreen />
      </BrowserRouter>
    );
  };

  describe('Form State', () => {
    it('renders the forgot password form', () => {
      renderForgotPasswordScreen();

      expect(screen.getByText('Reset Your Password')).toBeInTheDocument();
      expect(screen.getByText('Enter your email address and we\'ll send you a link to reset your password.')).toBeInTheDocument();
    });

    it('displays the logo and branding', () => {
      renderForgotPasswordScreen();

      const bookIcon = screen.getByTestId('book-icon');
      expect(bookIcon).toBeInTheDocument();
    });

    it('displays email input field with label and placeholder', () => {
      renderForgotPasswordScreen();

      expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter your email address')).toBeInTheDocument();
    });

    it('displays email hint text', () => {
      renderForgotPasswordScreen();

      expect(screen.getByText('We\'ll send a password reset link to this email address.')).toBeInTheDocument();
    });

    it('displays send reset link button', () => {
      renderForgotPasswordScreen();

      expect(screen.getByRole('button', { name: 'Send Reset Link' })).toBeInTheDocument();
    });

    it('displays back to sign in link', () => {
      renderForgotPasswordScreen();

      const backLink = screen.getByRole('link', { name: /back to sign in/i });
      expect(backLink).toBeInTheDocument();
      expect(backLink).toHaveAttribute('href', '/signin');
    });

    it('displays footer component', () => {
      renderForgotPasswordScreen();

      expect(screen.getByTestId('footer')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('calls setEmail when email input changes', () => {
      renderForgotPasswordScreen();

      const emailInput = screen.getByLabelText('Email Address');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      expect(mockHandlerReturn.setEmail).toHaveBeenCalledWith('<EMAIL>');
    });

    it('calls handleSubmit when form is submitted', () => {
      renderForgotPasswordScreen();

      const form = screen.getByRole('button', { name: 'Send Reset Link' }).closest('form');
      fireEvent.submit(form!);

      expect(mockHandlerReturn.handleSubmit).toHaveBeenCalled();
    });

    it('calls handleSubmit when send reset link button is clicked', () => {
      renderForgotPasswordScreen();

      const submitButton = screen.getByRole('button', { name: 'Send Reset Link' });
      const form = submitButton.closest('form');
      fireEvent.submit(form!);

      expect(mockHandlerReturn.handleSubmit).toHaveBeenCalled();
    });
  });

  describe('Success State', () => {
    beforeEach(() => {
      vi.mocked(useForgotPasswordScreen).mockReturnValue({
        ...mockHandlerReturn,
        email: '<EMAIL>',
        isSubmitted: true
      });
    });

    it('displays success message when email is submitted', () => {
      renderForgotPasswordScreen();

      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
      expect(screen.getByText('We\'ve sent a password reset link to')).toBeInTheDocument();
    });

    it('displays the submitted email address', () => {
      renderForgotPasswordScreen();

      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('displays success hint message', () => {
      renderForgotPasswordScreen();

      expect(screen.getByText('If you don\'t see the email, check your spam folder.')).toBeInTheDocument();
    });

    it('displays mail icon in success state', () => {
      renderForgotPasswordScreen();

      const mailIcon = screen.getByTestId('mail-icon');
      expect(mailIcon).toBeInTheDocument();
    });

    it('displays back to sign in link in success state', () => {
      renderForgotPasswordScreen();

      const backLink = screen.getByRole('link', { name: /back to sign in/i });
      expect(backLink).toBeInTheDocument();
      expect(backLink).toHaveAttribute('href', '/signin');
    });

    it('displays footer in success state', () => {
      renderForgotPasswordScreen();

      expect(screen.getByTestId('footer')).toBeInTheDocument();
    });

    it('does not display form elements in success state', () => {
      renderForgotPasswordScreen();

      expect(screen.queryByLabelText('Email Address')).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: 'Send Reset Link' })).not.toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('uses language context for translations', () => {
      renderForgotPasswordScreen();

      expect(useLanguage).toHaveBeenCalled();
      expect(mockT).toHaveBeenCalledWith('forgotPassword.title');
      expect(mockT).toHaveBeenCalledWith('forgotPassword.subtitle');
    });

    it('calls forgot password handler hook', () => {
      renderForgotPasswordScreen();

      expect(useForgotPasswordScreen).toHaveBeenCalled();
    });
  });

  describe('Form Validation', () => {
    it('email input has required attribute', () => {
      renderForgotPasswordScreen();

      const emailInput = screen.getByLabelText('Email Address');
      expect(emailInput).toHaveAttribute('required');
    });

    it('email input has email type', () => {
      renderForgotPasswordScreen();

      const emailInput = screen.getByLabelText('Email Address');
      expect(emailInput).toHaveAttribute('type', 'email');
    });
  });

  describe('Responsive Design', () => {
    it('applies responsive container classes', () => {
      renderForgotPasswordScreen();

      const container = screen.getByText('Reset Your Password').closest('.min-h-screen');
      expect(container).toHaveClass('min-h-screen', 'flex', 'flex-col');
    });

    it('applies responsive card classes', () => {
      renderForgotPasswordScreen();

      const card = screen.getByText('Reset Your Password').closest('.max-w-md');
      expect(card).toHaveClass('max-w-md', 'w-full');
    });
  });

  describe('Dark Mode Support', () => {
    it('applies dark mode classes to container', () => {
      renderForgotPasswordScreen();

      const container = screen.getByText('Reset Your Password').closest('.min-h-screen');
      expect(container).toHaveClass('dark:from-gray-900', 'dark:to-gray-800');
    });

    it('applies dark mode classes to card', () => {
      renderForgotPasswordScreen();

      const card = screen.getByText('Reset Your Password').closest('.max-w-md');
      expect(card).toHaveClass('dark:bg-gray-800');
    });

    it('applies dark mode classes to text elements', () => {
      renderForgotPasswordScreen();

      const title = screen.getByText('Reset Your Password');
      expect(title).toHaveClass('dark:text-white');
    });
  });
});
