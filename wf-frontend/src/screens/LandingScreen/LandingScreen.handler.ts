import { BookOpen, Target, TrendingUp, Users } from 'lucide-react';
import { useLanguage } from '@/context/LanguageContext';

export interface LandingScreenData {
  features: Array<{
    icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
    title: string;
    description: string;
  }>;
  stats: Array<{
    number: string;
    label: string;
  }>;
}

export const useLandingScreen = (): LandingScreenData => {
  const { t } = useLanguage();

  const features = [
    {
      icon: BookOpen,
      title: t('landing.features.comprehensive.title'),
      description: t('landing.features.comprehensive.description')
    },
    {
      icon: Target,
      title: t('landing.features.levelBased.title'),
      description: t('landing.features.levelBased.description')
    },
    {
      icon: TrendingUp,
      title: t('landing.features.trackProgress.title'),
      description: t('landing.features.trackProgress.description')
    },
    {
      icon: Users,
      title: t('landing.features.community.title'),
      description: t('landing.features.community.description')
    }
  ];

  const stats = [
    { number: '10,000+', label: 'Active Learners' },
    { number: '500+', label: 'Practice Quizzes' },
    { number: '5', label: 'Skill Categories' },
    { number: '95%', label: 'Success Rate' }
  ];

  return {
    features,
    stats
  };
};