import React from 'react';
import { Link } from 'react-router-dom';
import { BookO<PERSON>, ArrowRight, CheckCircle } from 'lucide-react';
import { useLandingScreen } from './LandingScreen.handler';
import styles from './LandingScreen.module.css';
import { useLanguage } from '@/context/LanguageContext';
import Footer from '@/components/Footer';

const LandingScreen: React.FC = () => {
  const { features, stats } = useLandingScreen();
  const { t } = useLanguage();

  return (
    <div className={styles.container}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerContainer}>
          <div className={styles.headerContent}>
            {/* Logo and App Name */}
            <div className={styles.logoContainer}>
              <div className={styles.logoIcon}>
                <BookOpen className={styles.logoIconSvg} />
              </div>
              <h1 className={styles.appName}>
                {t('landing.appName')}
              </h1>
            </div>

            {/* Navigation - Responsive Layout */}
            <div className={styles.navigation}>
              {/* Sign In Link - Hidden on very small screens, shown as text on small+ */}
              <Link
                to="/signin"
                className={styles.signInLink}
              >
                {t('landing.signIn')}
              </Link>

              {/* Start Learning Button - Responsive text */}
              <Link
                to="/signup"
                className={styles.startLearningButton}
              >
                <span className={styles.startLearningFullText}>{t('landing.startLearningFree')}</span>
                <span className={styles.startLearningShortText}>{t('landing.startLearningShort')}</span>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className={styles.heroSection}>
        <div className={styles.heroContent}>
          <h2 className={styles.heroTitle}>
            {t('landing.heroTitle')}
            <span className={styles.heroSubtitle}>{t('landing.heroSubtitle')}</span>
          </h2>
          <p className={styles.heroDescription}>
            {t('landing.heroDescription')}
          </p>

          <div className={styles.heroButtons}>
            <Link
              to="/signup"
              className={styles.buttonPrimary}
            >
              {t('landing.startLearningFree')}
              <ArrowRight className={styles.arrowIcon} />
            </Link>
            <Link
              to="/signin"
              className={styles.buttonSecondary}
            >
              {t('landing.signIn')}
            </Link>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">{stat.number}</div>
              <div className="text-gray-600 dark:text-gray-300 font-medium">{stat.label}</div>
            </div>
          ))}
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-white dark:bg-gray-800 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className={styles.sections.title}>{t('landing.features.title')}</h3>
            <p className={styles.sections.subtitle}>
              {t('landing.features.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center group">
                <div className="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-full w-16 h-16 mx-auto mb-6 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors duration-200">
                  <feature.icon className="h-8 w-8 text-blue-600 dark:text-blue-400 mx-auto" />
                </div>
                <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">{feature.title}</h4>
                <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className={styles.sections.title}>{t('landing.howItWorks.title')}</h3>
            <p className={styles.sections.subtitle}>{t('landing.howItWorks.subtitle')}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center text-xl font-bold mx-auto mb-6">
                1
              </div>
              <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">{t('landing.howItWorks.step1.title')}</h4>
              <p className="text-gray-600 dark:text-gray-300">{t('landing.howItWorks.step1.description')}</p>
            </div>

            <div className="text-center">
              <div className="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center text-xl font-bold mx-auto mb-6">
                2
              </div>
              <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">{t('landing.howItWorks.step2.title')}</h4>
              <p className="text-gray-600 dark:text-gray-300">{t('landing.howItWorks.step2.description')}</p>
            </div>

            <div className="text-center">
              <div className="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center text-xl font-bold mx-auto mb-6">
                3
              </div>
              <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">{t('landing.howItWorks.step3.title')}</h4>
              <p className="text-gray-600 dark:text-gray-300">{t('landing.howItWorks.step3.description')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-4xl font-bold text-white mb-6">
            {t('landing.cta.title')}
          </h3>
          <p className="text-xl text-blue-100 mb-8">
            {t('landing.cta.description')}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/signup"
              className="bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 transform hover:scale-105 inline-flex items-center justify-center"
            >
              {t('landing.cta.startFreeTrial')}
              <CheckCircle className="h-5 w-5 ml-2" />
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default LandingScreen; 