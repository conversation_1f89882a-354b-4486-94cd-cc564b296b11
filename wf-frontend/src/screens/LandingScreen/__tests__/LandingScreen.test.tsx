import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import LandingScreen from '../LandingScreen';
import { useLandingScreen } from '../LandingScreen.handler';
import { useLanguage } from '@/context/LanguageContext';
import { useTheme } from '@/context/ThemeContext';

// Mock dependencies
vi.mock('../LandingScreen.handler');
vi.mock('@/context/LanguageContext');
vi.mock('@/context/ThemeContext');
vi.mock('@/components/Footer', () => ({
  default: () => <div data-testid="footer">Footer Component</div>
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  BookOpen: () => <svg data-testid="book-open-icon" />,
  ArrowRight: () => <svg data-testid="arrow-right-icon" />,
  CheckCircle: () => <svg data-testid="check-circle-icon" />,
  Target: () => <svg data-testid="target-icon" />,
  TrendingUp: () => <svg data-testid="trending-up-icon" />,
  Users: () => <svg data-testid="users-icon" />
}));

// Mock data
const mockFeatures = [
  {
    icon: () => <svg data-testid="book-open-icon" />,
    title: 'Comprehensive Learning',
    description: 'Master word formation with our comprehensive curriculum'
  },
  {
    icon: () => <svg data-testid="target-icon" />,
    title: 'Level-Based Learning',
    description: 'Progress through different difficulty levels'
  },
  {
    icon: () => <svg data-testid="trending-up-icon" />,
    title: 'Track Progress',
    description: 'Monitor your learning journey'
  },
  {
    icon: () => <svg data-testid="users-icon" />,
    title: 'Community',
    description: 'Join thousands of learners'
  }
];

const mockStats = [
  { number: '10,000+', label: 'Active Learners' },
  { number: '500+', label: 'Practice Quizzes' },
  { number: '5', label: 'Skill Categories' },
  { number: '95%', label: 'Success Rate' }
];

const mockTranslations = {
  'landing.appName': 'Word Formation',
  'landing.signIn': 'Sign In',
  'landing.startLearningFree': 'Start Learning Free',
  'landing.heroTitle': 'Master English Word Formation',
  'landing.heroSubtitle': 'Build Your Vocabulary',
  'landing.heroDescription': 'Learn word formation patterns and improve your English vocabulary with interactive quizzes and exercises.',
  'landing.features.title': 'Why Choose Our Platform?',
  'landing.features.subtitle': 'Everything you need to master word formation',
  'landing.features.comprehensive.title': 'Comprehensive Learning',
  'landing.features.comprehensive.description': 'Master word formation with our comprehensive curriculum',
  'landing.features.levelBased.title': 'Level-Based Learning',
  'landing.features.levelBased.description': 'Progress through different difficulty levels',
  'landing.features.trackProgress.title': 'Track Progress',
  'landing.features.trackProgress.description': 'Monitor your learning journey',
  'landing.features.community.title': 'Community',
  'landing.features.community.description': 'Join thousands of learners',
  'landing.howItWorks.title': 'How It Works',
  'landing.howItWorks.subtitle': 'Get started in three simple steps',
  'landing.howItWorks.step1.title': 'Sign Up',
  'landing.howItWorks.step1.description': 'Create your free account',
  'landing.howItWorks.step2.title': 'Choose Level',
  'landing.howItWorks.step2.description': 'Select your difficulty level',
  'landing.howItWorks.step3.title': 'Start Learning',
  'landing.howItWorks.step3.description': 'Begin your learning journey',
  'landing.cta.title': 'Ready to Start Learning?',
  'landing.cta.description': 'Join thousands of learners improving their English skills',
  'landing.cta.startFreeTrial': 'Start Free Trial'
};

const mockHandler = {
  features: mockFeatures,
  stats: mockStats
};

const mockLanguageContext = {
  t: vi.fn((key: string) => mockTranslations[key as keyof typeof mockTranslations] || key),
  language: 'en' as const,
  setLanguage: vi.fn(),
  getRaw: vi.fn(),
  isLoading: false,
  availableLanguages: ['en', 'vi', 'fr'] as ('en' | 'vi' | 'fr')[],
  getLanguageDisplayName: vi.fn((_lang: string) => 'English'),
  getLanguageFlag: vi.fn((_lang: string) => '🇺🇸')
};

const mockThemeContext = {
  theme: 'light' as const,
  setTheme: vi.fn()
};

const renderLandingScreen = () => {
  return render(
    <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
      <LandingScreen />
    </BrowserRouter>
  );
};

describe('LandingScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset the mock function
    mockLanguageContext.t.mockImplementation((key: string) => mockTranslations[key as keyof typeof mockTranslations] || key);
    vi.mocked(useLandingScreen).mockReturnValue(mockHandler);
    vi.mocked(useLanguage).mockReturnValue(mockLanguageContext);
    vi.mocked(useTheme).mockReturnValue(mockThemeContext);
  });

  describe('Header Section', () => {
    it('displays app logo and name', () => {
      renderLandingScreen();

      const bookIcons = screen.getAllByTestId('book-open-icon');
      expect(bookIcons.length).toBeGreaterThan(0);
      expect(screen.getByText('Word Formation')).toBeInTheDocument();
    });

    it('displays navigation links', () => {
      renderLandingScreen();

      const signInLinks = screen.getAllByText('Sign In');
      const startLearningLinks = screen.getAllByText('Start Learning Free');
      
      expect(signInLinks.length).toBeGreaterThan(0);
      expect(startLearningLinks.length).toBeGreaterThan(0);
    });

    it('has correct link destinations', () => {
      renderLandingScreen();

      const signInLink = screen.getAllByRole('link', { name: /sign in/i })[0];
      const signUpLink = screen.getAllByRole('link', { name: /start learning free/i })[0];

      expect(signInLink).toHaveAttribute('href', '/signin');
      expect(signUpLink).toHaveAttribute('href', '/signup');
    });
  });

  describe('Hero Section', () => {
    it('displays hero title and subtitle', () => {
      renderLandingScreen();

      expect(screen.getByText('Master English Word Formation')).toBeInTheDocument();
      expect(screen.getByText('Build Your Vocabulary')).toBeInTheDocument();
    });

    it('displays hero description', () => {
      renderLandingScreen();

      expect(screen.getByText('Learn word formation patterns and improve your English vocabulary with interactive quizzes and exercises.')).toBeInTheDocument();
    });

    it('displays call-to-action buttons', () => {
      renderLandingScreen();

      const startLearningButtons = screen.getAllByText('Start Learning Free');
      const signInButtons = screen.getAllByText('Sign In');
      
      expect(startLearningButtons.length).toBeGreaterThan(0);
      expect(signInButtons.length).toBeGreaterThan(0);
    });

    it('displays arrow right icon in primary CTA button', () => {
      renderLandingScreen();

      expect(screen.getByTestId('arrow-right-icon')).toBeInTheDocument();
    });
  });

  describe('Stats Section', () => {
    it('displays all statistics', () => {
      renderLandingScreen();

      mockStats.forEach(stat => {
        expect(screen.getByText(stat.number)).toBeInTheDocument();
        expect(screen.getByText(stat.label)).toBeInTheDocument();
      });
    });

    it('renders stats in grid layout', () => {
      renderLandingScreen();

      const statsContainer = screen.getByText('10,000+').closest('.grid');
      expect(statsContainer).toHaveClass('grid-cols-2', 'md:grid-cols-4');
    });
  });

  describe('Features Section', () => {
    it('displays features section title and subtitle', () => {
      renderLandingScreen();

      expect(screen.getByText('Why Choose Our Platform?')).toBeInTheDocument();
      expect(screen.getByText('Everything you need to master word formation')).toBeInTheDocument();
    });

    it('displays all features', () => {
      renderLandingScreen();

      mockFeatures.forEach(feature => {
        expect(screen.getByText(feature.title)).toBeInTheDocument();
        expect(screen.getByText(feature.description)).toBeInTheDocument();
      });
    });

    it('displays feature icons', () => {
      renderLandingScreen();

      const bookIcons = screen.getAllByTestId('book-open-icon');
      expect(bookIcons.length).toBeGreaterThan(0);
      expect(screen.getByTestId('target-icon')).toBeInTheDocument();
      expect(screen.getByTestId('trending-up-icon')).toBeInTheDocument();
      expect(screen.getByTestId('users-icon')).toBeInTheDocument();
    });

    it('renders features in responsive grid', () => {
      renderLandingScreen();

      const featuresContainer = screen.getByText('Comprehensive Learning').closest('.grid');
      expect(featuresContainer).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-4');
    });
  });

  describe('How It Works Section', () => {
    it('displays section title and subtitle', () => {
      renderLandingScreen();

      expect(screen.getByText('How It Works')).toBeInTheDocument();
      expect(screen.getByText('Get started in three simple steps')).toBeInTheDocument();
    });

    it('displays all three steps', () => {
      renderLandingScreen();

      expect(screen.getByText('Sign Up')).toBeInTheDocument();
      expect(screen.getByText('Create your free account')).toBeInTheDocument();
      
      expect(screen.getByText('Choose Level')).toBeInTheDocument();
      expect(screen.getByText('Select your difficulty level')).toBeInTheDocument();
      
      expect(screen.getByText('Start Learning')).toBeInTheDocument();
      expect(screen.getByText('Begin your learning journey')).toBeInTheDocument();
    });

    it('displays step numbers', () => {
      renderLandingScreen();

      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('renders steps in responsive grid', () => {
      renderLandingScreen();

      const stepsContainer = screen.getByText('Sign Up').closest('.grid');
      expect(stepsContainer).toHaveClass('grid-cols-1', 'md:grid-cols-3');
    });
  });

  describe('CTA Section', () => {
    it('displays CTA title and description', () => {
      renderLandingScreen();

      expect(screen.getByText('Ready to Start Learning?')).toBeInTheDocument();
      expect(screen.getByText('Join thousands of learners improving their English skills')).toBeInTheDocument();
    });

    it('displays start free trial button', () => {
      renderLandingScreen();

      expect(screen.getByText('Start Free Trial')).toBeInTheDocument();
    });

    it('displays check circle icon in CTA button', () => {
      renderLandingScreen();

      expect(screen.getByTestId('check-circle-icon')).toBeInTheDocument();
    });

    it('has correct link destination for CTA button', () => {
      renderLandingScreen();

      const ctaButton = screen.getByRole('link', { name: /start free trial/i });
      expect(ctaButton).toHaveAttribute('href', '/signup');
    });
  });

  describe('Footer Integration', () => {
    it('renders footer component', () => {
      renderLandingScreen();

      expect(screen.getByTestId('footer')).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('uses language context for translations', () => {
      renderLandingScreen();

      expect(mockLanguageContext.t).toHaveBeenCalledWith('landing.appName');
      expect(mockLanguageContext.t).toHaveBeenCalledWith('landing.heroTitle');
      expect(mockLanguageContext.t).toHaveBeenCalledWith('landing.features.title');
    });

    it('calls landing screen handler', () => {
      renderLandingScreen();

      expect(useLandingScreen).toHaveBeenCalled();
    });

    it('renders with proper responsive layout classes', () => {
      renderLandingScreen();

      const mainContainer = screen.getByText('Master English Word Formation').closest('section');
      expect(mainContainer).toHaveClass('max-w-7xl', 'mx-auto', 'px-4', 'sm:px-6', 'lg:px-8');
    });
  });

  describe('Dark Mode Support', () => {
    it('applies dark mode classes to main container', () => {
      renderLandingScreen();

      // Find the root container with gradient background
      const rootContainer = screen.getByText('Master English Word Formation').closest('.min-h-screen');
      expect(rootContainer).toHaveClass('dark:from-gray-900', 'dark:to-gray-800');
    });

    it('applies dark mode classes to header', () => {
      renderLandingScreen();

      const header = screen.getByRole('banner');
      expect(header).toHaveClass('dark:bg-gray-800/90');
    });

    it('applies dark mode classes to text elements', () => {
      renderLandingScreen();

      const appName = screen.getByText('Word Formation');
      expect(appName).toHaveClass('dark:text-white');
    });
  });
});
