import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import LandingScreen from '../LandingScreen';
import { LanguageProvider } from '@/context/LanguageContext';
import { ThemeProvider } from '@/context/ThemeContext';

// Mock the handler
vi.mock('../LandingScreen.handler', () => ({
  useLandingScreen: () => ({
    features: [
      {
        icon: () => <div>Icon</div>,
        title: 'Test Feature',
        description: 'Test Description'
      }
    ],
    stats: [
      { value: '1000+', label: 'Students' }
    ]
  })
}));

const renderLandingScreen = () => {
  return render(
    <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
      <ThemeProvider>
        <LanguageProvider>
          <LandingScreen />
        </LanguageProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('LandingScreen Responsive Design', () => {
  beforeEach(() => {
    // Reset viewport to default
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
  });

  describe('Header Responsive Behavior', () => {
    it('should show full text on larger screens', () => {
      renderLandingScreen();
      
      // Check that the header contains responsive classes
      const header = document.querySelector('header');
      expect(header).toBeInTheDocument();
      
      // Check for responsive padding classes
      const headerContainer = header?.querySelector('.max-w-7xl');
      expect(headerContainer).toHaveClass('px-3', 'sm:px-6', 'lg:px-8');
    });

    it('should have responsive logo and app name sizing', () => {
      renderLandingScreen();

      // Check logo has responsive sizing
      const logo = document.querySelector('.bg-blue-600');
      expect(logo).toHaveClass('p-1.5', 'sm:p-2');

      // Check app name has responsive text sizing (using translation key)
      const appName = screen.getByText('landing.appName');
      expect(appName).toHaveClass('text-lg', 'sm:text-xl');
    });

    it('should have responsive button layout', () => {
      renderLandingScreen();
      
      // Check for responsive spacing
      const navContainer = document.querySelector('.flex.items-center.space-x-2');
      expect(navContainer).toHaveClass('space-x-2', 'sm:space-x-4');
    });
  });

  describe('Footer Responsive Behavior', () => {
    it('should have mobile and desktop layouts', () => {
      renderLandingScreen();
      
      // Check for mobile layout (hidden on sm+)
      const mobileLayout = document.querySelector('.block.sm\\:hidden');
      expect(mobileLayout).toBeInTheDocument();
      
      // Check for desktop layout (hidden on mobile)
      const desktopLayout = document.querySelector('.hidden.sm\\:flex');
      expect(desktopLayout).toBeInTheDocument();
    });

    it('should have responsive footer padding', () => {
      renderLandingScreen();
      
      const footer = document.querySelector('footer');
      expect(footer).toHaveClass('py-3', 'sm:py-4');
      
      const footerContainer = footer?.querySelector('.max-w-7xl');
      expect(footerContainer).toHaveClass('px-3', 'sm:px-6', 'lg:px-8');
    });

    it('should have responsive text sizes in footer', () => {
      renderLandingScreen();
      
      // Check mobile copyright text size
      const mobileCopyright = document.querySelector('.text-xs.text-center');
      expect(mobileCopyright).toBeInTheDocument();
      
      // Check mobile links text size
      const mobileLinks = document.querySelector('.text-xs');
      expect(mobileLinks).toBeInTheDocument();
    });
  });

  describe('Translation Keys', () => {
    it('should use correct translation keys for responsive elements', () => {
      renderLandingScreen();

      // These elements should be present and use translation keys
      expect(screen.getByText('landing.appName')).toBeInTheDocument();
      expect(screen.getAllByText('landing.signIn')).toHaveLength(2); // Header and hero section
      expect(screen.getAllByText('landing.startLearningFree')).toHaveLength(2); // Header and hero section
      expect(screen.getByText('landing.startLearningShort')).toBeInTheDocument();
    });
  });
});
