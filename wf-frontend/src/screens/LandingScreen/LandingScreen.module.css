/**
 * LandingScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 337 lines | AFTER: ~220 lines | REDUCTION: ~35%
 * 
 * Uses shared modules for:
 * - Container layouts (fullHeightContainer, pageContainer)
 * - Card components (cardBase)
 * - Button components (buttonPrimary, buttonSecondary)
 * - Typography patterns (pageTitle, subtitle, description)
 */

/* Container using shared full-height layout */
.container {
  composes: fullHeightContainer from '@/styles/shared/layouts/containers.module.css';
  background: linear-gradient(to bottom right, theme('colors.blue.50'), theme('colors.indigo.100'));
}

:global(.dark) .container {
  background: linear-gradient(to bottom right, theme('colors.gray.900'), theme('colors.gray.800'));
}

/* Header */
.header {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  box-shadow: theme('boxShadow.sm');
}

:global(.dark) .header {
  background-color: rgba(31, 41, 55, 0.9);
}

/* Header container using shared layout */
.headerContainer {
  composes: pageContainer from '@/styles/shared/layouts/containers.module.css';
  max-width: theme('maxWidth.7xl');
  padding: 0 theme('spacing.3');
}

@media (min-width: theme('screens.sm')) {
  .headerContainer {
    padding: 0 theme('spacing.6');
  }
}

@media (min-width: theme('screens.lg')) {
  .headerContainer {
    padding: 0 theme('spacing.8');
  }
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: theme('spacing.16');
}

.logoContainer {
  display: flex;
  align-items: center;
  min-width: 0;
  flex-shrink: 1;
}

.logoIcon {
  background-color: theme('colors.blue.600');
  padding: theme('spacing.1.5');
  border-radius: theme('borderRadius.lg');
  margin-right: theme('spacing.2');
  flex-shrink: 0;
}

@media (min-width: theme('screens.sm')) {
  .logoIcon {
    padding: theme('spacing.2');
    margin-right: theme('spacing.3');
  }
}

.logoIconSvg {
  height: theme('spacing.5');
  width: theme('spacing.5');
  color: white;
}

@media (min-width: theme('screens.sm')) {
  .logoIconSvg {
    height: theme('spacing.6');
    width: theme('spacing.6');
  }
}

/* App name using shared typography */
.appName {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.lg');
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

@media (min-width: theme('screens.sm')) {
  .appName {
    font-size: theme('fontSize.xl');
  }
}

.navigation {
  display: flex;
  align-items: center;
  gap: theme('spacing.2');
  flex-shrink: 0;
}

@media (min-width: theme('screens.sm')) {
  .navigation {
    gap: theme('spacing.4');
  }
}

/* Sign in link using shared typography */
.signInLink {
  composes: linkText from '@/styles/shared/components/typography.module.css';
  display: none;
  font-size: theme('fontSize.sm');
}

@media (min-width: 375px) {
  .signInLink {
    display: inline-block;
  }
}

@media (min-width: theme('screens.sm')) {
  .signInLink {
    font-size: theme('fontSize.base');
  }
}

/* Start learning button using shared components */
.startLearningButton {
  composes: buttonPrimary from '@/styles/shared/components/buttons.module.css';
  padding: theme('spacing.1.5') theme('spacing.2');
  font-size: theme('fontSize.xs');
  white-space: nowrap;
}

@media (min-width: theme('screens.sm')) {
  .startLearningButton {
    padding: theme('spacing.2') theme('spacing.4');
    font-size: theme('fontSize.sm');
  }
}

@media (min-width: theme('screens.lg')) {
  .startLearningButton {
    font-size: theme('fontSize.base');
  }
}

.startLearningButton:hover {
  background-color: theme('colors.blue.700');
}

.startLearningFullText {
  display: none;
}

@media (min-width: theme('screens.sm')) {
  .startLearningFullText {
    display: inline;
  }
}

.startLearningShortText {
  display: inline;
}

@media (min-width: theme('screens.sm')) {
  .startLearningShortText {
    display: none;
  }
}

/* Hero Section */
.heroSection {
  max-width: theme('maxWidth.7xl');
  margin: 0 auto;
  padding: theme('spacing.20') theme('spacing.4');
}

@media (min-width: theme('screens.sm')) {
  .heroSection {
    padding: theme('spacing.20') theme('spacing.6');
  }
}

@media (min-width: theme('screens.lg')) {
  .heroSection {
    padding: theme('spacing.20') theme('spacing.8');
  }
}

.heroContent {
  text-align: center;
}

/* Hero title using shared typography */
.heroTitle {
  composes: pageTitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.5xl');
  margin-bottom: theme('spacing.6');
}

/* Hero description using shared typography */
.heroDescription {
  composes: subtitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.xl');
  margin-bottom: theme('spacing.8');
  max-width: theme('maxWidth.3xl');
  margin-left: auto;
  margin-right: auto;
}

/* Primary button using shared components */
.buttonPrimary {
  composes: buttonPrimary from '@/styles/shared/components/buttons.module.css';
  padding: theme('spacing.4') theme('spacing.8');
  font-size: theme('fontSize.lg');
  transform: scale(1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.buttonPrimary:hover {
  transform: scale(1.05);
}

/* Secondary button using shared components */
.buttonSecondary {
  composes: buttonSecondary from '@/styles/shared/components/buttons.module.css';
  padding: theme('spacing.4') theme('spacing.8');
  font-size: theme('fontSize.lg');
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2px solid theme('colors.blue.600');
}

.buttonSecondary:hover {
  background-color: theme('colors.blue.50');
}

:global(.dark) .buttonSecondary {
  background-color: theme('colors.gray.800');
  color: theme('colors.blue.400');
  border-color: theme('colors.blue.400');
}

:global(.dark) .buttonSecondary:hover {
  background-color: theme('colors.gray.700');
}

.sectionTitle {
  font-size: theme('fontSize.4xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.4');
}

:global(.dark) .sectionTitle {
  color: white;
}

.sectionSubtitle {
  font-size: theme('fontSize.xl');
  color: theme('colors.gray.600');
  max-width: theme('maxWidth.2xl');
  margin-left: auto;
  margin-right: auto;
}

:global(.dark) .sectionSubtitle {
  color: theme('colors.gray.300');
}

.heroSubtitle {
  color: theme('colors.blue.600');
  display: block;
}

.heroButtons {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
  justify-content: center;
}

@media (min-width: theme('screens.sm')) {
  .heroButtons {
    flex-direction: row;
  }
}

.arrowIcon {
  height: theme('spacing.5');
  width: theme('spacing.5');
  margin-left: theme('spacing.2');
}