import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import QuizModeSelectionScreen from '../QuizModeSelectionScreen';
import { useQuizModeSelectionScreenHandler } from '../QuizModeSelectionScreen.handler';
import { useLanguage } from '@/context/LanguageContext';
import type { Quiz, QuizMode } from '@/types';

// Mock dependencies
vi.mock('../QuizModeSelectionScreen.handler');
vi.mock('@/context/LanguageContext');
vi.mock('@/components/Layout/ResponsiveContainer', () => ({
  PageWrapper: ({ children }: { children: React.ReactNode }) => <div data-testid="page-wrapper">{children}</div>,
  ResponsiveCard: ({ children }: { children: React.ReactNode }) => <div data-testid="responsive-card">{children}</div>
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  Clock: () => <svg data-testid="clock-icon" />,
  BookOpen: () => <svg data-testid="book-open-icon" />,
  Star: ({ className }: { className?: string }) => <svg data-testid="star-icon" className={className} />,
  PlayCircle: () => <svg data-testid="play-circle-icon" />,
  CheckCircle: () => <svg data-testid="check-circle-icon" />
}));

// Mock quiz data
const mockQuiz: Quiz = {
  id: 'quiz-1',
  key: 'word-formation-1',
  title: 'Word Formation - Basic Level',
  description: 'Learn basic word formation patterns and improve your vocabulary skills.',
  instructions: 'Choose the correct word form for each sentence.',
  category_id: 'cat-1',
  level_id: 'level-1',
  quiz_type_id: 'type-1',
  difficulty_level: 3,
  total_questions: 15,
  time_limit_minutes: 20,
  quiz_config: {},
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  level: {
    id: 'level-1',
    key: 'B1',
    name: 'Intermediate',
    description: 'Intermediate level',
    sort_order: 3,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z'
  }
};

// Mock modes data
const mockModes = [
  {
    id: 'practice' as QuizMode,
    name: 'Practice Mode',
    icon: '🎯',
    description: 'Learn at your own pace with instant feedback',
    features: ['Instant feedback', 'Unlimited attempts', 'Detailed explanations'],
    color: 'bg-blue-50 dark:bg-gray-800 border-blue-200',
    selectedColor: 'bg-blue-100 dark:bg-gray-700 border-blue-500',
    buttonColor: 'bg-blue-600 hover:bg-blue-700',
    recommended: true
  },
  {
    id: 'test' as QuizMode,
    name: 'Test Mode',
    icon: '📝',
    description: 'Challenge yourself with timed assessment',
    features: ['Timed assessment', 'Final score only', 'Real exam conditions'],
    color: 'bg-purple-50 dark:bg-gray-800 border-purple-200',
    selectedColor: 'bg-purple-100 dark:bg-gray-700 border-purple-500',
    buttonColor: 'bg-purple-600 hover:bg-purple-700',
    recommended: false
  }
];

// Mock handler return value
const mockHandler = {
  quiz: mockQuiz,
  selectedMode: null as QuizMode | null,
  modes: mockModes,
  isLoading: false,
  error: null,
  handleModeSelect: vi.fn(),
  handleStartQuiz: vi.fn(),
  handleBack: vi.fn(),
  getSelectedModeConfig: vi.fn(),
  canStartQuiz: vi.fn(() => false)
};

// Mock language context
const mockLanguageContext = {
  t: vi.fn((key: string) => {
    const translations: Record<string, string> = {
      'common.loading': 'Loading...',
      'common.error': 'Error',
      'common.back': 'Back',
      'modeSelection.easy': 'Easy',
      'modeSelection.medium': 'Medium',
      'modeSelection.hard': 'Hard',
      'modeSelection.questions': 'Questions',
      'modeSelection.duration': 'Duration',
      'modeSelection.level': 'Level',
      'modeSelection.chooseMode': 'Choose Your Mode',
      'modeSelection.recommended': 'Recommended',
      'modeSelection.startQuiz': 'Start Quiz',
      'modeSelection.selectMode': 'Select a mode first'
    };
    return translations[key] || key;
  }),
  getRaw: vi.fn(),
  language: 'en',
  setLanguage: vi.fn()
};

describe('QuizModeSelectionScreen', () => {
  let queryClient: QueryClient;

  const renderQuizModeSelectionScreen = (props = {}) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
          <QuizModeSelectionScreen {...props} />
        </BrowserRouter>
      </QueryClientProvider>
    );
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });
    
    vi.clearAllMocks();
    vi.mocked(useLanguage).mockReturnValue(mockLanguageContext);
    vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue(mockHandler);
  });

  describe('Loading State', () => {
    it('displays loading spinner when loading is true', () => {
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        isLoading: true
      });

      renderQuizModeSelectionScreen();

      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(screen.getByTestId('page-wrapper')).toBeInTheDocument();
      // Check for loading spinner by CSS class
      const spinner = document.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
    });
  });

  describe('Error State', () => {
    it('displays error message when error exists', () => {
      const mockError = new Error('Failed to load quiz');
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        error: mockError
      });

      renderQuizModeSelectionScreen();

      expect(screen.getByText('Error')).toBeInTheDocument();
      expect(screen.getByText('Failed to load quiz')).toBeInTheDocument();
      expect(screen.getByText('Back')).toBeInTheDocument();
    });

    it('handles back button click in error state', () => {
      const mockError = new Error('Failed to load quiz');
      const mockHandleBack = vi.fn();
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        error: mockError,
        handleBack: mockHandleBack
      });

      renderQuizModeSelectionScreen();

      const backButton = screen.getByText('Back');
      fireEvent.click(backButton);

      expect(mockHandleBack).toHaveBeenCalledTimes(1);
    });
  });

  describe('Quiz Header Section', () => {
    it('displays quiz title and description', () => {
      renderQuizModeSelectionScreen();

      expect(screen.getByText('Word Formation - Basic Level')).toBeInTheDocument();
      expect(screen.getByText('Learn basic word formation patterns and improve your vocabulary skills.')).toBeInTheDocument();
    });

    it('displays quiz level badge', () => {
      renderQuizModeSelectionScreen();

      expect(screen.getByText('B1')).toBeInTheDocument();
    });

    it('displays fallback content when quiz data is missing', () => {
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        quiz: null
      });

      renderQuizModeSelectionScreen();

      expect(screen.getByText('Word Formation Quiz')).toBeInTheDocument();
      expect(screen.getByText('Master word formation patterns and improve your vocabulary skills with practical examples and exercises.')).toBeInTheDocument();
    });
  });

  describe('Quiz Statistics Grid', () => {
    it('displays quiz statistics correctly', () => {
      renderQuizModeSelectionScreen();

      expect(screen.getByText('15')).toBeInTheDocument(); // total_questions
      expect(screen.getByText('20 min')).toBeInTheDocument(); // time_limit_minutes
      expect(screen.getByText('Questions')).toBeInTheDocument();
      expect(screen.getByText('Duration')).toBeInTheDocument();
    });

    it('displays difficulty level and stars', () => {
      renderQuizModeSelectionScreen();

      expect(screen.getByText('Level 3')).toBeInTheDocument();
      expect(screen.getByText('Medium')).toBeInTheDocument();
      
      // Check for 5 star icons (difficulty stars)
      const starIcons = screen.getAllByTestId('star-icon');
      expect(starIcons).toHaveLength(5);
    });

    it('displays fallback values when quiz data is incomplete', () => {
      const incompleteQuiz = { ...mockQuiz, total_questions: undefined, time_limit_minutes: undefined, difficulty_level: undefined };
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        quiz: incompleteQuiz as Quiz
      });

      renderQuizModeSelectionScreen();

      // Should have multiple "..." fallback values for missing data
      const fallbackElements = screen.getAllByText('...');
      expect(fallbackElements.length).toBeGreaterThan(0);
    });
  });

  describe('Difficulty Label Function', () => {
    it('returns correct difficulty labels for different levels', () => {
      // Test easy levels (1-2)
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        quiz: { ...mockQuiz, difficulty_level: 1 }
      });
      renderQuizModeSelectionScreen();
      expect(screen.getByText('Easy')).toBeInTheDocument();

      // Test medium level (3)
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        quiz: { ...mockQuiz, difficulty_level: 3 }
      });
      renderQuizModeSelectionScreen();
      expect(screen.getByText('Medium')).toBeInTheDocument();

      // Test hard levels (4+)
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        quiz: { ...mockQuiz, difficulty_level: 5 }
      });
      renderQuizModeSelectionScreen();
      expect(screen.getByText('Hard')).toBeInTheDocument();
    });
  });

  describe('Mode Selection Section', () => {
    it('displays mode selection header', () => {
      renderQuizModeSelectionScreen();

      expect(screen.getByText('Choose Your Mode')).toBeInTheDocument();
    });

    it('displays both practice and test modes', () => {
      renderQuizModeSelectionScreen();

      expect(screen.getByText('Practice Mode')).toBeInTheDocument();
      expect(screen.getByText('Test Mode')).toBeInTheDocument();
      expect(screen.getByText('🎯')).toBeInTheDocument();
      expect(screen.getByText('📝')).toBeInTheDocument();
    });

    it('displays mode descriptions and features', () => {
      renderQuizModeSelectionScreen();

      expect(screen.getByText('Learn at your own pace with instant feedback')).toBeInTheDocument();
      expect(screen.getByText('Challenge yourself with timed assessment')).toBeInTheDocument();
      expect(screen.getByText('Instant feedback')).toBeInTheDocument();
      expect(screen.getByText('Unlimited attempts')).toBeInTheDocument();
      expect(screen.getByText('Detailed explanations')).toBeInTheDocument();
      expect(screen.getByText('Timed assessment')).toBeInTheDocument();
      expect(screen.getByText('Final score only')).toBeInTheDocument();
      expect(screen.getByText('Real exam conditions')).toBeInTheDocument();
    });

    it('displays recommended badge for practice mode', () => {
      renderQuizModeSelectionScreen();

      expect(screen.getByText('Recommended')).toBeInTheDocument();
    });

    it('handles mode selection clicks', () => {
      const mockHandleModeSelect = vi.fn();
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        handleModeSelect: mockHandleModeSelect
      });

      renderQuizModeSelectionScreen();

      const practiceMode = screen.getByText('Practice Mode').closest('div');
      fireEvent.click(practiceMode!);

      expect(mockHandleModeSelect).toHaveBeenCalledWith(mockModes[0]);
    });

    it('shows selected mode with check icon', () => {
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        selectedMode: 'practice'
      });

      renderQuizModeSelectionScreen();

      const checkIcons = screen.getAllByTestId('check-circle-icon');
      // Should have check icons for features plus one for selected mode
      expect(checkIcons.length).toBeGreaterThan(6); // 6 features + 1 selected mode indicator
    });

    it('applies correct styling for selected mode', () => {
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        selectedMode: 'practice'
      });

      renderQuizModeSelectionScreen();

      // Find the mode card container (the clickable div with border-2 class)
      const practiceMode = screen.getByText('Practice Mode').closest('.cursor-pointer');
      expect(practiceMode).toHaveClass('bg-blue-100');
    });

    it('applies correct styling for unselected mode', () => {
      renderQuizModeSelectionScreen();

      // Find the mode card container (the clickable div with border-2 class)
      const practiceMode = screen.getByText('Practice Mode').closest('.cursor-pointer');
      expect(practiceMode).toHaveClass('bg-blue-50');
    });
  });

  describe('Start Quiz Button', () => {
    it('displays disabled start button when no mode selected', () => {
      renderQuizModeSelectionScreen();

      const startButton = screen.getByText('Select a mode first');
      expect(startButton).toBeInTheDocument();
      expect(startButton).toBeDisabled();
    });

    it('displays enabled start button when mode is selected', () => {
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        selectedMode: 'practice',
        canStartQuiz: vi.fn(() => true)
      });

      renderQuizModeSelectionScreen();

      const startButton = screen.getByText('Start Quiz');
      expect(startButton).toBeInTheDocument();
      expect(startButton).not.toBeDisabled();
    });

    it('handles start quiz button click', () => {
      const mockHandleStartQuiz = vi.fn();
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        selectedMode: 'practice',
        canStartQuiz: vi.fn(() => true),
        handleStartQuiz: mockHandleStartQuiz
      });

      renderQuizModeSelectionScreen();

      const startButton = screen.getByText('Start Quiz');
      fireEvent.click(startButton);

      expect(mockHandleStartQuiz).toHaveBeenCalledTimes(1);
    });

    it('displays play circle icon in start button', () => {
      renderQuizModeSelectionScreen();

      expect(screen.getByTestId('play-circle-icon')).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('renders within PageWrapper component', () => {
      renderQuizModeSelectionScreen();

      expect(screen.getByTestId('page-wrapper')).toBeInTheDocument();
    });

    it('uses language context for translations', () => {
      renderQuizModeSelectionScreen();

      expect(mockLanguageContext.t).toHaveBeenCalledWith('modeSelection.chooseMode');
      expect(mockLanguageContext.t).toHaveBeenCalledWith('modeSelection.questions');
      expect(mockLanguageContext.t).toHaveBeenCalledWith('modeSelection.duration');
      expect(mockLanguageContext.t).toHaveBeenCalledWith('modeSelection.level');
    });

    it('calls handler with correct props', () => {
      const props = { quiz: mockQuiz, onModeSelect: vi.fn(), onBack: vi.fn() };
      renderQuizModeSelectionScreen(props);

      expect(useQuizModeSelectionScreenHandler).toHaveBeenCalledWith(props);
    });
  });

  describe('Props vs Route Parameter Handling', () => {
    it('uses provided quiz prop when available', () => {
      const customQuiz = { ...mockQuiz, title: 'Custom Quiz Title' };
      vi.mocked(useQuizModeSelectionScreenHandler).mockReturnValue({
        ...mockHandler,
        quiz: customQuiz
      });

      renderQuizModeSelectionScreen({ quiz: customQuiz });

      expect(screen.getByText('Custom Quiz Title')).toBeInTheDocument();
    });

    it('handles component without props (route usage)', () => {
      renderQuizModeSelectionScreen();

      // Should render without errors and show default content
      expect(screen.getByText('Word Formation - Basic Level')).toBeInTheDocument();
    });
  });
});
