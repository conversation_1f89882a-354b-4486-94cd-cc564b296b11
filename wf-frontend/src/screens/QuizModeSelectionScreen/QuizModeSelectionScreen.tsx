import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, Star, PlayCircle, CheckCircle } from 'lucide-react';
import { PageWrapper } from '@/components/Layout/ResponsiveContainer';
import { useQuizModeSelectionScreenHandler } from './QuizModeSelectionScreen.handler';
import { useLanguage } from '@/context/LanguageContext';
import type { QuizModeSelectionProps } from '@/types';

/**
 * QuizModeSelectionScreen component for choosing between Practice and Test modes.
 * Pure UI component - all business logic handled by the handler
 * Updated with new pre-quiz UI design and enhanced responsive layout
 * Can be used as a route component (no props) or as a child component (with props)
 */
const QuizModeSelectionScreen: React.FC<QuizModeSelectionProps> = (props = {}) => {
  const { quiz } = props;
  const { t } = useLanguage();
  const {
    selectedMode,
    modes,
    handleModeSelect,
    handleStartQuiz,
    handleBack,
    canStartQuiz,
    quiz: fetchedQuiz,
    isLoading,
    error
  } = useQuizModeSelectionScreenHandler(props);

  const activeQuiz = quiz || fetchedQuiz;

  // Loading state
  if (isLoading) {
    return (
      <PageWrapper>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 sm:p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-300">{t('common.loading')}</p>
          </div>
        </div>
      </PageWrapper>
    );
  }

  // Error state
  if (error) {
    return (
      <PageWrapper>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 sm:p-8 text-center">
            <div className="bg-red-100 dark:bg-red-900/20 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <span className="text-red-600 text-2xl">⚠️</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('common.error')}</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">{error.message}</p>
            <button
              onClick={handleBack}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
            >
              {t('common.back')}
            </button>
          </div>
        </div>
      </PageWrapper>
    );
  }

  const getDifficultyLabel = (level: number): string => {
    if (level <= 2) return t('modeSelection.easy');
    if (level === 3) return t('modeSelection.medium');
    if (level >= 4) return t('modeSelection.hard');
    return t('modeSelection.easy'); // fallback
  };

  const getDifficultyStars = (difficulty: number = 0) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < difficulty ? 'text-yellow-400 fill-current' : 'text-gray-300 dark:text-gray-600'
        }`}
      />
    ));
  };

  return (
    <PageWrapper>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 sm:p-8">
          {/* Quiz Header */}
          <div className="text-center mb-8">
            <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">
              {activeQuiz?.level?.key || ''}
            </span>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mt-4 mb-2">
              {activeQuiz?.title || 'Word Formation Quiz'}
            </h1>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-sm sm:text-base">
              {activeQuiz?.description || 'Master word formation patterns and improve your vocabulary skills with practical examples and exercises.'}
            </p>
          </div>

          {/* Quiz Statistics Grid - Enhanced for mobile */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-8">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 sm:p-6 rounded-lg text-center">
              <BookOpen className="h-6 w-6 sm:h-8 sm:w-8 text-gray-600 dark:text-gray-300 mx-auto mb-2" />
              <div className="font-semibold text-gray-900 dark:text-white text-lg sm:text-xl">{activeQuiz?.total_questions ?? '...'}</div>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">{t('modeSelection.questions')}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 sm:p-6 rounded-lg text-center">
              <Clock className="h-6 w-6 sm:h-8 sm:w-8 text-gray-600 dark:text-gray-300 mx-auto mb-2" />
              <div className="font-semibold text-gray-900 dark:text-white text-lg sm:text-xl">{activeQuiz?.time_limit_minutes ?? '...'} min</div>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">{t('modeSelection.duration')}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 sm:p-6 rounded-lg text-center">
              <div className="flex justify-center mb-2">
                {getDifficultyStars(activeQuiz?.difficulty_level ?? 1)}
              </div>
              <div className="font-semibold text-gray-900 dark:text-white">{t('modeSelection.level')} {activeQuiz?.difficulty_level ?? '...'}</div>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                {activeQuiz?.difficulty_level ? getDifficultyLabel(activeQuiz.difficulty_level) : '...'}
              </div>
            </div>
          </div>

          {/* Mode Selection - Enhanced for mobile */}
          <div className="mb-8">
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-4 sm:mb-6 text-center">{t('modeSelection.chooseMode')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              {modes.map((mode) => (
                <div
                  key={mode.id}
                  onClick={() => handleModeSelect(mode)}
                  className={`cursor-pointer rounded-lg border-2 p-4 sm:p-6 transition-all duration-200 ${
                    selectedMode === mode.id
                      ? mode.selectedColor
                      : mode.color
                  }`}
                >
                  <div className="relative">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        <span className="text-xl sm:text-2xl mr-3">{mode.icon}</span>
                        <div>
                          <h4 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">{mode.name}</h4>
                          {mode.recommended && (
                            <span className="inline-block bg-green-500 text-white text-xs px-2 py-1 rounded-full mt-1">
                              {t('modeSelection.recommended')}
                            </span>
                          )}
                        </div>
                      </div>
                      {selectedMode === mode.id && (
                        <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                      )}
                    </div>

                    <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm sm:text-base">{mode.description}</p>

                    <ul className="space-y-2">
                      {mode.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700 dark:text-gray-300 text-xs sm:text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons - Enhanced for mobile */}
          <div className="flex justify-center">
            <button
              onClick={handleStartQuiz}
              disabled={!canStartQuiz()}
              className={`${
                canStartQuiz()
                  ? 'bg-blue-600 text-white hover:bg-blue-700 transform hover:scale-105'
                  : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              } px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-medium text-base sm:text-lg transition-all duration-200 inline-flex items-center justify-center`}
            >
              <PlayCircle className="h-5 w-5 sm:h-6 sm:w-6 mr-2" />
              {canStartQuiz() ? t('modeSelection.startQuiz') : t('modeSelection.selectMode')}
            </button>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default QuizModeSelectionScreen; 