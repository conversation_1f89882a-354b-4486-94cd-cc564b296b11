import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { QuizService } from '@/services/quizService';
// Mode styling constants (moved from .style.ts)
const modeStyles = {
  practice: {
    color: 'bg-blue-50 border-blue-200',
    selectedColor: 'bg-blue-100 border-blue-500'
  },
  test: {
    color: 'bg-purple-50 border-purple-200', 
    selectedColor: 'bg-purple-100 border-purple-500'
  }
};
import { useLanguage } from '@/context/LanguageContext';
import type { QuizMode, QuizModeSelectionProps } from '@/types';

// Interface for mode configuration
interface ModeConfig {
  id: QuizMode;
  name: string;
  icon: string;
  description: string;
  features: string[];
  color: string;
  selectedColor: string;
  buttonColor: string;
  recommended: boolean;
}

/**
 * QuizModeSelectionScreen Handler
 * Manages business logic and state for quiz mode selection screen
 */
export const useQuizModeSelectionScreenHandler = (props?: QuizModeSelectionProps) => {
  const { t, getRaw } = useLanguage();
  const navigate = useNavigate();
  const { quizId: quizIdParam } = useParams();
  const [selectedMode, setSelectedMode] = useState<QuizMode | null>(null);

  // Fetch quiz details when id param exists and quiz prop not provided
  const { data: quiz, isLoading, error } = useQuery({
    queryKey: ['quiz-overview', props?.quiz?.id || quizIdParam],
    enabled: !props?.quiz && !!quizIdParam,
    queryFn: async () => {
      if (!quizIdParam) return null;
      const { data, error } = await QuizService.getQuizById(quizIdParam);
      if (error) throw new Error(error);
      return data;
    },
    initialData: props?.quiz || null,
  });



  // Mode configurations
  const modes: ModeConfig[] = [
    {
      id: 'practice',
      name: t('modeSelection.practiceMode'),
      icon: '🎯',
      description: t('modeSelection.practiceDescription'),
      features: getRaw('modeSelection.practiceFeatures') || [],
      color: modeStyles.practice.color,
      selectedColor: modeStyles.practice.selectedColor,
      buttonColor: 'bg-blue-600 hover:bg-blue-700',
      recommended: true
    },
    {
      id: 'test',
      name: t('modeSelection.testMode'),
      icon: '📝',
      description: t('modeSelection.testDescription'),
      features: getRaw('modeSelection.testFeatures') || [],
      color: modeStyles.test.color,
      selectedColor: modeStyles.test.selectedColor,
      buttonColor: 'bg-purple-600 hover:bg-purple-700',
      recommended: false
    }
  ];

  /**
   * Handle mode selection
   */
  const handleModeSelect = (mode: ModeConfig): void => {
    setSelectedMode(mode.id);
  };

  /**
   * Start quiz with selected mode
   */
  const handleStartQuiz = (): void => {
    if (!selectedMode) return;

    if (props?.onModeSelect) {
      props.onModeSelect(selectedMode);
    } else {
      const targetId = quiz?.id || quizIdParam || 'unknown';
      navigate(`/quiz/${targetId}?mode=${selectedMode}`);
    }
  };

  /**
   * Handle back navigation
   */
  const handleBack = (): void => {
    if (props?.onBack) {
      props.onBack();
    } else {
      navigate('/home');
    }
  };

  /**
   * Get selected mode configuration
   */
  const getSelectedModeConfig = (): ModeConfig | undefined => {
    return modes.find(mode => mode.id === selectedMode);
  };

  /**
   * Check if quiz can be started
   */
  const canStartQuiz = (): boolean => {
    return selectedMode !== null;
  };

  return {
    quiz,
    // State
    selectedMode,
    modes,
    isLoading,
    error,

    // Actions
    handleModeSelect,
    handleStartQuiz,
    handleBack,

    // Computed values
    getSelectedModeConfig,
    canStartQuiz
  };
}; 