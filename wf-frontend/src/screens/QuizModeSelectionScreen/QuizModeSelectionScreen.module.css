/**
 * QuizModeSelectionScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 446 lines | AFTER: ~340 lines | REDUCTION: ~24%
 * 
 * Uses shared modules for:
 * - Container layouts (pageContainer)
 * - Card components (cardBase)
 * - Button components (buttonPrimary, buttonSecondary)
 * - Typography patterns (pageTitle, subtitle, description)
 */

.pageContainer {
  padding: theme('spacing.8') 0;
}

/* Header Styles */
.backButton {
  display: flex;
  align-items: center;
  color: theme('colors.gray.600');
  text-decoration: none;
  margin-bottom: theme('spacing.4');
  transition: color 0.2s ease;
}

.backButton:hover {
  color: theme('colors.gray.800');
}

.backIcon {
  width: theme('spacing.5');
  height: theme('spacing.5');
  margin-right: theme('spacing.2');
}

/* Typography using shared components */
.title {
  composes: pageTitle from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.2');
}

.subtitle {
  composes: subtitle from '@/styles/shared/components/typography.module.css';
}

/* Quiz Info Card using shared components */
.quizInfoContainer {
  composes: cardBase from '@/styles/shared/components/cards.module.css';
  padding: theme('spacing.6');
  margin-bottom: theme('spacing.8');
  display: flex;
  gap: theme('spacing.4');
}

.quizIconContainer {
  flex-shrink: 0;
  width: theme('spacing.12');
  height: theme('spacing.12');
  background: linear-gradient(to bottom right, theme('colors.blue.500'), theme('colors.purple.600'));
  border-radius: theme('borderRadius.lg');
  display: flex;
  align-items: center;
  justify-content: center;
}

.quizIconText {
  color: white;
  font-size: theme('fontSize.xl');
  font-weight: theme('fontWeight.bold');
}

.quizInfoContent {
  flex-grow: 1;
}

.quizTitle {
  font-size: theme('fontSize.xl');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.2');
}

:global(.dark) .quizTitle {
  color: white;
}

.quizDescription {
  composes: description from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.3');
}

.quizTags {
  display: flex;
  flex-wrap: wrap;
  gap: theme('spacing.2');
}

.quizTag {
  padding: theme('spacing.1') theme('spacing.3');
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.700');
  border-radius: theme('borderRadius.full');
  font-size: theme('fontSize.sm');
}

:global(.dark) .quizTag {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
}

/* Mode Selection Grid */
.modeGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: theme('spacing.6');
  margin-bottom: theme('spacing.8');
}

@media (min-width: theme('screens.lg')) {
  .modeGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.modeCard {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid;
  border-radius: theme('borderRadius.xl');
  padding: theme('spacing.6');
}

.modeCardPractice {
  background-color: theme('colors.blue.50');
  border-color: theme('colors.blue.200');
}

.modeCardPractice:hover {
  border-color: theme('colors.blue.300');
}

.modeCardPracticeSelected {
  background-color: theme('colors.blue.100');
  border-color: theme('colors.blue.500');
}

.modeCardTest {
  background-color: theme('colors.purple.50');
  border-color: theme('colors.purple.200');
}

.modeCardTest:hover {
  border-color: theme('colors.purple.300');
}

.modeCardTestSelected {
  background-color: theme('colors.purple.100');
  border-color: theme('colors.purple.500');
}

:global(.dark) .modeCardPractice,
:global(.dark) .modeCardTest {
  background-color: theme('colors.gray.800');
}

:global(.dark) .modeCardPractice {
  border-color: theme('colors.blue.700');
}

:global(.dark) .modeCardPractice:hover {
  border-color: theme('colors.blue.600');
}

:global(.dark) .modeCardPracticeSelected {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.blue.400');
}

:global(.dark) .modeCardTest {
  border-color: theme('colors.purple.700');
}

:global(.dark) .modeCardTest:hover {
  border-color: theme('colors.purple.600');
}

:global(.dark) .modeCardTestSelected {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.purple.400');
}

.recommendedBadge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: theme('colors.green.500');
  color: white;
  font-size: theme('fontSize.xs');
  padding: theme('spacing.1') theme('spacing.2');
  border-radius: theme('borderRadius.full');
  font-weight: theme('fontWeight.medium');
}

/* Mode Card Content */
.modeHeader {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
  margin-bottom: theme('spacing.4');
}

.modeIcon {
  font-size: theme('fontSize.2xl');
}

.modeName {
  font-size: theme('fontSize.xl');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
}

:global(.dark) .modeName {
  color: white;
}

.selectedIcon {
  margin-left: auto;
}

.selectedCheckmark {
  width: theme('spacing.6');
  height: theme('spacing.6');
  background-color: theme('colors.blue.600');
  border-radius: theme('borderRadius.full');
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkIcon {
  width: theme('spacing.4');
  height: theme('spacing.4');
  color: white;
}

.modeDescription {
  composes: description from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.4');
}

/* Feature List */
.featureList {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.2');
}

.featureItem {
  display: flex;
  align-items: flex-start;
  gap: theme('spacing.2');
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.700');
}

:global(.dark) .featureItem {
  color: theme('colors.gray.300');
}

.featureIcon {
  width: theme('spacing.4');
  height: theme('spacing.4');
  color: theme('colors.green.500');
  margin-top: 2px;
  flex-shrink: 0;
}

/* Action Buttons */
.actionButtons {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
  justify-content: center;
}

@media (min-width: theme('screens.sm')) {
  .actionButtons {
    flex-direction: row;
  }
}

/* Button using shared components */
.backButtonSecondary {
  composes: buttonSecondary from '@/styles/shared/components/buttons.module.css';
  order: 2;
}

.backButtonSecondary:hover {
  background-color: theme('colors.gray.700');
}

@media (min-width: theme('screens.sm')) {
  .backButtonSecondary {
    order: 1;
  }
}

.startButton {
  color: white;
  padding: theme('spacing.3') theme('spacing.6');
  border-radius: theme('borderRadius.lg');
  border: none;
  cursor: pointer;
  font-weight: theme('fontWeight.medium');
  transition: background-color 0.2s ease;
  order: 1;
}

@media (min-width: theme('screens.sm')) {
  .startButton {
    order: 2;
  }
}

.startButtonPractice {
  background-color: theme('colors.blue.600');
}

.startButtonPractice:hover {
  background-color: theme('colors.blue.700');
}

.startButtonTest {
  background-color: theme('colors.purple.600');
}

.startButtonTest:hover {
  background-color: theme('colors.purple.700');
}

.disabledButton {
  background-color: theme('colors.gray.400');
  color: theme('colors.gray.600');
  cursor: not-allowed;
  padding: theme('spacing.3') theme('spacing.6');
  border-radius: theme('borderRadius.lg');
  border: none;
  font-weight: theme('fontWeight.medium');
  order: 1;
}

@media (min-width: theme('screens.sm')) {
  .disabledButton {
    order: 2;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -8px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -1px, 0);
  }
}

.fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.slideUp {
  animation: slideUp 0.3s ease-out;
}

.bounce {
  animation: bounce 1s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: theme('screens.sm')) {
  .quizInfoContainer {
    flex-direction: column;
    text-align: center;
  }
  
  .modeGrid {
    gap: theme('spacing.4');
  }
  
  .actionButtons {
    gap: theme('spacing.3');
  }
}