import React from 'react';
import { TrendingUp, Target, Calendar, Award, BookOpen, Clock, CheckCircle, Flame, BarChart3 } from 'lucide-react';
import { PageWrapper } from '@/components/Layout/ResponsiveContainer';
import { useProgressScreenHandler } from './ProgressScreen.handler';
import { useLanguage } from '@/context/LanguageContext';
import styles from './ProgressScreen.module.css';

/**
 * ProgressScreen component for displaying user quiz history and statistics
 * Pure UI component - all business logic handled by the handler
 * Updated with rich Progress.tsx template design
 */
const ProgressScreen: React.FC = () => {
  const { t } = useLanguage();
  const {
    attempts,
    loading,
    stats,
    hasAttempts,
    weeklyData,
    weeklyGoal,
    weeklyProgress,
    handleTakeFirstQuiz,
    handleViewQuizResult,
    formatTime,
    formatDate
  } = useProgressScreenHandler();

  const getScoreColorClass = (score: number) => {
    if (score >= 90) return `${styles.attemptBadge} ${styles.scoreExcellent}`;
    if (score >= 80) return `${styles.attemptBadge} ${styles.scoreVeryGood}`;
    if (score >= 70) return `${styles.attemptBadge} ${styles.scoreGood}`;
    if (score >= 60) return `${styles.attemptBadge} ${styles.scoreFair}`;
    return `${styles.attemptBadge} ${styles.scoreNeedsImprovement}`;
  };

  if (loading) {
    return (
      <PageWrapper>
        <div className={styles.containerWrapper}>
          <div className={styles.loadingContainer}>
            <div className={styles.loadingContent}>
              <div className={styles.loadingSpinner}></div>
              <p className={styles.loadingText}>{t('progress.loadingProgress')}</p>
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <div className={styles.containerWrapper}>
        {/* Welcome Section */}
        <div className={styles.headerContainer}>
          <div className={styles.headerContent}>
            <h2 className={styles.title}>{t('progress.title')}</h2>
            <p className={styles.subtitle}>{t('progress.subtitle')}</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('progress.completedQuizzes')}</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.totalAttempts}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{t('progress.totalAttempts')}</p>
              </div>
              <div className="bg-blue-100 dark:bg-blue-900/20 p-3 rounded-full">
                <CheckCircle className="h-8 w-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('progress.averageScore')}</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.averageScore}%</p>
                <p className="text-sm text-green-600">
                  {stats.averageScore >= 80 ? t('progress.goodProgress') : t('progress.keepPracticing')}
                </p>
              </div>
              <div className="bg-green-100 dark:bg-green-900/20 p-3 rounded-full">
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('progress.studyTime')}</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{formatTime(stats.totalTimeSpent)}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{t('progress.totalTime')}</p>
              </div>
              <div className="bg-purple-100 dark:bg-purple-900/20 p-3 rounded-full">
                <Clock className="h-8 w-8 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('progress.bestScore')}</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.bestScore}%</p>
                <p className="text-sm text-orange-600">{t('progress.personalBest')}</p>
              </div>
              <div className="bg-orange-100 dark:bg-orange-900/20 p-3 rounded-full">
                <Flame className="h-8 w-8 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Weekly Goal */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('progress.weeklyGoal')}</h3>
              <Target className="h-5 w-5 text-gray-400" />
            </div>
            
            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
                <span>{t('progress.progress')}</span>
                <span>{weeklyProgress}/{weeklyGoal} {t('progress.quizzes')}</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div
                  className="bg-blue-600 h-3 rounded-full transition-all duration-500"
                  style={{ width: `${Math.min((weeklyProgress / weeklyGoal) * 100, 100)}%` }}
                ></div>
              </div>
            </div>

            <p className="text-sm text-gray-600 dark:text-gray-300">
              {weeklyGoal - weeklyProgress > 0
                ? `${weeklyGoal - weeklyProgress} ${t('progress.moreQuizzesToReach')}`
                : t('progress.weeklyGoalAchieved')
              }
            </p>
          </div>

          {/* Weekly Activity */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('progress.thisWeek')}</h3>
              <Calendar className="h-5 w-5 text-gray-400" />
            </div>
            
            <div className="space-y-3">
              {weeklyData.map((day) => (
                <div key={day.day} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">{day.day}</span>
                  <div className="flex items-center">
                    <div className="flex space-x-1 mr-2">
                      {Array.from({ length: Math.max(day.quizzes, 1) }, (_, i) => (
                        <div
                          key={i}
                          className={`w-3 h-3 rounded-full ${
                            i < day.quizzes ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-900 dark:text-white w-8">{day.quizzes}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Achievements */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('progress.achievements')}</h3>
              <Award className="h-5 w-5 text-gray-400" />
            </div>
            
            <div className="space-y-4">
              {stats.totalAttempts >= 10 && (
                <div className="flex items-center">
                  <div className="bg-yellow-100 dark:bg-yellow-900/20 p-2 rounded-full mr-3">
                    <Award className="h-5 w-5 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">Quiz Master</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Complete 10 quizzes</p>
                  </div>
                </div>
              )}

              {stats.averageScore >= 80 && (
                <div className="flex items-center">
                  <div className="bg-green-100 dark:bg-green-900/20 p-2 rounded-full mr-3">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">High Achiever</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Maintain 80%+ average</p>
                  </div>
                </div>
              )}

              {stats.totalAttempts < 10 && (
                <div className="flex items-center opacity-50">
                  <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-full mr-3">
                    <BookOpen className="h-5 w-5 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{t('progress.gettingStarted')}</p>
                    <p className="text-xs text-gray-500">{t('progress.takeMoreQuizzes')}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quiz History */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('progress.recentActivity')}</h3>
            <BarChart3 className="h-5 w-5 text-gray-400" />
          </div>

          {!hasAttempts ? (
            <div className="text-center py-12">
              <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <BookOpen className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('progress.noQuizHistory')}</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">{t('progress.startTakingQuizzes')}</p>
              <button
                onClick={handleTakeFirstQuiz}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
              >
                {t('progress.takeFirstQuiz')}
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {attempts.slice(0, 5).map((attempt) => {
                return (
                  <div
                    key={attempt.id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                    onClick={() => handleViewQuizResult(attempt)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {(attempt as { quizzes?: { title?: string } }).quizzes?.title || 'Quiz'}
                          </h4>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getScoreColorClass(attempt.score)}`}>
                            {attempt.score}%
                          </span>
                        </div>

                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-300 space-x-4">
                          <span>{attempt.correct_answers}/{attempt.total_questions} {t('quiz.correct')}</span>
                          <span>{formatTime(attempt.time_taken_seconds)}</span>
                          <span>{attempt.mode === 'practice' ? `🎯 ${t('quiz.practice')}` : `📝 ${t('quiz.test')}`}</span>
                        </div>
                      </div>

                      <div className="text-right text-sm text-gray-500 dark:text-gray-400 ml-4">
                        {formatDate(attempt.created_at)}
                      </div>
                    </div>
                  </div>
                );
              })}
              

            </div>
          )}
        </div>


      </div>
    </PageWrapper>
  );
};

export default ProgressScreen; 