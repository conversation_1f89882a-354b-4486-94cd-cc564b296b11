import { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { QuizService } from '@/services/quizService';
import type { QuizAttempt } from 'wf-shared/types';
import type { UserStats, ScoreBadge } from '@/types';

/**
 * ProgressScreen Handler
 * Manages business logic and state for progress tracking screen
 */
export const useProgressScreenHandler = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [attempts, setAttempts] = useState<QuizAttempt[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [stats, setStats] = useState<UserStats>({
    totalAttempts: 0,
    averageScore: 0,
    bestScore: 0,
    totalTimeSpent: 0
  });
  const [weeklyData, setWeeklyData] = useState<Array<{day: string, quizzes: number}>>([]);
  const [weeklyGoal] = useState<number>(5);
  const [weeklyProgress, setWeeklyProgress] = useState<number>(0);

  // Ref to track if data is being loaded to prevent duplicate calls
  const isLoadingRef = useRef<boolean>(false);

  /**
   * Load progress data from database
   */
  const loadProgressData = useCallback(async (): Promise<void> => {
    try {
      // Prevent duplicate calls if already loading
      if (isLoadingRef.current) {
        console.log('Progress data already loading, skipping duplicate call');
        return;
      }
      
      isLoadingRef.current = true;
      setLoading(true);

      // Fetch quiz attempts with quiz details using service layer
      const { data: attemptsData, error } = await QuizService.getUserQuizAttempts(user?.id || '');

      if (error) throw new Error(error);

      const typedAttempts = attemptsData || [];
      setAttempts(typedAttempts);

      // Calculate statistics
      if (typedAttempts && typedAttempts.length > 0) {
        const totalAttempts = typedAttempts.length;
        const averageScore = Math.round(
          typedAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / totalAttempts
        );
        const bestScore = Math.max(...typedAttempts.map(attempt => attempt.score));
        const totalTimeSpent = typedAttempts.reduce((sum, attempt) => sum + (attempt.time_taken_seconds || 0), 0);

        setStats({
          totalAttempts,
          averageScore,
          bestScore,
          totalTimeSpent
        });

        // Calculate weekly data
        calculateWeeklyData(typedAttempts);
      } else {
        // Initialize empty weekly data
        const emptyWeeklyData = [
          { day: 'Mon', quizzes: 0 },
          { day: 'Tue', quizzes: 0 },
          { day: 'Wed', quizzes: 0 },
          { day: 'Thu', quizzes: 0 },
          { day: 'Fri', quizzes: 0 },
          { day: 'Sat', quizzes: 0 },
          { day: 'Sun', quizzes: 0 }
        ];
        setWeeklyData(emptyWeeklyData);
        setWeeklyProgress(0);
      }
    } catch (error) {
      console.error('Error loading progress data:', error);
    } finally {
      isLoadingRef.current = false;
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      loadProgressData();
    }
  }, [user]);

  /**
   * Calculate weekly quiz data from attempts
   */
  const calculateWeeklyData = (attempts: QuizAttempt[]): void => {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay() + 1); // Monday
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // Sunday
    endOfWeek.setHours(23, 59, 59, 999);

    // Filter attempts for this week
    const thisWeekAttempts = attempts.filter(attempt => {
      const attemptDate = new Date(attempt.created_at);
      return attemptDate >= startOfWeek && attemptDate <= endOfWeek;
    });

    // Count quizzes per day
    const dailyCounts = [0, 0, 0, 0, 0, 0, 0]; // Mon-Sun
    thisWeekAttempts.forEach(attempt => {
      const attemptDate = new Date(attempt.created_at);
      const dayOfWeek = attemptDate.getDay();
      const mondayBasedDay = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert Sunday=0 to Sunday=6
      dailyCounts[mondayBasedDay]++;
    });

    const weeklyData = [
      { day: 'Mon', quizzes: dailyCounts[0] },
      { day: 'Tue', quizzes: dailyCounts[1] },
      { day: 'Wed', quizzes: dailyCounts[2] },
      { day: 'Thu', quizzes: dailyCounts[3] },
      { day: 'Fri', quizzes: dailyCounts[4] },
      { day: 'Sat', quizzes: dailyCounts[5] },
      { day: 'Sun', quizzes: dailyCounts[6] }
    ];

    setWeeklyData(weeklyData);
    setWeeklyProgress(thisWeekAttempts.length);
  };

  /**
   * Format time display
   */
  const formatTime = (seconds: number | null | undefined): string => {
    // Handle invalid or missing values
    if (!seconds || isNaN(seconds) || seconds < 0) {
      return '0s';
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  /**
   * Format date display
   */
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  /**
   * Get score color based on percentage
   */
  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * Get score badge configuration
   */
  const getScoreBadge = (score: number): ScoreBadge => {
    if (score >= 90) return { text: 'Excellent', class: 'bg-green-100 text-green-800' };
    if (score >= 80) return { text: 'Very Good', class: 'bg-blue-100 text-blue-800' };
    if (score >= 70) return { text: 'Good', class: 'bg-yellow-100 text-yellow-800' };
    if (score >= 60) return { text: 'Fair', class: 'bg-orange-100 text-orange-800' };
    return { text: 'Needs Work', class: 'bg-red-100 text-red-800' };
  };

  /**
   * Handle take first quiz navigation
   */
  const handleTakeFirstQuiz = (): void => {
    navigate('/home');
  };

  /**
   * Navigate to quiz results for a specific attempt
   */
  const handleViewQuizResult = async (attempt: QuizAttempt): Promise<void> => {
    try {
      // Fetch quiz data and attempt details using service layer
      const [quizResult, attemptDetailsResult] = await Promise.all([
        QuizService.getQuizForResults(attempt.quiz_id),
        QuizService.getQuizAttemptDetails(attempt.id)
      ]);

      const { data: quiz, error: quizError } = quizResult;
      const { data: attemptDetails, error: attemptError } = attemptDetailsResult;

      if (quizError || !quiz) {
        console.error('Failed to fetch quiz data:', quizError);
      }

      if (attemptError || !attemptDetails) {
        console.error('Failed to fetch attempt details:', attemptError);
      }

      // Navigate to quiz results with available data
      navigate('/quiz-results', {
        state: {
          source: 'progress', // Add source context for navigation highlighting
          results: {
            correct: attempt.correct_answers,
            total: attempt.total_questions,
            answered: attempt.total_questions,
            percentage: attempt.score,
            timeSpent: attempt.time_taken_seconds || 0,
            passed: attempt.score >= 70,
            mode: attempt.mode,
            answers: [] // Will be populated from attemptDetails if available
          },
          quiz: quiz || {
            id: attempt.quiz_id,
            title: 'Quiz Results',
            description: 'Review your quiz attempt',
            total_questions: attempt.total_questions
          },
          mode: attempt.mode,
          // Pass the detailed data if available
          userAnswers: attemptDetails?.userAnswers || {},
          questions: attemptDetails?.questions || []
        }
      });
    } catch (error) {
      console.error('Error navigating to quiz results:', error);
      // Fallback navigation with minimal data
      navigate('/quiz-results', {
        state: {
          source: 'progress', // Add source context for navigation highlighting
          results: {
            correct: attempt.correct_answers,
            total: attempt.total_questions,
            answered: attempt.total_questions,
            percentage: attempt.score,
            timeSpent: attempt.time_taken_seconds || 0,
            passed: attempt.score >= 70,
            mode: attempt.mode,
            answers: []
          },
          quiz: {
            id: attempt.quiz_id,
            title: 'Quiz Results',
            description: 'Review your quiz attempt',
            total_questions: attempt.total_questions
          },
          mode: attempt.mode,
          userAnswers: {},
          questions: []
        }
      });
    }
  };

  /**
   * Check if there are any attempts
   */
  const hasAttempts = (): boolean => {
    return attempts.length > 0;
  };

  return {
    // State
    attempts,
    loading,
    stats,
    user,
    weeklyData,
    weeklyGoal,
    weeklyProgress,

    // Computed values
    hasAttempts: hasAttempts(),

    // Actions
    handleTakeFirstQuiz,
    handleViewQuizResult,

    // Utilities
    formatTime,
    formatDate,
    getScoreColor,
    getScoreBadge
  };
}; 