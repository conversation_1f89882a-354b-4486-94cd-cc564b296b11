import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import ProgressScreen from '../ProgressScreen';
import { useProgressScreenHandler } from '../ProgressScreen.handler';
import { useLanguage } from '@/context/LanguageContext';

// Mock dependencies
vi.mock('../ProgressScreen.handler', () => ({
  useProgressScreenHandler: vi.fn()
}));

vi.mock('@/context/LanguageContext', () => ({
  useLanguage: vi.fn()
}));

vi.mock('@/components/Layout/ResponsiveContainer', () => ({
  PageWrapper: ({ children }: { children: React.ReactNode }) => <div data-testid="page-wrapper">{children}</div>
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  TrendingUp: () => <svg data-testid="trending-up-icon" />,
  Target: () => <svg data-testid="target-icon" />,
  Calendar: () => <svg data-testid="calendar-icon" />,
  Award: () => <svg data-testid="award-icon" />,
  BookOpen: () => <svg data-testid="book-open-icon" />,
  Clock: () => <svg data-testid="clock-icon" />,
  CheckCircle: () => <svg data-testid="check-circle-icon" />,
  Flame: () => <svg data-testid="flame-icon" />,
  BarChart3: () => <svg data-testid="bar-chart-icon" />
}));

describe('ProgressScreen', () => {
  const mockT = vi.fn((key: string) => key);
  const mockHandler = {
    attempts: [],
    loading: false,
    user: {
      id: 'user1',
      email: '<EMAIL>',
      first_name: 'John',
      last_name: 'Doe',
      level_id: 'level-1',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    stats: {
      totalAttempts: 5,
      averageScore: 85,
      bestScore: 95,
      totalTimeSpent: 3600
    },
    hasAttempts: true,
    weeklyData: [
      { day: 'Mon', quizzes: 2 },
      { day: 'Tue', quizzes: 1 },
      { day: 'Wed', quizzes: 0 },
      { day: 'Thu', quizzes: 3 },
      { day: 'Fri', quizzes: 1 },
      { day: 'Sat', quizzes: 0 },
      { day: 'Sun', quizzes: 1 }
    ],
    weeklyGoal: 5,
    weeklyProgress: 3,
    handleTakeFirstQuiz: vi.fn(),
    handleViewQuizResult: vi.fn(),
    formatTime: vi.fn((seconds: number | null | undefined) => {
      if (!seconds) return '0:00';
      return `${Math.floor(seconds / 60)}:${(seconds % 60).toString().padStart(2, '0')}`;
    }),
    formatDate: vi.fn((date: string) => new Date(date).toLocaleDateString()),
    getScoreColor: vi.fn(() => 'text-blue-600'),
    getScoreBadge: vi.fn(() => ({ text: 'Good', class: 'bg-blue-100' }))
  };

  const mockAttempts = [
    {
      id: '1',
      user_id: 'user1',
      quiz_id: 'quiz1',
      mode: 'practice' as const,
      score: 85,
      correct_answers: 17,
      total_questions: 20,
      time_taken_seconds: 300,
      completed_at: '2024-01-15T10:00:00Z',
      is_completed: true,
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-15T10:00:00Z',
      quizzes: { title: 'Grammar Quiz 1' }
    },
    {
      id: '2',
      user_id: 'user1',
      quiz_id: 'quiz2',
      mode: 'test' as const,
      score: 92,
      correct_answers: 18,
      total_questions: 20,
      time_taken_seconds: 420,
      completed_at: '2024-01-14T15:30:00Z',
      is_completed: true,
      created_at: '2024-01-14T15:30:00Z',
      updated_at: '2024-01-14T15:30:00Z',
      quizzes: { title: 'Vocabulary Quiz 2' }
    }
  ];

  const renderProgressScreen = () => {
    return render(
      <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        <ProgressScreen />
      </BrowserRouter>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useLanguage).mockReturnValue({ t: mockT } as { t: typeof mockT });
    vi.mocked(useProgressScreenHandler).mockReturnValue(mockHandler);
  });

  describe('Loading State', () => {
    it('displays loading spinner when loading is true', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        loading: true
      });

      renderProgressScreen();

      expect(screen.getByTestId('page-wrapper')).toBeInTheDocument();
      expect(screen.getByText('progress.loadingProgress')).toBeInTheDocument();
      expect(screen.getByText('progress.loadingProgress').previousElementSibling).toHaveClass('animate-spin');
    });
  });

  describe('Header Section', () => {
    it('displays progress title and subtitle', () => {
      renderProgressScreen();

      expect(screen.getByText('progress.title')).toBeInTheDocument();
      expect(screen.getByText('progress.subtitle')).toBeInTheDocument();
    });
  });

  describe('Stats Cards', () => {
    it('displays completed quizzes stat card', () => {
      renderProgressScreen();

      expect(screen.getByText('progress.completedQuizzes')).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.getByText('progress.totalAttempts')).toBeInTheDocument();
      expect(screen.getByTestId('check-circle-icon')).toBeInTheDocument();
    });

    it('displays average score stat card', () => {
      renderProgressScreen();

      expect(screen.getByText('progress.averageScore')).toBeInTheDocument();
      expect(screen.getByText('85%')).toBeInTheDocument(); // Only in stats card since no quiz history by default
      expect(screen.getByText('progress.goodProgress')).toBeInTheDocument();
      expect(screen.getAllByTestId('trending-up-icon')).toHaveLength(2); // One in stats, one in achievements
    });

    it('displays keep practicing message for low average score', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        stats: { ...mockHandler.stats, averageScore: 65 }
      });

      renderProgressScreen();

      expect(screen.getByText('progress.keepPracticing')).toBeInTheDocument();
    });

    it('displays study time stat card', () => {
      renderProgressScreen();

      expect(screen.getByText('progress.studyTime')).toBeInTheDocument();
      expect(screen.getByText('60:00')).toBeInTheDocument(); // formatTime(3600)
      expect(screen.getByText('progress.totalTime')).toBeInTheDocument();
      expect(screen.getByTestId('clock-icon')).toBeInTheDocument();
    });

    it('displays best score stat card', () => {
      renderProgressScreen();

      expect(screen.getByText('progress.bestScore')).toBeInTheDocument();
      expect(screen.getByText('95%')).toBeInTheDocument();
      expect(screen.getByText('progress.personalBest')).toBeInTheDocument();
      expect(screen.getByTestId('flame-icon')).toBeInTheDocument();
    });
  });

  describe('Weekly Goal Section', () => {
    it('displays weekly goal progress', () => {
      renderProgressScreen();

      expect(screen.getByText('progress.weeklyGoal')).toBeInTheDocument();
      expect(screen.getByText('progress.progress')).toBeInTheDocument();
      expect(screen.getByText('3/5 progress.quizzes')).toBeInTheDocument();
      expect(screen.getByTestId('target-icon')).toBeInTheDocument();
    });

    it('displays remaining quizzes message when goal not reached', () => {
      renderProgressScreen();

      expect(screen.getByText('2 progress.moreQuizzesToReach')).toBeInTheDocument();
    });

    it('displays goal achieved message when goal is reached', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        weeklyProgress: 5
      });

      renderProgressScreen();

      expect(screen.getByText('progress.weeklyGoalAchieved')).toBeInTheDocument();
    });

    it('displays progress bar with correct width', () => {
      renderProgressScreen();

      const progressBar = screen.getByText('progress.progress').parentElement?.nextElementSibling?.querySelector('.bg-blue-600');
      expect(progressBar).toHaveStyle({ width: '60%' }); // 3/5 * 100%
    });
  });

  describe('Weekly Activity Section', () => {
    it('displays weekly activity header', () => {
      renderProgressScreen();

      expect(screen.getByText('progress.thisWeek')).toBeInTheDocument();
      expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
    });

    it('displays all days of the week with quiz counts', () => {
      renderProgressScreen();

      expect(screen.getByText('Mon')).toBeInTheDocument();
      expect(screen.getByText('Tue')).toBeInTheDocument();
      expect(screen.getByText('Wed')).toBeInTheDocument();
      expect(screen.getByText('Thu')).toBeInTheDocument();
      expect(screen.getByText('Fri')).toBeInTheDocument();
      expect(screen.getByText('Sat')).toBeInTheDocument();
      expect(screen.getByText('Sun')).toBeInTheDocument();

      // Check quiz counts - use getAllByText since numbers appear multiple times
      expect(screen.getAllByText('2')).toContain(screen.getByText('Mon').parentElement?.querySelector('.w-8'));
      expect(screen.getAllByText('1')).toHaveLength(3); // Tuesday, Friday, Sunday
      expect(screen.getAllByText('0')).toHaveLength(2); // Wednesday, Saturday
      expect(screen.getAllByText('3')).toContain(screen.getByText('Thu').parentElement?.querySelector('.w-8'));
    });
  });

  describe('Achievements Section', () => {
    it('displays achievements header', () => {
      renderProgressScreen();

      expect(screen.getByText('progress.achievements')).toBeInTheDocument();
      expect(screen.getByTestId('award-icon')).toBeInTheDocument();
    });

    it('displays Quiz Master achievement when totalAttempts >= 10', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        stats: { ...mockHandler.stats, totalAttempts: 15 }
      });

      renderProgressScreen();

      expect(screen.getByText('Quiz Master')).toBeInTheDocument();
      expect(screen.getByText('Complete 10 quizzes')).toBeInTheDocument();
    });

    it('displays High Achiever achievement when averageScore >= 80', () => {
      renderProgressScreen();

      expect(screen.getByText('High Achiever')).toBeInTheDocument();
      expect(screen.getByText('Maintain 80%+ average')).toBeInTheDocument();
    });

    it('displays Getting Started message when totalAttempts < 10', () => {
      renderProgressScreen();

      expect(screen.getByText('progress.gettingStarted')).toBeInTheDocument();
      expect(screen.getByText('progress.takeMoreQuizzes')).toBeInTheDocument();
    });
  });

  describe('Quiz History Section', () => {
    it('displays recent activity header', () => {
      renderProgressScreen();

      expect(screen.getByText('progress.recentActivity')).toBeInTheDocument();
      expect(screen.getByTestId('bar-chart-icon')).toBeInTheDocument();
    });

    it('displays empty state when no attempts', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        hasAttempts: false,
        attempts: []
      });

      renderProgressScreen();

      expect(screen.getByText('progress.noQuizHistory')).toBeInTheDocument();
      expect(screen.getByText('progress.startTakingQuizzes')).toBeInTheDocument();
      expect(screen.getByText('progress.takeFirstQuiz')).toBeInTheDocument();
      expect(screen.getAllByTestId('book-open-icon')).toHaveLength(2); // One in achievements, one in empty state
    });

    it('handles take first quiz button click', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        hasAttempts: false,
        attempts: []
      });

      renderProgressScreen();

      const takeFirstQuizButton = screen.getByText('progress.takeFirstQuiz');
      fireEvent.click(takeFirstQuizButton);

      expect(mockHandler.handleTakeFirstQuiz).toHaveBeenCalledTimes(1);
    });

    it('displays quiz attempts when available', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        attempts: mockAttempts
      });

      renderProgressScreen();

      expect(screen.getByText('Grammar Quiz 1')).toBeInTheDocument();
      expect(screen.getByText('Vocabulary Quiz 2')).toBeInTheDocument();
      expect(screen.getAllByText('85%')).toHaveLength(2); // One in stats, one in quiz history
      expect(screen.getByText('92%')).toBeInTheDocument();
    });

    it('handles quiz attempt click', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        attempts: mockAttempts
      });

      renderProgressScreen();

      const firstAttempt = screen.getByText('Grammar Quiz 1').closest('div[role="button"]') || 
                          screen.getByText('Grammar Quiz 1').closest('.cursor-pointer');
      
      if (firstAttempt) {
        fireEvent.click(firstAttempt);
        expect(mockHandler.handleViewQuizResult).toHaveBeenCalledWith(mockAttempts[0]);
      }
    });

    it('displays quiz attempt details correctly', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        attempts: mockAttempts
      });

      renderProgressScreen();

      expect(screen.getByText('17/20 quiz.correct')).toBeInTheDocument();
      expect(screen.getByText('18/20 quiz.correct')).toBeInTheDocument();
      expect(screen.getByText('🎯 quiz.practice')).toBeInTheDocument();
      expect(screen.getByText('📝 quiz.test')).toBeInTheDocument();
    });
  });

  describe('Score Color Classes', () => {
    it('applies correct color class for excellent scores (90%+)', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        attempts: [{ ...mockAttempts[0], score: 95 }]
      });

      renderProgressScreen();

      // Check the quiz history score element (the span with color classes)
      const scoreElements = screen.getAllByText('95%');
      const spanElement = scoreElements.find(el => el.tagName === 'SPAN');
      expect(spanElement).toHaveClass('text-green-600', 'bg-green-100');
    });

    it('applies correct color class for very good scores (80-89%)', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        attempts: [{ ...mockAttempts[0], score: 85 }]
      });

      renderProgressScreen();

      // Check the quiz history score element (the span with color classes)
      const scoreElements = screen.getAllByText('85%');
      const spanElement = scoreElements.find(el => el.tagName === 'SPAN');
      expect(spanElement).toHaveClass('text-blue-600', 'bg-blue-100');
    });

    it('applies correct color class for good scores (70-79%)', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        attempts: [{ ...mockAttempts[0], score: 75 }]
      });

      renderProgressScreen();

      const scoreElement = screen.getByText('75%');
      expect(scoreElement).toHaveClass('text-yellow-600', 'bg-yellow-100');
    });

    it('applies correct color class for poor scores (<70%)', () => {
      vi.mocked(useProgressScreenHandler).mockReturnValue({
        ...mockHandler,
        attempts: [{ ...mockAttempts[0], score: 65 }]
      });

      renderProgressScreen();

      const scoreElement = screen.getByText('65%');
      expect(scoreElement).toHaveClass('text-red-600', 'bg-red-100');
    });
  });

  describe('Component Integration', () => {
    it('renders within PageWrapper component', () => {
      renderProgressScreen();

      expect(screen.getByTestId('page-wrapper')).toBeInTheDocument();
    });

    it('uses language context for translations', () => {
      renderProgressScreen();

      expect(mockT).toHaveBeenCalledWith('progress.title');
      expect(mockT).toHaveBeenCalledWith('progress.subtitle');
      expect(mockT).toHaveBeenCalledWith('progress.completedQuizzes');
    });

    it('calls handler functions with correct parameters', () => {
      renderProgressScreen();

      expect(mockHandler.formatTime).toHaveBeenCalledWith(3600);
    });
  });
});
