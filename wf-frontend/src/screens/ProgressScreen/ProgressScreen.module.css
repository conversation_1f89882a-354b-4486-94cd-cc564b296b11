/**
 * ProgressScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 490 lines | AFTER: ~300 lines | REDUCTION: ~39%
 * 
 * Uses shared modules for:
 * - Container layouts (pageContainer)
 * - Card components (cardBase)
 * - Button components (buttonSecondary)
 * - Typography patterns (pageTitle, subtitle, mutedText)
 */

.pageContainer {
  padding: theme('spacing.8') 0;
}

/* Container using shared layout */
.containerWrapper {
  composes: pageContainer from '@/styles/shared/layouts/containers.module.css';
  max-width: theme('maxWidth.7xl');
}

/* Header Styles */
.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: theme('spacing.8');
}

.headerContent {
  flex: 1;
}

/* Typography using shared components */
.title {
  composes: pageTitle from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.2');
}

.subtitle {
  composes: subtitle from '@/styles/shared/components/typography.module.css';
}

/* Button using shared components */
.backButton {
  composes: buttonSecondary from '@/styles/shared/components/buttons.module.css';
}

.backButton:hover {
  background-color: theme('colors.gray.700');
}

/* Statistics Grid */
.statsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: theme('spacing.6');
  margin-bottom: theme('spacing.8');
}

@media (min-width: theme('screens.md')) {
  .statsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Stat Card using shared components */
.statCard {
  composes: cardBase from '@/styles/shared/components/cards.module.css';
  padding: theme('spacing.6');
  text-align: center;
}

.statValue {
  font-size: theme('fontSize.3xl');
  font-weight: theme('fontWeight.bold');
  margin-bottom: theme('spacing.2');
}

.statLabel {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.sm');
}

/* Stat value colors */
.statValuePrimary {
  color: theme('colors.blue.600');
}

.statValueSuccess {
  color: theme('colors.green.600');
}

.statValueWarning {
  color: theme('colors.yellow.600');
}

.statValueDanger {
  color: theme('colors.red.600');
}

.statValuePurple {
  color: theme('colors.purple.600');
}

/* History Card using shared components */
.historyCard {
  composes: cardBase from '@/styles/shared/components/cards.module.css';
  padding: theme('spacing.6');
}

.historyTitle {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.6');
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: theme('spacing.8') 0;
}

.emptyIcon {
  width: theme('spacing.16');
  height: theme('spacing.16');
  margin: 0 auto theme('spacing.4');
  color: theme('colors.gray.400');
}

.emptyTitle {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.lg');
  margin-bottom: theme('spacing.2');
}

.emptyDescription {
  color: theme('colors.gray.600');
  margin-bottom: theme('spacing.4');
}

:global(.dark) .emptyDescription {
  color: theme('colors.gray.300');
}

.emptyButton {
  background-color: theme('colors.blue.600');
  color: white;
  padding: theme('spacing.2') theme('spacing.4');
  border-radius: theme('borderRadius.md');
  border: none;
  cursor: pointer;
  font-weight: theme('fontWeight.medium');
  transition: background-color 0.2s ease;
}

.emptyButton:hover {
  background-color: theme('colors.blue.700');
}

/* Quiz Attempt List */
.attemptList {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

/* Attempt Item using shared components */
.attemptItem {
  composes: cardBase from '@/styles/shared/components/cards.module.css';
  padding: theme('spacing.4');
  cursor: pointer;
  border: 1px solid theme('colors.gray.200');
}

.attemptItem:hover {
  background-color: theme('colors.gray.50');
}

:global(.dark) .attemptItem {
  border-color: theme('colors.gray.700');
}

:global(.dark) .attemptItem:hover {
  background-color: theme('colors.gray.700');
}

.attemptHeader {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.2');
}

@media (min-width: theme('screens.sm')) {
  .attemptHeader {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.attemptContent {
  flex-grow: 1;
}

.attemptTitleRow {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
  margin-bottom: theme('spacing.2');
}

.attemptTitle {
  composes: bodyText from '@/styles/shared/components/typography.module.css';
  font-weight: theme('fontWeight.medium');
}

.attemptBadge {
  padding: theme('spacing.1') theme('spacing.2');
  border-radius: theme('borderRadius.full');
  font-size: theme('fontSize.xs');
  font-weight: theme('fontWeight.medium');
}

.attemptDetails {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: theme('spacing.4');
  font-size: theme('fontSize.sm');
}

.attemptScore {
  font-weight: theme('fontWeight.medium');
}

.attemptDate {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  color: theme('colors.gray.500');
}

/* Score Performance Colors */
.scoreExcellent {
  color: theme('colors.green.600');
  background-color: theme('colors.green.100');
}

:global(.dark) .scoreExcellent {
  background-color: rgba(34, 197, 94, 0.2);
  color: theme('colors.green.200');
}

.scoreVeryGood {
  color: theme('colors.blue.600');
  background-color: theme('colors.blue.100');
}

:global(.dark) .scoreVeryGood {
  background-color: rgba(59, 130, 246, 0.2);
  color: theme('colors.blue.200');
}

.scoreGood {
  color: theme('colors.yellow.600');
  background-color: theme('colors.yellow.100');
}

:global(.dark) .scoreGood {
  background-color: rgba(245, 158, 11, 0.2);
  color: theme('colors.yellow.200');
}

.scoreFair {
  color: theme('colors.orange.600');
  background-color: theme('colors.orange.100');
}

:global(.dark) .scoreFair {
  background-color: rgba(249, 115, 22, 0.2);
  color: theme('colors.orange.200');
}

.scoreNeedsImprovement {
  color: theme('colors.red.600');
  background-color: theme('colors.red.100');
}

:global(.dark) .scoreNeedsImprovement {
  background-color: rgba(239, 68, 68, 0.2);
  color: theme('colors.red.200');
}

/* Loading State */
.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 384px; /* 24rem */
}

.loadingContent {
  background-color: white;
  border-radius: theme('borderRadius.xl');
  box-shadow: theme('boxShadow.sm');
  padding: theme('spacing.8');
  text-align: center;
}

:global(.dark) .loadingContent {
  background-color: theme('colors.gray.800');
}

.loadingSpinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: theme('spacing.12');
  width: theme('spacing.12');
  border: 2px solid transparent;
  border-bottom: 2px solid theme('colors.blue.600');
  margin: 0 auto theme('spacing.4');
}

.loadingText {
  color: theme('colors.gray.600');
}

:global(.dark) .loadingText {
  color: theme('colors.gray.300');
}

/* Weekly Progress Section */
.weeklySection {
  margin-bottom: theme('spacing.8');
}

.weeklyHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: theme('spacing.4');
}

.weeklyTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
}

:global(.dark) .weeklyTitle {
  color: white;
}

.progressBar {
  width: 100%;
  height: theme('spacing.2');
  background-color: theme('colors.gray.200');
  border-radius: theme('borderRadius.full');
  overflow: hidden;
}

:global(.dark) .progressBar {
  background-color: theme('colors.gray.700');
}

.progressFill {
  height: 100%;
  background-color: theme('colors.blue.600');
  border-radius: theme('borderRadius.full');
  transition: width 0.3s ease;
}

/* Chart Container */
.chartContainer {
  background-color: white;
  border-radius: theme('borderRadius.xl');
  box-shadow: theme('boxShadow.sm');
  padding: theme('spacing.6');
  margin-bottom: theme('spacing.8');
}

:global(.dark) .chartContainer {
  background-color: theme('colors.gray.800');
}

.chartTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.4');
}

:global(.dark) .chartTitle {
  color: white;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.attemptItem {
  animation: fadeIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: theme('screens.sm')) {
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: theme('spacing.4');
  }
  
  .headerContainer {
    flex-direction: column;
    align-items: flex-start;
    gap: theme('spacing.4');
  }
  
  .attemptDetails {
    flex-direction: column;
    align-items: flex-start;
    gap: theme('spacing.2');
  }
}