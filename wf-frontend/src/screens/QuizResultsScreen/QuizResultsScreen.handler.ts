import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import type {
  QuizResults as QuizResultsType,
  Quiz,
  QuizMode,
  QuizQuestion,
  PerformanceLevel
} from '@/types';

// Interface for location state
interface LocationState {
  results?: QuizResultsType;
  quiz?: Quiz;
  mode?: QuizMode;
  userAnswers?: Record<string, string>;
  questions?: QuizQuestion[];
}

/**
 * QuizResultsScreen Handler
 * Manages business logic and state for quiz results screen
 */
export const useQuizResultsScreenHandler = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const { t } = useLanguage();
  
  const [showDetailedResults, setShowDetailedResults] = useState<boolean>(false);
  const [animateScore, setAnimateScore] = useState<boolean>(false);

  // Get results data from navigation state
  const { results, quiz, mode, userAnswers, questions } = (location.state as LocationState) || {};

  useEffect(() => {
    // Redirect if no results data
    if (!results || !quiz) {
      navigate('/home');
      return;
    }

    // Animate score after component mounts
    setTimeout(() => setAnimateScore(true), 500);
  }, [results, quiz, navigate]);

  /**
   * Get performance level based on percentage
   */
  const getPerformanceLevel = (percentage: number): PerformanceLevel => {
    if (percentage >= 90) return { level: t('results.excellent'), color: 'text-green-600', bg: 'bg-green-100' };
    if (percentage >= 80) return { level: t('results.veryGood'), color: 'text-blue-600', bg: 'bg-blue-100' };
    if (percentage >= 70) return { level: t('results.good'), color: 'text-yellow-600', bg: 'bg-yellow-100' };
    if (percentage >= 60) return { level: t('results.fair'), color: 'text-orange-600', bg: 'bg-orange-100' };
    return { level: t('results.needsImprovement'), color: 'text-red-600', bg: 'bg-red-100' };
  };

  /**
   * Format time display
   */
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  /**
   * Get score color based on percentage
   */
  const getScoreColor = (percentage: number): string => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 70) return 'text-blue-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * Get progress bar color based on percentage
   */
  const getProgressColor = (percentage: number): string => {
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 70) return 'bg-blue-500';
    if (percentage >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  /**
   * Toggle detailed results view
   */
  const toggleDetailedResults = (): void => {
    setShowDetailedResults(prev => !prev);
  };

  /**
   * Handle retry quiz action
   */
  const handleRetryQuiz = (): void => {
    if (quiz?.id) {
      navigate(`/quiz/${quiz.id}?mode=${mode}`);
    } else {
      navigate('/home');
    }
  };

  /**
   * Handle try different mode action
   */
  const handleTryDifferentMode = (): void => {
    const newMode: QuizMode = mode === 'practice' ? 'test' : 'practice';
    if (quiz?.id) {
      navigate(`/quiz/${quiz.id}?mode=${newMode}`);
    } else {
      navigate('/home');
    }
  };



  /**
   * Handle view progress action
   */
  const handleViewProgress = (): void => {
    navigate('/progress');
  };

  /**
   * Check if data is available
   */
  const hasValidData = (): boolean => {
    return !!(results && quiz);
  };

  /**
   * Get completion rate percentage
   */
  const getCompletionRate = (): number => {
    if (!results) return 0;
    return Math.round((results.answered / results.total) * 100);
  };

  return {
    // State
    results,
    quiz,
    mode,
    userAnswers,
    questions,
    showDetailedResults,
    animateScore,
    user,
    
    // Computed values
    performance: results ? getPerformanceLevel(results.percentage) : null,
    hasValidData: hasValidData(),
    completionRate: getCompletionRate(),
    
    // Actions
    toggleDetailedResults,
    handleRetryQuiz,
    handleTryDifferentMode,
    handleViewProgress,
    
    // Utilities
    formatTime,
    getScoreColor,
    getProgressColor
  };
}; 