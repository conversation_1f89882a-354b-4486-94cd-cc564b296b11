/**
 * QuizResultsScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 154 lines | AFTER: ~85 lines | REDUCTION: ~45%
 * 
 * Uses shared modules for:
 * - Container layouts (pageContainer)
 * - Card components (cardBase)
 * - Button components (buttonPrimary, buttonSecondary)
 * - Typography patterns (pageTitle, subtitle, mutedText)
 */

/* Container using shared layout */
.container {
  composes: pageContainer from '@/styles/shared/layouts/containers.module.css';
}

/* Card using shared components */
.card {
  composes: cardBase from '@/styles/shared/components/cards.module.css';
  padding: theme('spacing.8');
  text-align: center;
}

/* Typography using shared components */
.title {
  composes: pageTitle from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.6');
}

.scoreSection {
  margin-bottom: theme('spacing.8');
}

.scoreValue {
  font-size: theme('fontSize.6xl');
  font-weight: theme('fontWeight.bold');
  margin-bottom: theme('spacing.4');
}

.scoreExcellent {
  color: theme('colors.green.600');
}

.scoreGood {
  color: theme('colors.blue.600');
}

.scoreFair {
  color: theme('colors.yellow.600');
}

.scoreNeedsImprovement {
  color: theme('colors.red.600');
}

.resultMessage {
  composes: subtitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.xl');
  margin-bottom: theme('spacing.6');
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: theme('spacing.4');
  margin-bottom: theme('spacing.8');
}

@media (min-width: theme('screens.md')) {
  .statsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.statItem {
  text-align: center;
}

.statValue {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.2xl');
}

.statLabel {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.sm');
}

.actionButtons {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
  align-items: center;
}

@media (min-width: theme('screens.sm')) {
  .actionButtons {
    flex-direction: row;
    justify-content: center;
  }
}

/* Buttons using shared components */
.button {
  padding: theme('spacing.3') theme('spacing.6');
}

.buttonPrimary {
  composes: buttonPrimary from '@/styles/shared/components/buttons.module.css';
}

.buttonSecondary {
  composes: buttonSecondary from '@/styles/shared/components/buttons.module.css';
}