import React from 'react';
import { CheckCircle, RotateCcw } from 'lucide-react';
import { PageWrapper } from '@/components/Layout/ResponsiveContainer';
import { useQuizResultsScreenHandler } from './QuizResultsScreen.handler';
import { useLanguage } from '@/context/LanguageContext';

/**
 * QuizResultsScreen component for displaying comprehensive quiz results
 * Pure UI component - all business logic handled by the handler
 * Updated with new completion UI design
 */
const QuizResultsScreen: React.FC = () => {
  const { t } = useLanguage();
  const {
    results,
    quiz,
    mode,
    userAnswers,
    questions,
    showDetailedResults,
    animateScore,
    performance,
    hasValidData,
    toggleDetailedResults,
    handleRetryQuiz,
    handleTryDifferentMode,
    formatTime
  } = useQuizResultsScreenHandler();

  const getScoreColorClass = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!hasValidData) {
    return (
      <PageWrapper>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">No Results Found</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">Unable to load quiz results.</p>

          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8 text-center">
          <div className="mb-6">
            <div className="bg-green-100 dark:bg-green-900/20 p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{t('results.quizComplete')}</h2>
            <p className="text-gray-600 dark:text-gray-300">
              {quiz?.title || 'Quiz'} - {mode === 'practice' ? t('quiz.practiceMode') : t('quiz.testMode')}
            </p>
            {quiz?.description && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                {quiz.description}
              </p>
            )}
            {(quiz?.category || quiz?.level) && (
              <div className="flex items-center justify-center space-x-4 mt-3">
                {quiz?.category && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {quiz.category.parent ? quiz.category.parent.name : quiz.category.name}
                    {quiz.category.parent && (
                      <span className="ml-1 text-blue-600">• {quiz.category.name}</span>
                    )}
                  </span>
                )}
                {quiz?.level && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {quiz.level.key}
                  </span>
                )}
              </div>
            )}
          </div>

          <div className="mb-8">
            <div className={`text-6xl font-bold mb-2 transition-all duration-1000 ${
              animateScore ? 'scale-100 opacity-100' : 'scale-50 opacity-0'
            } ${getScoreColorClass(results?.percentage || 0)}`}>
              {results?.percentage || 0}%
            </div>
            <p className="text-gray-600 dark:text-gray-300">
              {t('results.youGot')} {results?.correct || 0} {t('results.outOf')} {results?.total || 0} {t('results.questionsCorrect')}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {t('results.timeSpent')}: {formatTime(results?.timeSpent || 0)}
            </p>
          </div>

          {/* Performance Badge */}
          <div className="mb-8">
            <div className={`inline-block px-4 py-2 rounded-full text-sm font-medium ${
              (results?.percentage || 0) >= 90 ? 'bg-green-100 text-green-800' :
              (results?.percentage || 0) >= 80 ? 'bg-blue-100 text-blue-800' :
              (results?.percentage || 0) >= 70 ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {performance?.level || t('results.needsPractice')}
            </div>
          </div>

          {/* Detailed Results Toggle */}
          {questions && questions.length > 0 && (
            <div className="mb-8 text-left">
              <button
                onClick={toggleDetailedResults}
                className="w-full bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 p-4 rounded-lg transition-colors duration-200 flex items-center justify-between"
              >
                <span className="font-medium text-gray-900 dark:text-white">
                  {showDetailedResults ? t('results.hide') : t('results.show')} {t('results.detailedResults')}
                </span>
                <svg
                  className={`h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform duration-200 ${showDetailedResults ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {showDetailedResults && (
                <div className="mt-4 space-y-4">
                  {questions.map((question, index) => {
                    const userAnswer = userAnswers?.[question.id] || '';
                    const isCorrect = userAnswer === question.correct_answer;
                    
                    return (
                      <div key={question.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900 dark:text-white">{t('results.question')} {index + 1}</h4>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            isCorrect
                              ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200'
                              : 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200'
                          }`}>
                            {isCorrect ? t('results.correct') : t('results.incorrect')}
                          </span>
                        </div>

                        <p className="text-gray-700 dark:text-gray-300 mb-3">{question.question_text}</p>
                        
                        {question.metadata?.example_usage && (
                          <p className="text-gray-600 dark:text-gray-400 italic mb-3 text-sm">
                            {t('results.example')}: &quot;{question.metadata.example_usage}&quot;
                          </p>
                        )}

                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="font-medium text-gray-700 dark:text-gray-300">{t('results.yourAnswer')}: </span>
                            <span className={isCorrect ? 'text-green-600' : 'text-red-600'}>
                              {userAnswer || t('results.noAnswer')}
                            </span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700 dark:text-gray-300">{t('results.correctAnswer')}: </span>
                            <span className="text-green-600">{question.correct_answer}</span>
                          </div>
                        </div>

                        {question.explanations && question.explanations.length > 0 && (
                          <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                            <p className="text-sm text-blue-800 dark:text-blue-200">
                              <strong>{t('results.explanation')}:</strong> {question.explanations[0].content}
                            </p>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={handleRetryQuiz}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center"
            >
              <RotateCcw className="h-5 w-5 mr-2" />
              {t('results.retakeQuiz')}
            </button>
            {mode === 'practice' && (
              <button
                onClick={handleTryDifferentMode}
                className="bg-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors duration-200"
              >
                {t('results.tryTestMode')}
              </button>
            )}
            {mode === 'test' && (
              <button
                onClick={handleTryDifferentMode}
                className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200"
              >
                {t('results.tryPracticeMode')}
              </button>
            )}
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default QuizResultsScreen; 