import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { QuizService } from '@/services/quizService';
import { Category, Level, Quiz } from 'wf-shared/types';
import { APP_CONSTANTS } from 'wf-shared/constants';

export type AssessmentType = 'all' | 'assessment' | 'toeic' | 'ielts';

export interface UseAssessmentScreenReturn {
  searchTerm: string;
  setSearchTerm: (v: string) => void;
  selectedAssessmentType: AssessmentType;
  setSelectedAssessmentType: (v: AssessmentType) => void;
  selectedLevel: string;
  setSelectedLevel: (v: string) => void;
  assessmentQuizzes: Quiz[];
  levels: Level[];
  isLoading: boolean;
  isLoadingLevels: boolean;
  error: string | null;
  refetch: () => void;
  // Pagination
  currentPage: number;
  setCurrentPage: (page: number) => void;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

export const useAssessmentScreen = (): UseAssessmentScreenReturn => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAssessmentType, setSelectedAssessmentType] = useState<AssessmentType>('all');
  const [selectedLevel, setSelectedLevel] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = APP_CONSTANTS.PAGINATION.DEFAULT_PAGE_SIZE;

  // Fetch levels
  const {
    data: levels = [],
    isLoading: isLoadingLevels
  } = useQuery({
    queryKey: ['levels'],
    queryFn: async () => {
      const { data, error } = await QuizService.getLevels();
      if (error) throw new Error(error);
      return data ?? [];
    }
  });

  // Fetch assessment quizzes based on filters with pagination
  const {
    data: assessmentResponse,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['assessment-quizzes', { searchTerm, selectedAssessmentType, selectedLevel, currentPage }],
    queryFn: async () => {
      const response = await QuizService.getAssessmentQuizzes({
        assessmentType: selectedAssessmentType,
        levelKey: selectedLevel !== 'all' ? selectedLevel : undefined,
        search: searchTerm || undefined,
        page: currentPage,
        pageSize
      });
      if (response.error) throw new Error(response.error);
      return response;
    }
  });

  const assessmentQuizzes = assessmentResponse?.data || [];
  const totalCount = assessmentResponse?.totalCount || 0;
  const totalPages = assessmentResponse?.totalPages || 0;

  return {
    searchTerm,
    setSearchTerm,
    selectedAssessmentType,
    setSelectedAssessmentType,
    selectedLevel,
    setSelectedLevel,
    assessmentQuizzes,
    levels,
    isLoading,
    isLoadingLevels,
    error: error?.message || null,
    refetch,
    currentPage,
    setCurrentPage,
    totalPages,
    totalCount,
    pageSize
  };
};
