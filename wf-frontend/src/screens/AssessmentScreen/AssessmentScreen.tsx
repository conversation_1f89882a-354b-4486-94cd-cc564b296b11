import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/context/LanguageContext';
import { useAssessmentScreen } from './AssessmentScreen.handler';
import Layout from '@/components/Layout';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, FileText, Users, Trophy, Target, BookOpen, ArrowRight, Eye } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * AssessmentScreen Component
 * Dedicated screen for assessment mode quizzes with comprehensive evaluation features
 */
const AssessmentScreen: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const {
    searchTerm,
    setSearchTerm,
    selectedAssessmentType,
    setSelectedAssessmentType,
    selectedLevel,
    setSelectedLevel,
    assessmentQuizzes,
    levels,
    isLoading,
    isLoadingLevels,
    error,
    refetch,
    currentPage,
    setCurrentPage,
    totalPages,
    totalCount,
    pageSize
  } = useAssessmentScreen();

  const getAssessmentTypeIcon = (assessmentType: string) => {
    switch (assessmentType) {
      case 'test_quiz': return <Target className="h-5 w-5" />;
      case 'toeic_test': return <Trophy className="h-5 w-5" />;
      case 'ielts_test': return <BookOpen className="h-5 w-5" />;
      default: return <FileText className="h-5 w-5" />;
    }
  };

  const getAssessmentTypeColor = (assessmentType: string) => {
    switch (assessmentType) {
      case 'test_quiz': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'toeic_test': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'ielts_test': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleStartAssessment = (quizId: string) => {
    navigate(`/quiz/${quizId}?mode=test`);
  };

  const handleViewDetails = (quizId: string) => {
    navigate(`/quiz/${quizId}/details?mode=test`);
  };

  if (error) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Error Loading Assessments</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => refetch()}>Try Again</Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          {t('assessment.title', 'Skill Assessment Center')}
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          {t('assessment.description', 'Challenge yourself with timed assessments and official format tests')}
        </p>
      </div>

      {/* Filters */}
      <div className="mb-8 space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <Input
              type="text"
              placeholder={t('assessment.searchPlaceholder', 'Search assessments...')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>

          {/* Assessment Type Filter */}
          <Select value={selectedAssessmentType} onValueChange={setSelectedAssessmentType}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder={t('assessment.selectAssessmentType', 'Assessment Type')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('assessment.allAssessments', 'All Assessments')}</SelectItem>
              <SelectItem value="assessment">{t('assessment.skillAssessments', 'Skill Assessments')}</SelectItem>
              <SelectItem value="toeic">{t('assessment.toeicAssessments', 'TOEIC Assessments')}</SelectItem>
              <SelectItem value="ielts">{t('assessment.ieltsAssessments', 'IELTS Assessments')}</SelectItem>
            </SelectContent>
          </Select>

          {/* Level Filter */}
          <Select value={selectedLevel} onValueChange={setSelectedLevel} disabled={isLoadingLevels}>
            <SelectTrigger className="w-full sm:w-32">
              <SelectValue placeholder={t('common.level', 'Level')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('common.allLevels', 'All Levels')}</SelectItem>
              {levels.map((level) => (
                <SelectItem key={level.id} value={level.key}>
                  {level.key}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Assessment Quizzes Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3 mb-4" />
                <Skeleton className="h-10 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : assessmentQuizzes.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {t('assessment.noAssessments', 'No assessments found')}
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            {t('assessment.noAssessmentsDescription', 'Try adjusting your filters to find assessments')}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {assessmentQuizzes.map((quiz) => (
            <Card key={quiz.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2">{quiz.title}</CardTitle>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary" className={getAssessmentTypeColor(quiz.quiz_type?.key || '')}>
                        {getAssessmentTypeIcon(quiz.quiz_type?.key || '')}
                        <span className="ml-1">{quiz.quiz_type?.name}</span>
                      </Badge>
                      <Badge variant="outline">{quiz.level?.key}</Badge>
                    </div>
                  </div>
                </div>
                <CardDescription className="line-clamp-2">
                  {quiz.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Assessment Info */}
                  <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300">
                    <div className="flex items-center gap-1">
                      <FileText className="h-4 w-4" />
                      <span>{quiz.total_questions} questions</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{quiz.time_limit_minutes} min</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-between">
                    <a
                      href={`/quiz/${quiz.id}?mode=test`}
                      className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
                    >
                      {t('assessment.startAssessment', 'Start Assessment')}
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </a>
                    <button
                      onClick={() => handleViewDetails(quiz.id)}
                      className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 font-medium transition-colors duration-200"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      {t('common.details', 'Details')}
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-gray-600 dark:text-gray-300">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
      </div>
    </Layout>
  );
};

export default AssessmentScreen;
