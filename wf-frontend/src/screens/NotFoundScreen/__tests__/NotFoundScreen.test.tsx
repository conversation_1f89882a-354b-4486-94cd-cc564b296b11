import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import NotFoundScreen from '../NotFoundScreen';
import { useNotFoundScreen } from '../NotFoundScreen.handler';
import { useLanguage } from '@/context/LanguageContext';

// Mock dependencies
vi.mock('../NotFoundScreen.handler');
vi.mock('@/context/LanguageContext');

// Mock translation function
const mockT = vi.fn((key: string) => {
  const translations: Record<string, string> = {
    'notFound.title': '404 - Page Not Found',
    'notFound.subtitle': 'The page you are looking for does not exist.',
    'notFound.returnToHome': 'Return to Home'
  };
  return translations[key] || key;
});

// Mock handler return values
const mockHandlerReturn = {
  pathname: '/unknown-page'
};

describe('NotFoundScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    vi.mocked(useLanguage).mockReturnValue({
      t: mockT,
      language: 'en',
      setLanguage: vi.fn(),
      getRaw: vi.fn(),
      isLoading: false,
      availableLanguages: ['en', 'vi', 'fr'],
      getLanguageDisplayName: vi.fn((_lang: string) => 'English'),
      getLanguageFlag: vi.fn((_lang: string) => '🇺🇸')
    });
    
    vi.mocked(useNotFoundScreen).mockReturnValue(mockHandlerReturn);
  });

  const renderNotFoundScreen = () => {
    return render(<NotFoundScreen />);
  };

  describe('Content Display', () => {
    it('displays 404 title', () => {
      renderNotFoundScreen();

      expect(screen.getByText('404 - Page Not Found')).toBeInTheDocument();
    });

    it('displays subtitle message', () => {
      renderNotFoundScreen();

      expect(screen.getByText('The page you are looking for does not exist.')).toBeInTheDocument();
    });

    it('displays return to home link', () => {
      renderNotFoundScreen();

      const homeLink = screen.getByRole('link', { name: 'Return to Home' });
      expect(homeLink).toBeInTheDocument();
      expect(homeLink).toHaveAttribute('href', '/');
    });
  });

  describe('Component Integration', () => {
    it('uses language context for translations', () => {
      renderNotFoundScreen();

      expect(useLanguage).toHaveBeenCalled();
      expect(mockT).toHaveBeenCalledWith('notFound.title');
      expect(mockT).toHaveBeenCalledWith('notFound.subtitle');
      expect(mockT).toHaveBeenCalledWith('notFound.returnToHome');
    });

    it('calls not found screen handler hook', () => {
      renderNotFoundScreen();

      expect(useNotFoundScreen).toHaveBeenCalled();
    });
  });

  describe('Styling and Layout', () => {
    it('applies container styling classes', () => {
      renderNotFoundScreen();

      const container = screen.getByText('404 - Page Not Found').closest('div');
      expect(container?.parentElement).toHaveClass('min-h-screen');
    });

    it('applies content styling classes', () => {
      renderNotFoundScreen();

      const content = screen.getByText('404 - Page Not Found').closest('div');
      expect(content).toHaveClass('text-center');
    });

    it('applies title styling classes', () => {
      renderNotFoundScreen();

      const title = screen.getByText('404 - Page Not Found');
      expect(title).toHaveClass('text-4xl', 'font-bold', 'mb-4');
    });

    it('applies subtitle styling classes', () => {
      renderNotFoundScreen();

      const subtitle = screen.getByText('The page you are looking for does not exist.');
      expect(subtitle).toHaveClass('text-xl', 'text-gray-600');
    });

    it('applies link styling classes', () => {
      renderNotFoundScreen();

      const link = screen.getByRole('link', { name: 'Return to Home' });
      expect(link).toHaveClass('text-blue-500', 'hover:text-blue-700', 'underline');
    });
  });

  describe('Dark Mode Support', () => {
    it('applies container styling classes', () => {
      renderNotFoundScreen();

      const container = screen.getByText('404 - Page Not Found').closest('div');
      expect(container?.parentElement).toHaveClass('min-h-screen', 'flex', 'items-center', 'justify-center', 'bg-gray-100');
    });

    it('applies content styling classes', () => {
      renderNotFoundScreen();

      const content = screen.getByText('404 - Page Not Found').closest('div');
      expect(content).toHaveClass('text-center');
    });

    it('verifies basic styling structure', () => {
      renderNotFoundScreen();

      const title = screen.getByText('404 - Page Not Found');
      expect(title).toHaveClass('text-4xl', 'font-bold', 'mb-4');

      const subtitle = screen.getByText('The page you are looking for does not exist.');
      expect(subtitle).toHaveClass('text-xl', 'text-gray-600', 'mb-4');

      const link = screen.getByRole('link', { name: 'Return to Home' });
      expect(link).toHaveClass('text-blue-500', 'hover:text-blue-700', 'underline');
    });
  });

  describe('Accessibility', () => {
    it('has proper heading hierarchy', () => {
      renderNotFoundScreen();

      const title = screen.getByText('404 - Page Not Found');
      expect(title.tagName).toBe('H1');
    });

    it('has descriptive link text', () => {
      renderNotFoundScreen();

      const link = screen.getByRole('link', { name: 'Return to Home' });
      expect(link).toHaveAccessibleName('Return to Home');
    });
  });

  describe('Handler Integration', () => {
    it('uses pathname from handler', () => {
      renderNotFoundScreen();

      expect(useNotFoundScreen).toHaveBeenCalled();
      // The pathname is used internally by the handler but not displayed in the UI
    });

    it('works with different pathnames', () => {
      vi.mocked(useNotFoundScreen).mockReturnValue({
        pathname: '/different-unknown-page'
      });

      renderNotFoundScreen();

      // Component should still render the same content regardless of pathname
      expect(screen.getByText('404 - Page Not Found')).toBeInTheDocument();
    });
  });

  describe('Translation Support', () => {
    it('handles missing translations gracefully', () => {
      const mockTWithMissing = vi.fn((key: string) => key);
      vi.mocked(useLanguage).mockReturnValue({
        t: mockTWithMissing,
        language: 'en',
        setLanguage: vi.fn(),
        getRaw: vi.fn(),
        isLoading: false,
        availableLanguages: ['en', 'vi', 'fr'],
        getLanguageDisplayName: vi.fn((_lang: string) => 'English'),
        getLanguageFlag: vi.fn((_lang: string) => '🇺🇸')
      });

      renderNotFoundScreen();

      expect(screen.getByText('notFound.title')).toBeInTheDocument();
      expect(screen.getByText('notFound.subtitle')).toBeInTheDocument();
      expect(screen.getByText('notFound.returnToHome')).toBeInTheDocument();
    });
  });
});
