import { useLanguage } from '@/context/LanguageContext';
import { useNotFoundScreen } from './NotFoundScreen.handler';
import styles from './NotFoundScreen.module.css';

const NotFoundScreen: React.FC = () => {
  const { t } = useLanguage();
  useNotFoundScreen();

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <h1 className={styles.title}>{t('notFound.title')}</h1>
        <p className={styles.subtitle}>{t('notFound.subtitle')}</p>
        <a href="/" className={styles.link}>
          {t('notFound.returnToHome')}
        </a>
      </div>
    </div>
  );
};

export default NotFoundScreen; 