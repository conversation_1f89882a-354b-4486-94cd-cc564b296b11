import { useLocation } from "react-router-dom";
import { useEffect } from "react";

export interface UseNotFoundScreenReturn {
  pathname: string;
}

export const useNotFoundScreen = (): UseNotFoundScreenReturn => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return {
    pathname: location.pathname
  };
}; 