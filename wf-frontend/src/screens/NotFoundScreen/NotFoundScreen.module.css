/**
 * NotFoundScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 32 lines | AFTER: ~20 lines | REDUCTION: ~38%
 * 
 * Uses shared modules for:
 * - Container layouts (centeredContainer) 
 * - Typography patterns (pageTitle, subtitle, linkText)
 */

/* Container using shared centered layout */
.container {
  composes: centeredContainer from '@/styles/shared/layouts/containers.module.css';
}

/* Typography using shared components */
.title {
  composes: pageTitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.4xl');
  margin-bottom: theme('spacing.4');
}

.subtitle {
  composes: subtitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.xl');
  margin-bottom: theme('spacing.4');
}

.link {
  composes: linkText from '@/styles/shared/components/typography.module.css';
}

/* Component-specific styles */
.content {
  text-align: center;
}