/**
 * Performance Monitoring Integration for WordFormation
 * Tracks Web Vitals and sends metrics to monitoring services
 */

import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

interface VitalsMetric {
  name: string;
  value: number;
  id: string;
  delta: number;
}

/**
 * Initialize performance monitoring for the application
 * Tracks Core Web Vitals and sends data to monitoring services
 */
export const initPerformanceMonitoring = (): void => {
  if (typeof window === 'undefined') {
    return; // Skip on server-side rendering
  }

  /**
   * Send performance metrics to monitoring service
   */
  const sendToAnalytics = (metric: VitalsMetric): void => {
    // In production, send to monitoring service (Sentry, Analytics, etc.)
    if (import.meta.env.PROD) {
      // Sentry performance monitoring integration
      // window.gtag?.('event', metric.name, {
      //   value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      //   metric_id: metric.id,
      //   metric_value: metric.value,
      //   metric_delta: metric.delta,
      // });
      
      // For now, structured console output for monitoring
      console.log('[PERFORMANCE]', JSON.stringify({
        metric: metric.name,
        value: metric.value,
        id: metric.id,
        delta: metric.delta,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent.slice(0, 100) // First 100 chars
      }));
    } else {
      // Development: detailed console logging
      console.log(`[Web Vitals] ${metric.name}:`, {
        value: metric.value,
        id: metric.id,
        delta: metric.delta,
        rating: getPerformanceRating(metric.name, metric.value),
        url: window.location.href
      });
    }
  };

  /**
   * Get performance rating based on Web Vitals thresholds
   */
  const getPerformanceRating = (metricName: string, value: number): 'good' | 'needs-improvement' | 'poor' => {
    const thresholds = {
      CLS: { good: 0.1, poor: 0.25 },
      FID: { good: 100, poor: 300 },
      FCP: { good: 1800, poor: 3000 },
      LCP: { good: 2500, poor: 4000 },
      TTFB: { good: 800, poor: 1800 }
    };

    const threshold = thresholds[metricName as keyof typeof thresholds];
    if (!threshold) return 'good';

    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  };

  // Initialize Web Vitals tracking
  try {
    getCLS(sendToAnalytics);
    getFID(sendToAnalytics);
    getFCP(sendToAnalytics);
    getLCP(sendToAnalytics);
    getTTFB(sendToAnalytics);

    console.log('[Performance] Web Vitals monitoring initialized');
  } catch (error) {
    console.error('[Performance] Failed to initialize Web Vitals:', error);
  }
};

/**
 * Track custom performance metrics
 */
export const trackCustomMetric = (name: string, value: number, unit = 'ms'): void => {
  const metric = {
    name: `custom_${name}`,
    value,
    unit,
    timestamp: new Date().toISOString(),
    url: window.location.href
  };

  if (import.meta.env.PROD) {
    console.log('[PERFORMANCE]', JSON.stringify(metric));
  } else {
    console.log(`[Custom Metric] ${name}: ${value}${unit}`);
  }
};

/**
 * Performance mark utilities for measuring operations
 */
export const performanceMark = {
  start: (name: string): void => {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(`${name}-start`);
    }
  },

  end: (name: string): number | null => {
    if ('performance' in window && 'mark' in performance && 'measure' in performance) {
      try {
        performance.mark(`${name}-end`);
        performance.measure(name, `${name}-start`, `${name}-end`);
        
        const measure = performance.getEntriesByName(name, 'measure')[0];
        const duration = measure?.duration || 0;
        
        trackCustomMetric(name, Math.round(duration));
        
        // Clean up marks
        performance.clearMarks(`${name}-start`);
        performance.clearMarks(`${name}-end`);
        performance.clearMeasures(name);
        
        return duration;
      } catch (error) {
        console.warn(`[Performance] Failed to measure ${name}:`, error);
        return null;
      }
    }
    return null;
  }
};