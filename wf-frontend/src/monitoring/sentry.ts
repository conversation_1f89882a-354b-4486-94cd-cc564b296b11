// Sentry initialization for React app

import * as Sentry from "@sentry/react";


Sentry.init({
  dsn: import.meta.env.REACT_APP_SENTRY_DSN,
  integrations: [
    Sentry.browserTracingIntegration(),
  ],
  tracesSampleRate: 1.0, // Adjust for production (e.g., 0.2)
  environment: process.env.NODE_ENV,
  // You can add more options here (release, beforeSend, etc.)
  tracePropagationTargets: ["localhost"],
});

export default Sentry;
