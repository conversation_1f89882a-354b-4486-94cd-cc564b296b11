/**
 * Translation Service
 * Handles loading and managing translations with type safety and async loading
 */

export type Language = 'en' | 'vi' | 'fr';

export interface TranslationNamespace {
  [key: string]: string | string[] | TranslationNamespace;
}

export interface Translations {
  [namespace: string]: TranslationNamespace;
}

export interface TranslationCache {
  [language: string]: Translations;
}

class TranslationService {
  private cache: TranslationCache = {};
  private loadingPromises: Map<string, Promise<Translations>> = new Map();
  private currentLanguage: Language = 'en';

  /**
   * Set the current language
   */
  setLanguage(language: Language): void {
    this.currentLanguage = language;
  }

  /**
   * Get the current language
   */
  getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  /**
   * Load translations for a specific language
   */
  async loadTranslations(language: Language): Promise<Translations> {
    // Check if already cached
    if (this.cache[language]) {
      return this.cache[language];
    }

    // Check if already loading
    const loadingKey = `loading-${language}`;
    if (this.loadingPromises.has(loadingKey)) {
      return this.loadingPromises.get(loadingKey)!;
    }

    // Start loading
    const loadingPromise = this.fetchTranslations(language);
    this.loadingPromises.set(loadingKey, loadingPromise);

    try {
      const translations = await loadingPromise;
      this.cache[language] = translations;
      this.loadingPromises.delete(loadingKey);
      return translations;
    } catch (error) {
      this.loadingPromises.delete(loadingKey);
      throw error;
    }
  }

  /**
   * Fetch translations from the appropriate source
   */
  private async fetchTranslations(language: Language): Promise<Translations> {
    try {
      // Dynamic import with explicit file extensions for Vite compatibility
      let translationModule;
      switch (language) {
        case 'en':
          translationModule = await import('./translations/en.js');
          return translationModule.en;
        case 'vi':
          translationModule = await import('./translations/vi.js');
          return translationModule.vi;
        case 'fr':
          translationModule = await import('./translations/fr.js');
          return translationModule.fr;
        default:
          throw new Error(`Unsupported language: ${language}`);
      }
    } catch (error) {
      console.error(`Failed to load translations for language: ${language}`, error);

      // Fallback to English if available
      if (language !== 'en') {
        console.warn(`Falling back to English translations`);
        return this.fetchTranslations('en');
      }

      // Return empty translations as last resort
      return {};
    }
  }

  /**
   * Get a translation by key with dot notation support
   * @param key - Translation key (e.g., 'common.loading' or 'nav.home')
   * @param language - Optional language override
   * @param fallback - Fallback text if translation not found
   */
  translate(key: string, language?: Language, fallback?: string): string {
    const lang = language || this.currentLanguage;
    const translations = this.cache[lang];

    if (!translations) {
      return fallback || key;
    }

    // Split key by dots and traverse the object
    const keys = key.split('.');
    let current: Record<string, unknown> = translations;

    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k] as Record<string, unknown>;
      } else {
        return fallback || key;
      }
    }

    return typeof current === 'string' ? current : (fallback || key);
  }

  /**
   * Get raw translation value (can be string, array, or object)
   * @param key - Translation key (e.g., 'modeSelection.practiceFeatures')
   * @param language - Optional language override
   */
  getRaw(key: string, language?: Language): unknown {
    const lang = language || this.currentLanguage;
    const translations = this.cache[lang];

    if (!translations) {
      return null;
    }

    // Split key by dots and traverse the object
    const keys = key.split('.');
    let current: Record<string, unknown> = translations;

    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k] as Record<string, unknown>;
      } else {
        return null;
      }
    }

    return current;
  }

  /**
   * Get translations for a specific namespace
   */
  getNamespace(namespace: string, language?: Language): TranslationNamespace {
    const lang = language || this.currentLanguage;
    const translations = this.cache[lang];
    
    if (!translations || !translations[namespace]) {
      return {};
    }
    
    return translations[namespace];
  }

  /**
   * Check if translations are loaded for a language
   */
  isLoaded(language: Language): boolean {
    return !!this.cache[language];
  }

  /**
   * Preload translations for multiple languages
   */
  async preloadLanguages(languages: Language[]): Promise<void> {
    const promises = languages.map(lang => this.loadTranslations(lang));
    await Promise.all(promises);
  }

  /**
   * Clear cache for a specific language or all languages
   */
  clearCache(language?: Language): void {
    if (language) {
      delete this.cache[language];
    } else {
      this.cache = {};
    }
  }

  /**
   * Get all available languages
   */
  getAvailableLanguages(): Language[] {
    return ['en', 'vi', 'fr'];
  }

  /**
   * Get language display name
   */
  getLanguageDisplayName(language: Language): string {
    const displayNames: Record<Language, string> = {
      'en': 'English',
      'vi': 'Tiếng Việt',
      'fr': 'Français'
    };
    
    return displayNames[language] || language;
  }

  /**
   * Get language flag emoji
   */
  getLanguageFlag(language: Language): string {
    const flags: Record<Language, string> = {
      'en': '🇺🇸',
      'vi': '🇻🇳',
      'fr': '🇫🇷'
    };
    
    return flags[language] || '🌐';
  }
}

// Export singleton instance
export const translationService = new TranslationService();

// Export types for use in components
export type TranslationKey = string;
export type TranslateFunction = (key: TranslationKey, fallback?: string) => string;
