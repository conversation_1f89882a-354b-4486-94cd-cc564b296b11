import { describe, it, expect, beforeEach } from 'vitest';
import { translationService } from '../translationService';
import type { Language } from '../translationService';

describe('TranslationService', () => {
  beforeEach(() => {
    // Clear cache and loading promises
    (translationService as { cache: Record<string, unknown> }).cache = {};
    (translationService as { loadingPromises: Map<string, Promise<unknown>> }).loadingPromises.clear();
    // Reset to default language
    translationService.setLanguage('en');
  });

  describe('setLanguage and getCurrentLanguage', () => {
    it('sets and gets current language', () => {
      translationService.setLanguage('vi');
      expect(translationService.getCurrentLanguage()).toBe('vi');
    });

    it('defaults to English initially', () => {
      const service = translationService;
      service.setLanguage('en');
      expect(service.getCurrentLanguage()).toBe('en');
    });

    it('handles language changes', () => {
      translationService.setLanguage('fr');
      expect(translationService.getCurrentLanguage()).toBe('fr');

      translationService.setLanguage('en');
      expect(translationService.getCurrentLanguage()).toBe('en');
    });
  });

  describe('translate', () => {
    it('returns key when no translations loaded', () => {
      const result = translationService.translate('common.loading', 'en');
      expect(result).toBe('common.loading');
    });

    it('returns key when translation not found', () => {
      const result = translationService.translate('nonexistent.key', 'en');
      expect(result).toBe('nonexistent.key');
    });

    it('handles empty key', () => {
      const result = translationService.translate('', 'en');
      expect(result).toBe('');
    });

    it('handles undefined language', () => {
      const result = translationService.translate('common.loading', undefined as unknown as 'en');
      expect(result).toBe('common.loading');
    });
  });

  describe('getLanguageFlag', () => {
    it('returns correct flag for English', () => {
      const result = translationService.getLanguageFlag('en');
      expect(result).toBe('🇺🇸');
    });

    it('returns correct flag for Vietnamese', () => {
      const result = translationService.getLanguageFlag('vi');
      expect(result).toBe('🇻🇳');
    });

    it('returns correct flag for French', () => {
      const result = translationService.getLanguageFlag('fr');
      expect(result).toBe('🇫🇷');
    });

    it('returns default flag for unknown language', () => {
      const result = translationService.getLanguageFlag('unknown' as Language);
      expect(result).toBe('🌐');
    });
  });

  describe('cache management', () => {
    it('initializes with empty cache', () => {
      const cache = (translationService as { cache: Record<string, unknown> }).cache;
      expect(cache).toEqual({});
    });

    it('initializes with empty loading promises', () => {
      const loadingPromises = (translationService as { loadingPromises: Map<string, Promise<unknown>> }).loadingPromises;
      expect(loadingPromises.size).toBe(0);
    });
  });
});
