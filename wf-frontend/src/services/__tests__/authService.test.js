import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as authService from '../authService';

// Mock the repositories
vi.mock('../../repositories', () => ({
  repositories: {
    authApi: {
      signUp: vi.fn(),
      signIn: vi.fn(),
      signOut: vi.fn(),
      getCurrentSession: vi.fn(),
      onAuthStateChange: vi.fn(),
      resendConfirmation: vi.fn()
    },
    authStorage: {
      saveCredentials: vi.fn(),
      saveSession: vi.fn(),
      clearSession: vi.fn(),
      loadCredentials: vi.fn(),
      clearCredentials: vi.fn(),
      hasStoredCredentials: vi.fn(),
      updateLastActive: vi.fn()
    }
  }
}));

import { repositories } from '../../repositories';

describe('AuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('signUp', () => {
    it('should sign up a user successfully', async () => {
      const mockUser = { id: '123', email: '<EMAIL>' };
      repositories.authApi.signUp.mockResolvedValue({ user: mockUser });

      const result = await authService.signUp('<EMAIL>', 'password123', { 
        first_name: 'John', 
        last_name: 'Doe',
        level_id: 'a2'
      });

      expect(repositories.authApi.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        levelId: 'a2'
      });
      expect(result).toEqual({ user: mockUser });
    });

    it('should handle sign up error', async () => {
      repositories.authApi.signUp.mockResolvedValue({ 
        user: null, 
        error: 'Email already exists' 
      });

      const result = await authService.signUp('<EMAIL>', 'password123');

      expect(result).toEqual({ user: null, error: 'Email already exists' });
    });

    it('should handle sign up with metadata', async () => {
      const mockUser = { id: '123', email: '<EMAIL>' };
      repositories.authApi.signUp.mockResolvedValue({ user: mockUser });

      await authService.signUp('<EMAIL>', 'password123', { 
        first_name: 'John', 
        last_name: 'Doe',
        level_id: 'b1'
      });

      expect(repositories.authApi.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        levelId: 'b1'
      });
    });
  });

  describe('signIn', () => {
    it('should sign in a user successfully', async () => {
      const mockUser = { 
        id: '123', 
        email: '<EMAIL>',
        user_metadata: { first_name: 'John', last_name: 'Doe' },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };
      const mockSession = { access_token: 'token', refresh_token: 'refresh' };
      
      repositories.authApi.signIn.mockResolvedValue({ 
        user: mockUser, 
        session: mockSession 
      });
      repositories.authStorage.saveCredentials.mockResolvedValue();
      repositories.authStorage.saveSession.mockResolvedValue();

      const result = await authService.signIn('<EMAIL>', 'password123');

      expect(repositories.authApi.signIn).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(repositories.authStorage.saveCredentials).toHaveBeenCalledWith('<EMAIL>', false);
      expect(result.user).toBeDefined();
      expect(result.user.id).toBe('123');
    });

    it('should handle sign in error', async () => {
      repositories.authApi.signIn.mockResolvedValue({ 
        user: null, 
        error: 'Invalid credentials' 
      });

      const result = await authService.signIn('<EMAIL>', 'wrongpassword');

      expect(result).toEqual({ user: null, error: 'Invalid credentials' });
    });

    it('should save credentials when remember me is enabled', async () => {
      const mockUser = { 
        id: '123', 
        email: '<EMAIL>',
        user_metadata: {},
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };
      
      repositories.authApi.signIn.mockResolvedValue({ user: mockUser });
      repositories.authStorage.saveCredentials.mockResolvedValue();

      await authService.signIn('<EMAIL>', 'password123', true);

      expect(repositories.authStorage.saveCredentials).toHaveBeenCalledWith('<EMAIL>', true);
    });
  });

  describe('signOut', () => {
    it('should sign out successfully', async () => {
      repositories.authApi.signOut.mockResolvedValue({});
      repositories.authStorage.clearSession.mockResolvedValue();

      await expect(authService.signOut()).resolves.not.toThrow();

      expect(repositories.authApi.signOut).toHaveBeenCalled();
      expect(repositories.authStorage.clearSession).toHaveBeenCalled();
    });

    it('should handle sign out error', async () => {
      repositories.authApi.signOut.mockResolvedValue({ error: 'Sign out failed' });

      await expect(authService.signOut()).rejects.toThrow('Sign out failed');
    });
  });

  describe('getCurrentSession', () => {
    it('should get current session successfully', async () => {
      const mockUser = { 
        id: '123', 
        email: '<EMAIL>',
        user_metadata: { first_name: 'John' },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };
      
      repositories.authApi.getCurrentSession.mockResolvedValue({ user: mockUser });

      const result = await authService.getCurrentSession();

      expect(result).toBeDefined();
      expect(result.id).toBe('123');
      expect(result.email).toBe('<EMAIL>');
    });

    it('should return null when no session', async () => {
      repositories.authApi.getCurrentSession.mockResolvedValue({ user: null });

      const result = await authService.getCurrentSession();

      expect(result).toBeNull();
    });

    it('should handle session error', async () => {
      repositories.authApi.getCurrentSession.mockResolvedValue({ 
        user: null, 
        error: 'Session expired' 
      });

      await expect(authService.getCurrentSession()).rejects.toThrow('Session expired');
    });
  });

  describe('onAuthStateChange', () => {
    it('should set up auth state change listener', () => {
      const callback = vi.fn();
      const unsubscribe = vi.fn();
      repositories.authApi.onAuthStateChange.mockReturnValue(unsubscribe);

      const result = authService.onAuthStateChange(callback);

      expect(repositories.authApi.onAuthStateChange).toHaveBeenCalledWith(callback);
      expect(result).toBe(unsubscribe);
    });
  });

  describe('resendConfirmation', () => {
    it('should resend confirmation successfully', async () => {
      repositories.authApi.resendConfirmation.mockResolvedValue({});

      const result = await authService.resendConfirmation('<EMAIL>');

      expect(repositories.authApi.resendConfirmation).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toEqual({});
    });

    it('should handle resend confirmation error', async () => {
      repositories.authApi.resendConfirmation.mockResolvedValue({ 
        error: 'Failed to resend' 
      });

      const result = await authService.resendConfirmation('<EMAIL>');

      expect(result).toEqual({ error: 'Failed to resend' });
    });
  });

  describe('credential management', () => {
    it('should get remembered credentials', async () => {
      const mockCredentials = { email: '<EMAIL>' };
      repositories.authStorage.loadCredentials.mockResolvedValue(mockCredentials);

      const result = await authService.getRememberedCredentials();

      expect(repositories.authStorage.loadCredentials).toHaveBeenCalled();
      expect(result).toBe(mockCredentials);
    });

    it('should clear remembered credentials', async () => {
      repositories.authStorage.clearCredentials.mockResolvedValue();

      await authService.clearRememberedCredentials();

      expect(repositories.authStorage.clearCredentials).toHaveBeenCalled();
    });

    it('should check if has remembered credentials', async () => {
      repositories.authStorage.hasStoredCredentials.mockResolvedValue(true);

      const result = await authService.hasRememberedCredentials();

      expect(repositories.authStorage.hasStoredCredentials).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should update last active', async () => {
      repositories.authStorage.updateLastActive.mockResolvedValue();

      await authService.updateLastActive('123');

      expect(repositories.authStorage.updateLastActive).toHaveBeenCalledWith('123');
    });
  });
});