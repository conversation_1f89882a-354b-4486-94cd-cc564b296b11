import { describe, it, expect, vi, beforeEach } from 'vitest';
import { QuizService } from '../quizService';
import type { Level, Category, QuizQuestion, QuizAttempt, User } from '@/types';

// Mock the repositories
vi.mock('../../repositories', () => ({
  repositories: {
    quizApi: {
      getLevels: vi.fn(),
      getCategories: vi.fn(),
      getMainCategories: vi.fn(),
      getSubCategories: vi.fn(),
      getActiveQuizzes: vi.fn(),
      getUserQuizById: vi.fn(),
      getQuizByLevelAndCategory: vi.fn(),
      getQuizByKey: vi.fn(),
      getQuizQuestions: vi.fn(),
      saveQuizAttempt: vi.fn(),
      getUserStats: vi.fn(),
      getQuizForResults: vi.fn(),
      getQuizAttemptDetails: vi.fn(),
      searchQuizzes: vi.fn(),
      getUserProfile: vi.fn(),
      updateUserProfile: vi.fn(),
      getUserQuizAttempts: vi.fn()
    }
  }
}));

import { repositories } from '../../repositories';
const mockQuizApi = vi.mocked(repositories.quizApi);

describe('QuizService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getLevels', () => {
    it('fetches all levels successfully', async () => {
      const mockLevels: Level[] = [
        { 
          id: '1', 
          system: 'CEFR', 
          key: 'A2', 
          name: 'Elementary', 
          description: 'Basic level', 
          sort_order: 1, 
          is_active: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        },
        { 
          id: '2', 
          system: 'CEFR', 
          key: 'B1', 
          name: 'Intermediate', 
          description: 'Intermediate level', 
          sort_order: 2, 
          is_active: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      ];

      mockQuizApi.getLevels.mockResolvedValue({
        success: true,
        data: mockLevels
      });

      const result = await QuizService.getLevels();

      expect(mockQuizApi.getLevels).toHaveBeenCalled();
      expect(result).toEqual({ data: mockLevels, error: null });
    });

    it('handles error when fetching levels fails', async () => {
      mockQuizApi.getLevels.mockResolvedValue({
        success: false,
        error: { message: 'Database connection failed', code: 'DB_ERROR' }
      });

      const result = await QuizService.getLevels();

      expect(result).toEqual({ data: null, error: 'Database connection failed' });
    });
  });

  describe('getCategories', () => {
    it('fetches all categories successfully', async () => {
      const mockCategories: Category[] = [
        {
          id: '1',
          key: 'grammar',
          name: 'Grammar',
          description: 'Grammar exercises',
          category_type: 'main',
          parent_id: null,
          is_active: true,
          sort_order: 1,
          color: '#blue',
          icon: 'book',
          metadata: {},
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        },
        {
          id: '2',
          key: 'vocabulary',
          name: 'Vocabulary',
          description: 'Vocabulary exercises',
          category_type: 'main',
          parent_id: null,
          is_active: true,
          sort_order: 2,
          color: '#green',
          icon: 'words',
          metadata: {},
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      ];

      mockQuizApi.getCategories.mockResolvedValue({
        success: true,
        data: mockCategories
      });

      const result = await QuizService.getCategories();

      expect(mockQuizApi.getCategories).toHaveBeenCalled();
      expect(result).toEqual({ data: mockCategories, error: null });
    });

    it('handles error when fetching categories fails', async () => {
      mockQuizApi.getCategories.mockResolvedValue({
        success: false,
        error: { message: 'Failed to fetch categories', code: 'FETCH_ERROR' }
      });

      const result = await QuizService.getCategories();

      expect(result).toEqual({ data: null, error: 'Failed to fetch categories' });
    });
  });

  describe('getMainCategories', () => {
    it('fetches main categories successfully', async () => {
      const mockCategories: Category[] = [
        {
          id: '1',
          key: 'grammar',
          name: 'Grammar',
          description: 'Grammar exercises',
          category_type: 'main',
          parent_id: null,
          is_active: true,
          sort_order: 1,
          color: '#blue',
          icon: 'book',
          metadata: {},
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      ];

      mockQuizApi.getMainCategories.mockResolvedValue({
        success: true,
        data: mockCategories
      });

      const result = await QuizService.getMainCategories();

      expect(mockQuizApi.getMainCategories).toHaveBeenCalled();
      expect(result).toEqual({ data: mockCategories, error: null });
    });
  });

  describe('getSubCategories', () => {
    it('fetches subcategories successfully', async () => {
      const mockCategories: Category[] = [
        {
          id: '2',
          key: 'verb-tenses',
          name: 'Verb Tenses',
          description: 'Verb tense exercises',
          category_type: 'sub',
          parent_id: '1',
          is_active: true,
          sort_order: 1,
          color: '#blue',
          icon: 'verb',
          metadata: {},
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      ];

      mockQuizApi.getSubCategories.mockResolvedValue({
        success: true,
        data: mockCategories
      });

      const result = await QuizService.getSubCategories('grammar');

      expect(mockQuizApi.getSubCategories).toHaveBeenCalledWith('grammar');
      expect(result).toEqual({ data: mockCategories, error: null });
    });
  });

  describe('getAllQuizzes', () => {
    it('fetches all quizzes successfully', async () => {
      const mockQuizzes = [
        {
          id: '1',
          title: 'Test Quiz',
          categories: { id: '1', name: 'Grammar', key: 'grammar' },
          levels: { id: '1', name: 'A2', key: 'a2' },
          quiz_types: { id: '1', name: 'Practice', key: 'practice' }
        }
      ];

      mockQuizApi.getActiveQuizzes.mockResolvedValue({
        success: true,
        data: {
          data: mockQuizzes,
          total: 1,
          page: 1,
          pageSize: 1000
        }
      });

      const result = await QuizService.getAllQuizzes();

      expect(mockQuizApi.getActiveQuizzes).toHaveBeenCalledWith({
        page: 1,
        pageSize: 1000
      });
      expect(result.data).toHaveLength(1);
      expect(result.error).toBeNull();
    });
  });

  describe('getQuizById', () => {
    it('fetches quiz by ID successfully', async () => {
      const mockQuiz = {
        id: '1',
        title: 'Test Quiz',
        categories: { id: '1', name: 'Grammar', key: 'grammar' },
        levels: { id: '1', name: 'A2', key: 'a2' },
        quiz_types: { id: '1', name: 'Practice', key: 'practice' }
      };

      mockQuizApi.getUserQuizById.mockResolvedValue({
        success: true,
        data: mockQuiz
      });

      const result = await QuizService.getQuizById('1');

      expect(mockQuizApi.getUserQuizById).toHaveBeenCalledWith('1');
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
    });
  });

  describe('getQuizByKey', () => {
    it('fetches quiz by key successfully', async () => {
      const mockQuiz = {
        id: '1',
        title: 'Test Quiz',
        key: 'test-quiz',
        categories: { id: '1', name: 'Grammar', key: 'grammar' },
        levels: { id: '1', name: 'A2', key: 'a2' },
        quiz_types: { id: '1', name: 'Practice', key: 'practice' }
      };

      mockQuizApi.getQuizByKey.mockResolvedValue({
        success: true,
        data: mockQuiz
      });

      const result = await QuizService.getQuizByKey('test-quiz');

      expect(mockQuizApi.getQuizByKey).toHaveBeenCalledWith('test-quiz');
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
    });
  });

  describe('getQuizQuestions', () => {
    it('fetches quiz questions successfully', async () => {
      const mockQuestions: QuizQuestion[] = [
        {
          id: '1',
          quiz_id: '1',
          question_text: 'What is the correct form?',
          question_type_id: '1',
          difficulty_level: 1,
          order_index: 1,
          is_active: true,
          metadata: {},
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          correct_answer: 'Option A',
          options: [],
          explanations: []
        }
      ];

      mockQuizApi.getQuizQuestions.mockResolvedValue({
        success: true,
        data: mockQuestions
      });

      const result = await QuizService.getQuizQuestions('1');

      expect(mockQuizApi.getQuizQuestions).toHaveBeenCalledWith('1');
      expect(result.data).toEqual(mockQuestions);
      expect(result.error).toBeNull();
    });
  });

  describe('saveQuizAttempt', () => {
    it('saves quiz attempt successfully', async () => {
      const mockResults = {
        correct: 8,
        total: 10,
        answered: 10,
        percentage: 80,
        timeSpent: 300,
        passed: true,
        mode: 'practice' as const,
        answers: []
      };

      mockQuizApi.saveQuizAttempt.mockResolvedValue({
        success: true,
        data: { success: true }
      });

      const result = await QuizService.saveQuizAttempt('user-1', 'quiz-1', mockResults);

      expect(mockQuizApi.saveQuizAttempt).toHaveBeenCalledWith('user-1', 'quiz-1', mockResults);
      expect(result).toEqual({ success: true, error: null });
    });
  });

  describe('getUserStats', () => {
    it('fetches user stats successfully', async () => {
      const mockStats = {
        totalAttempts: 5,
        averageScore: 85,
        bestScore: 95,
        totalTimeSpent: 1200
      };

      mockQuizApi.getUserStats.mockResolvedValue({
        success: true,
        data: mockStats
      });

      const result = await QuizService.getUserStats('user-1');

      expect(mockQuizApi.getUserStats).toHaveBeenCalledWith('user-1');
      expect(result.data).toEqual(mockStats);
      expect(result.error).toBeNull();
    });
  });

  describe('getUserProfile', () => {
    it('fetches user profile successfully', async () => {
      const mockUser: User = {
        id: 'user-1',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        level_id: 'a2',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };

      mockQuizApi.getUserProfile.mockResolvedValue({
        success: true,
        data: mockUser
      });

      const result = await QuizService.getUserProfile('user-1');

      expect(mockQuizApi.getUserProfile).toHaveBeenCalledWith('user-1');
      expect(result.data).toEqual(mockUser);
      expect(result.error).toBeNull();
    });
  });

  describe('updateUserProfile', () => {
    it('updates user profile successfully', async () => {
      const mockUser: User = {
        id: 'user-1',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Smith',
        level_id: 'b1',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };

      const profileData = {
        first_name: 'John',
        last_name: 'Smith',
        level_id: 'b1'
      };

      mockQuizApi.updateUserProfile.mockResolvedValue({
        success: true,
        data: mockUser
      });

      const result = await QuizService.updateUserProfile('user-1', profileData);

      expect(mockQuizApi.updateUserProfile).toHaveBeenCalledWith('user-1', profileData);
      expect(result.data).toEqual(mockUser);
      expect(result.error).toBeNull();
    });
  });

  describe('getUserQuizAttempts', () => {
    it('fetches user quiz attempts successfully', async () => {
      const mockAttempts: QuizAttempt[] = [
        {
          id: 'attempt-1',
          user_id: 'user-1',
          quiz_id: 'quiz-1',
          mode: 'practice',
          score: 85,
          correct_answers: 8,
          total_questions: 10,
          time_taken_seconds: 300,
          completed_at: '2023-01-01T00:00:00Z',
          is_completed: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      ];

      mockQuizApi.getUserQuizAttempts.mockResolvedValue({
        success: true,
        data: mockAttempts
      });

      const result = await QuizService.getUserQuizAttempts('user-1');

      expect(mockQuizApi.getUserQuizAttempts).toHaveBeenCalledWith('user-1');
      expect(result.data).toEqual(mockAttempts);
      expect(result.error).toBeNull();
    });
  });
});