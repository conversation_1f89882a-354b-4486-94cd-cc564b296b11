import { repositories } from '../repositories';
import type { User } from 'wf-shared/types';
import type { AuthResponse } from '@/types';

/**
 * Helper function to handle authentication repository calls with consistent error handling
 */
async function handleAuthCall<T>(
  authCall: () => Promise<T>,
  errorContext: string,
  defaultError: string = 'An unexpected error occurred'
): Promise<T | { user: null; error: string }> {
  try {
    const result = await authCall();
    
    if (result && typeof result === 'object' && 'error' in result && result.error) {
      console.error(`${errorContext}:`, result.error);
      return { user: null, error: result.error as string };
    }
    
    return result;
  } catch (error) {
    console.error(`${errorContext}:`, error);
    return { user: null, error: defaultError };
  }
}

/**
 * Helper function to transform repository user object to User type
 */
function transformToUser(repositoryUser: Record<string, unknown>): User {
  return {
    id: repositoryUser.id,
    email: repositoryUser.email || '',
    first_name: repositoryUser.user_metadata?.first_name || '',
    last_name: repositoryUser.user_metadata?.last_name || '',
    level_id: repositoryUser.user_metadata?.level_id || '',
    created_at: repositoryUser.created_at || new Date().toISOString(),
    updated_at: repositoryUser.updated_at || new Date().toISOString()
  };
}

/**
 * Authentication service for handling user sign up, sign in, and sign out operations.
 * Uses repository pattern for all authentication operations.
 */

/**
 * Sign up a new user with email and password
 * @param email - User's email address
 * @param password - User's password
 * @param userData - User data (first_name, last_name, level_id)
 * @returns Promise with user data or error
 */
export const signUp = async (
  email: string,
  password: string,
  userData?: { first_name: string; last_name: string; level_id: string }
): Promise<AuthResponse> => {
  console.log('Attempting sign up with:', { email, userData });

  const result = await handleAuthCall(
    () => repositories.authApi.signUp({
      email,
      password,
      firstName: userData?.first_name,
      lastName: userData?.last_name,
      levelId: userData?.level_id
    }),
    'Sign up error'
  );

  if ('error' in result) {
    return result;
  }

  console.log('Sign up successful:', result.user);
  return { user: result.user as User | null };
};

/**
 * Sign in an existing user with email and password
 * @param email - User's email address
 * @param password - User's password
 * @param rememberMe - Whether to save credentials for future use
 * @returns Promise with user data or error
 */
export const signIn = async (email: string, password: string, rememberMe: boolean = false): Promise<AuthResponse> => {
  console.log('Attempting sign in with:', email);
  
  const result = await handleAuthCall(
    () => repositories.authApi.signIn(email, password),
    'Sign in error'
  );

  if ('error' in result) {
    return result;
  }

  console.log('Sign in successful:', result.user);

  // Save credentials if remember me is enabled
  await repositories.authStorage.saveCredentials(email, rememberMe);

  // Save session data for tracking
  if (result.user && 'session' in result && result.session) {
    await repositories.authStorage.saveSession({
      userId: result.user.id,
      email: result.user.email || '',
      accessToken: result.session.access_token,
      refreshToken: result.session.refresh_token,
      lastActive: new Date().toISOString()
    });
  }

  // Create a proper User object from Supabase auth user
  if (result.user) {
    const user = transformToUser(result.user);
    return { user };
  }

  return { user: null };
};

/**
 * Sign out the current user
 * @returns Promise that resolves when sign out is complete
 */
export const signOut = async (): Promise<void> => {
  try {
    const result = await repositories.authApi.signOut();
    
    if (result.error) {
      console.error('Sign out error:', result.error);
      throw new Error(result.error);
    }
    
    // Clear stored session data
    await repositories.authStorage.clearSession();
    
    console.log('Sign out successful');
  } catch (error) {
    console.error('Sign out error:', error);
    throw error;
  }
};

/**
 * Get the current user session
 * @returns Promise with current user or null
 */
export const getCurrentSession = async (): Promise<User | null> => {
  try {
    const result = await repositories.authApi.getCurrentSession();
    
    if (result.error) {
      console.error('Get session error:', result.error);
      throw new Error(result.error);
    }
    
    // Create a proper User object from repository result
    if (result.user) {
      return transformToUser(result.user);
    }

    return null;
  } catch (error) {
    console.error('Get session error:', error);
    throw error;
  }
};

/**
 * Listen for authentication state changes
 * @param callback - Function to call when auth state changes
 * @returns Unsubscribe function
 */
export const onAuthStateChange = (
  callback: (event: string, session: Record<string, unknown> | null) => void
) => {
  return repositories.authApi.onAuthStateChange(callback);
};

/**
 * Check if user's email is confirmed
 * @param user - User object
 * @returns boolean indicating if email is confirmed
 */
export const isEmailConfirmed = (user: User | null): boolean => {
  return !!(user?.email_confirmed_at);
};

/**
 * Resend email confirmation
 * @param email - User's email address
 * @returns Promise with success/error status
 */
export const resendConfirmation = async (email: string): Promise<{ error?: string }> => {
  const result = await handleAuthCall(
    () => repositories.authApi.resendConfirmation(email),
    'Resend confirmation error'
  );

  if ('error' in result) {
    return { error: result.error };
  }

  return {};
};

/**
 * Load remembered credentials from storage
 * @returns Promise with remembered credentials
 */
export const getRememberedCredentials = async () => {
  return await repositories.authStorage.loadCredentials();
};

/**
 * Clear remembered credentials from storage
 * @returns Promise that resolves when credentials are cleared
 */
export const clearRememberedCredentials = async (): Promise<void> => {
  await repositories.authStorage.clearCredentials();
};

/**
 * Check if there are valid stored credentials
 * @returns Promise with boolean indicating if credentials exist
 */
export const hasRememberedCredentials = async (): Promise<boolean> => {
  return await repositories.authStorage.hasStoredCredentials();
};

/**
 * Update user's last active timestamp
 * @param userId - User ID
 * @returns Promise that resolves when timestamp is updated
 */
export const updateLastActive = async (userId: string): Promise<void> => {
  await repositories.authStorage.updateLastActive(userId);
}; 