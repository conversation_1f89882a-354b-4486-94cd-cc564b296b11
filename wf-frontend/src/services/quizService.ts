import { repositories } from '../repositories';
import type { 
  DbLevel, 
  DbCategory, 
  DbQuizAttempt
} from 'wf-shared/types';
import type { QuizResults } from '@/types';

// Standardize response types
interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
  };
}

/**
 * Convert ApiResponse to ServiceResponse format
 */
function convertApiResponse<T>(apiResponse: any): ServiceResponse<T> {
  if (apiResponse.success) {
    return {
      success: true,
      data: apiResponse.data
    };
  } else {
    return {
      success: false,
      error: {
        message: apiResponse.error?.message || apiResponse.error || 'Operation failed',
        code: apiResponse.error?.code
      }
    };
  }
}


/**
 * Quiz Service
 * Handles all quiz-related API operations and business logic
 * Updated for the new normalized database schema
 */
export class QuizService {
  /**
   * Get all available levels
   */
  static async getLevels(): Promise<ServiceResponse<DbLevel[]>> {
    try {
      const result = await repositories.quizApi.getLevels();
      return convertApiResponse<DbLevel[]>(result);
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to fetch levels'
        }
      };
    }
  }

  /**
   * Get all available categories with hierarchical structure
   */
  static async getCategories(): Promise<ServiceResponse<DbCategory[]>> {
    try {
      const result = await repositories.quizApi.getCategories();
      return convertApiResponse<DbCategory[]>(result);
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to fetch categories'
        }
      };
    }
  }

  /**
   * Get main categories (those without parent)
   */
  static async getMainCategories(): Promise<ServiceResponse<DbCategory[]>> {
    try {
      const result = await repositories.quizApi.getMainCategories();
      return convertApiResponse<DbCategory[]>(result);
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to fetch main categories'
        }
      };
    }
  }

  /**
   * Get subcategories for a specific parent category (excluding level-type categories)
   */
  static async getSubCategories(parentCategoryKey: string): Promise<ServiceResponse<DbCategory[]>> {
    try {
      const result = await repositories.quizApi.getSubCategories(parentCategoryKey);
      return convertApiResponse<DbCategory[]>(result);
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to fetch subcategories'
        }
      };
    }
  }

  /**
   * Get all available quizzes with level and category information
   */
  static async getAllQuizzes(): Promise<ServiceResponse<any[]>> {
    try {
      const result = await repositories.quizApi.getActiveQuizzes({
        page: 1,
        pageSize: 1000 // Get all quizzes
      });

      if (!result.success) {
        return convertApiResponse<any[]>(result);
      }

      if (!result.data?.data) {
        return { success: true, data: [] };
      }

      // Transform QuizWithRelations to simplified format
      const quizzes = result.data.data.map(quizData => ({
        ...quizData,
        category: quizData.categories,
        level: quizData.levels,
        quiz_type: quizData.quiz_types,
      }));

      return { success: true, data: quizzes };
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to fetch quizzes'
        }
      };
    }
  }

  /**
   * Get practice quizzes for Home screen with direct access
   */
  static async getPracticeQuizzes(params: {
    categoryKey?: string;
    levelKey?: string;
    search?: string;
    page?: number;
    pageSize?: number;
  }): Promise<{ data: Quiz[] | null; error: string | null; totalCount?: number; totalPages?: number }> {
    try {
      const result = await repositories.quizApi.getPracticeQuizzes(params);

      if (!result.success) {
        console.error('Practice quizzes fetch error:', result.error);
        return {
          data: null,
          error: result.error?.message || 'Failed to fetch practice quizzes'
        };
      }

      // Transform to Quiz type
      const quizzes: Quiz[] = (result.data?.data || []).map((quizData: any) => ({
        id: quizData.id,
        key: quizData.key,
        title: quizData.title,
        description: quizData.description,
        instructions: quizData.instructions,
        category_id: quizData.category_id,
        level_id: quizData.level_id,
        quiz_type_id: quizData.quiz_type_id,
        difficulty_level: quizData.difficulty_level,
        total_questions: quizData.total_questions,
        time_limit_minutes: quizData.time_limit_minutes,
        quiz_config: quizData.quiz_config,
        is_active: quizData.is_active,
        created_at: quizData.created_at,
        updated_at: quizData.updated_at,
        category: {
          id: quizData.category_id,
          key: quizData.category_key,
          name: quizData.category_name
        } as Category,
        level: {
          id: quizData.level_id,
          key: quizData.level_key,
          name: quizData.level_name
        } as Level,
        quiz_type: {
          id: quizData.quiz_type_id,
          key: quizData.quiz_type_key,
          name: quizData.quiz_type_name
        }
      }));

      return {
        data: quizzes,
        error: null,
        totalCount: result.data?.total,
        totalPages: result.data?.total ? Math.ceil(result.data.total / (params.pageSize || 12)) : 0
      };
    } catch (error) {
      console.error('Practice quizzes service error:', error);
      return {
        data: null,
        error: 'An unexpected error occurred while fetching practice quizzes'
      };
    }
  }

  /**
   * Get assessment quizzes for Assessment navigation tab
   */
  static async getAssessmentQuizzes(params: {
    assessmentType?: 'all' | 'assessment' | 'toeic' | 'ielts';
    levelKey?: string;
    search?: string;
    page?: number;
    pageSize?: number;
  }): Promise<{ data: Quiz[] | null; error: string | null; totalCount?: number; totalPages?: number }> {
    try {
      const result = await repositories.quizApi.getTestQuizzes({
        testType: params.assessmentType,
        levelKey: params.levelKey,
        search: params.search,
        page: params.page,
        pageSize: params.pageSize
      });

      if (!result.success) {
        console.error('Assessment quizzes fetch error:', result.error);
        return {
          data: null,
          error: result.error?.message || 'Failed to fetch assessment quizzes'
        };
      }

      // Transform to Quiz type
      const quizzes: Quiz[] = (result.data?.data || []).map((quizData: any) => ({
        id: quizData.id,
        key: quizData.key,
        title: quizData.title,
        description: quizData.description,
        instructions: quizData.instructions,
        category_id: quizData.category_id,
        level_id: quizData.level_id,
        quiz_type_id: quizData.quiz_type_id,
        difficulty_level: quizData.difficulty_level,
        total_questions: quizData.total_questions,
        time_limit_minutes: quizData.time_limit_minutes,
        quiz_config: quizData.quiz_config,
        is_active: quizData.is_active,
        created_at: quizData.created_at,
        updated_at: quizData.updated_at,
        category: {
          id: quizData.category_id,
          key: quizData.category_key,
          name: quizData.category_name
        } as Category,
        level: {
          id: quizData.level_id,
          key: quizData.level_key,
          name: quizData.level_name
        } as Level,
        quiz_type: {
          id: quizData.quiz_type_id,
          key: quizData.quiz_type_key,
          name: quizData.quiz_type_name
        }
      }));

      return {
        data: quizzes,
        error: null,
        totalCount: result.data?.total,
        totalPages: result.data?.total ? Math.ceil(result.data.total / (params.pageSize || 10)) : 0
      };
    } catch (error) {
      console.error('Assessment quizzes service error:', error);
      return {
        data: null,
        error: 'An unexpected error occurred while fetching assessment quizzes'
      };
    }
  }

  /**
   * Get quiz by level and category
   */
  static async getQuizByLevelAndCategory(
    levelKey: string, 
    categoryKey: string
  ): Promise<{ data: Quiz | null; error: string | null }> {
    return handleRepositoryCall(
      () => repositories.quizApi.getQuizByLevelAndCategory(levelKey, categoryKey),
      'Quiz fetch error',
      'Failed to fetch quiz'
    );
  }

  /**
   * Get quiz by ID with full details
   */
  static async getQuizById(
    quizId: string
  ): Promise<{ data: Quiz | null; error: string | null }> {
    return handleRepositoryCall(
      async () => {
        const result = await repositories.quizApi.getUserQuizById(quizId);
        
        if (!result.success) {
          return result;
        }

        if (!result.data) {
          return { success: false, error: { message: 'Quiz not found' } };
        }

        // Transform QuizWithRelations to Quiz type
        const quiz: Quiz = {
          ...result.data,
          category: result.data.categories as Category,
          level: result.data.levels as Level,
          quiz_type: result.data.quiz_types as { id: string; key: string; name: string; description?: string } | null,
          // Include questions array from repository response
          questions: result.data.questions,
        };

        return { success: true, data: quiz };
      },
      'Quiz fetch error',
      'Failed to fetch quiz'
    );
  }

  /**
   * Get quiz by key with full details
   */
  static async getQuizByKey(
    quizKey: string
  ): Promise<{ data: Quiz | null; error: string | null }> {
    return handleRepositoryCall(
      () => repositories.quizApi.getQuizByKey(quizKey),
      'Quiz fetch error',
      'Failed to fetch quiz'
    );
  }

  /**
   * Get questions for a specific quiz with options and explanations
   */
  static async getQuizQuestions(
    quizId: string
  ): Promise<{ data: QuizQuestion[] | null; error: string | null }> {
    return handleRepositoryCall(
      () => repositories.quizApi.getQuizQuestions(quizId),
      'Quiz questions fetch error',
      'Failed to fetch quiz questions',
      []
    );
  }

  /**
   * Save a quiz attempt with user answers
   */
  static async saveQuizAttempt(
    userId: string,
    quizId: string,
    results: QuizResults
  ): Promise<{ success: boolean; error: string | null }> {
    console.log('Saving quiz attempt:', { userId, quizId, results });
    
    return handleRepositoryCallSuccess(
      () => repositories.quizApi.saveQuizAttempt(userId, quizId, results),
      'Quiz attempt save error',
      'Failed to save quiz attempt'
    );
  }


  /**
   * Get user statistics across all attempts
   */
  static async getUserStats(
    userId: string
  ): Promise<{ data: Record<string, unknown> | null; error: string | null }> {
    return handleRepositoryCall(
      () => repositories.quizApi.getUserStats(userId),
      'User stats fetch error',
      'Failed to fetch user stats'
    );
  }

  /**
   * Get quiz by ID with full details for results screen
   */
  static async getQuizForResults(quizId: string): Promise<ServiceResponse<any>> {
    return handleRepositoryCall(
      () => repositories.quizApi.getQuizForResults(quizId),
      'Quiz fetch error',
      'Failed to fetch quiz'
    );
  }

  /**
   * Get quiz attempt details with questions and user answers
   */
  static async getQuizAttemptDetails(attemptId: string): Promise<{
    data: {
      questions: QuizQuestion[];
      userAnswers: Record<string, string>;
      attempt: QuizAttempt;
    } | null;
    error: string | null
  }> {
    return handleRepositoryCall(
      () => repositories.quizApi.getQuizAttemptDetails(attemptId),
      'Quiz attempt details fetch error',
      'Failed to fetch quiz attempt details'
    );
  }

  /**
   * Search quizzes with filters including subcategory support and pagination
   */
  static async searchQuizzes(params: {
    searchTerm?: string;
    categoryKey?: string;
    subCategoryKey?: string;
    levelKey?: string;
    difficulty?: number;
    page?: number;
    pageSize?: number;
  }): Promise<{ data: Quiz[] | null; error: string | null; totalCount?: number; totalPages?: number }> {
    try {
      const result = await repositories.quizApi.searchQuizzes(params);
      
      if (!result.success) {
        console.error('Quiz search error:', result.error);
        return { 
          data: null, 
          error: result.error?.message || 'Failed to search quizzes' 
        };
      }

      return {
        data: result.data?.data || null,
        error: null,
        totalCount: result.data?.totalCount,
        totalPages: result.data?.totalPages
      };
    } catch (error) {
      console.error('Quiz search error:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      };
    }
  }




  /**
   * Get user profile data from public.users table
   */
  static async getUserProfile(userId: string): Promise<{ data: User | null; error: string | null }> {
    return handleRepositoryCall(
      () => repositories.quizApi.getUserProfile(userId),
      'User profile fetch error',
      'Failed to fetch user profile'
    );
  }

  /**
   * Update user profile data in public.users table
   */
  static async updateUserProfile(
    userId: string,
    profileData: {
      first_name?: string;
      last_name?: string;
      level_id?: string;
    }
  ): Promise<{ data: User | null; error: string | null }> {
    return handleRepositoryCall(
      () => repositories.quizApi.updateUserProfile(userId, profileData),
      'User profile update error',
      'Failed to update user profile'
    );
  }

  /**
   * Get user quiz attempts with quiz details
   */
  static async getUserQuizAttempts(userId: string): Promise<ServiceResponse<DbQuizAttempt[]>> {
    return handleRepositoryCall(
      () => repositories.quizApi.getUserQuizAttempts(userId),
      'User quiz attempts fetch error',
      'Failed to fetch user quiz attempts',
      []
    );
  }
}
