import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from '@/context/AuthContext';
import { ThemeProvider } from '@/context/ThemeContext';
import { LanguageProvider } from '@/context/LanguageContext';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuth } from '@/context/AuthContext';
import ErrorBoundary from '@/components/ErrorBoundary';
import { toast } from '@/components/ui/use-toast';
// Screen imports
import LandingScreen from '@/screens/LandingScreen/LandingScreen';
import LoginScreen from '@/screens/LoginScreen/LoginScreen';
import ForgotPasswordScreen from '@/screens/ForgotPasswordScreen/ForgotPasswordScreen';
import HomeScreen from '@/screens/HomeScreen/HomeScreen';
import AssessmentScreen from '@/screens/AssessmentScreen/AssessmentScreen';
import AssessmentDetailScreen from '@/screens/AssessmentDetailScreen/AssessmentDetailScreen';
import ProgressScreen from '@/screens/ProgressScreen/ProgressScreen';
import QuizModeSelectionScreen from '@/screens/QuizModeSelectionScreen/QuizModeSelectionScreen';
import QuizScreen from '@/screens/QuizScreen/QuizScreen';

import ProfileScreen from '@/screens/ProfileScreen/ProfileScreen';
import AboutScreen from '@/screens/AboutScreen/AboutScreen';
import TermsScreen from '@/screens/TermsScreen/TermsScreen';
import PrivacyScreen from '@/screens/PrivacyScreen/PrivacyScreen';
import NotFoundScreen from '@/screens/NotFoundScreen/NotFoundScreen';
import QuizResultsScreen from '@/screens/QuizResultsScreen/QuizResultsScreen';

// Enhanced QueryClient with global error handling and retry logic
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Retry failed requests up to 3 times
      retry: (failureCount, error: Error & { status?: number }) => {
        // Don't retry on 4xx errors (client errors)
        if ((error as any)?.status >= 400 && (error as any)?.status < 500) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      // Retry with exponential backoff
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Stale time of 5 minutes
      staleTime: 5 * 60 * 1000,
      // Cache time of 10 minutes
      gcTime: 10 * 60 * 1000,
      // Refetch on window focus only if data is stale
      refetchOnWindowFocus: false,
    },
    mutations: {
      // Global mutation error handler
      onError: (error: Error) => {
        console.error('React Query Mutation Error:', error);
        
        // Don't show toast for validation errors (handled by forms)
        if ((error as any)?.status === 400 || (error as any)?.status === 422) {
          return;
        }
        
        toast({
          title: "Operation Failed",
          description: error?.message || "Something went wrong. Please try again.",
          variant: "destructive",
        });
      }
    }
  }
});

const AppRoutes: React.FC = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <Routes>
        {/* Public Routes */}
        <Route 
          path="/signin" 
          element={user ? <Navigate to="/home" replace /> : <LoginScreen />} 
        />
        <Route 
          path="/signup" 
          element={user ? <Navigate to="/home" replace /> : <LoginScreen />} 
        />
        <Route path="/about" element={<AboutScreen />} />
        <Route path="/terms" element={<TermsScreen />} />
        <Route path="/privacy" element={<PrivacyScreen />} />
        
        {/* Protected Routes */}
        <Route
          path="/home"
          element={user ? <HomeScreen /> : <Navigate to="/signin" replace />}
        />
        <Route
          path="/assessment"
          element={user ? <AssessmentScreen /> : <Navigate to="/signin" replace />}
        />
        <Route
          path="/quiz/:quizId/details"
          element={user ? <AssessmentDetailScreen /> : <Navigate to="/signin" replace />}
        />
        <Route
          path="/pre-quiz/:quizId"
          element={user ? <QuizModeSelectionScreen /> : <Navigate to="/signin" replace />}
        />
        <Route
          path="/quiz/:quizId"
          element={user ? <QuizScreen /> : <Navigate to="/signin" replace />}
        />
        <Route
          path="/quiz-results"
          element={user ? <QuizResultsScreen /> : <Navigate to="/signin" replace />}
        />
        <Route
          path="/progress"
          element={user ? <ProgressScreen /> : <Navigate to="/signin" replace />}
        />
        <Route
          path="/profile"
          element={user ? <ProfileScreen /> : <Navigate to="/signin" replace />}
        />
        
        {/* Landing and Forgot Password Routes */}
        <Route 
          path="/" 
          element={user ? <Navigate to="/home" replace /> : <LandingScreen />} 
        />
        <Route 
          path="/forgot-password" 
          element={user ? <Navigate to="/home" replace /> : <ForgotPasswordScreen />} 
        />

        
        {/* 404 Route */}
        <Route 
          path="*" 
          element={<NotFoundScreen />} 
        />
      </Routes>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        // Log critical errors to console in development
        console.error('Global Error Boundary:', error, errorInfo);
        
        // In production, you would send this to your error tracking service
        if (process.env.NODE_ENV === 'production') {
          // TODO: Send to error tracking service (Sentry, LogRocket, etc.)
        }
      }}
    >
      <ThemeProvider>
        <LanguageProvider>
          <AuthProvider>
            <QueryClientProvider client={queryClient}>
              <TooltipProvider>
                <Toaster />
                <Sonner />
                <ErrorBoundary
                  fallback={
                    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
                      <div className="text-center p-8">
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                          Something went wrong with the app
                        </h2>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                          Please refresh the page to try again.
                        </p>
                        <button
                          onClick={() => window.location.reload()}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          Refresh Page
                        </button>
                      </div>
                    </div>
                  }
                >
                  <AppRoutes />
                </ErrorBoundary>
              </TooltipProvider>
            </QueryClientProvider>
          </AuthProvider>
        </LanguageProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App; 