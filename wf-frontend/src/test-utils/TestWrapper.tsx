import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { LanguageProvider } from '@/context/LanguageContext';
import { AuthProvider } from '@/context/AuthContext';
import { ThemeProvider } from '@/context/ThemeContext';

interface TestWrapperProps {
  children: React.ReactNode;
  includeRouter?: boolean;
  includeAuth?: boolean;
  includeLanguage?: boolean;
  includeTheme?: boolean;
  includeQuery?: boolean;
}

/**
 * Test wrapper component that provides necessary context providers for testing
 */
export const TestWrapper: React.FC<TestWrapperProps> = ({
  children,
  includeRouter = false,
  includeAuth = false,
  includeLanguage = true,
  includeTheme = false,
  includeQuery = false,
}) => {
  let wrappedChildren = children;

  // Create a test QueryClient if needed
  const queryClient = includeQuery ? new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  }) : null;

  // Wrap with ThemeProvider if needed
  if (includeTheme) {
    wrappedChildren = (
      <ThemeProvider>
        {wrappedChildren}
      </ThemeProvider>
    );
  }

  // Wrap with LanguageProvider if needed
  if (includeLanguage) {
    wrappedChildren = (
      <LanguageProvider>
        {wrappedChildren}
      </LanguageProvider>
    );
  }

  // Wrap with AuthProvider if needed
  if (includeAuth) {
    wrappedChildren = (
      <AuthProvider>
        {wrappedChildren}
      </AuthProvider>
    );
  }

  // Wrap with Router if needed
  if (includeRouter) {
    wrappedChildren = (
      <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        {wrappedChildren}
      </BrowserRouter>
    );
  }

  // Wrap with QueryClientProvider if needed
  if (includeQuery && queryClient) {
    wrappedChildren = (
      <QueryClientProvider client={queryClient}>
        {wrappedChildren}
      </QueryClientProvider>
    );
  }

  return <>{wrappedChildren}</>;
};

/**
 * Wrapper with all providers for components that need everything
 */
export const FullTestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <TestWrapper includeRouter includeAuth includeLanguage includeTheme includeQuery>
    {children}
  </TestWrapper>
);

/**
 * Wrapper with Router, Language, and Theme providers for layout components
 */
export const RouterLanguageWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <TestWrapper includeRouter includeLanguage includeTheme>
    {children}
  </TestWrapper>
);

/**
 * Wrapper with just Language provider for simple components
 */
export const LanguageWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <TestWrapper includeLanguage>
    {children}
  </TestWrapper>
);

/**
 * Wrapper with Language and Theme providers for components that need both
 */
export const LanguageThemeWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <TestWrapper includeLanguage includeTheme>
    {children}
  </TestWrapper>
);
