import React, { createContext, useContext, useState, useEffect } from 'react';
import { translationService, type Language, type TranslateFunction } from '@/services/translationService';
import { APP_CONSTANTS } from 'wf-shared/constants';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: TranslateFunction;
  getRaw: (key: string) => unknown;
  isLoading: boolean;
  availableLanguages: Language[];
  getLanguageDisplayName: (language: Language) => string;
  getLanguageFlag: (language: Language) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: React.ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>(() => {
    // Get language from localStorage or default to 'en'
    const savedLanguage = localStorage.getItem(APP_CONSTANTS.STORAGE_KEYS.LANGUAGE) as Language;
    return savedLanguage && translationService.getAvailableLanguages().includes(savedLanguage)
      ? savedLanguage
      : 'en';
  });

  const [isLoading, setIsLoading] = useState(true);

  // Initialize translations on mount and language change
  useEffect(() => {
    const initializeTranslations = async () => {
      setIsLoading(true);
      try {
        // Set the current language in the service
        translationService.setLanguage(language);

        // Load translations for the current language
        await translationService.loadTranslations(language);

        // Preload other languages in the background
        const otherLanguages = translationService.getAvailableLanguages().filter(lang => lang !== language);
        translationService.preloadLanguages(otherLanguages).catch(console.warn);
      } catch (error) {
        console.error('Failed to initialize translations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeTranslations();
  }, [language]);

  const setLanguage = async (newLanguage: Language) => {
    if (newLanguage === language) return;

    setIsLoading(true);
    try {
      // Load translations for the new language if not already loaded
      if (!translationService.isLoaded(newLanguage)) {
        await translationService.loadTranslations(newLanguage);
      }

      // Update the language
      setLanguageState(newLanguage);
      translationService.setLanguage(newLanguage);
      localStorage.setItem(APP_CONSTANTS.STORAGE_KEYS.LANGUAGE, newLanguage);
    } catch (error) {
      console.error('Failed to change language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const t: TranslateFunction = (key: string, fallback?: string) => {
    return translationService.translate(key, language, fallback);
  };

  const getRaw = (key: string) => {
    return translationService.getRaw(key, language);
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
    getRaw,
    isLoading,
    availableLanguages: translationService.getAvailableLanguages(),
    getLanguageDisplayName: translationService.getLanguageDisplayName,
    getLanguageFlag: translationService.getLanguageFlag,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// Export types for backward compatibility
export type { Language };
