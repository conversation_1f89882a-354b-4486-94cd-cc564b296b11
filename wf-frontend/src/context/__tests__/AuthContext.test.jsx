import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor, act } from '@testing-library/react';
import { AuthProvider, useAuth } from '../AuthContext';

// Mock the auth service
vi.mock('../../services/authService', () => ({
  signIn: vi.fn(),
  signUp: vi.fn(),
  signOut: vi.fn(),
  getCurrentSession: vi.fn(),
  onAuthStateChange: vi.fn(),
  isEmailConfirmed: vi.fn(),
  resendConfirmation: vi.fn()
}));

import * as authService from '../../services/authService';

// Test component to use the auth context
function TestComponent() {
  const auth = useAuth();
  
  return (
    <div>
      <div data-testid="user">{auth.user ? auth.user.email : 'No user'}</div>
      <div data-testid="authenticated">{auth.isAuthenticated ? 'Yes' : 'No'}</div>
      <div data-testid="loading">{auth.loading ? 'Loading' : 'Not loading'}</div>
      <button onClick={() => auth.signIn('<EMAIL>', 'password')}>Sign In</button>
      <button onClick={() => auth.signUp('<EMAIL>', 'password')}>Sign Up</button>
      <button onClick={() => auth.signOut()}>Sign Out</button>
    </div>
  );
}

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementations
    authService.getCurrentSession.mockResolvedValue(null);
    authService.onAuthStateChange.mockReturnValue({ 
      data: { 
        subscription: { 
          unsubscribe: vi.fn() 
        } 
      } 
    });
  });

  it('should provide initial auth state', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Initially loading should be true
    expect(screen.getByTestId('loading')).toHaveTextContent('Loading');
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });

    expect(screen.getByTestId('user')).toHaveTextContent('No user');
    expect(screen.getByTestId('authenticated')).toHaveTextContent('No');
  });

  it('should set user when getCurrentSession returns a user', async () => {
    const mockUser = { id: '123', email: '<EMAIL>' };
    authService.getCurrentSession.mockResolvedValue(mockUser);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('Yes');
    });
  });

  it('should handle auth state changes', async () => {
    let authStateCallback;
    authService.onAuthStateChange.mockImplementation((callback) => {
      authStateCallback = callback;
      return { 
        data: { 
          subscription: { 
            unsubscribe: vi.fn() 
          } 
        } 
      };
    });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });

    // Simulate auth state change with user
    const mockUser = { id: '123', email: '<EMAIL>' };
    await act(async () => {
      authStateCallback('SIGNED_IN', { user: mockUser });
    });

    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('Yes');
    });

    // Simulate sign out
    await act(async () => {
      authStateCallback('SIGNED_OUT', null);
    });

    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('No user');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('No');
    });
  });

  it('should call signIn service when signIn is called', async () => {
    authService.signIn.mockResolvedValue({ user: { email: '<EMAIL>' } });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const signInButton = screen.getByText('Sign In');
    await act(async () => {
      signInButton.click();
    });

    await waitFor(() => {
      expect(authService.signIn).toHaveBeenCalledWith('<EMAIL>', 'password');
    });
  });

  it('should call signUp service when signUp is called', async () => {
    authService.signUp.mockResolvedValue({ user: { email: '<EMAIL>' } });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const signUpButton = screen.getByText('Sign Up');
    await act(async () => {
      signUpButton.click();
    });

    await waitFor(() => {
      expect(authService.signUp).toHaveBeenCalledWith('<EMAIL>', 'password', undefined);
    });
  });

  it('should call signOut service when signOut is called', async () => {
    authService.signOut.mockResolvedValue();

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const signOutButton = screen.getByText('Sign Out');
    await act(async () => {
      signOutButton.click();
    });

    await waitFor(() => {
      expect(authService.signOut).toHaveBeenCalled();
    });
  });

  it('should handle service errors gracefully', async () => {
    const mockError = new Error('Service error');
    authService.getCurrentSession.mockRejectedValue(mockError);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      expect(screen.getByTestId('user')).toHaveTextContent('No user');
    });
  });
}); 