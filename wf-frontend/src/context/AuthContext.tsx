import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { getCurrentSession, onAuthStateChange } from '@/services/authService';
import * as authService from '@/services/authService';
import type { User } from '@/types';
import type { AuthContextType } from '@/types';
import { toast } from '@/components/ui/use-toast';

// Create the context with proper typing
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Props interface for the AuthProvider
interface AuthProviderProps {
  children: ReactNode;
}

/**
 * AuthProvider component that manages authentication state and provides auth functions
 * to child components through React Context.
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async (): Promise<void> => {
      try {
        const currentUser = await getCurrentSession();
        setUser(currentUser);
      } catch (error) {
        console.error('Error getting initial session:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = onAuthStateChange((event, session) => {
      const supabaseSession = session as any; // Cast to access Supabase session properties
      console.log('Auth state changed:', event, supabaseSession?.user?.email);

      // Create a proper User object from Supabase auth user
      if (supabaseSession?.user) {
        const user: User = {
          id: supabaseSession.user.id,
          email: supabaseSession.user.email || '',
          first_name: supabaseSession.user.user_metadata?.first_name || '',
          last_name: supabaseSession.user.user_metadata?.last_name || '',
          level_id: supabaseSession.user.user_metadata?.level_id || '',
          role: supabaseSession.user.user_metadata?.role || null,
          created_at: supabaseSession.user.created_at || new Date().toISOString(),
          updated_at: supabaseSession.user.updated_at || new Date().toISOString()
        };
        setUser(user);
      } else {
        setUser(null);
      }

      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  /**
   * Sign in a user with email and password
   */
  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      const result = await authService.signIn(email, password);
      if (result.user) {
        setUser(result.user);
        toast({
          title: "Welcome back!",
          description: "You have successfully signed in.",
          variant: "default",
        });
      } else if (result.error) {
        toast({
          title: "Sign In Failed",
          description: result.error,
          variant: "destructive",
        });
      }
      return result;
    } catch (error) {
      console.error('Sign in error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
      toast({
        title: "Sign In Error",
        description: errorMessage,
        variant: "destructive",
      });
      return { user: null, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Sign up a new user with email, password, and user data
   */
  const signUp = async (email: string, password: string, userData: { first_name: string; last_name: string; level_id: string }) => {
    setLoading(true);
    try {
      const result = await authService.signUp(email, password, userData);
      if (result.user) {
        setUser(result.user);
        toast({
          title: "Account Created!",
          description: "Welcome to Word Formation! Your account has been created successfully.",
          variant: "default",
        });
      } else if (result.error) {
        toast({
          title: "Sign Up Failed",
          description: result.error,
          variant: "destructive",
        });
      }
      return result;
    } catch (error) {
      console.error('Sign up error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Sign up failed';
      toast({
        title: "Sign Up Error",
        description: errorMessage,
        variant: "destructive",
      });
      return { user: null, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Sign out the current user
   */
  const signOut = async (): Promise<void> => {
    setLoading(true);
    try {
      await authService.signOut();
      setUser(null);
      toast({
        title: "Signed Out",
        description: "You have been successfully signed out.",
        variant: "default",
      });
    } catch (error) {
      console.error('Sign out error:', error);
      toast({
        title: "Sign Out Error",
        description: "Failed to sign out. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Context value with proper typing
  const value: AuthContextType = {
    user,
    loading,
    isAuthenticated: !!user,
    signIn,
    signUp,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Custom hook to use the AuthContext
 * Throws an error if used outside of AuthProvider
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

export default AuthContext; 