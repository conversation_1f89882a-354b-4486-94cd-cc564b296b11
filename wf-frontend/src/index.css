@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base responsive styles */
@layer base {
  html {
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
  }
  
  body {
    /* Improve text rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Prevent horizontal scroll */
    overflow-x: hidden;
  }
  
  /* Ensure touch targets are at least 44px for accessibility */
  button, 
  [role="button"], 
  input[type="submit"], 
  input[type="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

/* Custom responsive utilities */
@layer utilities {
  /* Safe area padding for mobile devices with notches */
  .safe-area-padding {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
  
  /* Responsive text utilities */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }
  
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }
  
  .text-responsive-base {
    @apply text-base sm:text-lg;
  }
  
  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }
  
  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl;
  }
  
  /* Responsive spacing utilities */
  .spacing-responsive-sm {
    @apply p-3 sm:p-4 md:p-6;
  }
  
  .spacing-responsive-md {
    @apply p-4 sm:p-6 md:p-8;
  }
  
  .spacing-responsive-lg {
    @apply p-6 sm:p-8 md:p-12;
  }
  
  /* Focus styles for better accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
  
  /* Interactive element styles */
  .interactive-scale {
    @apply transition-transform duration-150 active:scale-95;
  }
}

/* Component-specific responsive styles */
@layer components {
  /* Responsive button styles */
  .btn-responsive {
    @apply px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base font-medium rounded-lg transition-all duration-200 focus-ring interactive-scale;
  }
  
  /* Responsive card styles */
  .card-responsive {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 spacing-responsive-sm;
  }
  
  /* Responsive form input styles */
  .input-responsive {
    @apply w-full px-3 py-2 sm:px-4 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-lg focus-ring;
  }
}
