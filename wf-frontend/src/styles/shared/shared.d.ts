/**
 * TypeScript declarations for shared CSS modules
 * This file provides type safety for all shared CSS module imports
 */

declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}

// Shared module type definitions
export interface SharedButtonStyles {
  buttonBase: string;
  buttonPrimary: string;
  buttonSecondary: string;
  buttonDanger: string;
  buttonOutline: string;
  buttonGhost: string;
  buttonSmall: string;
  buttonLarge: string;
  fullWidth: string;
  loading: string;
  disabled: string;
}

export interface SharedContainerStyles {
  pageContainer: string;
  centeredContainer: string;
  fullHeightContainer: string;
  compactContainer: string;
  wideContainer: string;
  sectionContainer: string;
}

export interface SharedTypographyStyles {
  pageTitle: string;
  sectionTitle: string;
  subsectionTitle: string;
  subtitle: string;
  description: string;
  bodyText: string;
  captionText: string;
  linkText: string;
  mutedText: string;
}

export interface SharedCardStyles {
  cardBase: string;
  cardCentered: string;
  cardCompact: string;
  cardElevated: string;
  cardBordered: string;
  cardHoverable: string;
  cardHeader: string;
  cardContent: string;
  cardFooter: string;
}

export interface SharedFormStyles {
  formContainer: string;
  formSection: string;
  formGroup: string;
  label: string;
  labelRequired: string;
  input: string;
  inputError: string;
  inputSuccess: string;
  textarea: string;
  select: string;
  checkbox: string;
  radio: string;
  fieldError: string;
  fieldSuccess: string;
  helpText: string;
}

export interface SharedStateStyles {
  loading: string;
  loadingSpinner: string;
  loadingOverlay: string;
  empty: string;
  emptyIcon: string;
  emptyMessage: string;
  error: string;
  errorIcon: string;
  errorMessage: string;
  success: string;
  successIcon: string;
  successMessage: string;
  warning: string;
  warningIcon: string;
  warningMessage: string;
}

export interface SharedLayoutStyles {
  gridTwoColumns: string;
  gridThreeColumns: string;
  gridFourColumns: string;
  gridResponsive: string;
  flexRow: string;
  flexColumn: string;
  flexCenter: string;
  flexBetween: string;
  flexStart: string;
  flexEnd: string;
  spacingSmall: string;
  spacingMedium: string;
  spacingLarge: string;
}

export interface SharedAnimationStyles {
  fadeIn: string;
  fadeOut: string;
  slideIn: string;
  slideOut: string;
  scaleIn: string;
  scaleOut: string;
  spin: string;
  pulse: string;
  bounce: string;
}

// Combined interface for all shared styles
export interface SharedStyles {
  buttons: SharedButtonStyles;
  containers: SharedContainerStyles;
  typography: SharedTypographyStyles;
  cards: SharedCardStyles;
  forms: SharedFormStyles;
  states: SharedStateStyles;
  layouts: SharedLayoutStyles;
  animations: SharedAnimationStyles;
}