/**
 * Centralized export system for all shared CSS modules
 * This file provides a single import point for all shared styles
 */

// Import all shared CSS modules
import buttonsStyles from './components/buttons.module.css';
import cardsStyles from './components/cards.module.css';
import formsStyles from './components/forms.module.css';
import typographyStyles from './components/typography.module.css';
import navigationStyles from './components/navigation.module.css';

import containersStyles from './layouts/containers.module.css';
import gridsStyles from './layouts/grids.module.css';
import sectionsStyles from './layouts/sections.module.css';

import loadingStyles from './states/loading.module.css';
import feedbackStyles from './states/feedback.module.css';
import interactiveStyles from './states/interactive.module.css';

import transitionsStyles from './animations/transitions.module.css';
import keyframesStyles from './animations/keyframes.module.css';

// Export individual module collections for direct import
export { buttonsStyles };
export { cardsStyles };
export { formsStyles };
export { typographyStyles };
export { navigationStyles };
export { containersStyles };
export { gridsStyles };
export { sectionsStyles };
export { loadingStyles };
export { feedbackStyles };
export { interactiveStyles };
export { transitionsStyles };
export { keyframesStyles };

// Export grouped collections for organized imports
export const components = {
  buttons: buttonsStyles,
  cards: cardsStyles,
  forms: formsStyles,
  typography: typographyStyles,
  navigation: navigationStyles,
};

export const layouts = {
  containers: containersStyles,
  grids: gridsStyles,
  sections: sectionsStyles,
};

export const states = {
  loading: loadingStyles,
  feedback: feedbackStyles,
  interactive: interactiveStyles,
};

export const animations = {
  transitions: transitionsStyles,
  keyframes: keyframesStyles,
};

// Export complete shared styles object
export const sharedStyles = {
  components,
  layouts,
  states,
  animations,
};

// Export default for convenient access
export default sharedStyles;

/**
 * Usage Examples:
 * 
 * // Import specific module
 * import { buttonsStyles } from '@/styles/shared';
 * 
 * // Import grouped collections
 * import { components, layouts } from '@/styles/shared';
 * 
 * // Import everything
 * import sharedStyles from '@/styles/shared';
 * 
 * // Use in component
 * <button className={buttonsStyles.buttonPrimary}>Click me</button>
 * <div className={components.buttons.buttonPrimary}>Click me</div>
 * <div className={sharedStyles.components.buttons.buttonPrimary}>Click me</div>
 */