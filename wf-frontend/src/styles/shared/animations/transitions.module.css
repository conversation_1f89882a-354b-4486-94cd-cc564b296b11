/**
 * Shared Transitions Module - Phase 5A Implementation
 * Standardized transition patterns for consistent animations across the application
 * 
 * Usage:
 * import { transitionQuick, transitionSmooth, transitionSlow } from '@/styles/shared/animations/transitions.module.css';
 * 
 * Features:
 * - Standardized timing functions and durations
 * - Interactive state transitions (hover, focus, active)
 * - UI element transitions (colors, opacity, transforms)
 * - Performance-optimized transitions
 */

/* ==========================================================================
   Base Transition Durations
   ========================================================================== */

.transitionQuick {
  transition: all 0.15s ease;
}

.transitionNormal {
  transition: all 0.2s ease;
}

.transitionSmooth {
  transition: all 0.3s ease;
}

.transitionSlow {
  transition: all 0.5s ease;
}

/* ==========================================================================
   Specific Property Transitions
   ========================================================================== */

/* Color Transitions */
.transitionColors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.transitionColorsQuick {
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease;
}

.transitionColorsSlow {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* Opacity Transitions */
.transitionOpacity {
  transition: opacity 0.2s ease;
}

.transitionOpacityQuick {
  transition: opacity 0.15s ease;
}

.transitionOpacitySlow {
  transition: opacity 0.3s ease;
}

/* Transform Transitions */
.transitionTransform {
  transition: transform 0.2s ease;
}

.transitionTransformQuick {
  transition: transform 0.15s ease;
}

.transitionTransformSmooth {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Shadow Transitions */
.transitionShadow {
  transition: box-shadow 0.2s ease;
}

.transitionShadowQuick {
  transition: box-shadow 0.15s ease;
}

.transitionShadowSlow {
  transition: box-shadow 0.3s ease;
}

/* ==========================================================================
   Interactive States
   ========================================================================== */

/* Hover Scale Effects */
.hoverScale {
  composes: transitionTransform;
  transform: scale(1);
}

.hoverScale:hover {
  transform: scale(1.02);
}

.hoverScaleLarge {
  composes: transitionTransform;
  transform: scale(1);
}

.hoverScaleLarge:hover {
  transform: scale(1.05);
}

.hoverScaleSmall {
  composes: transitionTransform;
  transform: scale(1);
}

.hoverScaleSmall:hover {
  transform: scale(1.01);
}

/* Active Scale Effects */
.activeScale {
  composes: transitionTransform;
}

.activeScale:active {
  transform: scale(0.95);
}

.activeScaleSmall {
  composes: transitionTransform;
}

.activeScaleSmall:active {
  transform: scale(0.98);
}

/* Focus Ring Transitions */
.focusRing {
  transition: box-shadow 0.15s ease, border-color 0.15s ease;
  outline: none;
}

.focusRing:focus-visible {
  box-shadow: 0 0 0 2px theme('colors.blue.500');
  border-color: theme('colors.blue.500');
}

.focusRingLarge {
  transition: box-shadow 0.15s ease, border-color 0.15s ease;
  outline: none;
}

.focusRingLarge:focus-visible {
  box-shadow: 0 0 0 3px theme('colors.blue.500');
  border-color: theme('colors.blue.500');
}

/* Dark mode focus rings */
:global(.dark) .focusRing:focus-visible {
  box-shadow: 0 0 0 2px theme('colors.blue.400');
  border-color: theme('colors.blue.400');
}

:global(.dark) .focusRingLarge:focus-visible {
  box-shadow: 0 0 0 3px theme('colors.blue.400');
  border-color: theme('colors.blue.400');
}

/* ==========================================================================
   UI Element Transitions
   ========================================================================== */

/* Button Transitions */
.buttonTransition {
  composes: transitionColors hoverScale activeScale focusRing;
}

.buttonTransitionQuick {
  composes: transitionColorsQuick hoverScaleSmall activeScaleSmall focusRing;
}

.buttonTransitionSmooth {
  composes: transitionColorsSlow hoverScaleLarge activeScale focusRingLarge;
}

/* Card Transitions */
.cardTransition {
  composes: transitionShadow hoverScaleSmall;
}

.cardTransitionInteractive {
  composes: transitionShadow transitionTransform;
  transform: translateY(0px);
}

.cardTransitionInteractive:hover {
  transform: translateY(-2px);
  box-shadow: theme('boxShadow.lg');
}

/* Link Transitions */
.linkTransition {
  composes: transitionColors;
  text-decoration: none;
}

.linkTransition:hover {
  text-decoration: underline;
}

/* Input Transitions */
.inputTransition {
  composes: transitionColors focusRing;
}

.inputTransitionShadow {
  composes: transitionColors transitionShadow focusRing;
}

/* ==========================================================================
   Loading State Transitions
   ========================================================================== */

/* Progress Bar Transitions */
.progressTransition {
  transition: width 0.3s ease;
}

.progressTransitionSlow {
  transition: width 0.5s ease;
}

/* Loading Spinner Transition */
.spinnerTransition {
  transition: opacity 0.2s ease;
}

/* ==========================================================================
   Advanced Easing Functions
   ========================================================================== */

/* Spring Animations */
.easeSpring {
  transition-timing-function: cubic-bezier(0.34, 1.56, 0.64, 1);
}

.easeBackOut {
  transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.easeBackIn {
  transition-timing-function: cubic-bezier(0.6, -0.28, 0.735, 0.045);
}

/* Smooth Curves */
.easeInOut {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.easeOut {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.easeIn {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

/* ==========================================================================
   Mobile-Optimized Transitions
   ========================================================================== */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transitionQuick,
  .transitionNormal,
  .transitionSmooth,
  .transitionSlow,
  .transitionColors,
  .transitionColorsQuick,
  .transitionColorsSlow,
  .transitionOpacity,
  .transitionOpacityQuick,
  .transitionOpacitySlow,
  .transitionTransform,
  .transitionTransformQuick,
  .transitionTransformSmooth,
  .transitionShadow,
  .transitionShadowQuick,
  .transitionShadowSlow,
  .progressTransition,
  .progressTransitionSlow,
  .spinnerTransition {
    transition: none !important;
  }
  
  .hoverScale:hover,
  .hoverScaleLarge:hover,
  .hoverScaleSmall:hover {
    transform: none !important;
  }
  
  .activeScale:active,
  .activeScaleSmall:active {
    transform: none !important;
  }
  
  .cardTransitionInteractive:hover {
    transform: none !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .hoverScale:hover,
  .hoverScaleLarge:hover,
  .hoverScaleSmall:hover {
    transform: none;
  }
  
  .cardTransitionInteractive:hover {
    transform: none;
  }
}