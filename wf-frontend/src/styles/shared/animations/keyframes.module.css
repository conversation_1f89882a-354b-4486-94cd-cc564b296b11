/**
 * Shared Keyframes Module - Phase 5A Implementation
 * Reusable animation keyframes for consistent motion design across the application
 * 
 * Usage:
 * import { spin, fadeIn, slideUp, bounce } from '@/styles/shared/animations/keyframes.module.css';
 * 
 * Features:
 * - Loading spinner animations
 * - Enter/exit animations
 * - Interactive feedback animations
 * - Reduced motion support
 */

/* ==========================================================================
   Loading Animations
   ========================================================================== */

/* Spinner Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

.spinSlow {
  animation: spin 2s linear infinite;
}

.spinFast {
  animation: spin 0.5s linear infinite;
}

/* Pulse Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.pulseFast {
  animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.pulseSlow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Shimmer Animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  animation: shimmer 1.5s ease-in-out infinite;
  background: linear-gradient(
    90deg,
    theme('colors.gray.200') 0px,
    theme('colors.gray.300') 40px,
    theme('colors.gray.200') 80px
  );
  background-size: 200px 100%;
}

:global(.dark) .shimmer {
  background: linear-gradient(
    90deg,
    theme('colors.gray.700') 0px,
    theme('colors.gray.600') 40px,
    theme('colors.gray.700') 80px
  );
  background-size: 200px 100%;
}

/* ==========================================================================
   Enter/Exit Animations
   ========================================================================== */

/* Fade Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.fadeInSlow {
  animation: fadeIn 0.5s ease-out;
}

.fadeInFast {
  animation: fadeIn 0.15s ease-out;
}

.fadeOut {
  animation: fadeOut 0.3s ease-in;
}

.fadeOutSlow {
  animation: fadeOut 0.5s ease-in;
}

.fadeOutFast {
  animation: fadeOut 0.15s ease-in;
}

/* Fade with Transform */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fadeInUp {
  animation: fadeInUp 0.3s ease-out;
}

.fadeInDown {
  animation: fadeInDown 0.3s ease-out;
}

.fadeInLeft {
  animation: fadeInLeft 0.3s ease-out;
}

.fadeInRight {
  animation: fadeInRight 0.3s ease-out;
}

/* Slide Animations */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slideIn {
  animation: slideIn 0.2s ease-out;
}

.slideOut {
  animation: slideOut 0.2s ease-in;
}

.slideUp {
  animation: slideUp 0.3s ease-out;
}

.slideDown {
  animation: slideDown 0.3s ease-out;
}

/* Scale Animations */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);  
  }
}

.scaleIn {
  animation: scaleIn 0.2s ease-out;
}

.scaleOut {
  animation: scaleOut 0.2s ease-in;
}

/* ==========================================================================
   Interactive Feedback Animations
   ========================================================================== */

/* Bounce Animation */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -8px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -1px, 0);
  }
}

.bounce {
  animation: bounce 1s ease-in-out;
}

.bounceShort {
  animation: bounce 0.6s ease-in-out;
}

/* Shake Animation */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

.shake {
  animation: shake 0.5s ease-in-out;
}

/* Wobble Animation */
@keyframes wobble {
  0% {
    transform: translateX(0%);
  }
  15% {
    transform: translateX(-25%) rotate(-5deg);
  }
  30% {
    transform: translateX(20%) rotate(3deg);
  }
  45% {
    transform: translateX(-15%) rotate(-3deg);
  }
  60% {
    transform: translateX(10%) rotate(2deg);
  }
  75% {
    transform: translateX(-5%) rotate(-1deg);
  }
  100% {
    transform: translateX(0%);
  }
}

.wobble {
  animation: wobble 0.8s ease-in-out;
}

/* Ping Animation */
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* ==========================================================================
   Progress & Loading States
   ========================================================================== */

/* Progress Fill Animation */
@keyframes progressFill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width, 100%);
  }
}

.progressFill {
  animation: progressFill 0.3s ease-out;
}

.progressFillSlow {
  animation: progressFill 0.8s ease-out;
}

/* Skeleton Loading */
@keyframes skeletonPulse {
  0% {
    background-color: theme('colors.gray.200');
  }
  50% {
    background-color: theme('colors.gray.300');
  }
  100% {
    background-color: theme('colors.gray.200');
  }
}

.skeletonPulse {
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

:global(.dark) .skeletonPulse {
  animation: skeletonPulseDark 1.5s ease-in-out infinite;
}

@keyframes skeletonPulseDark {
  0% {
    background-color: theme('colors.gray.700');
  }
  50% {
    background-color: theme('colors.gray.600');
  }
  100% {
    background-color: theme('colors.gray.700');
  }
}

/* ==========================================================================
   Success & Error States
   ========================================================================== */

/* Success Check Animation */
@keyframes checkmark {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.checkmark {
  stroke-dasharray: 100;
  animation: checkmark 0.3s ease-in-out;
}

/* Error Cross Animation */
@keyframes errorCross {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.errorCross {
  animation: errorCross 0.3s ease-out;
}

/* ==========================================================================
   Accessibility & Performance
   ========================================================================== */

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .spin,
  .spinSlow,
  .spinFast,
  .pulse,
  .pulseFast,
  .pulseSlow,
  .shimmer,
  .fadeIn,
  .fadeInSlow,
  .fadeInFast,
  .fadeOut,
  .fadeOutSlow,
  .fadeOutFast,
  .fadeInUp,
  .fadeInDown,
  .fadeInLeft,
  .fadeInRight,
  .slideIn,
  .slideOut,
  .slideUp,
  .slideDown,
  .scaleIn,
  .scaleOut,
  .bounce,
  .bounceShort,
  .shake,
  .wobble,
  .ping,
  .progressFill,
  .progressFillSlow,
  .skeletonPulse,
  .checkmark,
  .errorCross {
    animation: none !important;
  }
}

/* Performance optimization for lower-end devices */
@media (prefers-reduced-motion: no-preference) and (max-width: 768px) {
  .bounce,
  .wobble,
  .shake {
    animation-duration: 0.5s;
  }
  
  .fadeInSlow,
  .fadeOutSlow,
  .progressFillSlow {
    animation-duration: 0.3s;
  }
}