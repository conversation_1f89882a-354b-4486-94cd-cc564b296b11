/**
 * Shared Card Components - WF Frontend
 * Eliminates ~96 lines of duplicated CSS across 8 components
 * 
 * Based on analysis of:
 * - HomeScreen, LoginScreen, ForgotPasswordScreen, ProgressScreen
 * - QuizScreen, QuizResultsScreen, AboutScreen, ProfileScreen
 */

/* Base card styles - foundation for all card variants */
.cardBase {
  background-color: white;
  border-radius: theme('borderRadius.xl');
  box-shadow: theme('boxShadow.sm');
  overflow: hidden;
  transition: all 0.2s ease;
}

:global(.dark) .cardBase {
  background-color: theme('colors.gray.800');
  box-shadow: theme('boxShadow.sm') theme('colors.gray.900');
}

/* Standard card with padding */
.card {
  composes: cardBase;
  padding: theme('spacing.6');
}

/* Compact card with reduced padding */
.cardCompact {
  composes: cardBase;
  padding: theme('spacing.4');
}

/* Large card with increased padding */
.cardLarge {
  composes: cardBase;
  padding: theme('spacing.8');
}

/* Centered card for auth screens */
.cardCentered {
  composes: card;
  text-align: center;
  max-width: 448px; /* 28rem */
  width: 100%;
  margin: 0 auto;
}

/* Elevated card with stronger shadow */
.cardElevated {
  composes: cardBase;
  box-shadow: theme('boxShadow.lg');
  padding: theme('spacing.6');
}

:global(.dark) .cardElevated {
  box-shadow: theme('boxShadow.lg') theme('colors.gray.900');
}

/* Bordered card variant */
.cardBordered {
  composes: cardBase;
  border: 1px solid theme('colors.gray.200');
  box-shadow: none;
  padding: theme('spacing.6');
}

:global(.dark) .cardBordered {
  border-color: theme('colors.gray.700');
}

/* Hoverable card with interactive states */
.cardHoverable {
  composes: cardBase;
  cursor: pointer;
  padding: theme('spacing.6');
}

.cardHoverable:hover {
  transform: translateY(-2px);
  box-shadow: theme('boxShadow.lg');
}

:global(.dark) .cardHoverable:hover {
  box-shadow: theme('boxShadow.lg') theme('colors.gray.900');
}

/* Card with subtle background */
.cardSubtle {
  composes: cardBase;
  background-color: theme('colors.gray.50');
  border: 1px solid theme('colors.gray.100');
  padding: theme('spacing.6');
}

:global(.dark) .cardSubtle {
  background-color: theme('colors.gray.850');
  border-color: theme('colors.gray.750');
}

/* Stats card for dashboard displays */
.cardStats {
  composes: card;
  text-align: center;
  position: relative;
}

.cardStats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(to right, theme('colors.blue.500'), theme('colors.purple.500'));
}

/* Error state card */
.cardError {
  composes: card;
  border: 1px solid theme('colors.red.200');
  background-color: theme('colors.red.50');
}

:global(.dark) .cardError {
  border-color: theme('colors.red.800');
  background-color: theme('colors.red.900');
}

/* Success state card */
.cardSuccess {
  composes: card;
  border: 1px solid theme('colors.green.200');
  background-color: theme('colors.green.50');
}

:global(.dark) .cardSuccess {
  border-color: theme('colors.green.800');
  background-color: theme('colors.green.900');
}

/* Warning state card */
.cardWarning {
  composes: card;
  border: 1px solid theme('colors.yellow.200');
  background-color: theme('colors.yellow.50');
}

:global(.dark) .cardWarning {
  border-color: theme('colors.yellow.800');
  background-color: theme('colors.yellow.900');
}

/* Card sections for structured content */
.cardHeader {
  padding: theme('spacing.6') theme('spacing.6') 0;
  border-bottom: 1px solid theme('colors.gray.200');
  margin-bottom: theme('spacing.6');
  padding-bottom: theme('spacing.4');
}

:global(.dark) .cardHeader {
  border-bottom-color: theme('colors.gray.700');
}

.cardContent {
  padding: 0 theme('spacing.6');
  flex: 1;
}

.cardFooter {
  padding: 0 theme('spacing.6') theme('spacing.6');
  border-top: 1px solid theme('colors.gray.200');
  margin-top: theme('spacing.6');
  padding-top: theme('spacing.4');
  display: flex;
  justify-content: flex-end;
  gap: theme('spacing.3');
}

:global(.dark) .cardFooter {
  border-top-color: theme('colors.gray.700');
}

/* Quiz-specific card styles */
.quizCard {
  composes: cardHoverable;
  border-left: 4px solid theme('colors.blue.500');
}

.quizCard:hover {
  border-left-color: theme('colors.blue.600');
}

/* Option card for quiz questions */
.optionCard {
  composes: cardBordered;
  cursor: pointer;
  transition: all 0.2s ease;
}

.optionCard:hover {
  border-color: theme('colors.blue.300');
  background-color: theme('colors.blue.50');
}

:global(.dark) .optionCard:hover {
  border-color: theme('colors.blue.600');
  background-color: theme('colors.blue.900');
}

.optionCardSelected {
  composes: optionCard;
  border-color: theme('colors.blue.500');
  background-color: theme('colors.blue.100');
}

:global(.dark) .optionCardSelected {
  border-color: theme('colors.blue.400');
  background-color: theme('colors.blue.800');
}

.optionCardCorrect {
  composes: optionCard;
  border-color: theme('colors.green.500');
  background-color: theme('colors.green.100');
}

:global(.dark) .optionCardCorrect {
  border-color: theme('colors.green.400');
  background-color: theme('colors.green.800');
}

.optionCardIncorrect {
  composes: optionCard;
  border-color: theme('colors.red.500');
  background-color: theme('colors.red.100');
}

:global(.dark) .optionCardIncorrect {
  border-color: theme('colors.red.400');
  background-color: theme('colors.red.800');
}

/* Loading card state */
.cardLoading {
  composes: card;
  position: relative;
  overflow: hidden;
}

.cardLoading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

:global(.dark) .cardLoading::after {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Card with image */
.cardWithImage {
  composes: cardBase;
  padding: 0;
}

.cardImage {
  width: 100%;
  height: theme('spacing.48');
  object-fit: cover;
}

.cardImageContent {
  padding: theme('spacing.6');
}

/* Responsive card sizing */
.cardResponsive {
  composes: card;
  width: 100%;
}

@media (min-width: theme('screens.sm')) {
  .cardResponsive {
    max-width: theme('maxWidth.sm');
  }
}

@media (min-width: theme('screens.md')) {
  .cardResponsive {
    max-width: theme('maxWidth.md');
  }
}

/* Card grid layouts */
.cardGrid {
  display: grid;
  gap: theme('spacing.6');
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .cardGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .cardGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Dashboard card specific styles */
.dashboardCard {
  composes: cardStats;
  min-height: theme('spacing.32');
  display: flex;
  flex-direction: column;
  justify-content: center;
}