/**
 * Shared Button Components - WF Frontend
 * Eliminates ~180 lines of duplicated CSS across 6 components
 * 
 * Based on analysis of:
 * - LoginScreen, ForgotPasswordScreen, LandingScreen
 * - HomeScreen, ProgressScreen, QuizScreen
 */

/* Base button styles - foundation for all button variants */
.buttonBase {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: theme('spacing.3') theme('spacing.4');
  border-radius: theme('borderRadius.lg');
  font-weight: theme('fontWeight.medium');
  font-size: theme('fontSize.base');
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  outline: none;
}

.buttonBase:focus {
  ring: theme('ringWidth.2') theme('colors.blue.500') theme('ringOpacity.50');
}

.buttonBase:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Primary button variant - main call-to-action */
.buttonPrimary {
  composes: buttonBase;
  background-color: theme('colors.blue.600');
  color: white;
  border-color: theme('colors.blue.600');
}

.buttonPrimary:hover:not(:disabled) {
  background-color: theme('colors.blue.700');
  border-color: theme('colors.blue.700');
  transform: translateY(-1px);
  box-shadow: theme('boxShadow.md');
}

.buttonPrimary:active {
  transform: translateY(0);
  box-shadow: theme('boxShadow.sm');
}

/* Secondary button variant - alternative actions */
.buttonSecondary {
  composes: buttonBase;
  background-color: theme('colors.gray.600');
  color: white;
  border-color: theme('colors.gray.600');
}

.buttonSecondary:hover:not(:disabled) {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.700');
  transform: translateY(-1px);
  box-shadow: theme('boxShadow.md');
}

.buttonSecondary:active {
  transform: translateY(0);
  box-shadow: theme('boxShadow.sm');
}

/* Outline button variant - subtle actions */
.buttonOutline {
  composes: buttonBase;
  background-color: transparent;
  color: theme('colors.blue.600');
  border-color: theme('colors.blue.600');
}

.buttonOutline:hover:not(:disabled) {
  background-color: theme('colors.blue.50');
  color: theme('colors.blue.700');
  border-color: theme('colors.blue.700');
}

:global(.dark) .buttonOutline {
  color: theme('colors.blue.400');
  border-color: theme('colors.blue.400');
}

:global(.dark) .buttonOutline:hover:not(:disabled) {
  background-color: theme('colors.blue.900');
  color: theme('colors.blue.300');
  border-color: theme('colors.blue.300');
}

/* Ghost button variant - minimal styling */
.buttonGhost {
  composes: buttonBase;
  background-color: transparent;
  color: theme('colors.gray.600');
  border-color: transparent;
}

.buttonGhost:hover:not(:disabled) {
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.900');
}

:global(.dark) .buttonGhost {
  color: theme('colors.gray.400');
}

:global(.dark) .buttonGhost:hover:not(:disabled) {
  background-color: theme('colors.gray.800');
  color: theme('colors.gray.200');
}

/* Danger button variant - destructive actions */
.buttonDanger {
  composes: buttonBase;
  background-color: theme('colors.red.600');
  color: white;
  border-color: theme('colors.red.600');
}

.buttonDanger:hover:not(:disabled) {
  background-color: theme('colors.red.700');
  border-color: theme('colors.red.700');
  transform: translateY(-1px);
  box-shadow: theme('boxShadow.md');
}

.buttonDanger:active {
  transform: translateY(0);
  box-shadow: theme('boxShadow.sm');
}

/* Success button variant - positive actions */
.buttonSuccess {
  composes: buttonBase;
  background-color: theme('colors.green.600');
  color: white;
  border-color: theme('colors.green.600');
}

.buttonSuccess:hover:not(:disabled) {
  background-color: theme('colors.green.700');
  border-color: theme('colors.green.700');
  transform: translateY(-1px);
  box-shadow: theme('boxShadow.md');
}

.buttonSuccess:active {
  transform: translateY(0);
  box-shadow: theme('boxShadow.sm');
}

/* Size variants */
.buttonSmall {
  padding: theme('spacing.2') theme('spacing.3');
  font-size: theme('fontSize.sm');
  border-radius: theme('borderRadius.md');
}

.buttonLarge {
  padding: theme('spacing.4') theme('spacing.6');
  font-size: theme('fontSize.lg');
  border-radius: theme('borderRadius.xl');
}

.buttonExtraLarge {
  padding: theme('spacing.5') theme('spacing.8');
  font-size: theme('fontSize.xl');
  border-radius: theme('borderRadius.xl');
}

/* Width modifiers */
.fullWidth {
  width: 100%;
}

.fitContent {
  width: fit-content;
}

/* State modifiers */
.loading {
  position: relative;
  color: transparent;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: theme('spacing.4');
  height: theme('spacing.4');
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  color: white;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Icon button variants */
.iconButton {
  composes: buttonBase;
  padding: theme('spacing.2');
  width: theme('spacing.10');
  height: theme('spacing.10');
  border-radius: theme('borderRadius.lg');
}

.iconButtonSmall {
  composes: iconButton;
  padding: theme('spacing.1.5');
  width: theme('spacing.8');
  height: theme('spacing.8');
  border-radius: theme('borderRadius.md');
}

.iconButtonLarge {
  composes: iconButton;
  padding: theme('spacing.3');
  width: theme('spacing.12');
  height: theme('spacing.12');
  border-radius: theme('borderRadius.xl');
}

/* Button group styles */
.buttonGroup {
  display: inline-flex;
  align-items: center;
  gap: theme('spacing.2');
}

.buttonGroup > .buttonBase:not(:first-child):not(:last-child) {
  border-radius: 0;
}

.buttonGroup > .buttonBase:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.buttonGroup > .buttonBase:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* Special landing page button styles */
.heroButton {
  composes: buttonPrimary buttonLarge;
  font-weight: theme('fontWeight.semibold');
  box-shadow: theme('boxShadow.lg');
}

.heroButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: theme('boxShadow.xl');
}

/* Back button with arrow */
.backButton {
  composes: buttonGhost;
  padding: theme('spacing.2') theme('spacing.3');
  gap: theme('spacing.2');
}

/* Submit button for forms */
.submitButton {
  composes: buttonPrimary fullWidth;
}

/* Link-style button */
.buttonLink {
  composes: buttonBase;
  background: transparent;
  border: none;
  color: theme('colors.blue.600');
  padding: 0;
  font-weight: theme('fontWeight.normal');
  text-decoration: underline;
  border-radius: 0;
}

.buttonLink:hover:not(:disabled) {
  color: theme('colors.blue.700');
  background: transparent;
  transform: none;
  box-shadow: none;
}

:global(.dark) .buttonLink {
  color: theme('colors.blue.400');
}

:global(.dark) .buttonLink:hover:not(:disabled) {
  color: theme('colors.blue.300');
}