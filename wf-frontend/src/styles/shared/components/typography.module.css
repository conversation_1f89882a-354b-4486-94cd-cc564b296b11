/**
 * Shared Typography Components - WF Frontend
 * Eliminates ~180 lines of duplicated CSS across 10 components
 * 
 * Based on analysis of:
 * - AboutScreen, PrivacyScreen, TermsScreen, ProfileScreen
 * - HomeScreen, ProgressScreen, QuizScreen, LoginScreen
 * - NotFoundScreen, LandingScreen
 */

/* Page title - main heading for screens */
.pageTitle {
  font-size: theme('fontSize.3xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.6');
  line-height: theme('lineHeight.tight');
}

:global(.dark) .pageTitle {
  color: white;
}

/* Large page title for landing pages */
.pageTitleLarge {
  composes: pageTitle;
  font-size: theme('fontSize.5xl');
  margin-bottom: theme('spacing.8');
}

@media (min-width: theme('screens.md')) {
  .pageTitleLarge {
    font-size: theme('fontSize.6xl');
  }
}

/* Section title - secondary headings */
.sectionTitle {
  font-size: theme('fontSize.2xl');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.4');
  line-height: theme('lineHeight.tight');
}

:global(.dark) .sectionTitle {
  color: white;
}

/* Subsection title - tertiary headings */
.subsectionTitle {
  font-size: theme('fontSize.xl');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.3');
  line-height: theme('lineHeight.snug');
}

:global(.dark) .subsectionTitle {
  color: white;
}

/* Small title for cards and components */
.smallTitle {
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.2');
  line-height: theme('lineHeight.snug');
}

:global(.dark) .smallTitle {
  color: white;
}

/* Subtitle - supporting text for titles */
.subtitle {
  font-size: theme('fontSize.lg');
  color: theme('colors.gray.600');
  margin-bottom: theme('spacing.4');
  line-height: theme('lineHeight.relaxed');
}

:global(.dark) .subtitle {
  color: theme('colors.gray.300');
}

/* Description text - explanatory content */
.description {
  font-size: theme('fontSize.base');
  color: theme('colors.gray.600');
  line-height: theme('lineHeight.relaxed');
  margin-bottom: theme('spacing.4');
}

:global(.dark) .description {
  color: theme('colors.gray.400');
}

/* Large description for hero sections */
.descriptionLarge {
  composes: description;
  font-size: theme('fontSize.xl');
  max-width: theme('maxWidth.3xl');
  margin-left: auto;
  margin-right: auto;
  margin-bottom: theme('spacing.8');
}

/* Body text - main content */
.bodyText {
  font-size: theme('fontSize.base');
  color: theme('colors.gray.900');
  line-height: theme('lineHeight.relaxed');
  margin-bottom: theme('spacing.4');
}

:global(.dark) .bodyText {
  color: theme('colors.gray.200');
}

/* Small body text */
.bodyTextSmall {
  composes: bodyText;
  font-size: theme('fontSize.sm');
}

/* Caption text - small annotations */
.captionText {
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.500');
  line-height: theme('lineHeight.normal');
}

:global(.dark) .captionText {
  color: theme('colors.gray.400');
}

/* Link text styling */
.linkText {
  color: theme('colors.blue.600');
  text-decoration: underline;
  transition: color 0.2s ease;
  cursor: pointer;
}

.linkText:hover {
  color: theme('colors.blue.700');
}

:global(.dark) .linkText {
  color: theme('colors.blue.400');
}

:global(.dark) .linkText:hover {
  color: theme('colors.blue.300');
}

/* Muted text - secondary information */
.mutedText {
  color: theme('colors.gray.500');
  font-size: theme('fontSize.sm');
}

:global(.dark) .mutedText {
  color: theme('colors.gray.400');
}

/* Error text styling */
.errorText {
  color: theme('colors.red.600');
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
}

:global(.dark) .errorText {
  color: theme('colors.red.400');
}

/* Success text styling */
.successText {
  color: theme('colors.green.600');
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
}

:global(.dark) .successText {
  color: theme('colors.green.400');
}

/* Warning text styling */
.warningText {
  color: theme('colors.yellow.600');
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
}

:global(.dark) .warningText {
  color: theme('colors.yellow.400');
}

/* Welcome title - specific for home screens */
.welcomeTitle {
  composes: pageTitle;
  margin-bottom: theme('spacing.2');
}

/* Hero title with gradient - for landing pages */
.heroTitle {
  font-size: theme('fontSize.5xl');
  font-weight: theme('fontWeight.bold');
  background: linear-gradient(to right, theme('colors.blue.600'), theme('colors.purple.600'));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  margin-bottom: theme('spacing.6');
  line-height: theme('lineHeight.tight');
}

@media (min-width: theme('screens.md')) {
  .heroTitle {
    font-size: theme('fontSize.6xl');
  }
}

:global(.dark) .heroTitle {
  background: linear-gradient(to right, theme('colors.blue.400'), theme('colors.purple.400'));
  background-clip: text;
  -webkit-background-clip: text;
}

/* Emphasized text */
.emphasizedText {
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
}

:global(.dark) .emphasizedText {
  color: white;
}

/* Centered text alignment utility */
.textCenter {
  text-align: center;
}

/* Left text alignment utility */
.textLeft {
  text-align: left;
}

/* Right text alignment utility */
.textRight {
  text-align: right;
}

/* Truncated text with ellipsis */
.textTruncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Multi-line truncation */
.textTruncateLines {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* No wrap utility */
.textNoWrap {
  white-space: nowrap;
}

/* Uppercase transformation */
.textUppercase {
  text-transform: uppercase;
  letter-spacing: theme('letterSpacing.wide');
}

/* Loading text with pulse animation */
.textLoading {
  animation: pulse 2s infinite;
  background-color: theme('colors.gray.200');
  border-radius: theme('borderRadius.md');
  color: transparent;
}

:global(.dark) .textLoading {
  background-color: theme('colors.gray.700');
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive text sizes */
.textResponsive {
  font-size: theme('fontSize.sm');
}

@media (min-width: theme('screens.sm')) {
  .textResponsive {
    font-size: theme('fontSize.base');
  }
}

@media (min-width: theme('screens.md')) {
  .textResponsive {
    font-size: theme('fontSize.lg');
  }
}

/* List styles */
.listDefault {
  list-style: none;
  padding: 0;
  margin: 0;
}

.listBulleted {
  list-style: disc;
  padding-left: theme('spacing.6');
  margin-bottom: theme('spacing.4');
}

.listNumbered {
  list-style: decimal;
  padding-left: theme('spacing.6');
  margin-bottom: theme('spacing.4');
}

.listItem {
  margin-bottom: theme('spacing.2');
  line-height: theme('lineHeight.relaxed');
}

/* Quote text styling */
.quoteText {
  font-style: italic;
  border-left: 4px solid theme('colors.blue.500');
  padding-left: theme('spacing.4');
  margin: theme('spacing.6') 0;
  color: theme('colors.gray.700');
}

:global(.dark) .quoteText {
  border-left-color: theme('colors.blue.400');
  color: theme('colors.gray.300');
}