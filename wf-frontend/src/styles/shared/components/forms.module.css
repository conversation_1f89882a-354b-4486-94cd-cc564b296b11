/**
 * Shared Form Components Module - Phase 5A Implementation  
 * Standardized form element patterns for consistent user input across the application
 * 
 * Usage:
 * import { formContainer, inputBase, inputError, selectBase } from '@/styles/shared/components/forms.module.css';
 * 
 * Features:
 * - Input field variants (text, email, password, etc.)
 * - Select dropdown styling
 * - Form validation states
 * - Dark mode support
 * - Accessibility-first design
 */

/* ==========================================================================
   Form Containers
   ========================================================================== */

.formContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.6');
}

.formContainerCompact {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
}

.formRow {
  display: flex;
  gap: theme('spacing.4');
}

@media (max-width: theme('screens.sm')) {
  .formRow {
    flex-direction: column;
  }
}

.formField {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.2');
}

.formFieldHorizontal {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

/* ==========================================================================
   Labels
   ========================================================================== */

.labelBase {
  display: block;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.700');
}

:global(.dark) .labelBase {
  color: theme('colors.gray.300');
}

.labelRequired::after {
  content: ' *';
  color: theme('colors.red.500');
}

.labelOptional::after {
  content: ' (optional)';
  color: theme('colors.gray.500');
  font-weight: theme('fontWeight.normal');
}

:global(.dark) .labelOptional::after {
  color: theme('colors.gray.400');
}

.labelLarge {
  composes: labelBase;
  font-size: theme('fontSize.base');
}

.labelSmall {
  composes: labelBase;
  font-size: theme('fontSize.xs');
}

/* ==========================================================================
   Input Fields
   ========================================================================== */

.inputBase {
  width: 100%;
  padding: theme('spacing.3') theme('spacing.4');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.lg');
  background-color: white;
  color: theme('colors.gray.900');
  font-size: theme('fontSize.base');
  transition: all 0.2s ease;
}

.inputBase:focus {
  outline: none;
  ring: theme('ringWidth.2') theme('colors.blue.500');
  border-color: theme('colors.blue.500');
}

.inputBase::placeholder {
  color: theme('colors.gray.400');
}

:global(.dark) .inputBase {
  border-color: theme('colors.gray.600');
  background-color: theme('colors.gray.700');
  color: white;
}

:global(.dark) .inputBase:focus {
  ring: theme('ringWidth.2') theme('colors.blue.400');
  border-color: theme('colors.blue.400');
}

:global(.dark) .inputBase::placeholder {
  color: theme('colors.gray.500');
}

/* Input Variants */
.inputSmall {
  composes: inputBase;
  padding: theme('spacing.2') theme('spacing.3');
  font-size: theme('fontSize.sm');
}

.inputLarge {
  composes: inputBase;
  padding: theme('spacing.4') theme('spacing.5');
  font-size: theme('fontSize.lg');
}

.inputCompact {
  composes: inputBase;
  padding: theme('spacing.1.5') theme('spacing.3');
  font-size: theme('fontSize.sm');
}

/* Input States */
.inputError {
  composes: inputBase;
  border-color: theme('colors.red.500');
}

.inputError:focus {
  ring: theme('ringWidth.2') theme('colors.red.500');
  border-color: theme('colors.red.500');
}

:global(.dark) .inputError {
  border-color: theme('colors.red.400');
}

:global(.dark) .inputError:focus {
  ring: theme('ringWidth.2') theme('colors.red.400');
  border-color: theme('colors.red.400');
}

.inputSuccess {
  composes: inputBase;
  border-color: theme('colors.green.500');
}

.inputSuccess:focus {
  ring: theme('ringWidth.2') theme('colors.green.500');
  border-color: theme('colors.green.500');
}

.inputWarning {
  composes: inputBase;
  border-color: theme('colors.yellow.500');
}

.inputWarning:focus {
  ring: theme('ringWidth.2') theme('colors.yellow.500');
  border-color: theme('colors.yellow.500');
}

.inputDisabled {
  composes: inputBase;
  opacity: 0.5;
  cursor: not-allowed;
  background-color: theme('colors.gray.100');
}

:global(.dark) .inputDisabled {
  background-color: theme('colors.gray.800');
}

.inputReadonly {
  composes: inputBase;
  background-color: theme('colors.gray.50');
  cursor: default;
}

:global(.dark) .inputReadonly {
  background-color: theme('colors.gray.800');
}

/* ==========================================================================
   Select Dropdowns
   ========================================================================== */

.selectBase {
  composes: inputBase;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right theme('spacing.2') center;
  background-repeat: no-repeat;
  background-size: theme('spacing.5') theme('spacing.5');
  padding-right: theme('spacing.10');
}

.selectBase:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

:global(.dark) .selectBase {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%259CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

:global(.dark) .selectBase:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2360A5FA' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.selectSmall {
  composes: selectBase;
  padding: theme('spacing.2') theme('spacing.8') theme('spacing.2') theme('spacing.3');
  font-size: theme('fontSize.sm');
}

.selectLarge {
  composes: selectBase;
  padding: theme('spacing.4') theme('spacing.12') theme('spacing.4') theme('spacing.5');
  font-size: theme('fontSize.lg');
}

.selectError {
  composes: selectBase;
  border-color: theme('colors.red.500');
}

.selectDisabled {
  composes: selectBase;
  opacity: 0.5;
  cursor: not-allowed;
  background-color: theme('colors.gray.100');
}

/* ==========================================================================
   Textarea
   ========================================================================== */

.textareaBase {
  composes: inputBase;
  min-height: theme('spacing.24');
  resize: vertical;
  font-family: inherit;
  line-height: theme('lineHeight.normal');
}

.textareaSmall {
  composes: textareaBase;
  min-height: theme('spacing.20');
  padding: theme('spacing.2') theme('spacing.3');
  font-size: theme('fontSize.sm');
}

.textareaLarge {
  composes: textareaBase;
  min-height: theme('spacing.32');
  padding: theme('spacing.4') theme('spacing.5');
  font-size: theme('fontSize.lg');
}

.textareaNoResize {
  composes: textareaBase;
  resize: none;
}

.textareaAutoResize {
  composes: textareaBase;
  resize: none;
  overflow: hidden;
}

/* ==========================================================================
   Checkbox and Radio
   ========================================================================== */

.checkboxBase {
  width: theme('spacing.4');
  height: theme('spacing.4'); 
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.sm');
  background-color: white;
  transition: all 0.2s ease;
  cursor: pointer;
}

.checkboxBase:checked {
  background-color: theme('colors.blue.600');
  border-color: theme('colors.blue.600');
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
}

.checkboxBase:focus {
  outline: none;
  ring: theme('ringWidth.2') theme('colors.blue.500');
}

:global(.dark) .checkboxBase {
  border-color: theme('colors.gray.600');
  background-color: theme('colors.gray.700');
}

:global(.dark) .checkboxBase:checked {
  background-color: theme('colors.blue.500');
  border-color: theme('colors.blue.500');
}

.radioBase {
  composes: checkboxBase;
  border-radius: theme('borderRadius.full');
}

.radioBase:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

.checkboxLarge {
  composes: checkboxBase;
  width: theme('spacing.5');
  height: theme('spacing.5');
}

.radioLarge {
  composes: radioBase;
  width: theme('spacing.5');
  height: theme('spacing.5');
}

/* ==========================================================================
   Input Groups
   ========================================================================== */

.inputGroup {
  display: flex;
  align-items: stretch;
}

.inputGroupPrepend {
  background-color: theme('colors.gray.50');
  border: 1px solid theme('colors.gray.300');
  border-right: none;
  border-radius: theme('borderRadius.lg') 0 0 theme('borderRadius.lg');
  padding: theme('spacing.3') theme('spacing.4');
  display: flex;
  align-items: center;
  color: theme('colors.gray.500');
  font-size: theme('fontSize.sm');
  white-space: nowrap;
}

.inputGroupAppend {
  composes: inputGroupPrepend;
  border-left: none;
  border-right: 1px solid theme('colors.gray.300');
  border-radius: 0 theme('borderRadius.lg') theme('borderRadius.lg') 0;
}

.inputGroupInput {
  composes: inputBase;
  border-radius: 0;
  flex: 1;
}

.inputGroupInput:first-child {
  border-radius: theme('borderRadius.lg') 0 0 theme('borderRadius.lg');
}

.inputGroupInput:last-child {
  border-radius: 0 theme('borderRadius.lg') theme('borderRadius.lg') 0;
}

.inputGroupInput:only-child {
  border-radius: theme('borderRadius.lg');
}

:global(.dark) .inputGroupPrepend,
:global(.dark) .inputGroupAppend {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.400');
}

/* ==========================================================================
   Password Input
   ========================================================================== */

.passwordContainer {
  position: relative;
}

.passwordInput {
  composes: inputBase;
  padding-right: theme('spacing.12');
}

.passwordToggle {
  position: absolute;
  right: theme('spacing.3');
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: theme('colors.gray.400');
  padding: theme('spacing.1');
  border-radius: theme('borderRadius.sm');
  transition: color 0.2s ease;
}

.passwordToggle:hover {
  color: theme('colors.gray.600');
}

.passwordToggle:focus {
  outline: none;
  ring: theme('ringWidth.2') theme('colors.blue.500');
}

:global(.dark) .passwordToggle {
  color: theme('colors.gray.500');
}

:global(.dark) .passwordToggle:hover {
  color: theme('colors.gray.300');
}

/* ==========================================================================
   Validation Messages
   ========================================================================== */

.validationMessage {
  font-size: theme('fontSize.sm');
  margin-top: theme('spacing.1');
}

.validationError {
  composes: validationMessage;
  color: theme('colors.red.600');
}

:global(.dark) .validationError {
  color: theme('colors.red.400');
}

.validationSuccess {
  composes: validationMessage;
  color: theme('colors.green.600');
}

:global(.dark) .validationSuccess {
  color: theme('colors.green.400');
}

.validationWarning {
  composes: validationMessage;
  color: theme('colors.yellow.600');
}

:global(.dark) .validationWarning {
  color: theme('colors.yellow.400');
}

.validationInfo {
  composes: validationMessage;
  color: theme('colors.blue.600');
}

:global(.dark) .validationInfo {
  color: theme('colors.blue.400');
}

.validationHelp {
  composes: validationMessage;
  color: theme('colors.gray.500');
}

:global(.dark) .validationHelp {
  color: theme('colors.gray.400');
}

/* ==========================================================================
   Form Actions
   ========================================================================== */

.formActions {
  display: flex;
  gap: theme('spacing.3');
  justify-content: flex-end;
  margin-top: theme('spacing.6');
}

.formActionsCenter {
  composes: formActions;
  justify-content: center;
}

.formActionsStart {
  composes: formActions;
  justify-content: flex-start;
}

.formActionsFull {
  composes: formActions;
  justify-content: stretch;
}

.formActionsFull > * {
  flex: 1;
}

@media (max-width: theme('screens.sm')) {
  .formActions,
  .formActionsCenter,
  .formActionsStart {
    flex-direction: column;
  }
  
  .formActionsFull {
    flex-direction: column;
  }
  
  .formActionsFull > * {
    flex: none;
  }
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.inputLoading {
  composes: inputBase;
  background-image: url("data:image/svg+xml,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M10 3V6M10 14V17M17 10H14M6 10H3M15.364 4.636L13.95 6.05M6.05 13.95L4.636 15.364M15.364 15.364L13.95 13.95M6.05 6.05L4.636 4.636' stroke='%239CA3AF' stroke-width='2' stroke-linecap='round'/%3e%3c/svg%3e");
  background-position: right theme('spacing.3') center;
  background-repeat: no-repeat;
  background-size: theme('spacing.5') theme('spacing.5');
  padding-right: theme('spacing.12');
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ==========================================================================
   Accessibility & Reduced Motion
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
  .inputBase,
  .selectBase,
  .textareaBase,
  .checkboxBase,
  .radioBase,
  .passwordToggle,
  .inputLoading {
    transition: none !important;
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .inputBase,
  .selectBase,
  .textareaBase,
  .checkboxBase,
  .radioBase {
    border-width: 2px;
  }
  
  .inputError,
  .selectError {
    border-width: 3px;
  }
}