/**
 * Shared Grid Layouts Module - Phase 5A Implementation
 * Responsive grid systems for consistent layout patterns across the application
 * 
 * Usage:
 * import { gridCols2, gridCols3, gridCols4, responsiveGrid } from '@/styles/shared/layouts/grids.module.css';
 * 
 * Features:
 * - Responsive grid layouts (1-12 columns)
 * - Quiz card grids
 * - Stats grid layouts
 * - Form field grids
 * - Mobile-first responsive design
 */

/* ==========================================================================
   Base Grid Container
   ========================================================================== */

.gridBase {
  display: grid;
  gap: theme('spacing.6');
}

.gridCompact {
  display: grid;
  gap: theme('spacing.4');
}

.gridSpacious {
  display: grid;
  gap: theme('spacing.8');
}

.gridTight {
  display: grid;
  gap: theme('spacing.3');
}

.gridMinimal {
  display: grid;
  gap: theme('spacing.2');
}

/* ==========================================================================
   Fixed Column Grids
   ========================================================================== */

/* Single Column */
.gridCol1 {
  composes: gridBase;
  grid-template-columns: 1fr;
}

/* Two Columns */
.gridCols2 {
  composes: gridBase;
  grid-template-columns: repeat(2, 1fr);
}

.gridCols2Compact {
  composes: gridCompact;
  grid-template-columns: repeat(2, 1fr);
}

/* Three Columns */
.gridCols3 {
  composes: gridBase;
  grid-template-columns: repeat(3, 1fr);
}

.gridCols3Compact {
  composes: gridCompact;
  grid-template-columns: repeat(3, 1fr);
}

/* Four Columns */
.gridCols4 {
  composes: gridBase;
  grid-template-columns: repeat(4, 1fr);
}

.gridCols4Compact {
  composes: gridCompact;
  grid-template-columns: repeat(4, 1fr);
}

/* Five Columns */
.gridCols5 {
  composes: gridBase;
  grid-template-columns: repeat(5, 1fr);
}

/* Six Columns */
.gridCols6 {
  composes: gridBase;
  grid-template-columns: repeat(6, 1fr);
}

/* ==========================================================================
   Responsive Grid Patterns
   ========================================================================== */

/* Mobile-First Responsive Grids */
.responsiveGrid2 {
  composes: gridBase;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .responsiveGrid2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

.responsiveGrid3 {
  composes: gridBase;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .responsiveGrid3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .responsiveGrid3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

.responsiveGrid4 {
  composes: gridBase;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.sm')) {
  .responsiveGrid4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .responsiveGrid4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.responsiveGrid6 {
  composes: gridBase;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: theme('screens.md')) {
  .responsiveGrid6 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .responsiveGrid6 {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* ==========================================================================
   Common Application Patterns
   ========================================================================== */

/* Quiz Cards Grid */
.quizGrid {
  composes: gridBase;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .quizGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .quizGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Stats Grid */
.statsGrid {
  composes: gridBase;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: theme('screens.md')) {
  .statsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Features Grid */
.featuresGrid {
  composes: gridBase;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .featuresGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: theme('screens.lg')) {
  .featuresGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Steps/Process Grid */
.stepsGrid {
  composes: gridBase;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .stepsGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Form Grid */
.formGrid {
  composes: gridCompact;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .formGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.formGridHorizontal {
  composes: gridCompact;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.sm')) {
  .formGridHorizontal {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Dashboard Grid */
.dashboardGrid {
  composes: gridBase;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.lg')) {
  .dashboardGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Quiz Selection Grid */
.quizSelectionGrid {
  composes: gridBase;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.md')) {
  .quizSelectionGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Question Overview Grid (5 columns for mobile navigation) */
.questionOverviewGrid {
  composes: gridMinimal;
  grid-template-columns: repeat(5, 1fr);
}

/* ==========================================================================
   Layout Utilities
   ========================================================================== */

/* Auto-fit and Auto-fill Grids */
.gridAutoFit {
  composes: gridBase;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.gridAutoFitCompact {
  composes: gridBase;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.gridAutoFitLarge {
  composes: gridBase;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.gridAutoFill {
  composes: gridBase;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Asymmetric Grids */
.gridAsymmetric2to1 {
  composes: gridBase;
  grid-template-columns: 2fr 1fr;
}

.gridAsymmetric1to2 {
  composes: gridBase;
  grid-template-columns: 1fr 2fr;
}

.gridAsymmetric3to2 {
  composes: gridBase;
  grid-template-columns: 3fr 2fr;
}

/* Sidebar Layouts */
.gridSidebar {
  composes: gridBase;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.lg')) {
  .gridSidebar {
    grid-template-columns: 250px 1fr;
  }
}

.gridSidebarWide {
  composes: gridBase;
  grid-template-columns: 1fr;
}

@media (min-width: theme('screens.lg')) {
  .gridSidebarWide {
    grid-template-columns: 320px 1fr;
  }
}

/* ==========================================================================
   Item Alignment
   ========================================================================== */

.gridItemsStart {
  align-items: start;
}

.gridItemsCenter {
  align-items: center;
}

.gridItemsEnd {
  align-items: end;
}

.gridItemsStretch {
  align-items: stretch;
}

.gridContentStart {
  justify-content: start;
}

.gridContentCenter {
  justify-content: center;
}

.gridContentEnd {
  justify-content: end;
}

.gridContentBetween {
  justify-content: space-between;
}

.gridContentAround {
  justify-content: space-around;
}

.gridContentEvenly {
  justify-content: space-evenly;
}

/* ==========================================================================
   Dense and Flow Utilities
   ========================================================================== */

.gridDense {
  grid-auto-flow: dense;
}

.gridFlowRow {
  grid-auto-flow: row;
}

.gridFlowColumn {
  grid-auto-flow: column;
}

/* ==========================================================================
   Masonry-like Layouts
   ========================================================================== */

.gridMasonry {
  composes: gridBase;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-auto-rows: max-content;
}

.gridMasonryCompact {
  composes: gridCompact;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-auto-rows: max-content;
}

/* ==========================================================================
   Loading and Skeleton Grids
   ========================================================================== */

.gridSkeleton {
  composes: quizGrid;
}

.gridSkeletonItem {
  background-color: theme('colors.gray.200');
  border-radius: theme('borderRadius.lg');
  animation: pulse 2s infinite;
}

:global(.dark) .gridSkeletonItem {
  background-color: theme('colors.gray.700');
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* ==========================================================================
   Mobile Optimizations
   ========================================================================== */

/* Mobile-specific grid adjustments */
@media (max-width: theme('screens.sm')) {
  .gridCols2,
  .gridCols3,  
  .gridCols4,
  .gridCols5,
  .gridCols6 {
    grid-template-columns: 1fr;
  }
  
  .gridAsymmetric2to1,
  .gridAsymmetric1to2,
  .gridAsymmetric3to2 {
    grid-template-columns: 1fr;
  }
  
  .gridSidebar,
  .gridSidebarWide {
    grid-template-columns: 1fr;
  }
}

/* Extra small screens */
@media (max-width: 360px) {
  .gridBase,
  .gridCompact,
  .gridSpacious {
    gap: theme('spacing.3');
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
}

/* ==========================================================================
   Print Optimizations
   ========================================================================== */

@media print {
  .gridBase,
  .gridCompact,
  .gridSpacious {
    gap: theme('spacing.4');
  }
  
  .responsiveGrid2,
  .responsiveGrid3,
  .responsiveGrid4,
  .responsiveGrid6 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quizGrid,
  .featuresGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* ==========================================================================
   Accessibility & Reduced Motion
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
  .gridSkeletonItem {
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .gridBase,
  .gridCompact,
  .gridSpacious,
  .gridTight,
  .gridMinimal {
    gap: calc(theme('spacing.6') + 2px);
  }
}