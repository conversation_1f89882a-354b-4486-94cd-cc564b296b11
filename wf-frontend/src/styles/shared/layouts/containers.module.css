/**
 * Shared Container Layouts - WF Frontend
 * Eliminates ~225 lines of duplicated CSS across 9 components
 * 
 * Based on analysis of:
 * - AboutScreen, PrivacyScreen, TermsScreen, ProfileScreen, QuizResultsScreen
 * - NotFoundScreen, ForgotPasswordScreen, LoginScreen, LandingScreen
 */

/* Standard page container with responsive padding */
.pageContainer {
  max-width: theme('maxWidth.4xl');
  margin: 0 auto;
  padding: theme('spacing.8') theme('spacing.4');
}

@media (min-width: theme('screens.sm')) {
  .pageContainer {
    padding: theme('spacing.8') theme('spacing.6');
  }
}

@media (min-width: theme('screens.lg')) {
  .pageContainer {
    padding: theme('spacing.8') theme('spacing.8');
  }
}

/* Full-height centered container for auth screens */
.centeredContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: theme('colors.gray.100');
  padding: theme('spacing.4');
}

:global(.dark) .centeredContainer {
  background-color: theme('colors.gray.900');
}

/* Full-height wrapper for quiz and complex screens */
.fullHeightContainer {
  min-height: 100vh;
  background-color: theme('colors.gray.50');
}

:global(.dark) .fullHeightContainer {
  background-color: theme('colors.gray.900');
}

/* Compact container with reduced padding */
.compactContainer {
  max-width: theme('maxWidth.2xl');
  margin: 0 auto;
  padding: theme('spacing.6') theme('spacing.4');
}

@media (min-width: theme('screens.sm')) {
  .compactContainer {
    padding: theme('spacing.6') theme('spacing.6');
  }
}

/* Wide container for dashboard-style layouts */
.wideContainer {
  max-width: theme('maxWidth.7xl');
  margin: 0 auto;
  padding: theme('spacing.8') theme('spacing.4');
}

@media (min-width: theme('screens.sm')) {
  .wideContainer {
    padding: theme('spacing.8') theme('spacing.6');
  }
}

@media (min-width: theme('screens.lg')) {
  .wideContainer {
    padding: theme('spacing.8') theme('spacing.8');
  }
}

/* Section container for content grouping */
.sectionContainer {
  margin-bottom: theme('spacing.8');
}

.sectionContainer:last-child {
  margin-bottom: 0;
}

/* Content wrapper with max-width constraint */
.contentWrapper {
  max-width: theme('maxWidth.3xl');
  margin: 0 auto;
}

/* Auth form container */
.authContainer {
  composes: centeredContainer;
}

/* Quiz container with specific styling */
.quizContainer {
  composes: fullHeightContainer;
  padding: theme('spacing.6') theme('spacing.4');
}

@media (min-width: theme('screens.md')) {
  .quizContainer {
    padding: theme('spacing.8') theme('spacing.6');
  }
}

/* Landing page hero container */
.heroContainer {
  max-width: theme('maxWidth.7xl');
  margin: 0 auto;
  padding: theme('spacing.20') theme('spacing.4');
  text-align: center;
}

@media (min-width: theme('screens.sm')) {
  .heroContainer {
    padding: theme('spacing.20') theme('spacing.6');
  }
}

@media (min-width: theme('screens.lg')) {
  .heroContainer {
    padding: theme('spacing.20') theme('spacing.8');
  }
}

/* Modal/dialog container */
.modalContainer {
  max-width: theme('maxWidth.md');
  margin: 0 auto;
  padding: theme('spacing.6');
  background-color: white;
  border-radius: theme('borderRadius.xl');
  box-shadow: theme('boxShadow.xl');
}

:global(.dark) .modalContainer {
  background-color: theme('colors.gray.800');
}

/* Card container for content cards */
.cardContainer {
  background-color: white;
  border-radius: theme('borderRadius.xl');
  box-shadow: theme('boxShadow.sm');
  overflow: hidden;
}

:global(.dark) .cardContainer {
  background-color: theme('colors.gray.800');
}

/* Responsive container utilities */
.responsiveContainer {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 theme('spacing.4');
}

@media (min-width: theme('screens.sm')) {
  .responsiveContainer {
    padding: 0 theme('spacing.6');
  }
}

@media (min-width: theme('screens.md')) {
  .responsiveContainer {
    padding: 0 theme('spacing.8');
  }
}

@media (min-width: theme('screens.lg')) {
  .responsiveContainer {
    max-width: theme('maxWidth.screen-lg');
    padding: 0 theme('spacing.8');
  }
}

@media (min-width: theme('screens.xl')) {
  .responsiveContainer {
    max-width: theme('maxWidth.screen-xl');
  }
}

/* Scrollable container for long content */
.scrollableContainer {
  max-height: 80vh;
  overflow-y: auto;
  padding: theme('spacing.4');
}

/* Loading container with centered spinner */
.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: theme('spacing.96'); /* 24rem */
  padding: theme('spacing.8');
}

/* Error container with centered message */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: theme('spacing.96'); /* 24rem */
  padding: theme('spacing.8');
  text-align: center;
}