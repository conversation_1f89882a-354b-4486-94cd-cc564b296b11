// Frontend Repository Manager - Singleton access to repositories
// Handles frontend-specific repository initialization and access

import { RepositoryManager, type RepositoryConfig } from 'wf-shared/repositories'
import { RepositoryContainer } from './RepositoryContainer'

export class FrontendRepositoryManager extends RepositoryManager {
  private static instance?: FrontendRepositoryManager

  private constructor() {
    super()
  }

  /**
   * Initialize frontend repository system
   */
  static initialize(config: RepositoryConfig): void {
    // Initialize base repository system
    super.initialize(config)
    
    // Create and set frontend container
    const container = new RepositoryContainer()
    this.setContainer(container)
  }

  /**
   * Get frontend repository container
   */
  static getRepositories(): RepositoryContainer {
    return super.getContainer() as RepositoryContainer
  }

  /**
   * Convenience method to get singleton instance
   */
  static getInstance(): FrontendRepositoryManager {
    if (!this.instance) {
      this.instance = new FrontendRepositoryManager()
    }
    return this.instance
  }
}

// Export convenient access to repositories
export const repositories = {
  get container() {
    return FrontendRepositoryManager.getRepositories()
  },
  get userApi() {
    return this.container.userApi
  },
  get quizApi() {
    return this.container.quizApi
  },
  get authApi() {
    return this.container.authApi
  },
  get authStorage() {
    return this.container.authStorage
  },
  get userStorage() {
    return this.container.userStorage
  }
}