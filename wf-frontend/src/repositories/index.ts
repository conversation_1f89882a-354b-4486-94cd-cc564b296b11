// Repository exports for frontend
// Centralized export of all repository classes and interfaces

// Interfaces (re-exported from wf-shared)
export type { IRepository, IStorageRepository, BaseApiRepository, BaseStorageRepository } from 'wf-shared/repositories'
export { DatabaseProvider } from 'wf-shared/repositories'

// API Repositories
export { QuizApiRepository } from './api/QuizApiRepository'
export { UserApiRepository } from './api/UserApiRepository'

// Storage Repositories
export { UserStorageRepository } from './storage/UserStorageRepository'
export { AuthStorageRepository } from './storage/AuthStorageRepository'
export type { UserPreferences } from './storage/UserStorageRepository'
export type { RememberedCredentials, UserSession } from './storage/AuthStorageRepository'

// Repository Container
export { RepositoryContainer } from './RepositoryContainer'

// Repository Manager
export { FrontendRepositoryManager } from './FrontendRepositoryManager'

// Pre-initialized repositories (auto-initializes when imported)
export { repositories } from './init'