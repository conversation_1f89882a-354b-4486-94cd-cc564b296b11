// User API repository for frontend
// Handles user-related database operations for user-facing features

import { BaseApiRepository } from 'wf-shared/repositories'
import type { DbUser, UserProgressWithRelations, ApiResponse } from 'wf-shared/types'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from 'wf-shared/types'

export class UserApiRepository extends BaseApiRepository<DbUser, Partial<DbUser>, Partial<DbUser>> {
  protected tableName = 'users' as const

  constructor(database: SupabaseClient<Database>) {
    super(database)
  }

  /**
   * Get current user profile with progress data
   */
  async getUserProfile(userId: string): Promise<ApiResponse<DbUser & {
    user_progress?: UserProgressWithRelations[]
    level?: { id: string; name: string; key: string }
  }>> {
    try {
      const { data, error: _error } = await this.db
        .from(this.tableName)
        .select(`
          *,
          levels(id, name, key),
          user_progress(
            *,
            categories(id, name, key),
            levels(id, name, key)
          )
        `)
        .eq('id', userId)
        .single()

      if (_error) {
        return {
          success: false,
          error: { message: _error.message, code: _error.code }
        }
      }

      return {
        success: true,
        data: data as DbUser & {
          user_progress?: UserProgressWithRelations[];
          level?: { id: string; name: string; key: string };
        }
      }
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch user profile', code: 'FETCH_ERROR' }
      }
    }
  }

  /**
   * Update user profile information
   */
  async updateProfile(userId: string, updates: Partial<DbUser>): Promise<ApiResponse<DbUser>> {
    try {
      const { data, error: _error } = await this.db
        .from(this.tableName)
        .update(updates)
        .eq('id', userId)
        .select()
        .single()

      if (_error) {
        return {
          success: false,
          error: { message: _error.message, code: _error.code }
        }
      }

      return {
        success: true,
        data: data as DbUser
      }
    } catch {
      return {
        success: false,
        error: { message: 'Failed to update profile', code: 'UPDATE_ERROR' }
      }
    }
  }

  /**
   * Get user's quiz attempt history
   */
  async getUserQuizAttempts(userId: string, filters?: {
    page?: number
    pageSize?: number
    quizId?: string
  }): Promise<ApiResponse<{
    data: QuizAttempt[]
    total: number
    page: number
    pageSize: number
  }>> {
    try {
      const { page = 1, pageSize = 10, quizId } = filters || {}
      const offset = (page - 1) * pageSize

      let query = this.db
        .from('quiz_attempts')
        .select(`
          *,
          quizzes(id, title, key),
          users(id, first_name, last_name)
        `, { count: 'exact' })
        .eq('user_id', userId)

      if (quizId) {
        query = query.eq('quiz_id', quizId)
      }

      query = query
        .range(offset, offset + pageSize - 1)
        .order('created_at', { ascending: false })

      const { data, error: _error, count } = await query

      if (_error) {
        return {
          success: false,
          error: { message: _error.message, code: _error.code }
        }
      }

      return {
        success: true,
        data: {
          data: data || [],
          total: count || 0,
          page,
          pageSize
        }
      }
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz attempts', code: 'FETCH_ERROR' }
      }
    }
  }

  /**
   * Update user progress for a category/level
   */
  async updateUserProgress(userId: string, progressData: {
    categoryId: string
    levelId: string
    progressPercentage: number
  }): Promise<ApiResponse<UserProgressWithRelations>> {
    try {
      const { data, error: _error } = await this.db
        .from('user_progress')
        .upsert({
          user_id: userId,
          category_id: progressData.categoryId,
          level_id: progressData.levelId,
          progress_percentage: progressData.progressPercentage,
          updated_at: new Date().toISOString()
        })
        .select(`
          *,
          categories(id, name, key),
          levels(id, name, key)
        `)
        .single()

      if (_error) {
        return {
          success: false,
          error: { message: _error.message, code: _error.code }
        }
      }

      return {
        success: true,
        data: data as UserProgressWithRelations
      }
    } catch {
      return {
        success: false,
        error: { message: 'Failed to update user progress', code: 'UPDATE_ERROR' }
      }
    }
  }
}