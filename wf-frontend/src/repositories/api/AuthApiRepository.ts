// Auth API repository for frontend
// Handles authentication operations using repository pattern

import { BaseAuthRepository } from 'wf-shared/repositories';
import { User } from '@supabase/supabase-js';

export class AuthApiRepository extends BaseAuthRepository {
  constructor() {
    super() // Uses default database client
  }

  /**
   * Sign in with remember me functionality
   */
  async signInWithRememberMe(email: string, password: string, rememberMe: boolean = false) {
    const result = await this.signIn(email, password)
    
    // If sign in successful and remember me is enabled, this will be handled by the service layer
    // Repository should only handle the auth operation
    
    return { ...result, rememberMe }
  }

  /**
   * Check if user's email is confirmed
   */
  isEmailConfirmed(user: User): boolean {
    return !!(user?.email_confirmed_at)
  }
}