// Quiz API repository for frontend
// Handles quiz-related database operations for user-facing features

import { BaseApiRepository } from 'wf-shared/repositories'
import type { 
  DbQuiz, QuizWithRelations, Database, ApiResponse 
} from 'wf-shared/types'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { QuizMode, User } from '@/types'

export class QuizApiRepository extends BaseApiRepository<QuizWithRelations, Partial<DbQuiz>, Partial<DbQuiz>> {
  protected tableName = 'quizzes' as const

  constructor(database: SupabaseClient<Database>) {
    super(database)
  }

  /**
   * Get active quizzes for users with filtering
   */
  async getActiveQuizzes(filters?: {
    categoryId?: string
    levelId?: string
    search?: string
    page?: number
    pageSize?: number
  }) {
    try {
      const { page = 1, pageSize = 6, ...otherFilters } = filters || {}
      const offset = (page - 1) * pageSize

      // Execute query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        let query = this.db
          .from(this.tableName)
          .select(`
            *,
            categories(id, name, key),
            levels(id, name, key),
            quiz_types(id, name, key)
          `, { count: 'exact' })
          .eq('is_active', true)

        // Apply filters
        query = this.applyFilters(query, otherFilters)

        // Apply pagination
        query = query.range(offset, offset + pageSize - 1)
          .order('created_at', { ascending: false })

        return await query
      });

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch active quizzes', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data, error, count } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true,
        data: {
          data: data as QuizWithRelations[],
          total: count || 0,
          page,
          pageSize
        }
      }
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch active quizzes', code: 'FETCH_ERROR' }
      }
    }
  }

  /**
   * Get practice quizzes for Home screen - direct access
   */
  async getPracticeQuizzes(filters?: {
    categoryKey?: string
    levelKey?: string
    search?: string
    page?: number
    pageSize?: number
  }) {
    try {
      const { page = 1, pageSize = 12, ...otherFilters } = filters || {}
      const offset = (page - 1) * pageSize

      let query = this.db
        .from('quiz_modes')
        .select('*', { count: 'exact' })
        .eq('mode_type', 'practice')

      // Apply filters
      if (otherFilters.categoryKey && otherFilters.categoryKey !== 'all') {
        query = query.eq('category_key', otherFilters.categoryKey)
      }
      if (otherFilters.levelKey && otherFilters.levelKey !== 'all') {
        query = query.eq('level_key', otherFilters.levelKey)
      }
      if (otherFilters.search) {
        query = query.or(`title.ilike.%${otherFilters.search}%,description.ilike.%${otherFilters.search}%`)
      }

      // Apply pagination and ordering
      query = query.range(offset, offset + pageSize - 1)
        .order('level_key', { ascending: true })
        .order('difficulty_level', { ascending: true })

      const { data, error, count } = await query

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true,
        data: {
          data: data || [],
          total: count || 0,
          page,
          pageSize
        }
      }
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch practice quizzes', code: 'FETCH_ERROR' }
      }
    }
  }

  /**
   * Get assessment quizzes for Assessment navigation tab
   */
  async getTestQuizzes(filters?: {
    testType?: 'all' | 'assessment' | 'toeic' | 'ielts'
    levelKey?: string
    search?: string
    page?: number
    pageSize?: number
  }) {
    try {
      const { page = 1, pageSize = 10, ...otherFilters } = filters || {}
      const offset = (page - 1) * pageSize

      let query = this.db
        .from('quiz_modes')
        .select('*', { count: 'exact' })
        .eq('mode_type', 'test')

      // Apply test type filter
      if (otherFilters.testType && otherFilters.testType !== 'all') {
        switch (otherFilters.testType) {
          case 'assessment':
            query = query.eq('quiz_type_key', 'test_quiz')
            break
          case 'toeic':
            query = query.eq('quiz_type_key', 'toeic_test')
            break
          case 'ielts':
            query = query.eq('quiz_type_key', 'ielts_test')
            break
        }
      }

      // Apply other filters
      if (otherFilters.levelKey && otherFilters.levelKey !== 'all') {
        query = query.eq('level_key', otherFilters.levelKey)
      }
      if (otherFilters.search) {
        query = query.or(`title.ilike.%${otherFilters.search}%,description.ilike.%${otherFilters.search}%`)
      }

      // Apply pagination and ordering
      query = query.range(offset, offset + pageSize - 1)
        .order('quiz_type_key', { ascending: true })
        .order('level_key', { ascending: true })
        .order('difficulty_level', { ascending: true })

      const { data, error, count } = await query

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true,
        data: {
          data: data || [],
          total: count || 0,
          page,
          pageSize
        }
      }
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch assessment quizzes', code: 'FETCH_ERROR' }
      }
    }
  }

  /**
   * Get a single quiz by ID for users
   * Updated to use the many-to-many relationship through quiz_questions table
   */
  async getUserQuizById(id: string) {
    try {
      // First get the quiz data through circuit breaker
      const quizResult = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select(`
            *,
            categories(id, name, key),
            levels(id, name, key),
            quiz_types(id, name, key)
          `)
          .eq('id', id)
          .eq('is_active', true)
          .single()
      });

      if (!quizResult.success) {
        return {
          success: false,
          error: { message: quizResult.error || 'Circuit breaker: Failed to fetch quiz data', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data: quizData, error } = quizResult.data!;

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      // Then get the quiz questions separately through the junction table using circuit breaker
      const questionsResult = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quiz_questions')
          .select(`
            id,
            order_index,
            points,
            is_active,
            questions(
              *,
              question_options(*),
              question_types(id, name, key),
              explanations(*)
            )
          `)
          .eq('quiz_id', id)
          .order('order_index', { ascending: true })
      });

      if (!questionsResult.success) {
        return {
          success: false,
          error: { message: questionsResult.error || 'Circuit breaker: Failed to fetch quiz questions', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data: questionsData, error: questionsError } = questionsResult.data!

      if (questionsError) {
        return {
          success: false,
          error: { message: questionsError.message, code: questionsError.code }
        }
      }

      // Transform quiz_questions to a clean questions array for the UI
      const questions = (questionsData || []).map((qq: any) => {
        const questionData = qq.questions;
        const questionOptions = questionData?.question_options || [];
        const questionExplanations = questionData?.explanations || [];

        const correctOption = questionOptions.find((opt: any) => opt.is_correct);
        const correct_answer = correctOption?.option_text || '';

        return {
          // Question data from the nested questions object
          ...questionData,
          // Quiz-specific metadata from quiz_questions junction table
          order_index: qq.order_index,
          points: qq.points,
          quiz_question_id: qq.id,
          is_quiz_active: qq.is_active,
          // Transformed related data
          question_type: questionData?.question_types?.key,
          options: questionOptions,
          explanations: questionExplanations,
          correct_answer,
        };
      })

      // Combine the data with UI-friendly structure
      const data = {
        ...quizData,
        quiz_questions: questionsData || [], // Keep original for other uses
        questions: questions // Clean array for UI consumption
      }

      return {
        success: true,
        data: data as QuizWithRelations
      }
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz', code: 'FETCH_ERROR' }
      }
    }
  }

  /**
   * Helper method to determine if a quiz is a practice quiz
   */
  isPracticeQuiz(quiz: any): boolean {
    return quiz.quiz_type_key === 'practice_quiz' ||
           (quiz.quiz_types?.key === 'practice_quiz')
  }

  /**
   * Helper method to determine if a quiz is a test quiz
   */
  isTestQuiz(quiz: any): boolean {
    const testTypes = ['test_quiz', 'toeic_test', 'ielts_test']
    return testTypes.includes(quiz.quiz_type_key) ||
           (quiz.quiz_types?.key && testTypes.includes(quiz.quiz_types.key))
  }

  /**
   * Get quiz configuration with defaults
   */
  getQuizConfig(quiz: any): Record<string, any> {
    const defaultConfig = this.isPracticeQuiz(quiz) ? {
      show_explanations: true,
      allow_review: true,
      immediate_feedback: true,
      unlimited_attempts: true,
      show_progress: true,
      time_limit: false
    } : {
      show_explanations: false,
      allow_review: false,
      immediate_feedback: false,
      strict_timing: true,
      forward_only: true,
      show_timer: true,
      time_limit: true
    }

    return { ...defaultConfig, ...(quiz.quiz_config || {}) }
  }

  /**
   * Get all available levels
   */
  async getLevels(): Promise<ApiResponse<Level[]>> {
    try {
      // Execute query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('levels')
          .select('*')
          .eq('is_active', true)
          .order('sort_order')
      });

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch levels', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        };
      }

      return {
        success: true,
        data: data || []
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch levels', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Get all available categories with hierarchical structure
   */
  async getCategories(): Promise<ApiResponse<Category[]>> {
    try {
      // Execute query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('categories')
          .select(`
            *,
            parent:parent_id (
              id,
              key,
              name,
              category_type
            )
          `)
          .eq('is_active', true)
          .order('parent_id', { nullsFirst: true })
          .order('sort_order')
      });

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch categories', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        };
      }

      const categories: Category[] = (data || []).map(cat => ({
        ...cat,
        parent: cat.parent as Category | undefined
      }));

      return {
        success: true,
        data: categories
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch categories', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Get main categories (those without parent)
   */
  async getMainCategories(): Promise<ApiResponse<Category[]>> {
    try {
      const { data, error } = await this.db
        .from('categories')
        .select('*')
        .eq('is_active', true)
        .is('parent_id', null)
        .order('sort_order');

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        };
      }

      return {
        success: true,
        data: data || []
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch main categories', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Get subcategories for a specific parent category
   */
  async getSubCategories(parentCategoryKey: string): Promise<ApiResponse<Category[]>> {
    try {
      const { data, error } = await this.db
        .from('categories')
        .select(`
          *,
          parent:parent_id!inner (
            id,
            key,
            name
          )
        `)
        .eq('is_active', true)
        .eq('parent.key', parentCategoryKey)
        .neq('category_type', 'level')
        .order('sort_order');

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        };
      }

      const categories: Category[] = (data || []).map(item => ({
        ...item,
        parent: item.parent ? {
          ...item.parent,
          category_type: null,
          sort_order: null,
          is_active: null,
          created_at: null,
          updated_at: null,
          color: null,
          description: null,
          icon: null,
          parent_id: null,
          parent: undefined
        } : undefined
      }));

      return {
        success: true,
        data: categories
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch subcategories', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Get quiz by level and category
   */
  async getQuizByLevelAndCategory(levelKey: string, categoryKey: string): Promise<ApiResponse<Quiz>> {
    try {
      const { data: quizData, error: quizError } = await this.db
        .from('quizzes')
        .select(`
          *,
          categories!inner (
            id,
            key,
            name,
            description,
            category_type
          ),
          levels!inner (
            id,
            system,
            key,
            name,
            description,
            sort_order
          ),
          quiz_types (
            id,
            key,
            name,
            description
          )
        `)
        .eq('categories.key', categoryKey)
        .eq('levels.key', levelKey)
        .eq('is_active', true)
        .limit(1)
        .single();

      if (quizError) {
        return {
          success: false,
          error: { message: quizError.message, code: quizError.code }
        };
      }

      if (!quizData) {
        return {
          success: false,
          error: { message: 'No quiz found for the specified criteria', code: 'NOT_FOUND' }
        };
      }

      const quiz: Quiz = {
        ...quizData,
        category: (quizData.categories as Category),
        level: (quizData.levels as Level),
        quiz_type: (quizData.quiz_types as QuizMode),
      };

      return {
        success: true,
        data: quiz
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Get quiz by key
   */
  async getQuizByKey(quizKey: string): Promise<ApiResponse<Quiz>> {
    try {
      const { data: quizData, error: quizError } = await this.db
        .from('quizzes')
        .select(`
          *,
          categories (
            id,
            key,
            name,
            description,
            category_type
          ),
          levels (
            id,
            system,
            key,
            name,
            description,
            sort_order
          ),
          quiz_types (
            id,
            key,
            name,
            description
          )
        `)
        .eq('key', quizKey)
        .eq('is_active', true)
        .single();

      if (quizError) {
        return {
          success: false,
          error: { message: quizError.message, code: quizError.code }
        };
      }

      if (!quizData) {
        return {
          success: false,
          error: { message: 'Quiz not found', code: 'NOT_FOUND' }
        };
      }

      const quiz: Quiz = {
        ...quizData,
        category: (quizData.categories as Category),
        level: (quizData.levels as Level),
        quiz_type: (quizData.quiz_types as QuizMode),
      };

      return {
        success: true,
        data: quiz
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Get questions for a specific quiz with options and explanations
   * Updated to use the many-to-many relationship through quiz_questions table
   */
  async getQuizQuestions(quizId: string): Promise<ApiResponse<QuizQuestion[]>> {
    try {
      // Query through the quiz_questions junction table to get questions with quiz-specific metadata using circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('quiz_questions')
          .select(`
            id,
            order_index,
            points,
            is_active,
            questions (
              *,
              question_types (
                id,
                key,
                name,
                description,
                requires_options
              ),
              question_options (
                id,
                option_key,
                option_text,
                is_correct,
                sort_order
              ),
              explanations (
                id,
                content,
                explanation_type,
                metadata
              )
            )
          `)
          .eq('quiz_id', quizId)
          .eq('is_active', true)
          .order('order_index', { ascending: true })
      });

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch quiz questions', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data: quizQuestionsData, error: questionsError } = result.data!

      if (questionsError) {
        return {
          success: false,
          error: { message: questionsError.message, code: questionsError.code }
        };
      }

      if (!quizQuestionsData || quizQuestionsData.length === 0) {
        return {
          success: true,
          data: []
        };
      }

      // Transform the quiz_questions data to QuizQuestion format
      const questions: QuizQuestion[] = quizQuestionsData.map((quizQuestion: any) => {
        const questionData = quizQuestion.questions;
        const questionOptions = questionData.question_options || [];
        const questionExplanations = questionData.explanations || [];

        const correctOption = questionOptions.find((opt: any) => opt.is_correct);
        const correct_answer = correctOption?.option_text || '';

        return {
          // Question data from the nested questions object
          ...questionData,
          // Quiz-specific metadata from quiz_questions junction table
          order_index: quizQuestion.order_index,
          points: quizQuestion.points,
          quiz_question_id: quizQuestion.id,
          is_quiz_active: quizQuestion.is_active,
          // Transformed related data
          question_type: questionData.question_types?.key as QuizMode,
          options: questionOptions as QuestionOption[],
          explanations: questionExplanations as Explanation[],
          correct_answer,
        };
      });

      return {
        success: true,
        data: questions
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz questions', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Save a quiz attempt with user answers
   */
  async saveQuizAttempt(userId: string, quizId: string, results: QuizResults): Promise<ApiResponse<{ success: boolean }>> {
    try {
      const attemptData = {
        user_id: userId,
        quiz_id: quizId,
        mode: results.mode,
        score: results.percentage,
        total_questions: results.total,
        correct_answers: results.correct,
        time_taken_seconds: results.timeSpent,
        completed_at: new Date().toISOString(),
        is_completed: true
      };

      const { data: attemptResult, error: attemptError } = await this.db
        .from('quiz_attempts')
        .insert(attemptData)
        .select()
        .single();

      if (attemptError) {
        return {
          success: false,
          error: { message: attemptError.message, code: attemptError.code }
        };
      }

      const userAnswersData = results.answers.map(answer => ({
        attempt_id: attemptResult.id,
        question_id: answer.questionId,
        user_answer: answer.userAnswer,
        is_correct: answer.isCorrect,
        time_taken_seconds: answer.timeSpent || 0
      }));

      const { error: answersError } = await this.db
        .from('user_answers')
        .insert(userAnswersData);

      if (answersError) {
        return {
          success: false,
          error: { message: answersError.message, code: answersError.code }
        };
      }

      await this.updateUserProgress(userId, quizId, results);

      return {
        success: true,
        data: { success: true }
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to save quiz attempt', code: 'SAVE_ERROR' }
      };
    }
  }

  /**
   * Update user progress for the quiz category and level
   */
  private async updateUserProgress(userId: string, quizId: string, results: QuizResults): Promise<void> {
    try {
      const { data: quiz } = await this.db
        .from('quizzes')
        .select('category_id, level_id')
        .eq('id', quizId)
        .single();

      if (!quiz) return;

      const { data: existingProgress } = await this.db
        .from('user_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('category_id', quiz.category_id)
        .eq('level_id', quiz.level_id)
        .single();

      const newAttempts = (existingProgress?.total_attempts || 0) + 1;
      const newCorrect = (existingProgress?.total_correct || 0) + results.correct;
      const newQuestions = (existingProgress?.total_questions || 0) + results.total;
      const newAverage = Math.round((newCorrect / newQuestions) * 100);
      const newBest = Math.max(existingProgress?.best_score || 0, results.percentage);

      const passed = results.percentage >= 70;
      const newCurrentStreak = passed ? (existingProgress?.current_streak || 0) + 1 : 0;
      const newLongestStreak = Math.max(
        existingProgress?.longest_streak || 0,
        newCurrentStreak
      );

      const progressData = {
        user_id: userId,
        category_id: quiz.category_id,
        level_id: quiz.level_id,
        total_attempts: newAttempts,
        total_correct: newCorrect,
        total_questions: newQuestions,
        average_score: newAverage,
        best_score: newBest,
        current_streak: newCurrentStreak,
        longest_streak: newLongestStreak,
        last_attempt_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (existingProgress) {
        await this.db
          .from('user_progress')
          .update(progressData)
          .eq('id', existingProgress.id);
      } else {
        await this.db
          .from('user_progress')
          .insert(progressData);
      }
    } catch (_error) {
      console.error('Error loading progress data:', _error);
    }
  }

  /**
   * Get user statistics across all attempts
   */
  async getUserStats(userId: string): Promise<ApiResponse<{
    totalAttempts: number;
    averageScore: number;
    bestScore: number;
    totalTimeSpent: number;
  }>> {
    try {
      const { data: attemptsData, error: attemptsError } = await this.db
        .from('quiz_attempts')
        .select('score, time_taken_seconds, created_at')
        .eq('user_id', userId)
        .eq('is_completed', true);

      if (attemptsError) {
        return {
          success: false,
          error: { message: attemptsError.message, code: attemptsError.code }
        };
      }

      if (!attemptsData || attemptsData.length === 0) {
        return {
          success: true,
          data: {
            totalAttempts: 0,
            averageScore: 0,
            bestScore: 0,
            totalTimeSpent: 0
          }
        };
      }

      const totalAttempts = attemptsData.length;
      const averageScore = Math.round(
        attemptsData.reduce((sum, attempt) => sum + attempt.score, 0) / totalAttempts
      );
      const bestScore = Math.max(...attemptsData.map(attempt => attempt.score));
      const totalTimeSpent = attemptsData.reduce(
        (sum, attempt) => sum + (attempt.time_taken_seconds || 0),
        0
      );

      return {
        success: true,
        data: {
          totalAttempts,
          averageScore,
          bestScore,
          totalTimeSpent
        }
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch user stats', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Get quiz by ID for results screen
   */
  async getQuizForResults(quizId: string): Promise<ApiResponse<Quiz>> {
    try {
      const { data: quizData, error } = await this.db
        .from('quizzes')
        .select(`
          *,
          categories!inner (
            id,
            key,
            name,
            description,
            category_type
          ),
          levels!inner (
            id,
            system,
            key,
            name,
            description,
            sort_order
          ),
          quiz_types (
            id,
            key,
            name,
            description
          )
        `)
        .eq('id', quizId)
        .single();

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        };
      }

      if (!quizData) {
        return {
          success: false,
          error: { message: 'Quiz not found', code: 'NOT_FOUND' }
        };
      }

      const quiz: Quiz = {
        ...quizData,
        category: (quizData.categories as Category),
        level: (quizData.levels as Level),
        quiz_type: (quizData.quiz_types as QuizMode),
      };

      return {
        success: true,
        data: quiz
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Get quiz attempt details with questions and user answers
   */
  async getQuizAttemptDetails(attemptId: string): Promise<ApiResponse<{
    questions: QuizQuestion[];
    userAnswers: Record<string, string>;
    attempt: QuizAttempt;
  }>> {
    try {
      const { data: attemptData, error: attemptError } = await this.db
        .from('quiz_attempts')
        .select('*')
        .eq('id', attemptId)
        .single();

      if (attemptError || !attemptData) {
        return {
          success: false,
          error: { message: attemptError?.message || 'Attempt not found', code: attemptError?.code || 'NOT_FOUND' }
        };
      }

      const { data: userAnswersData, error: answersError } = await this.db
        .from('user_answers')
        .select('question_id, user_answer, is_correct')
        .eq('attempt_id', attemptId);

      if (answersError) {
        return {
          success: false,
          error: { message: answersError.message, code: answersError.code }
        };
      }

      // Get questions through quiz_questions junction table
      const { data: quizQuestionsData, error: questionsError } = await this.db
        .from('quiz_questions')
        .select(`
          id,
          order_index,
          points,
          is_active,
          questions (
            *,
            question_options (
              id,
              option_key,
              option_text,
              is_correct,
              sort_order
            )
          )
        `)
        .eq('quiz_id', attemptData.quiz_id!)
        .order('order_index', { ascending: true });

      if (questionsError) {
        return {
          success: false,
          error: { message: questionsError.message, code: questionsError.code }
        };
      }

      const questions: QuizQuestion[] = (quizQuestionsData || []).map((quizQuestion: any) => {
        const q = quizQuestion.questions;
        const questionOptions = (q.question_options as QuestionOption[]) || [];
        const correctOption = questionOptions.find(option => option.is_correct);

        return {
          id: q.id,
          quiz_id: q.quiz_id,
          question_type_id: q.question_type_id,
          difficulty_level: q.difficulty_level,
          order_index: quizQuestion.order_index, // From quiz_questions table
          points: quizQuestion.points, // From quiz_questions table
          is_active: q.is_active,
          created_at: q.created_at,
          updated_at: q.updated_at,
          question_text: q.question_text,
          metadata: q.metadata,
          correct_answer: correctOption?.option_text || null,
          options: questionOptions.map(option => ({
            id: option.id,
            question_id: q.id,
            option_key: option.option_key,
            option_text: option.option_text,
            is_correct: option.is_correct,
            sort_order: option.sort_order,
            created_at: option.created_at || null
          })),
          explanations: [] // Add empty explanations array for consistency
        };
      });

      const userAnswers: Record<string, string> = {};
      (userAnswersData || []).forEach(answer => {
        if (answer.question_id) {
          userAnswers[answer.question_id] = answer.user_answer;
        }
      });

      const attempt: QuizAttempt = {
        id: attemptData.id,
        user_id: attemptData.user_id || '',
        quiz_id: attemptData.quiz_id || '',
        mode: attemptData.mode as QuizMode,
        score: attemptData.score,
        correct_answers: attemptData.correct_answers,
        total_questions: attemptData.total_questions,
        time_taken_seconds: attemptData.time_taken_seconds || undefined,
        completed_at: attemptData.completed_at || undefined,
        is_completed: attemptData.is_completed || false,
        created_at: attemptData.created_at || '',
        updated_at: attemptData.updated_at || ''
      };

      return {
        success: true,
        data: {
          questions,
          userAnswers,
          attempt
        }
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch quiz attempt details', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Search quizzes with filters
   */
  async searchQuizzes(params: {
    searchTerm?: string;
    categoryKey?: string;
    subCategoryKey?: string;
    levelKey?: string;
    difficulty?: number;
    page?: number;
    pageSize?: number;
  }): Promise<ApiResponse<{
    data: Quiz[];
    totalCount: number;
    totalPages: number;
  }>> {
    try {
      let query = this.db
        .from('quizzes')
        .select(`
          *,
          categories!inner (
            id,
            key,
            name,
            description,
            category_type,
            parent_id,
            parent:parent_id (
              id,
              key,
              name,
              category_type
            )
          ),
          levels!inner (
            id,
            system,
            key,
            name,
            description,
            sort_order
          ),
          quiz_types (
            id,
            key,
            name,
            description
          )
        `)
        .eq('is_active', true);

      if (params.searchTerm) {
        query = query.or(`title.ilike.%${params.searchTerm}%,description.ilike.%${params.searchTerm}%`);
      }

      if (params.subCategoryKey) {
        query = query.eq('categories.key', params.subCategoryKey);
      } else if (params.categoryKey && params.categoryKey !== 'all') {
        const categoryIds = await this.getCategoryAndSubcategoryIds(params.categoryKey);
        if (categoryIds.length > 0) {
          query = query.in('category_id', categoryIds);
        }
      }

      if (params.levelKey && params.levelKey !== 'all') {
        query = query.eq('levels.key', params.levelKey);
      }

      if (params.difficulty) {
        query = query.eq('difficulty_level', params.difficulty);
      }

      const page = params.page || 1;
      const pageSize = params.pageSize || 10;

      const { data: allQuizzesData, error } = await query;

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        };
      }

      const allQuizzes: Quiz[] = (allQuizzesData || []).map(quizData => ({
        ...quizData,
        category: (quizData.categories as Category),
        level: (quizData.levels as Level),
        quiz_type: (quizData.quiz_types as QuizMode),
      }));

      const sortedQuizzes = allQuizzes.sort((a, b) => {
        const levelSortA = a.level?.sort_order || 0;
        const levelSortB = b.level?.sort_order || 0;

        if (levelSortA !== levelSortB) {
          return levelSortB - levelSortA;
        }

        return (a.title || '').localeCompare(b.title || '');
      });

      const finalTotalCount = sortedQuizzes.length;
      const finalTotalPages = Math.ceil(finalTotalCount / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedQuizzes = sortedQuizzes.slice(startIndex, endIndex);

      return {
        success: true,
        data: {
          data: paginatedQuizzes,
          totalCount: finalTotalCount,
          totalPages: finalTotalPages
        }
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to search quizzes', code: 'SEARCH_ERROR' }
      };
    }
  }

  /**
   * Helper method to get category and subcategory IDs for filtering
   */
  private async getCategoryAndSubcategoryIds(categoryKey: string): Promise<string[]> {
    try {
      const { data: mainCategory, error: mainError } = await this.db
        .from('categories')
        .select('id')
        .eq('key', categoryKey)
        .eq('is_active', true)
        .single();

      if (mainError || !mainCategory) {
        return [];
      }

      const { data: subCategories, error: subCategoriesError } = await this.db
        .from('categories')
        .select('id')
        .eq('parent_id', mainCategory.id)
        .eq('is_active', true);

      if (subCategoriesError) {
        return [mainCategory.id];
      }

      const allIds = [mainCategory.id];
      if (subCategories) {
        allIds.push(...subCategories.map(cat => cat.id));
      }

      return allIds;
    } catch {
      return [];
    }
  }

  /**
   * Get user profile data
   */
  async getUserProfile(userId: string): Promise<ApiResponse<User>> {
    try {
      const { data: userData, error } = await this.db
        .from('users')
        .select(`
          *,
          levels (
            id,
            system,
            key,
            name,
            description,
            sort_order
          )
        `)
        .eq('id', userId)
        .single();

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        };
      }

      if (!userData) {
        return {
          success: false,
          error: { message: 'User profile not found', code: 'NOT_FOUND' }
        };
      }

      const user: User = {
        id: userData.id,
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        level_id: userData.level_id,
        level: userData.levels as Level,
        created_at: userData.created_at || '',
        updated_at: userData.updated_at || ''
      };

      return {
        success: true,
        data: user
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch user profile', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Update user profile data
   */
  async updateUserProfile(
    userId: string,
    profileData: {
      first_name?: string;
      last_name?: string;
      level_id?: string;
    }
  ): Promise<ApiResponse<User>> {
    try {
      const updateData = {
        ...profileData,
        updated_at: new Date().toISOString()
      };

      const { data: userData, error } = await this.db
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select(`
          *,
          levels (
            id,
            system,
            key,
            name,
            description,
            sort_order
          )
        `)
        .single();

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        };
      }

      if (!userData) {
        return {
          success: false,
          error: { message: 'Failed to update user profile', code: 'UPDATE_ERROR' }
        };
      }

      const user: User = {
        id: userData.id,
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        level_id: userData.level_id,
        level: userData.levels as Level,
        created_at: userData.created_at || '',
        updated_at: userData.updated_at || ''
      };

      return {
        success: true,
        data: user
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to update user profile', code: 'UPDATE_ERROR' }
      };
    }
  }

  /**
   * Get user quiz attempts with quiz details
   */
  async getUserQuizAttempts(userId: string): Promise<ApiResponse<QuizAttempt[]>> {
    try {
      const { data, error } = await this.db
        .from('quiz_attempts')
        .select(`
          *,
          quizzes (
            id,
            title,
            key
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        };
      }

      return {
        success: true,
        data: data as QuizAttempt[] || []
      };
    } catch {
      return {
        success: false,
        error: { message: 'Failed to fetch user quiz attempts', code: 'FETCH_ERROR' }
      };
    }
  }

  /**
   * Apply filters to quiz queries (user-safe filters only)
   */
  protected applyFilters(query: unknown, filters: Record<string, unknown>): unknown {
    let filteredQuery = query

    if (filters.categoryId) {
      filteredQuery = filteredQuery.eq('category_id', filters.categoryId)
    }

    if (filters.levelId) {
      filteredQuery = filteredQuery.eq('level_id', filters.levelId)
    }

    if (filters.search) {
      filteredQuery = filteredQuery.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
    }

    return filteredQuery
  }
}