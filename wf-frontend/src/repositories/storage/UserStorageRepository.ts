// User storage repository for frontend
// Handles user preferences and session data in localStorage

import { BaseStorageRepository } from 'wf-shared/repositories'
import { APP_CONSTANTS } from 'wf-shared/constants'

export interface UserPreferences {
  language: string
  theme: string
  quizMode: string
}

export class UserStorageRepository extends BaseStorageRepository<UserPreferences> {
  protected storage = localStorage
  protected keyPrefix = 'user'

  /**
   * Save user language preference
   */
  async saveLanguage(language: string): Promise<void> {
    const preferences = await this.getPreferences()
    await this.save(APP_CONSTANTS.STORAGE_KEYS.LANGUAGE, {
      ...preferences,
      language
    })
  }

  /**
   * Save user theme preference
   */
  async saveTheme(theme: string): Promise<void> {
    const preferences = await this.getPreferences()
    await this.save(APP_CONSTANTS.STORAGE_KEYS.THEME, {
      ...preferences,
      theme
    })
  }

  /**
   * Save quiz mode preference
   */
  async saveQuizMode(quizMode: string): Promise<void> {
    const preferences = await this.getPreferences()
    await this.save(APP_CONSTANTS.STORAGE_KEYS.QUIZ_MODE, {
      ...preferences,
      quizMode
    })
  }

  /**
   * Get all user preferences
   */
  async getPreferences(): Promise<UserPreferences> {
    const language = await this.load(APP_CONSTANTS.STORAGE_KEYS.LANGUAGE) || { language: 'en' }
    const theme = await this.load(APP_CONSTANTS.STORAGE_KEYS.THEME) || { theme: 'light' }
    const quizMode = await this.load(APP_CONSTANTS.STORAGE_KEYS.QUIZ_MODE) || { quizMode: 'practice' }

    return {
      language: language.language || 'en',
      theme: theme.theme || 'light',
      quizMode: quizMode.quizMode || 'practice'
    }
  }

  /**
   * Clear all user preferences
   */
  async clearPreferences(): Promise<void> {
    await this.remove(APP_CONSTANTS.STORAGE_KEYS.LANGUAGE)
    await this.remove(APP_CONSTANTS.STORAGE_KEYS.THEME)
    await this.remove(APP_CONSTANTS.STORAGE_KEYS.QUIZ_MODE)
  }
}