// Auth storage repository for frontend
// Handles authentication-related data in localStorage using repository pattern

import { BaseStorageRepository } from 'wf-shared/repositories'
import { APP_CONSTANTS } from 'wf-shared/constants'

export interface RememberedCredentials {
  email: string
  rememberMe: boolean
}

export interface UserSession {
  userId: string
  email: string
  accessToken?: string
  refreshToken?: string
  lastActive: string
}

export class AuthStorageRepository extends BaseStorageRepository<RememberedCredentials | UserSession> {
  protected storage = localStorage
  protected keyPrefix = 'user'

  /**
   * Save user credentials for remember me functionality
   */
  async saveCredentials(email: string, rememberMe: boolean): Promise<void> {
    if (rememberMe) {
      const credentials: RememberedCredentials = { email, rememberMe }
      await this.save(APP_CONSTANTS.STORAGE_KEYS.USER_CREDENTIALS, credentials)
    } else {
      await this.clearCredentials()
    }
  }

  /**
   * Load remembered credentials
   */
  async loadCredentials(): Promise<RememberedCredentials> {
    const credentials = await this.load(APP_CONSTANTS.STORAGE_KEYS.USER_CREDENTIALS) as RememberedCredentials
    
    if (credentials) {
      return {
        email: credentials.email || '',
        rememberMe: credentials.rememberMe || false
      }
    }

    return { email: '', rememberMe: false }
  }

  /**
   * Clear stored credentials
   */
  async clearCredentials(): Promise<void> {
    await this.remove(APP_CONSTANTS.STORAGE_KEYS.USER_CREDENTIALS)
  }

  /**
   * Check if credentials are stored
   */
  async hasStoredCredentials(): Promise<boolean> {
    return await this.exists(APP_CONSTANTS.STORAGE_KEYS.USER_CREDENTIALS)
  }

  /**
   * Save user session data
   */
  async saveSession(session: UserSession): Promise<void> {
    await this.save(APP_CONSTANTS.STORAGE_KEYS.USER_SESSION, session)
  }

  /**
   * Load user session data
   */
  async loadSession(): Promise<UserSession | null> {
    return await this.load(APP_CONSTANTS.STORAGE_KEYS.USER_SESSION) as UserSession | null
  }

  /**
   * Clear user session
   */
  async clearSession(): Promise<void> {
    await this.remove(APP_CONSTANTS.STORAGE_KEYS.USER_SESSION)
  }

  /**
   * Update last active timestamp
   */
  async updateLastActive(userId: string): Promise<void> {
    const session = await this.loadSession()
    if (session && session.userId === userId) {
      session.lastActive = new Date().toISOString()
      await this.saveSession(session)
    }
  }

  /**
   * Check if session exists and is valid
   */
  async hasValidSession(): Promise<boolean> {
    const session = await this.loadSession()
    if (!session) return false

    // Check if session is within last 24 hours
    const lastActive = new Date(session.lastActive)
    const now = new Date()
    const hoursDiff = (now.getTime() - lastActive.getTime()) / (1000 * 60 * 60)
    
    return hoursDiff < 24
  }
}