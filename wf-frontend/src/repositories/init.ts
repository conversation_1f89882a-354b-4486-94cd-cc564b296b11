// Frontend Repository Initialization
// Initializes repository system with environment configuration

import { FrontendRepositoryManager } from './FrontendRepositoryManager'

// Initialize repository system
const supabaseUrl: string = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey: string = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.')
}

// Initialize the repository system
FrontendRepositoryManager.initialize({
  supabaseUrl,
  supabaseAnonKey
})

// Export the repositories for use throughout the app
export { repositories } from './FrontendRepositoryManager'