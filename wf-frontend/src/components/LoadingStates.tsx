import React from 'react';
import { Loader2, RefreshCw } from 'lucide-react';

// Basic loading spinner
export const LoadingSpinner: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  return (
    <Loader2 
      className={`animate-spin ${sizeClasses[size]} ${className}`}
      aria-label="Loading"
    />
  );
};

// Inline loading state for buttons and small components
export const InlineLoading: React.FC<{
  text?: string;
  size?: 'sm' | 'md';
}> = ({ text = 'Loading...', size = 'sm' }) => (
  <div className="inline-flex items-center gap-2">
    <LoadingSpinner size={size} className="text-current" />
    <span className="text-sm">{text}</span>
  </div>
);

// Full screen loading overlay
export const FullScreenLoading: React.FC<{
  message?: string;
  backdrop?: boolean;
}> = ({ message = 'Loading...', backdrop = true }) => (
  <div className={`fixed inset-0 z-50 flex items-center justify-center ${
    backdrop ? 'bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm' : ''
  }`}>
    <div className="text-center">
      <LoadingSpinner size="lg" className="text-blue-600 dark:text-blue-400 mx-auto mb-4" />
      <p className="text-gray-600 dark:text-gray-300 font-medium">{message}</p>
    </div>
  </div>
);

// Page-level loading component
export const PageLoading: React.FC<{
  message?: string;
  className?: string;
}> = ({ message = 'Loading...', className = '' }) => (
  <div className={`flex items-center justify-center py-12 ${className}`}>
    <div className="text-center">
      <LoadingSpinner size="lg" className="text-blue-600 dark:text-blue-400 mx-auto mb-4" />
      <p className="text-gray-600 dark:text-gray-300 font-medium">{message}</p>
    </div>
  </div>
);

// Card skeleton loader
export const CardSkeleton: React.FC<{
  rows?: number;
  className?: string;
}> = ({ rows = 3, className = '' }) => (
  <div className={`animate-pulse p-4 ${className}`}>
    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-3"></div>
    {Array.from({ length: rows }).map((_, i) => (
      <div key={i} className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"></div>
    ))}
    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
  </div>
);

// List skeleton loader
export const ListSkeleton: React.FC<{
  items?: number;
  itemHeight?: string;
  className?: string;
}> = ({ items = 5, itemHeight = 'h-16', className = '' }) => (
  <div className={`space-y-3 ${className}`}>
    {Array.from({ length: items }).map((_, i) => (
      <div key={i} className={`animate-pulse ${itemHeight} bg-gray-200 dark:bg-gray-700 rounded`}></div>
    ))}
  </div>
);

// Button loading state
export const LoadingButton: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}> = ({ 
  loading, 
  children, 
  onClick, 
  disabled = false, 
  variant = 'primary',
  size = 'md',
  className = '',
  type = 'button'
}) => {
  const baseClasses = 'inline-flex items-center justify-center gap-2 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-blue-500'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
    >
      {loading ? (
        <>
          <LoadingSpinner size="sm" className="text-current" />
          Loading...
        </>
      ) : (
        children
      )}
    </button>
  );
};

// Progress bar component
export const ProgressBar: React.FC<{
  value: number; // 0-100
  showLabel?: boolean;
  label?: string;
  className?: string;
  barClassName?: string;
}> = ({ value, showLabel = false, label, className = '', barClassName = '' }) => {
  const clampedValue = Math.max(0, Math.min(100, value));
  
  return (
    <div className={className}>
      {showLabel && (
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-1">
          <span>{label}</span>
          <span>{Math.round(clampedValue)}%</span>
        </div>
      )}
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div 
          className={`h-2 rounded-full transition-all duration-300 ease-out ${barClassName || 'bg-blue-600'}`}
          style={{ width: `${clampedValue}%` }}
        />
      </div>
    </div>
  );
};

// Retry button component
export const RetryButton: React.FC<{
  onRetry: () => void;
  loading?: boolean;
  text?: string;
  className?: string;
}> = ({ onRetry, loading = false, text = 'Try again', className = '' }) => (
  <button
    onClick={onRetry}
    disabled={loading}
    className={`inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${className}`}
  >
    <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
    {loading ? 'Retrying...' : text}
  </button>
);

// Composite loading state component
export const LoadingState: React.FC<{
  loading: boolean;
  error?: string | Error | null;
  onRetry?: () => void;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  children: React.ReactNode;
}> = ({ 
  loading, 
  error, 
  onRetry, 
  loadingComponent, 
  errorComponent, 
  children 
}) => {
  if (loading) {
    return <>{loadingComponent || <PageLoading />}</>;
  }

  if (error) {
    if (errorComponent) {
      return <>{errorComponent}</>;
    }

    const errorMessage = error instanceof Error ? error.message : error;
    
    return (
      <div className="text-center py-8">
        <p className="text-red-600 dark:text-red-400 mb-4">
          {errorMessage || 'An error occurred'}
        </p>
        {onRetry && <RetryButton onRetry={onRetry} />}
      </div>
    );
  }

  return <>{children}</>;
};

export default {
  LoadingSpinner,
  InlineLoading,
  FullScreenLoading,
  PageLoading,
  CardSkeleton,
  ListSkeleton,
  LoadingButton,
  ProgressBar,
  RetryButton,
  LoadingState
};