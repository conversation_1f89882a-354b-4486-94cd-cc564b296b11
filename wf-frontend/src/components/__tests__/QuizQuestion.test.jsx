import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import QuizQuestion from '../QuizQuestion';
import { LanguageWrapper } from '../../test-utils/TestWrapper';

describe('QuizQuestion', () => {
  const mockQuestion = {
    id: 'q1',
    quiz_id: 'quiz1',
    question_text: 'What is the past tense of "go"?',
    correct_answer: 'went',
    options: [
      { id: 'opt1', question_id: 'q1', option_key: 'A', option_text: 'went', is_correct: true, sort_order: 1 },
      { id: 'opt2', question_id: 'q1', option_key: 'B', option_text: 'gone', is_correct: false, sort_order: 2 },
      { id: 'opt3', question_id: 'q1', option_key: 'C', option_text: 'goes', is_correct: false, sort_order: 3 },
      { id: 'opt4', question_id: 'q1', option_key: 'D', option_text: 'going', is_correct: false, sort_order: 4 }
    ],
    explanations: [
      { id: 'exp1', question_id: 'q1', explanation_type: 'correct_answer', content: 'The past tense of "go" is "went".' }
    ]
  };

  const defaultProps = {
    question: mockQuestion,
    questionNumber: 1,
    totalQuestions: 10,
    selectedAnswer: undefined,
    onAnswerSelect: vi.fn(),
    showFeedback: false,
    mode: 'practice'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders question text and options', () => {
    render(
      <LanguageWrapper>
        <QuizQuestion {...defaultProps} />
      </LanguageWrapper>
    );

    // Check question text
    expect(screen.getByText('What is the past tense of "go"?')).toBeInTheDocument();

    // Check all option texts
    expect(screen.getByText('went')).toBeInTheDocument();
    expect(screen.getByText('gone')).toBeInTheDocument();
    expect(screen.getByText('goes')).toBeInTheDocument();
    expect(screen.getByText('going')).toBeInTheDocument();

    // Check option keys (A, B, C, D)
    expect(screen.getByText('A')).toBeInTheDocument();
    expect(screen.getByText('B')).toBeInTheDocument();
    expect(screen.getByText('C')).toBeInTheDocument();
    expect(screen.getByText('D')).toBeInTheDocument();
  });

  it('renders all option buttons', () => {
    render(
      <LanguageWrapper>
        <QuizQuestion {...defaultProps} />
      </LanguageWrapper>
    );

    const buttons = screen.getAllByRole('button');
    expect(buttons).toHaveLength(4); // 4 option buttons
  });

  it('calls onAnswerSelect when option is clicked', () => {
    const mockOnAnswerSelect = vi.fn();

    render(
      <LanguageWrapper>
        <QuizQuestion {...defaultProps} onAnswerSelect={mockOnAnswerSelect} />
      </LanguageWrapper>
    );

    const firstOption = screen.getByText('went');
    fireEvent.click(firstOption);

    // Should be called with the option text, not index
    expect(mockOnAnswerSelect).toHaveBeenCalledWith('went');
  });

  it('shows explanation when showFeedback is true and mode is practice', () => {
    render(
      <LanguageWrapper>
        <QuizQuestion {...defaultProps} showFeedback={true} mode="practice" selectedAnswer="went" />
      </LanguageWrapper>
    );

    expect(screen.getByText('The past tense of "go" is "went".')).toBeInTheDocument();
  });

  it('does not show explanation when showFeedback is false', () => {
    render(
      <LanguageWrapper>
        <QuizQuestion {...defaultProps} showFeedback={false} />
      </LanguageWrapper>
    );

    expect(screen.queryByText('The past tense of "go" is "went".')).not.toBeInTheDocument();
  });

  it('does not show explanation in test mode even with showFeedback true', () => {
    render(
      <LanguageWrapper>
        <QuizQuestion {...defaultProps} showFeedback={true} mode="test" selectedAnswer="went" />
      </LanguageWrapper>
    );

    expect(screen.queryByText('The past tense of "go" is "went".')).not.toBeInTheDocument();
  });

  it('highlights selected answer', () => {
    render(
      <LanguageWrapper>
        <QuizQuestion {...defaultProps} selectedAnswer="went" />
      </LanguageWrapper>
    );

    const selectedButton = screen.getByText('went').closest('button');
    expect(selectedButton).toHaveClass('border-blue-500'); // Check for blue border indicating selection
  });

  it('shows correct/incorrect feedback in practice mode', () => {
    render(
      <LanguageWrapper>
        <QuizQuestion {...defaultProps} selectedAnswer="went" showFeedback={true} mode="practice" />
      </LanguageWrapper>
    );

    // Should show correct feedback badge in header since "went" is the correct answer
    const feedbackBadge = screen.getByText('quiz.correct');
    expect(feedbackBadge).toBeInTheDocument();
    expect(feedbackBadge).toHaveClass('bg-green-100'); // Check it's the correct (green) badge
  });

  it('shows correct answer highlighting in practice mode with feedback', () => {
    render(
      <LanguageWrapper>
        <QuizQuestion {...defaultProps} selectedAnswer="gone" showFeedback={true} mode="practice" />
      </LanguageWrapper>
    );

    const correctButton = screen.getByText('went').closest('button');
    const incorrectButton = screen.getByText('gone').closest('button');

    expect(correctButton).toHaveClass('border-green-500'); // Correct answer highlighted in green
    expect(incorrectButton).toHaveClass('border-red-500'); // Wrong selected answer highlighted in red
  });

  it('renders without crashing with minimal props', () => {
    const minimalProps = {
      question: mockQuestion,
      questionNumber: 1,
      totalQuestions: 1,
      selectedAnswer: undefined,
      onAnswerSelect: vi.fn(),
      showFeedback: false,
      mode: 'test'
    };

    render(
      <LanguageWrapper>
        <QuizQuestion {...minimalProps} />
      </LanguageWrapper>
    );

    expect(screen.getByText('What is the past tense of "go"?')).toBeInTheDocument();
  });
});
