import React from 'react';
import { Link } from 'react-router-dom';
import { BookOpen } from 'lucide-react';
import Footer from './Footer';
import { useLanguage } from '@/context/LanguageContext';

interface PublicLayoutProps {
  children: React.ReactNode;
}

/**
 * PublicLayout component for pages that should be accessible to non-authenticated users
 * Only includes Footer, no TopNavigation
 */
const PublicLayout = ({ children }: PublicLayoutProps) => {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header with Logo */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Brand */}
            <div className="flex items-center">
              <Link to="/" className="flex items-center">
                <div className="bg-blue-600 p-2 rounded-lg mr-2 sm:mr-3">
                  <BookOpen className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                </div>
                <h1 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">
                  {t('landing.appName')}
                </h1>
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1 w-full">
        <div className="min-h-[calc(100vh-8rem)]">
          {children}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default PublicLayout;
