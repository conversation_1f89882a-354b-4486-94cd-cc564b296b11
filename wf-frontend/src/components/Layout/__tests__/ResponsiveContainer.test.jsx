import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import ResponsiveContainer, { PageWrapper, ResponsiveCard } from '../ResponsiveContainer';
import { FullTestWrapper } from '../../../test-utils/TestWrapper';

describe('ResponsiveContainer', () => {
  it('renders children correctly', () => {
    render(
      <ResponsiveContainer>
        <div>Test content</div>
      </ResponsiveContainer>
    );
    
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('applies default responsive classes', () => {
    const { container } = render(
      <ResponsiveContainer>
        <div>Test content</div>
      </ResponsiveContainer>
    );
    
    const containerDiv = container.firstChild;
    expect(containerDiv).toHaveClass('w-full', 'max-w-7xl', 'mx-auto', 'px-4', 'sm:px-6', 'lg:px-8');
  });

  it('accepts custom maxWidth prop', () => {
    const { container } = render(
      <ResponsiveContainer maxWidth="max-w-4xl">
        <div>Test content</div>
      </ResponsiveContainer>
    );
    
    const containerDiv = container.firstChild;
    expect(containerDiv).toHaveClass('max-w-4xl');
    expect(containerDiv).not.toHaveClass('max-w-7xl');
  });

  it('accepts custom padding prop', () => {
    const { container } = render(
      <ResponsiveContainer padding="px-2 sm:px-4">
        <div>Test content</div>
      </ResponsiveContainer>
    );
    
    const containerDiv = container.firstChild;
    expect(containerDiv).toHaveClass('px-2', 'sm:px-4');
  });

  it('accepts custom className', () => {
    const { container } = render(
      <ResponsiveContainer className="custom-class">
        <div>Test content</div>
      </ResponsiveContainer>
    );
    
    const containerDiv = container.firstChild;
    expect(containerDiv).toHaveClass('custom-class');
  });

  it('can disable center content', () => {
    const { container } = render(
      <ResponsiveContainer centerContent={false}>
        <div>Test content</div>
      </ResponsiveContainer>
    );
    
    const containerDiv = container.firstChild;
    expect(containerDiv).not.toHaveClass('mx-auto');
  });
});

describe('PageWrapper', () => {
  it('renders children with page layout', () => {
    render(
      <FullTestWrapper>
        <PageWrapper>
          <div>Page content</div>
        </PageWrapper>
      </FullTestWrapper>
    );

    expect(screen.getByText('Page content')).toBeInTheDocument();
  });

  it('applies page wrapper classes', () => {
    const { container } = render(
      <FullTestWrapper>
        <PageWrapper>
          <div>Page content</div>
        </PageWrapper>
      </FullTestWrapper>
    );

    const pageDiv = container.firstChild;
    expect(pageDiv).toHaveClass('min-h-screen', 'bg-gray-50');
  });

  it('accepts custom className', () => {
    const { container } = render(
      <FullTestWrapper>
        <PageWrapper className="custom-page-class">
          <div>Page content</div>
        </PageWrapper>
      </FullTestWrapper>
    );

    const pageDiv = container.firstChild;
    expect(pageDiv).toHaveClass('custom-page-class');
  });
});

describe('ResponsiveCard', () => {
  it('renders children with card styling', () => {
    render(
      <ResponsiveCard>
        <div>Card content</div>
      </ResponsiveCard>
    );
    
    expect(screen.getByText('Card content')).toBeInTheDocument();
  });

  it('applies responsive card classes', () => {
    const { container } = render(
      <ResponsiveCard>
        <div>Card content</div>
      </ResponsiveCard>
    );
    
    const cardDiv = container.firstChild;
    expect(cardDiv).toHaveClass(
      'bg-white', 
      'rounded-lg', 
      'shadow-sm', 
      'border', 
      'border-gray-200', 
      'p-4', 
      'sm:p-6', 
      'lg:p-8'
    );
  });

  it('accepts custom className', () => {
    const { container } = render(
      <ResponsiveCard className="custom-card-class">
        <div>Card content</div>
      </ResponsiveCard>
    );
    
    const cardDiv = container.firstChild;
    expect(cardDiv).toHaveClass('custom-card-class');
  });
}); 