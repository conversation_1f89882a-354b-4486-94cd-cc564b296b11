import React from 'react';
import type { PageWrapperProps, ResponsiveCardProps } from '@/types';
import TopNavigation from '@/components/TopNavigation';
import Footer from '@/components/Footer';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  maxWidth?: string;
  padding?: string;
  className?: string;
  centerContent?: boolean;
}

/**
 * ResponsiveContainer component that provides flexible responsive container layout
 */
const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  maxWidth = 'max-w-7xl',
  padding = 'px-4 sm:px-6 lg:px-8',
  className = '',
  centerContent = true
}) => {
  const centerClass = centerContent ? 'mx-auto' : '';
  const combinedClasses = `w-full ${maxWidth} ${centerClass} ${padding} ${className}`.trim();

  return (
    <div className={combinedClasses}>
      {children}
    </div>
  );
};

/**
 * PageWrapper component that provides consistent page layout
 */
export const PageWrapper: React.FC<PageWrapperProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900 ${className}`}>
      <TopNavigation />
      <ResponsiveContainer className="flex-1">
        {children}
      </ResponsiveContainer>
      <Footer />
    </div>
  );
};

/**
 * ResponsiveCard component that provides consistent card styling across the application
 */
export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({ 
  children, 
  className = '',
  onClick 
}) => {
  const baseClasses = 'bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 lg:p-8';
  const interactiveClasses = onClick ? 'cursor-pointer hover:shadow-md transition-shadow duration-200' : '';
  const combinedClasses = `${baseClasses} ${interactiveClasses} ${className}`;

  if (onClick) {
    return (
      <div 
        className={combinedClasses}
        onClick={onClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        }}
      >
        {children}
      </div>
    );
  }

  return (
    <div className={combinedClasses}>
      {children}
    </div>
  );
};

// Default export
export default ResponsiveContainer; 