import React from 'react';
import TopNavigation from './TopNavigation';
import Footer from './Footer';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      <TopNavigation />
      <main className="flex-1 w-full">
        <div className="min-h-[calc(100vh-8rem)]">
          {children}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Layout;
