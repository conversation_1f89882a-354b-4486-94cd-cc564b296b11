import React from 'react';
import { useAuth } from '@/context/AuthContext';
import Layout from './Layout';
import PublicLayout from './PublicLayout';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

/**
 * ConditionalLayout component that renders the appropriate layout based on authentication status
 * - If user is authenticated: renders Layout (with TopNavigation)
 * - If user is not authenticated: renders PublicLayout (without TopNavigation)
 */
const ConditionalLayout: React.FC<ConditionalLayoutProps> = ({ children }) => {
  const { user } = useAuth();

  if (user) {
    return <Layout>{children}</Layout>;
  }

  return <PublicLayout>{children}</PublicLayout>;
};

export default ConditionalLayout;
