import React, { ReactNode } from 'react';
import { AlertCircle, RefreshCw, WifiOff } from 'lucide-react';

export interface AsyncError {
  message: string;
  type: 'network' | 'authentication' | 'validation' | 'server' | 'unknown';
  code?: string | number;
  retryable?: boolean;
}

interface AsyncErrorBoundaryProps {
  error: AsyncError | Error | string | null;
  loading?: boolean;
  onRetry?: () => void;
  onDismiss?: () => void;
  children: ReactNode;
  fallback?: ReactNode;
  showErrorDetails?: boolean;
}

export const AsyncErrorBoundary: React.FC<AsyncErrorBoundaryProps> = ({
  error,
  loading = false,
  onRetry,
  onDismiss,
  children,
  fallback,
  showErrorDetails = false
}) => {
  // Parse error into consistent format
  const parsedError = React.useMemo((): AsyncError | null => {
    if (!error) return null;
    
    if (typeof error === 'string') {
      return {
        message: error,
        type: 'unknown',
        retryable: true
      };
    }

    if (error instanceof Error) {
      return {
        message: error.message,
        type: 'unknown',
        retryable: true
      };
    }

    return error;
  }, [error]);

  // No error, render children
  if (!parsedError) {
    return <>{children}</>;
  }

  // Get appropriate icon based on error type
  const getErrorIcon = () => {
    switch (parsedError.type) {
      case 'network':
        return <WifiOff className="h-5 w-5 text-orange-500" />;
      case 'authentication':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'server':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-amber-500" />;
    }
  };

  // Get error message based on type
  const getErrorMessage = () => {
    switch (parsedError.type) {
      case 'network':
        return 'Network connection problem. Please check your internet connection.';
      case 'authentication':
        return 'Authentication failed. Please sign in again.';
      case 'validation':
        return parsedError.message || 'Please check your input and try again.';
      case 'server':
        return 'Server error occurred. Please try again in a moment.';
      default:
        return parsedError.message || 'An unexpected error occurred.';
    }
  };

  // Get error color classes
  const getErrorClasses = () => {
    switch (parsedError.type) {
      case 'network':
        return 'border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/10';
      case 'authentication':
      case 'server':
        return 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/10';
      default:
        return 'border-amber-200 dark:border-amber-800 bg-amber-50 dark:bg-amber-900/10';
    }
  };

  // Use custom fallback if provided
  if (fallback) {
    return <>{fallback}</>;
  }

  // Default error UI
  return (
    <div className={`rounded-lg border p-4 ${getErrorClasses()}`}>
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          {getErrorIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {getErrorMessage()}
              </p>
              
              {showErrorDetails && parsedError.code && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Error code: {parsedError.code}
                </p>
              )}
            </div>
            
            {onDismiss && (
              <button
                onClick={onDismiss}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                aria-label="Dismiss error"
              >
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          
          {(onRetry && parsedError.retryable !== false) && (
            <div className="mt-3">
              <button
                onClick={onRetry}
                disabled={loading}
                className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                {loading ? 'Retrying...' : 'Try again'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Hook for creating consistent async errors
export const useAsyncError = () => {
  const createError = React.useCallback((
    message: string,
    type: AsyncError['type'] = 'unknown',
    options: Partial<Pick<AsyncError, 'code' | 'retryable'>> = {}
  ): AsyncError => ({
    message,
    type,
    retryable: true,
    ...options
  }), []);

  const createNetworkError = React.useCallback((message?: string) => 
    createError(
      message || 'Network connection failed',
      'network',
      { retryable: true }
    ), [createError]);

  const createAuthError = React.useCallback((message?: string) => 
    createError(
      message || 'Authentication required',
      'authentication',
      { retryable: false }
    ), [createError]);

  const createValidationError = React.useCallback((message: string) => 
    createError(message, 'validation', { retryable: false }), [createError]);

  const createServerError = React.useCallback((message?: string, code?: string | number) => 
    createError(
      message || 'Server error occurred',
      'server',
      { code, retryable: true }
    ), [createError]);

  return {
    createError,
    createNetworkError,
    createAuthError,
    createValidationError,
    createServerError
  };
};

export default AsyncErrorBoundary;