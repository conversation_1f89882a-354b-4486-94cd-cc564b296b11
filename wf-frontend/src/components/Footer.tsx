import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Sun, Moon, Globe, ChevronDown } from 'lucide-react';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { useTheme } from '@/context/ThemeContext';

const Footer: React.FC = () => {
  const { t, language, setLanguage, availableLanguages, getLanguageDisplayName, getLanguageFlag } = useLanguage();
  const { theme, setTheme } = useTheme();

  // Separate state for mobile and desktop language menus
  const [isMobileLanguageMenuOpen, setIsMobileLanguageMenuOpen] = useState(false);
  const [isDesktopLanguageMenuOpen, setIsDesktopLanguageMenuOpen] = useState(false);
  const mobileLanguageRef = useRef<HTMLDivElement>(null);
  const desktopLanguageRef = useRef<HTMLDivElement>(null);
  const year = new Date().getFullYear();

  // Close language menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileLanguageRef.current && !mobileLanguageRef.current.contains(event.target as Node)) {
        setIsMobileLanguageMenuOpen(false);
      }
      if (desktopLanguageRef.current && !desktopLanguageRef.current.contains(event.target as Node)) {
        setIsDesktopLanguageMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const getThemeIcon = () => {
    return theme === 'light' ? Sun : Moon;
  };

  const handleThemeToggle = () => {
    const nextTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(nextTheme);
  };

  const handleLanguageChange = (newLanguage: Language) => {
    // Close both mobile and desktop dropdowns
    setIsMobileLanguageMenuOpen(false);
    setIsDesktopLanguageMenuOpen(false);
    setLanguage(newLanguage);
  };


  const languageOptions = availableLanguages.map(lang => ({
    value: lang,
    label: getLanguageDisplayName(lang),
    flag: getLanguageFlag(lang),
    isSelected: lang === language
  }));



  return (
    <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-3 sm:py-4">
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
        {/* Mobile Layout - Stacked */}
        <div className="block sm:hidden space-y-3">
          {/* Copyright */}
          <div className="text-xs text-center text-gray-600 dark:text-gray-400">
            © {year} WordFormation. {t('footer.allRightsReserved')}
          </div>

          {/* Links Row */}
          <div className="flex justify-center items-center space-x-3 text-xs">
            <Link
              to="/terms"
              className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
            >
              {t('footer.terms')}
            </Link>
            <span className="text-gray-400 dark:text-gray-600">|</span>
            <Link
              to="/privacy"
              className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
            >
              {t('footer.privacy')}
            </Link>
            <span className="text-gray-400 dark:text-gray-600">|</span>
            <Link
              to="/about"
              className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
            >
              {t('footer.about')}
            </Link>
          </div>

          {/* Controls Row */}
          <div className="flex justify-center items-center space-x-3">
            {/* Language Selector - Mobile */}
            <div className="relative" ref={mobileLanguageRef}>
              <button
                onClick={() => setIsMobileLanguageMenuOpen(!isMobileLanguageMenuOpen)}
                className="flex items-center space-x-1 px-3 py-2 text-xs text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors duration-200 min-h-[44px] min-w-[44px] justify-center"
                aria-label="Select language"
              >
                <Globe className="h-4 w-4" />
                <span className="text-sm">{getLanguageFlag(language)}</span>
                <span className="hidden xs:inline text-xs">{getLanguageDisplayName(language)}</span>
                <ChevronDown className={`h-3 w-3 transition-transform duration-200 ${isMobileLanguageMenuOpen ? 'rotate-180' : ''}`} />
              </button>

              {/* Language Dropdown - Mobile */}
              {isMobileLanguageMenuOpen && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-40 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50">
                  <div className="p-2">
                    {languageOptions.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => handleLanguageChange(option.value)}
                        className={`w-full flex items-center space-x-2 px-3 py-2 text-sm rounded-md transition-colors duration-200 ${
                          option.isSelected
                            ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                        }`}
                      >
                        <span>{option.flag}</span>
                        <span>{option.label}</span>
                        {option.isSelected && (
                          <div className="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <span className="text-gray-400 dark:text-gray-600">|</span>

            {/* Theme Toggle - Mobile */}
            <button
              onClick={handleThemeToggle}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center"
              aria-label={t('theme.toggle')}
            >
              {React.createElement(getThemeIcon(), { className: "h-4 w-4" })}
            </button>
          </div>
        </div>

        {/* Desktop Layout - Original */}
        <div className="hidden sm:flex justify-between items-center">
          {/* Left side - Copyright */}
          <div className="text-sm text-gray-600 dark:text-gray-400">
            © {year} WordFormation. {t('footer.allRightsReserved')}
          </div>

          {/* Right side - Links and Controls */}
          <div className="flex items-center space-x-4">
            {/* Footer Links */}
            <div className="flex items-center space-x-4 text-sm">
              <Link
                to="/terms"
                className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
              >
                {t('footer.terms')}
              </Link>
              <span className="text-gray-400 dark:text-gray-600">|</span>
              <Link
                to="/privacy"
                className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
              >
                {t('footer.privacy')}
              </Link>
              <span className="text-gray-400 dark:text-gray-600">|</span>
              <Link
                to="/about"
                className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
              >
                {t('footer.about')}
              </Link>
              <span className="text-gray-400 dark:text-gray-600">|</span>
            </div>

            {/* Language Selector - Desktop */}
            <div className="relative" ref={desktopLanguageRef}>
              <button
                onClick={() => setIsDesktopLanguageMenuOpen(!isDesktopLanguageMenuOpen)}
                className="flex items-center space-x-1 px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors duration-200"
                aria-label="Select language"
              >
                <Globe className="h-4 w-4" />
                <span>{getLanguageFlag(language)}</span>
                <span>{getLanguageDisplayName(language)}</span>
                <ChevronDown className={`h-3 w-3 transition-transform duration-200 ${isDesktopLanguageMenuOpen ? 'rotate-180' : ''}`} />
              </button>

              {/* Language Dropdown - Desktop */}
              {isDesktopLanguageMenuOpen && (
                <div className="absolute bottom-full right-0 mb-2 w-40 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                  <div className="p-1">
                    {languageOptions.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => handleLanguageChange(option.value)}
                        className={`w-full flex items-center space-x-2 px-3 py-2 text-sm rounded-md transition-colors duration-200 ${
                          option.isSelected
                            ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                        }`}
                      >
                        <span>{option.flag}</span>
                        <span>{option.label}</span>
                        {option.isSelected && (
                          <div className="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <span className="text-gray-400 dark:text-gray-600">|</span>

            {/* Theme Toggle */}
            <button
              onClick={handleThemeToggle}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors duration-200"
              aria-label={t('theme.toggle')}
            >
              {React.createElement(getThemeIcon(), { className: "h-4 w-4" })}
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 