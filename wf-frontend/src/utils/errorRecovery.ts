import React from 'react';

// Retry configuration
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryIf?: (error: Error) => boolean;
}

// Default retry configuration
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  backoffFactor: 2,
  retryIf: (error: Error) => {
    // Retry network errors and server errors, but not client errors
    const status = (error as Error & { status?: number; statusCode?: number })?.status || (error as Error & { status?: number; statusCode?: number })?.statusCode;
    return !status || status >= 500 || status === 0;
  }
};

// Retry utility with exponential backoff
export const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<T> => {
  const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  let lastError: Error;

  for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      // Don't retry if this is the last attempt
      if (attempt === finalConfig.maxAttempts) {
        throw error;
      }

      // Don't retry if the error is not retryable
      if (finalConfig.retryIf && !finalConfig.retryIf(error)) {
        throw error;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        finalConfig.baseDelay * Math.pow(finalConfig.backoffFactor, attempt - 1),
        finalConfig.maxDelay
      );

      console.warn(`Operation failed (attempt ${attempt}/${finalConfig.maxAttempts}), retrying in ${delay}ms:`, error);
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};

// Offline detection utility
export class OfflineManager {
  private static instance: OfflineManager;
  private isOnline: boolean = navigator.onLine;
  private listeners: Set<(isOnline: boolean) => void> = new Set();
  private queuedRequests: Array<() => Promise<unknown>> = [];

  private constructor() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
  }

  static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager();
    }
    return OfflineManager.instance;
  }

  private handleOnline = () => {
    this.isOnline = true;
    this.notifyListeners();
    this.processQueuedRequests();
  };

  private handleOffline = () => {
    this.isOnline = false;
    this.notifyListeners();
  };

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.isOnline));
  }

  private async processQueuedRequests() {
    const requests = [...this.queuedRequests];
    this.queuedRequests = [];

    for (const request of requests) {
      try {
        await request();
      } catch (error) {
        console.error('Failed to process queued request:', error);
        // Re-queue if it's a network error
        if (this.isNetworkError(error)) {
          this.queuedRequests.push(request);
        }
      }
    }
  }

  private isNetworkError(error: Error): boolean {
    return error?.message?.includes('NetworkError') || 
           error?.message?.includes('fetch') ||
           error?.code === 'NETWORK_ERROR';
  }

  public getIsOnline(): boolean {
    return this.isOnline;
  }

  public addListener(listener: (isOnline: boolean) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  public queueRequest(request: () => Promise<unknown>) {
    if (this.isOnline) {
      return request();
    } else {
      this.queuedRequests.push(request);
      return Promise.reject(new Error('Offline - request queued'));
    }
  }

  public destroy() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
    this.listeners.clear();
    this.queuedRequests = [];
  }
}

// React hook for offline detection
export const useOfflineDetection = () => {
  const [isOnline, setIsOnline] = React.useState(OfflineManager.getInstance().getIsOnline());

  React.useEffect(() => {
    const offlineManager = OfflineManager.getInstance();
    const unsubscribe = offlineManager.addListener(setIsOnline);
    return unsubscribe;
  }, []);

  return { isOnline, isOffline: !isOnline };
};

// Enhanced fetch wrapper with retry and offline handling
export const fetchWithRetry = async <T>(
  url: string,
  options: RequestInit & { retryConfig?: Partial<RetryConfig> } = {}
): Promise<T> => {
  const { retryConfig, ...fetchOptions } = options;
  const offlineManager = OfflineManager.getInstance();

  const operation = async (): Promise<T> => {
    // Check if offline
    if (!offlineManager.getIsOnline()) {
      throw new Error('No network connection available');
    }

    const response = await fetch(url, fetchOptions);
    
    if (!response.ok) {
      const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
      (error as Error & { status: number }).status = response.status;
      throw error;
    }

    return response.json();
  };

  // If offline, queue the request
  if (!offlineManager.getIsOnline()) {
    return offlineManager.queueRequest(operation);
  }

  return retryWithBackoff(operation, retryConfig);
};

// Circuit breaker pattern for failing services
export class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private failureThreshold: number = 5,
    private timeout: number = 60000, // 1 minute
    private monitoringPeriod: number = 10000 // 10 seconds
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime >= this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN - service temporarily unavailable');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure() {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
    }
  }

  getState() {
    return this.state;
  }

  reset() {
    this.failures = 0;
    this.state = 'CLOSED';
  }
}

// Error recovery strategies
export const ErrorRecoveryStrategies = {
  // Automatic retry with exponential backoff
  autoRetry: <T>(operation: () => Promise<T>, config?: Partial<RetryConfig>) => 
    retryWithBackoff(operation, config),

  // Manual retry with user confirmation
  manualRetry: <T>(operation: () => Promise<T>, onRetry?: () => void) => ({
    retry: async () => {
      onRetry?.();
      return operation();
    }
  }),

  // Fallback to cached data or default values
  fallback: <T>(primary: () => Promise<T>, fallback: () => T | Promise<T>) =>
    primary().catch(() => fallback()),

  // Graceful degradation - disable feature if service is down
  gracefulDegradation: <T>(
    operation: () => Promise<T>, 
    fallbackValue: T,
    onDegradation?: () => void
  ) => 
    operation().catch(error => {
      console.warn('Service degraded, using fallback:', error);
      onDegradation?.();
      return fallbackValue;
    }),

  // Queue operations when offline
  queueWhenOffline: <T>(operation: () => Promise<T>) => {
    const offlineManager = OfflineManager.getInstance();
    return offlineManager.queueRequest(operation);
  }
};

