{"env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:react/jsx-runtime"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "react-hooks", "@typescript-eslint"], "rules": {"react/prop-types": "off", "react/react-in-jsx-scope": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "prefer-const": "error", "no-unused-vars": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["dist", "node_modules", "coverage", "vite.config.ts", "tailwind.config.js", "postcss.config.js"]}