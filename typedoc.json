{"entryPoints": ["./wf-shared/src/index.ts", "./wf-shared/src/types/index.ts", "./wf-shared/src/utils/index.ts", "./wf-shared/src/services/index.ts", "./wf-shared/src/repositories/index.ts"], "out": "./docs", "name": "WordFormation Monorepo Documentation", "readme": "./README.md", "includeVersion": true, "excludePrivate": true, "excludeProtected": false, "excludeExternals": true, "categorizeByGroup": true, "sort": ["source-order"], "kindSortOrder": ["Class", "<PERSON><PERSON><PERSON><PERSON>", "Property", "GetSignature", "SetSignature", "Method", "Function", "Accessor", "Interface", "TypeAlias", "Variable", "Enum"], "exclude": ["**/node_modules/**", "**/dist/**", "**/coverage/**", "**/*.test.ts", "**/*.test.tsx", "**/__tests__/**", "**/test-utils/**", "**/test/**"], "theme": "default", "hideGenerator": false, "gitRevision": "master", "disableSources": false}