#!/bin/bash
echo "🔍 Running frontend pre-commit checks..."

cd wf-frontend

# Check TypeScript compilation
echo "⚡ Checking TypeScript compilation..."
if ! npx tsc --noEmit; then
    echo "❌ TypeScript compilation failed! Please fix errors before committing."
    exit 1
fi

# Run ESLint
echo "🔧 Running ESLint..."
if ! npm run lint; then
    echo "❌ ESLint checks failed! Please fix linting errors before committing."
    echo "💡 Try running 'npm run lint:fix' to auto-fix some issues."
    exit 1
fi

# Run tests
echo "🧪 Running tests..."
if ! npm test; then
    echo "❌ Tests failed! Please fix failing tests before committing."
    exit 1
fi

echo "✅ Frontend pre-commit checks passed!"
cd ..
