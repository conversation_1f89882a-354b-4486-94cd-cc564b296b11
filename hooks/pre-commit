#!/bin/bash
echo "🚀 WordFormation Pre-commit Hook"
echo "================================"

# Check if we're in the root directory
if [ ! -f "documentation/DEVELOPMENT_STANDARDS.md" ]; then
    echo "❌ Not in WordFormation root directory!"
    exit 1
fi

# Detect which files are being committed
FRONTEND_FILES=$(git diff --cached --name-only | grep "^wf-frontend/" | head -1)
ADMIN_FILES=$(git diff --cached --name-only | grep "^wf-admin-portal/" | head -1)

# Run frontend checks if frontend files are modified
if [ ! -z "$FRONTEND_FILES" ]; then
    echo "📂 Frontend files detected, running frontend checks..."
    if ! .git/hooks/pre-commit-frontend; then
        exit 1
    fi
fi

# Run admin portal checks if admin files are modified
if [ ! -z "$ADMIN_FILES" ]; then
    echo "📂 Admin portal files detected, running admin portal checks..."
    if ! .git/hooks/pre-commit-admin; then
        exit 1
    fi
fi

# Check coding standards compliance
echo "📋 Checking coding standards compliance..."

# Look for common violations in staged files
STAGED_TS_FILES=$(git diff --cached --name-only | grep "\\.tsx\\?$")

if [ ! -z "$STAGED_TS_FILES" ]; then
    echo "🔍 Checking for coding standard violations..."
    
    # Check for 'any' types
    if git diff --cached | grep -q ": any\|<any>\|any\[\]"; then
        echo "❌ Found 'any' types in staged files!"
        echo "💡 Please use proper TypeScript types instead of 'any'"
        echo "📖 See documentation/DEVELOPMENT_STANDARDS.md for guidance"
        exit 1
    fi
    
    # Check for direct API calls in .tsx files
    TSX_FILES=$(echo "$STAGED_TS_FILES" | grep "\\.tsx$")
    if [ ! -z "$TSX_FILES" ]; then
        if git diff --cached | grep -E "(fetch\(|axios\.|supabase\.)" | grep -q "\.tsx"; then
            echo "❌ Found direct API calls in .tsx files!"
            echo "💡 Use service layer and handler hooks instead"
            echo "📖 See documentation/DEVELOPMENT_STANDARDS.md for 3-file pattern"
            exit 1
        fi
    fi
    
    echo "✅ Coding standards check passed!"
fi

echo "🎉 All pre-commit checks passed! Ready to commit."
