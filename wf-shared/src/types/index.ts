/**
 * Shared types exported for both frontend and admin portal.
 * These are SAFE types that contain no admin-specific logic.
 * 
 * This module provides a centralized export point for all type definitions
 * used across the WordFormation application ecosystem.
 * 
 * @example
 * ```typescript
 * import { ApiResponse, QuizWithRelations, UserSession } from 'wf-shared/types'
 * 
 * const response: ApiResponse<QuizWithRelations> = {
 *   success: true,
 *   data: { id: 'quiz_1', title: 'JavaScript Basics' }
 * }
 * ```
 */

/** Database schema types and table row interfaces */
export * from './database'

/** API interface types for standardized responses */
export * from './api'

/** Authentication types for user management */
export * from './auth'

/** Quiz-related types for learning content */
export * from './quiz'

/** Error handling types for structured error management */
export * from './errors'

/** Cache management types and interfaces */
export * from './cache'

/** Circuit breaker types and interfaces */
export * from './circuitBreaker'

/** Health monitoring types and interfaces */
export * from './health'