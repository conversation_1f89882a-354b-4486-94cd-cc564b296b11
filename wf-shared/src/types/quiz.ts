/**
 * Quiz-related types shared between frontend and admin portal.
 * This file contains ONLY safe, generic quiz types - no admin-specific logic.
 * 
 * These types provide standardized interfaces for quiz functionality,
 * ensuring consistent quiz management across all client applications.
 */

import type {
  DbQuiz,
  DbQuestion,
  DbQuestionOption,
  DbCategory,
  DbLevel,
  DbQuizType,
  DbQuizAttempt,
  DbUserProgress,
  DbQuizQuestion
} from './database'

/**
 * Quiz entity with populated relationship data.
 * Extends the base quiz type with related entities for convenience.
 * 
 * @example
 * ```typescript
 * const quiz: QuizWithRelations = {
 *   id: 'quiz_123',
 *   title: 'JavaScript Fundamentals',
 *   categories: { id: 'cat_1', name: 'Programming' },
 *   levels: { id: 'level_1', name: '<PERSON><PERSON><PERSON>' },
 *   questions: [
 *     { id: 'q1', content: 'What is a variable?' }
 *   ],
 *   _count: { questions: 10 }
 * }
 * ```
 */
export interface QuizWithRelations extends DbQuiz {
  /** Associated category information */
  categories?: DbCategory
  /** Associated level information */
  levels?: DbLevel
  /** Associated quiz type information */
  quiz_types?: DbQuizType
  /** Array of quiz-question relationships with question data */
  quiz_questions?: Array<DbQuizQuestion & {
    questions?: DbQuestion & {
      question_options?: DbQuestionOption[]
      explanations?: Array<{
        id: string
        content: string
        explanation_type: string
        metadata?: Record<string, unknown>
      }>
    }
  }>
  /** Convenience property: Direct access to questions (computed from quiz_questions) */
  questions?: Array<DbQuestion & {
    question_options?: DbQuestionOption[]
    explanations?: Array<{
      id: string
      content: string
      explanation_type: string
      metadata?: Record<string, unknown>
    }>
    order_index?: number
    points?: number
  }>
  /** Count of related entities */
  _count?: {
    /** Total number of questions in this quiz */
    questions: number
  }
}

/**
 * Question entity with populated relationship data.
 * Extends the base question type with related entities and metadata.
 * 
 * @example
 * ```typescript
 * const question: QuestionWithRelations = {
 *   id: 'q_123',
 *   content: 'What is JavaScript?',
 *   question_options: [
 *     { id: 'opt_1', content: 'A programming language', is_correct: true }
 *   ],
 *   quiz: { id: 'quiz_1', title: 'JS Basics', key: 'js-basics' },
 *   explanations: [
 *     { id: 'exp_1', content: 'JavaScript is a scripting language...' }
 *   ]
 * }
 * ```
 */
export interface QuestionWithRelations extends DbQuestion {
  /** Available answer options for this question */
  question_options?: DbQuestionOption[]
  /** Array of quiz-question relationships showing which quizzes use this question */
  quiz_questions?: Array<DbQuizQuestion & {
    quizzes?: {
      id: string
      title: string | null
      key: string
    }
  }>
  /** Convenience property: Direct access to quizzes (computed from quiz_questions) */
  quizzes?: Array<{
    id: string
    title: string | null
    key: string
  }>
  /** Question type information (singular) */
  question_type?: {
    id: string
    name: string
    key: string
  }
  /** Question type information (plural) */
  question_types?: {
    id: string
    name: string
    key: string
  }
  /** Explanations for this question */
  explanations?: Array<{
    id: string
    question_id: string
    content: string
    explanation_type: string
    metadata?: Record<string, unknown>
    created_at?: string | null
    updated_at?: string | null
  }>
}

/**
 * Question with quiz-specific context (order, points, etc.).
 * Used when displaying questions within a specific quiz context.
 *
 * @example
 * ```typescript
 * const quizQuestion: QuestionWithQuizContext = {
 *   id: 'q_123',
 *   question_text: 'What is JavaScript?',
 *   order_index: 1,
 *   points: 2,
 *   question_options: [
 *     { id: 'opt_1', option_text: 'A programming language', is_correct: true }
 *   ],
 *   explanations: [
 *     { id: 'exp_1', content: 'JavaScript is a scripting language...' }
 *   ]
 * }
 * ```
 */
export interface QuestionWithQuizContext extends DbQuestion {
  /** Order of this question in the specific quiz */
  order_index: number
  /** Points awarded for this question in the specific quiz */
  points: number
  /** Whether this question is active in the specific quiz */
  is_quiz_active?: boolean
  /** Available answer options for this question */
  question_options?: DbQuestionOption[]
  /** Question type information */
  question_type?: {
    id: string
    name: string
    key: string
  }
  /** Explanations for this question */
  explanations?: Array<{
    id: string
    question_id: string
    content: string
    explanation_type: string
    metadata?: Record<string, unknown>
    created_at?: string | null
  }>
}

/**
 * Quiz attempt entity with populated relationship data.
 * 
 * @example
 * ```typescript
 * const attempt: QuizAttemptWithRelations = {
 *   id: 'attempt_123',
 *   score: 85,
 *   quiz: { id: 'quiz_1', title: 'JS Fundamentals', key: 'js-fund' },
 *   user: { id: 'user_1', email: '<EMAIL>', first_name: 'John', last_name: 'Doe' }
 * }
 * ```
 */
export interface QuizAttemptWithRelations extends DbQuizAttempt {
  /** Associated quiz information */
  quiz?: {
    id: string
    title: string | null
    key: string
  }
  /** User who made this attempt */
  user?: {
    id: string
    email: string
    first_name: string
    last_name: string
  }
}

/**
 * User progress entity with populated relationship data.
 * 
 * @example
 * ```typescript
 * const progress: UserProgressWithRelations = {
 *   id: 'progress_123',
 *   completion_rate: 75,
 *   category: { id: 'cat_1', name: 'Programming' },
 *   level: { id: 'level_1', name: 'Beginner' },
 *   user: { id: 'user_1', email: '<EMAIL>', first_name: 'John', last_name: 'Doe' }
 * }
 * ```
 */
export interface UserProgressWithRelations extends DbUserProgress {
  /** Associated category */
  category?: DbCategory
  /** Associated level */
  level?: DbLevel
  /** User for this progress record */
  user?: {
    id: string
    email: string
    first_name: string
    last_name: string
  }
}

/**
 * Data structure for quiz submissions.
 * 
 * @example
 * ```typescript
 * const submission: QuizSubmission = {
 *   quizId: 'quiz_123',
 *   answers: [
 *     { questionId: 'q1', selectedAnswer: 'option_a', timeSpent: 30 },
 *     { questionId: 'q2', selectedAnswer: 'option_b', timeSpent: 45 }
 *   ],
 *   mode: 'practice',
 *   startedAt: '2023-10-01T10:00:00Z',
 *   completedAt: '2023-10-01T10:15:00Z'
 * }
 * ```
 */
export interface QuizSubmission {
  /** ID of the quiz being submitted */
  quizId: string
  /** Array of user answers */
  answers: Array<{
    /** ID of the question */
    questionId: string
    /** User's selected answer */
    selectedAnswer: string
    /** Time spent on this question in seconds */
    timeSpent?: number
  }>
  /** Quiz mode */
  mode: 'practice' | 'test'
  /** When the quiz was started */
  startedAt: string
  /** When the quiz was completed */
  completedAt: string
}

/**
 * Result data from a completed quiz attempt.
 * 
 * @example
 * ```typescript
 * const result: QuizResult = {
 *   score: 85,
 *   totalQuestions: 10,
 *   correctAnswers: 8,
 *   timeSpent: 900,
 *   passed: true,
 *   answers: [
 *     {
 *       questionId: 'q1',
 *       userAnswer: 'option_a',
 *       correctAnswer: 'option_a',
 *       isCorrect: true,
 *       explanation: 'Correct! JavaScript is indeed a programming language.'
 *     }
 *   ]
 * }
 * ```
 */
export interface QuizResult {
  /** Overall score percentage (0-100) */
  score: number
  /** Total number of questions in the quiz */
  totalQuestions: number
  /** Number of questions answered correctly */
  correctAnswers: number
  /** Total time spent in seconds */
  timeSpent: number
  /** Whether the user passed the quiz */
  passed: boolean
  /** Detailed answers breakdown */
  answers: Array<{
    /** ID of the question */
    questionId: string
    /** User's selected answer */
    userAnswer: string
    /** The correct answer */
    correctAnswer: string
    /** Whether the user's answer was correct */
    isCorrect: boolean
    /** Optional explanation for the answer */
    explanation?: string
  }>
}

/**
 * Aggregated statistics for a user's quiz performance.
 * 
 * @example
 * ```typescript
 * const stats: QuizStatistics = {
 *   totalQuizzes: 25,
 *   completedQuizzes: 20,
 *   averageScore: 78,
 *   bestScore: 95,
 *   timeSpent: 18000,
 *   streakCount: 5
 * }
 * ```
 */
export interface QuizStatistics {
  /** Total number of quizzes available to the user */
  totalQuizzes: number
  /** Number of quizzes the user has completed */
  completedQuizzes: number
  /** User's average score across all completed quizzes */
  averageScore: number
  /** User's highest score achieved */
  bestScore: number
  /** Total time spent taking quizzes in seconds */
  timeSpent: number
  /** Current streak of consecutive correct answers or completions */
  streakCount: number
}

/**
 * Filter parameters for searching and browsing quizzes.
 * 
 * @example
 * ```typescript
 * const filters: QuizFilters = {
 *   categoryId: 'programming',
 *   levelId: 'beginner',
 *   difficulty: 3,
 *   isActive: true,
 *   search: 'javascript'
 * }
 * ```
 */
export interface QuizFilters {
  /** Filter by category ID */
  categoryId?: string
  /** Filter by level ID */
  levelId?: string
  /** Filter by difficulty rating (1-5) */
  difficulty?: number
  /** Filter by active status */
  isActive?: boolean
  /** Text search query */
  search?: string
}

/**
 * Additional metadata for quiz configuration.
 * 
 * @example
 * ```typescript
 * const metadata: QuizMetadata = {
 *   estimatedTime: 900,
 *   difficulty: 3,
 *   tags: ['javascript', 'programming', 'web-development'],
 *   prerequisites: ['variables', 'functions']
 * }
 * ```
 */
export interface QuizMetadata {
  /** Estimated time to complete in seconds */
  estimatedTime: number
  /** Difficulty rating (1-5) */
  difficulty: number
  /** Descriptive tags for categorization */
  tags: string[]
  /** Required knowledge or completed quizzes */
  prerequisites: string[]
}

/**
 * Question with quiz-specific context and full relations.
 * Used when questions are displayed within a quiz context with all related data.
 *
 * @example
 * ```typescript
 * const questionInQuiz: QuestionWithFullQuizContext = {
 *   id: 'q1',
 *   question_text: 'What is JavaScript?',
 *   question_type_id: 'multiple_choice',
 *   difficulty_level: 3,
 *   is_active: true,
 *   order_index: 1,
 *   points: 2,
 *   is_quiz_active: true,
 *   question_options: [...],
 *   explanations: [...]
 * }
 * ```
 */
export interface QuestionWithFullQuizContext extends QuestionWithRelations {
  /** Order of this question within the quiz */
  order_index: number
  /** Points awarded for this question in the quiz */
  points: number
  /** Whether the quiz this question belongs to is active */
  is_quiz_active: boolean
}

/**
 * Progress tracking data for a user's performance in a specific category/level.
 * 
 * @example
 * ```typescript
 * const progress: ProgressData = {
 *   categoryId: 'programming',
 *   levelId: 'beginner',
 *   userId: 'user_123',
 *   totalAttempts: 15,
 *   bestScore: 92,
 *   averageScore: 78,
 *   completionRate: 80,
 *   lastAttemptAt: '2023-10-01T15:30:00Z'
 * }
 * ```
 */
export interface ProgressData {
  /** Category being tracked */
  categoryId: string
  /** Level being tracked */
  levelId: string
  /** User being tracked */
  userId: string
  /** Total number of quiz attempts */
  totalAttempts: number
  /** Highest score achieved */
  bestScore: number
  /** Average score across all attempts */
  averageScore: number
  /** Percentage of quizzes completed in this category/level */
  completionRate: number
  /** Timestamp of last quiz attempt */
  lastAttemptAt: string
}