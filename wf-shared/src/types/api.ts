/**
 * Common API types shared between frontend and admin portal.
 * This file contains ONLY safe, generic API interface types.
 * 
 * These types provide standardized interfaces for API communications,
 * ensuring consistent data structures across all client applications.
 */

/**
 * Parameters for pagination in API requests.
 * 
 * @example
 * ```typescript
 * const params: PaginationParams = {
 *   page: 2,
 *   pageSize: 20,
 *   sortBy: 'created_at',
 *   sortOrder: 'desc'
 * }
 * ```
 */
export interface PaginationParams {
  /** The page number to retrieve (1-based) */
  page: number
  /** Number of items per page */
  pageSize: number
  /** Field name to sort by */
  sortBy?: string
  /** Sort direction */
  sortOrder?: 'asc' | 'desc'
  /** Allow additional properties for extended pagination */
  [key: string]: unknown
}

/**
 * Standard response format for paginated API endpoints.
 * 
 * @template T - The type of items in the data array
 * 
 * @example
 * ```typescript
 * const response: PaginationResponse<User> = {
 *   data: [{ id: '1', name: '<PERSON>' }],
 *   count: 100,
 *   totalPages: 10,
 *   page: 1,
 *   pageSize: 10
 * }
 * ```
 */
export interface PaginationResponse<T> {
  /** Array of items for the current page */
  data: T[]
  /** Total number of items across all pages */
  count: number
  /** Total number of pages */
  totalPages: number
  /** Current page number */
  page: number
  /** Number of items per page */
  pageSize: number
}

/**
 * Generic filter interface for content filtering.
 * 
 * @example
 * ```typescript
 * const filter: ContentFilter = {
 *   category: 'education',
 *   level: 'beginner',
 *   status: 'published',
 *   dateRange: {
 *     from: new Date('2023-01-01'),
 *     to: new Date('2023-12-31')
 *   }
 * }
 * ```
 */
export interface ContentFilter {
  /** Content category filter */
  category?: string
  /** Difficulty level filter */
  level?: string
  /** Content status filter */
  status?: string
  /** Date range filter */
  dateRange?: {
    /** Start date */
    from: Date
    /** End date */
    to: Date
  }
  /** Allow additional properties for extended filtering */
  [key: string]: unknown
}

/**
 * Standard API response wrapper for all endpoints.
 * 
 * @template T - The type of data returned on success
 * 
 * @example
 * ```typescript
 * // Success response
 * const success: ApiResponse<User> = {
 *   success: true,
 *   data: { id: '1', name: 'John' }
 * }
 * 
 * // Error response
 * const error: ApiResponse<User> = {
 *   success: false,
 *   error: {
 *     message: 'User not found',
 *     code: 'USER_NOT_FOUND'
 *   }
 * }
 * ```
 */
export interface ApiResponse<T> {
  /** Whether the operation was successful */
  success: boolean
  /** Data payload (present on success) */
  data?: T
  /** Error information (present on failure) */
  error?: {
    /** Human-readable error message */
    message: string
    /** Optional error code for programmatic handling */
    code?: string
  }
}

/**
 * Result of bulk operations (e.g., bulk import, bulk delete).
 * 
 * @example
 * ```typescript
 * const result: BulkOperationResult = {
 *   success: false,
 *   processed: 85,
 *   failed: 15,
 *   errors: [
 *     { row: 3, field: 'email', message: 'Invalid email format' },
 *     { row: 7, field: 'name', message: 'Name is required' }
 *   ]
 * }
 * ```
 */
export interface BulkOperationResult {
  /** Whether the overall operation was successful */
  success: boolean
  /** Number of items successfully processed */
  processed: number
  /** Number of items that failed processing */
  failed: number
  /** Array of specific errors that occurred */
  errors: Array<{
    /** Row number where error occurred */
    row: number
    /** Field name that caused the error */
    field: string
    /** Error message */
    message: string
  }>
}

/**
 * Configuration for sorting data.
 * 
 * @example
 * ```typescript
 * const sortOption: SortOption = {
 *   field: 'created_at',
 *   direction: 'desc',
 *   label: 'Newest First'
 * }
 * ```
 */
export interface SortOption {
  /** Database field to sort by */
  field: string
  /** Sort direction */
  direction: 'asc' | 'desc'
  /** Human-readable label for UI display */
  label: string
}

/**
 * Comprehensive search parameters for API endpoints.
 * 
 * @example
 * ```typescript
 * const searchParams: SearchParams = {
 *   query: 'javascript tutorial',
 *   filters: { level: 'beginner', category: 'programming' },
 *   sort: { field: 'relevance', direction: 'desc', label: 'Most Relevant' },
 *   pagination: { page: 1, pageSize: 20 }
 * }
 * ```
 */
export interface SearchParams {
  /** Text search query */
  query?: string
  /** Additional filters to apply */
  filters?: Record<string, unknown>
  /** Sort configuration */
  sort?: SortOption
  /** Pagination settings */
  pagination?: PaginationParams
}

/**
 * Response from file upload operations.
 * 
 * @example
 * ```typescript
 * // Success response
 * const success: FileUploadResponse = {
 *   success: true,
 *   url: 'https://example.com/uploads/file.jpg',
 *   fileId: 'file_123456'
 * }
 * 
 * // Error response
 * const error: FileUploadResponse = {
 *   success: false,
 *   error: 'File size exceeds maximum limit'
 * }
 * ```
 */
export interface FileUploadResponse {
  /** Whether the upload was successful */
  success: boolean
  /** URL where the uploaded file can be accessed */
  url?: string
  /** Unique identifier for the uploaded file */
  fileId?: string
  /** Error message if upload failed */
  error?: string
}

/**
 * Result of create operations.
 * 
 * @template T - The type of data returned on successful creation
 */
export interface CreateResult<T> {
  /** Whether the creation was successful */
  success: boolean
  /** Created item data (present on success) */
  data?: T
  /** Error message if creation failed */
  error?: string
}

/**
 * Result of update operations.
 * 
 * @template T - The type of data returned on successful update
 */
export interface UpdateResult<T> {
  /** Whether the update was successful */
  success: boolean
  /** Updated item data (present on success) */
  data?: T
  /** Error message if update failed */
  error?: string
}

/**
 * Result of delete operations.
 */
export interface DeleteResult {
  /** Whether the deletion was successful */
  success: boolean
  /** Error message if deletion failed */
  error?: string
}

/**
 * Function type for building query filters.
 * 
 * @template T - The type of query object being modified
 * 
 * @example
 * ```typescript
 * const userFilter: QueryFilter<SupabaseQuery> = (query) => {
 *   return query.eq('status', 'active').gte('created_at', startDate)
 * }
 * ```
 */
export type QueryFilter<T = unknown> = (query: T) => T