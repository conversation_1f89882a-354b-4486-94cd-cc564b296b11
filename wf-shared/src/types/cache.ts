/**
 * Cache management types and interfaces
 * Part of Technical Debt Solution - Phase 1, Task 1.2
 * 
 * Provides types for the global cache management system that replaces
 * instance-based caching throughout the application.
 */

/**
 * Represents a single entry in the cache
 */
export interface CacheEntry<T = unknown> {
  /** The cached data */
  data: T;
  /** Timestamp when the entry was created */
  timestamp: number;
  /** Time-to-live in milliseconds */
  ttl: number;
  /** Optional tags for bulk invalidation */
  tags?: string[];
  /** Optional metadata about the cache entry */
  metadata?: Record<string, unknown>;
}

/**
 * Configuration for cache behavior
 */
export interface CacheConfig {
  /** Default time-to-live for cache entries (5 minutes) */
  defaultTtl: number;
  /** Maximum number of entries in cache */
  maxSize: number;
  /** Interval for automatic cleanup of expired entries (ms) */
  cleanupInterval: number;
  /** Enable automatic cleanup */
  enableAutoCleanup: boolean;
  /** Enable cache statistics tracking */
  enableStats: boolean;
}

/**
 * Cache statistics for monitoring and debugging
 */
export interface CacheStats {
  /** Current number of entries in cache */
  size: number;
  /** Total number of get operations */
  hits: number;
  /** Total number of missed get operations */
  misses: number;
  /** Cache hit rate as percentage */
  hitRate: number;
  /** Number of entries that have expired */
  expired: number;
  /** Number of entries evicted due to size limits */
  evicted: number;
  /** Memory usage estimate in bytes */
  memoryUsage: number;
  /** Most frequently accessed cache keys */
  topKeys: Array<{ key: string; accessCount: number }>;
}

/**
 * Cache invalidation pattern types
 */
export type CacheInvalidationPattern = 
  | string          // Exact key match
  | RegExp          // Regular expression pattern
  | { prefix: string }  // Keys starting with prefix
  | { suffix: string }  // Keys ending with suffix
  | { contains: string } // Keys containing substring

/**
 * Callback function type for cache event subscribers
 */
export type CacheEventCallback = (key: string, entry?: CacheEntry) => void;

/**
 * Cache event types
 */
export type CacheEventType = 'set' | 'get' | 'delete' | 'expire' | 'evict' | 'clear';

/**
 * Cache event subscription configuration
 */
export interface CacheSubscription {
  /** Pattern to match keys for this subscription */
  pattern: CacheInvalidationPattern;
  /** Events to listen for */
  events: CacheEventType[];
  /** Callback function to execute when event occurs */
  callback: CacheEventCallback;
  /** Unique identifier for the subscription */
  id: string;
}

/**
 * Options for cache operations
 */
export interface CacheSetOptions {
  /** Time-to-live for this specific entry */
  ttl?: number;
  /** Tags for bulk invalidation */
  tags?: string[];
  /** Whether to overwrite existing entry */
  overwrite?: boolean;
  /** Metadata for this cache entry */
  metadata?: Record<string, unknown>;
}

/**
 * Options for cache retrieval
 */
export interface CacheGetOptions {
  /** Whether to update access time on retrieval */
  updateAccessTime?: boolean;
  /** Whether to return expired entries */
  includeExpired?: boolean;
}

/**
 * Result of cache operations with metadata
 */
export interface CacheOperationResult<T = unknown> {
  /** Whether the operation was successful */
  success: boolean;
  /** The data (if successful) */
  data?: T;
  /** Error message (if unsuccessful) */
  error?: string;
  /** Whether the entry was found (for get operations) */
  found?: boolean;
  /** Whether the entry was expired */
  expired?: boolean;
  /** Metadata about the operation */
  metadata?: Record<string, unknown>;
}