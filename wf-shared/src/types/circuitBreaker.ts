/**
 * Circuit Breaker types and interfaces
 * Part of Technical Debt Solution - Phase 1, Task 1.3
 * 
 * Provides types for the circuit breaker pattern implementation that will protect
 * external service calls from cascade failures and improve system resilience.
 */

/**
 * Circuit breaker states following the classic circuit breaker pattern
 */
export type CircuitState = 'CLOSED' | 'OPEN' | 'HALF_OPEN';

/**
 * Configuration for circuit breaker behavior
 */
export interface CircuitBreakerConfig {
  /** Number of consecutive failures before opening circuit */
  failureThreshold: number;
  /** Time in milliseconds before attempting reset from OPEN to HALF_OPEN */
  resetTimeout: number;
  /** Time window in milliseconds for monitoring failures */
  monitoringWindow: number;
  /** Timeout in milliseconds for individual operations */
  timeoutMs: number;
  /** Number of successful calls needed in HALF_OPEN to close circuit */
  successThreshold: number;
  /** Optional name for the circuit breaker (for logging/monitoring) */
  name?: string;
}

/**
 * Statistics and metrics for circuit breaker monitoring
 */
export interface CircuitBreakerStats {
  /** Current state of the circuit breaker */
  state: CircuitState;
  /** Number of consecutive failures */
  failureCount: number;
  /** Number of successful calls */
  successCount: number;
  /** Timestamp of last failure */
  lastFailureTime: number;
  /** Timestamp of last success */
  lastSuccessTime: number;
  /** Total number of requests processed */
  totalRequests: number;
  /** Number of requests rejected due to open circuit */
  rejectedRequests: number;
  /** Average response time in milliseconds */
  averageResponseTime: number;
  /** Failure rate as percentage */
  failureRate: number;
}

/**
 * Result of a circuit breaker operation
 */
export interface CircuitBreakerResult<T> {
  /** Whether the operation was successful */
  success: boolean;
  /** The result data (if successful) */
  data?: T;
  /** Error information (if unsuccessful) */
  error?: string;
  /** Whether the request was rejected by circuit breaker */
  rejected?: boolean;
  /** Response time in milliseconds */
  responseTime?: number;
  /** Circuit state at time of operation */
  circuitState?: CircuitState;
}

/**
 * Circuit breaker event types for monitoring and notifications
 */
export type CircuitBreakerEventType = 
  | 'STATE_CHANGE'     // Circuit changed state
  | 'FAILURE'          // Operation failed
  | 'SUCCESS'          // Operation succeeded  
  | 'TIMEOUT'          // Operation timed out
  | 'REJECTED'         // Request rejected due to open circuit
  | 'RESET_ATTEMPTED'; // Attempted to reset from OPEN to HALF_OPEN

/**
 * Circuit breaker event data
 */
export interface CircuitBreakerEvent {
  /** Type of event */
  type: CircuitBreakerEventType;
  /** Circuit breaker name/identifier */
  circuitName: string;
  /** Current state */
  state: CircuitState;
  /** Previous state (for state changes) */
  previousState?: CircuitState;
  /** Error information (if applicable) */
  error?: Error;
  /** Response time (if applicable) */
  responseTime?: number;
  /** Timestamp of event */
  timestamp: number;
  /** Additional metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Callback function type for circuit breaker event listeners
 */
export type CircuitBreakerEventCallback = (event: CircuitBreakerEvent) => void;

/**
 * Health check function type for testing service availability
 */
export type HealthCheckFunction = () => Promise<boolean>;

/**
 * Configuration for different types of operations
 * Allows different circuit breaker settings for different operation types
 */
export interface OperationTypeConfig {
  /** Configuration for read operations */
  read?: Partial<CircuitBreakerConfig>;
  /** Configuration for write operations */
  write?: Partial<CircuitBreakerConfig>;
  /** Configuration for authentication operations */
  auth?: Partial<CircuitBreakerConfig>;
  /** Configuration for file/storage operations */
  storage?: Partial<CircuitBreakerConfig>;
  /** Default configuration for unspecified operations */
  default: CircuitBreakerConfig;
}

/**
 * Circuit breaker factory configuration
 */
export interface CircuitBreakerFactoryConfig {
  /** Global default configuration */
  globalDefaults: CircuitBreakerConfig;
  /** Operation-specific configurations */
  operationTypes?: OperationTypeConfig;
  /** Whether to enable automatic health checks */
  enableHealthChecks: boolean;
  /** Interval for health checks in milliseconds */
  healthCheckInterval: number;
}

/**
 * Predefined circuit breaker configurations for common scenarios
 */
export const CIRCUIT_BREAKER_PRESETS = {
  /** Fast-fail configuration for real-time operations */
  FAST_FAIL: {
    failureThreshold: 3,
    resetTimeout: 5000,
    monitoringWindow: 30000,
    timeoutMs: 2000,
    successThreshold: 2
  } as CircuitBreakerConfig,

  /** Moderate configuration for standard operations */
  STANDARD: {
    failureThreshold: 5,
    resetTimeout: 30000,
    monitoringWindow: 60000,
    timeoutMs: 5000,
    successThreshold: 3
  } as CircuitBreakerConfig,

  /** Patient configuration for batch/background operations */
  PATIENT: {
    failureThreshold: 10,
    resetTimeout: 60000,
    monitoringWindow: 300000,
    timeoutMs: 30000,
    successThreshold: 5
  } as CircuitBreakerConfig,

  /** Database-specific configuration */
  DATABASE: {
    failureThreshold: 5,
    resetTimeout: 15000,
    monitoringWindow: 60000,
    timeoutMs: 10000,
    successThreshold: 3
  } as CircuitBreakerConfig,

  /** Authentication service configuration */
  AUTH_SERVICE: {
    failureThreshold: 3,
    resetTimeout: 10000,
    monitoringWindow: 30000,
    timeoutMs: 5000,
    successThreshold: 2
  } as CircuitBreakerConfig
} as const;