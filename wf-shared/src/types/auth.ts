/**
 * Authentication types shared between frontend and admin portal.
 * This file contains ONLY safe, generic authentication types.
 * 
 * These types provide standardized interfaces for authentication flows,
 * ensuring consistent user management across all client applications.
 */

/**
 * Standard result format for authentication operations.
 * 
 * @example
 * ```typescript
 * // Success result
 * const success: AuthResult = { success: true }
 * 
 * // Error result
 * const error: AuthResult = {
 *   success: false,
 *   error: {
 *     message: 'Invalid credentials',
 *     code: 'INVALID_CREDENTIALS'
 *   }
 * }
 * ```
 */
export interface AuthResult {
  /** Whether the authentication operation was successful */
  success: boolean
  /** Error information if the operation failed */
  error?: {
    /** Human-readable error message */
    message: string
    /** Optional error code for programmatic handling */
    code?: string
  }
}

/**
 * User session information (safe subset).
 * Contains only non-sensitive user data for client-side use.
 * 
 * @example
 * ```typescript
 * const session: UserSession = {
 *   id: 'user_123',
 *   email: '<EMAIL>',
 *   isAuthenticated: true
 * }
 * ```
 */
export interface UserSession {
  /** Unique user identifier */
  id: string
  /** User's email address */
  email: string
  /** Whether the user is currently authenticated */
  isAuthenticated: boolean
}

/**
 * User login credentials.
 * 
 * @example
 * ```typescript
 * const credentials: LoginCredentials = {
 *   email: '<EMAIL>',
 *   password: 'securePassword123',
 *   rememberMe: true
 * }
 * ```
 */
export interface LoginCredentials {
  /** User's email address */
  email: string
  /** User's password */
  password: string
  /** Whether to remember the user for future sessions */
  rememberMe?: boolean
}

/**
 * User registration data.
 * 
 * @example
 * ```typescript
 * const registration: RegistrationData = {
 *   email: '<EMAIL>',
 *   password: 'securePassword123',
 *   firstName: 'John',
 *   lastName: 'Doe',
 *   levelId: 'beginner'
 * }
 * ```
 */
export interface RegistrationData {
  /** User's email address */
  email: string
  /** User's password */
  password: string
  /** User's first name */
  firstName: string
  /** User's last name */
  lastName: string
  /** Optional proficiency level identifier */
  levelId?: string
}

/**
 * Password reset request data.
 * 
 * @example
 * ```typescript
 * const resetRequest: PasswordResetRequest = {
 *   email: '<EMAIL>'
 * }
 * ```
 */
export interface PasswordResetRequest {
  /** Email address to send reset instructions to */
  email: string
}

/**
 * Password update data for authenticated users.
 * 
 * @example
 * ```typescript
 * const updateData: PasswordUpdateData = {
 *   currentPassword: 'oldPassword123',
 *   newPassword: 'newSecurePassword456'
 * }
 * ```
 */
export interface PasswordUpdateData {
  /** User's current password for verification */
  currentPassword: string
  /** New password to set */
  newPassword: string
}

/**
 * Current authentication state for the application.
 * 
 * @example
 * ```typescript
 * const authState: AuthState = {
 *   isLoading: false,
 *   isAuthenticated: true,
 *   user: { id: 'user_123', email: '<EMAIL>', isAuthenticated: true },
 *   error: null
 * }
 * ```
 */
export interface AuthState {
  /** Whether an authentication operation is in progress */
  isLoading: boolean
  /** Whether a user is currently authenticated */
  isAuthenticated: boolean
  /** Current user session data, null if not authenticated */
  user: UserSession | null
  /** Last authentication error, null if no error */
  error: string | null
}

/**
 * Authentication context interface for React context providers.
 * Defines all authentication methods and state available to components.
 * 
 * @example
 * AuthContextValue provides all authentication methods:
 * - signIn: authenticates user with email/password
 * - signUp: registers new user account
 * - signOut: terminates current session
 * - resetPassword: sends password reset email
 * - updatePassword: changes user password
 */
export interface AuthContextValue {
  /** Current authentication state */
  authState: AuthState
  /** Sign in with email and password */
  signIn: (email: string, password: string) => Promise<AuthResult>
  /** Register a new user account */
  signUp: (data: RegistrationData) => Promise<AuthResult>
  /** Sign out the current user */
  signOut: () => Promise<void>
  /** Request password reset for an email */
  resetPassword: (email: string) => Promise<AuthResult>
  /** Update the current user's password */
  updatePassword: (data: PasswordUpdateData) => Promise<AuthResult>
}

/**
 * Storage interface for remembered user credentials.
 * Used for persisting login state across browser sessions.
 * 
 * @example
 * RememberedCredentials stores user email and remember preference
 * for persistent login across browser sessions.
 */
export interface RememberedCredentials {
  /** Remembered email address */
  email: string
  /** Whether the user opted to be remembered */
  rememberMe: boolean
}