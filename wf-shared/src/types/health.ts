/**
 * Health monitoring types and interfaces
 * Part of Technical Debt Solution - Phase 3, Task 3.1
 * 
 * Provides comprehensive health monitoring for all system components including
 * databases, caches, circuit breakers, and external services.
 */

import type { CircuitState } from './circuitBreaker';

/**
 * Individual service health status
 */
export interface ServiceHealth {
  /** Current status of the service */
  status: 'up' | 'down' | 'degraded';
  /** Response time in milliseconds (if applicable) */
  responseTime?: number;
  /** Error rate as percentage (0-100) */
  errorRate?: number;
  /** Current circuit breaker state (if applicable) */
  circuitBreakerState?: CircuitState;
  /** Last successful health check timestamp */
  lastCheckTime: number;
  /** Additional service-specific metadata */
  metadata?: Record<string, unknown>;
  /** Human-readable status message */
  message?: string;
}

/**
 * Overall system health status
 */
export interface HealthStatus {
  /** Overall system health assessment */
  overall: 'healthy' | 'degraded' | 'unhealthy';
  /** Individual service health statuses */
  services: {
    database: ServiceHealth;
    auth: ServiceHealth;
    cache: ServiceHealth;
    [key: string]: ServiceHealth; // Allow additional services
  };
  /** Timestamp of this health assessment */
  timestamp: number;
  /** Overall system uptime in milliseconds */
  uptime?: number;
  /** System version information */
  version?: string;
}

/**
 * Health check configuration for individual services
 */
export interface HealthCheckConfig {
  /** Service name for identification */
  name: string;
  /** Interval between health checks in milliseconds */
  interval: number;
  /** Timeout for health check operations */
  timeout: number;
  /** Number of consecutive failures before marking as down */
  failureThreshold: number;
  /** Number of consecutive successes needed to mark as up */
  recoveryThreshold: number;
  /** Whether this health check is enabled */
  enabled: boolean;
  /** Custom health check function */
  healthCheckFn?: () => Promise<Partial<ServiceHealth>>;
}

/**
 * Health monitoring configuration
 */
export interface HealthMonitorConfig {
  /** Individual service configurations */
  services: Record<string, HealthCheckConfig>;
  /** Global health check interval */
  globalInterval: number;
  /** Whether to enable automatic health monitoring */
  enableAutoMonitoring: boolean;
  /** History retention period in milliseconds */
  historyRetention: number;
  /** Maximum number of health records to keep in memory */
  maxHistorySize: number;
}

/**
 * Health history entry for tracking service health over time
 */
export interface HealthHistoryEntry {
  /** Timestamp of this health check */
  timestamp: number;
  /** Service health at this point in time */
  health: ServiceHealth;
  /** Duration of the health check in milliseconds */
  checkDuration: number;
}

/**
 * Health statistics for analysis and reporting
 */
export interface HealthStats {
  /** Service name */
  serviceName: string;
  /** Uptime percentage over the monitored period */
  uptimePercentage: number;
  /** Average response time in milliseconds */
  averageResponseTime: number;
  /** Total number of health checks performed */
  totalChecks: number;
  /** Number of failed health checks */
  failedChecks: number;
  /** Time of last failure */
  lastFailureTime?: number;
  /** Duration of current status */
  currentStatusDuration: number;
  /** Health trend over recent checks */
  trend: 'improving' | 'stable' | 'degrading';
}

/**
 * Health event types for monitoring and alerting
 */
export type HealthEventType = 
  | 'SERVICE_UP'      // Service became healthy
  | 'SERVICE_DOWN'    // Service became unhealthy
  | 'SERVICE_DEGRADED' // Service performance degraded
  | 'SYSTEM_HEALTHY'  // Overall system health good
  | 'SYSTEM_DEGRADED' // Overall system performance issues
  | 'SYSTEM_UNHEALTHY' // Overall system has critical issues
  | 'HEALTH_CHECK_FAILED' // Individual health check failed
  | 'RECOVERY_DETECTED';  // Service recovery detected

/**
 * Health event data for notifications
 */
export interface HealthEvent {
  /** Type of health event */
  type: HealthEventType;
  /** Service name (if applicable) */
  serviceName?: string;
  /** Previous health status */
  previousStatus?: ServiceHealth['status'];
  /** Current health status */
  currentStatus: ServiceHealth['status'];
  /** Event timestamp */
  timestamp: number;
  /** Additional event details */
  details?: Record<string, unknown>;
  /** Severity level for alerting */
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Health event callback function type
 */
export type HealthEventCallback = (event: HealthEvent) => void;

/**
 * Predefined health check configurations for common services
 */
export const HEALTH_CHECK_PRESETS = {
  /** Database health check configuration */
  DATABASE: {
    interval: 30000,    // 30 seconds
    timeout: 10000,     // 10 seconds
    failureThreshold: 3,
    recoveryThreshold: 2,
    enabled: true
  } as Partial<HealthCheckConfig>,

  /** Authentication service health check */
  AUTH_SERVICE: {
    interval: 60000,    // 1 minute
    timeout: 5000,      // 5 seconds
    failureThreshold: 2,
    recoveryThreshold: 1,
    enabled: true
  } as Partial<HealthCheckConfig>,

  /** Cache health check configuration */
  CACHE: {
    interval: 120000,   // 2 minutes
    timeout: 2000,      // 2 seconds
    failureThreshold: 5,
    recoveryThreshold: 1,
    enabled: true
  } as Partial<HealthCheckConfig>,

  /** External API health check */
  EXTERNAL_API: {
    interval: 45000,    // 45 seconds
    timeout: 8000,      // 8 seconds
    failureThreshold: 3,
    recoveryThreshold: 2,
    enabled: true
  } as Partial<HealthCheckConfig>
} as const;

/**
 * Default health monitor configuration
 */
export const DEFAULT_HEALTH_CONFIG: HealthMonitorConfig = {
  services: {
    database: { name: 'database', ...HEALTH_CHECK_PRESETS.DATABASE } as HealthCheckConfig,
    auth: { name: 'auth', ...HEALTH_CHECK_PRESETS.AUTH_SERVICE } as HealthCheckConfig,
    cache: { name: 'cache', ...HEALTH_CHECK_PRESETS.CACHE } as HealthCheckConfig,
    'circuit-breaker': { name: 'circuit-breaker', ...HEALTH_CHECK_PRESETS.EXTERNAL_API } as HealthCheckConfig,
  },
  globalInterval: 30000,
  enableAutoMonitoring: true,
  historyRetention: 24 * 60 * 60 * 1000, // 24 hours
  maxHistorySize: 1000
};