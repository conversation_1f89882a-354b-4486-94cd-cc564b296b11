/**
 * Error handling types and interfaces for structured error management
 * Part of Technical Debt Solution - Phase 1, Task 1.1
 */

/**
 * Context information for error logging
 * Provides additional metadata about where and how an error occurred
 */
export interface ErrorContext {
  /** The operation that was being performed when the error occurred */
  operation: string;
  /** Optional user ID for user-specific error tracking */
  userId?: string;
  /** Additional metadata about the error context */
  metadata?: Record<string, unknown>;
  /** Timestamp when the error occurred (auto-generated if not provided) */
  timestamp?: number;
}

/**
 * Error severity levels for classification and alerting
 */
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * Complete error log entry with all context and metadata
 */
export interface ErrorLogEntry {
  /** The original error object */
  error: Error;
  /** Context information about the error */
  context: ErrorContext;
  /** Severity level of the error */
  severity: ErrorSeverity;
  /** Environment where the error occurred */
  environment: string;
  /** Unique identifier for this error occurrence */
  id: string;
}

/**
 * Configuration for error logging behavior
 */
export interface ErrorLoggerConfig {
  /** Whether to log to console in development */
  enableConsoleLogging: boolean;
  /** Whether to send errors to monitoring service */
  enableMonitoring: boolean;
  /** Maximum number of errors to buffer before sending */
  bufferSize: number;
  /** Interval in ms to flush buffered errors */
  flushInterval: number;
}

/**
 * Statistics about error logging
 */
export interface ErrorStats {
  /** Total number of errors logged */
  totalErrors: number;
  /** Errors by severity level */
  errorsBySeverity: Record<ErrorSeverity, number>;
  /** Errors by operation */
  errorsByOperation: Record<string, number>;
  /** Most recent error timestamp */
  lastErrorTime: number;
}