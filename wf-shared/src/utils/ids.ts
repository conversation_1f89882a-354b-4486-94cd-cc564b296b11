// ID generation utility functions shared between frontend and admin portal
// This file contains ONLY safe, generic ID utilities - no admin-specific logic

let count = 0;

/**
 * Generate a simple incremental ID
 * @returns Generated ID as string
 */
export function genId(): string {
  count = (count + 1) % Number.MAX_SAFE_INTEGER;
  return count.toString();
}

/**
 * Generate a random option key with timestamp
 * @param prefix - Optional prefix for the key (default: 'option')
 * @returns Generated option key string
 */
export function generateOptionKey(prefix = 'option'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generate a UUID v4 (pseudo-random)
 * @returns UUID v4 string
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Generate a short random ID
 * @param length - Length of the ID (default: 8)
 * @returns Short random ID string
 */
export function generateShortId(length = 8): string {
  return Math.random().toString(36).substr(2, length);
}