import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ErrorLogger } from '../errorHandling';
import type { ErrorLogEntry } from '../../types';

declare const global: typeof globalThis & {
    __errorLogEntries?: ErrorLogEntry[];
};

describe('ErrorLogger', () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    beforeEach(() => {
        ErrorLogger.reset();
        consoleErrorSpy.mockClear();
        if (global.__errorLogEntries) {
            global.__errorLogEntries = [];
        }
    });

    afterEach(() => {
        consoleErrorSpy.mockRestore();
    });

    describe('Configuration', () => {
        it('should allow overriding default configuration', () => {
            ErrorLogger.configure({ enableConsoleLogging: true, enableMonitoring: true });
            // @ts-ignore
            expect(ErrorLogger.config.enableConsoleLogging).toBe(true);
            // @ts-ignore
            expect(ErrorLogger.config.enableMonitoring).toBe(true);
        });
    });

    describe('Logging', () => {
        it('should log an error with medium severity by default', () => {
            ErrorLogger.log(new Error('Test error'), { operation: 'test' });
            const stats = ErrorLogger.getStats();
            expect(stats.totalErrors).toBe(1);
            expect(stats.errorsBySeverity.medium).toBe(1);
        });

        it('should log to console when enabled', () => {
            const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
            ErrorLogger.configure({ enableConsoleLogging: true });
            ErrorLogger.log(new Error('Console test'), { operation: 'console-test' });
            expect(consoleErrorSpy).toHaveBeenCalled();
            consoleErrorSpy.mockRestore();
        });

        it('should not log to console when disabled', () => {
            ErrorLogger.log(new Error('No console test'), { operation: 'no-console-test' });
            expect(consoleErrorSpy).not.toHaveBeenCalled();
        });

        it('should buffer errors for monitoring when enabled', () => {
            ErrorLogger.configure({ enableMonitoring: true });
            ErrorLogger.log(new Error('Buffered error'), { operation: 'buffer-test' });
            // @ts-ignore
            expect(ErrorLogger.errorBuffer.length).toBe(1);
        });

        it('should immediately send critical errors to monitoring', () => {
            ErrorLogger.configure({ enableMonitoring: true });
            ErrorLogger.log(new Error('Critical error'), { operation: 'critical-test' }, 'critical');
            // @ts-ignore
            expect(ErrorLogger.errorBuffer.length).toBe(0);
            expect(global.__errorLogEntries?.length).toBe(1);
        });
    });

    describe('logAndReturn', () => {
        it('should log an error and return the fallback value', () => {
            const fallback = 'default-value';
            const result = ErrorLogger.logAndReturn(new Error('Fallback test'), { operation: 'fallback' }, fallback);
            expect(result).toBe(fallback);
            expect(ErrorLogger.getStats().totalErrors).toBe(1);
        });
    });

    describe('Statistics', () => {
        it('should correctly update statistics', () => {
            ErrorLogger.log(new Error('Error 1'), { operation: 'op1' }, 'low');
            ErrorLogger.log(new Error('Error 2'), { operation: 'op1' }, 'high');
            ErrorLogger.log(new Error('Error 3'), { operation: 'op2' }, 'high');

            const stats = ErrorLogger.getStats();
            expect(stats.totalErrors).toBe(3);
            expect(stats.errorsBySeverity.low).toBe(1);
            expect(stats.errorsBySeverity.high).toBe(2);
            expect(stats.errorsByOperation.op1).toBe(2);
            expect(stats.errorsByOperation.op2).toBe(1);
        });

        it('should reset statistics', () => {
            ErrorLogger.log(new Error('An error'), { operation: 'reset-test' });
            ErrorLogger.resetStats();
            const stats = ErrorLogger.getStats();
            expect(stats.totalErrors).toBe(0);
        });
    });

    describe('Flushing', () => {
        it('should flush the error buffer manually', () => {
            ErrorLogger.configure({ enableMonitoring: true });
            ErrorLogger.log(new Error('Error 1'), { operation: 'flush-test' });
            ErrorLogger.log(new Error('Error 2'), { operation: 'flush-test' });

            // @ts-ignore
            expect(ErrorLogger.errorBuffer.length).toBe(2);
            ErrorLogger.flush();
            // @ts-ignore
            expect(ErrorLogger.errorBuffer.length).toBe(0);
            expect(global.__errorLogEntries?.length).toBe(2);
        });

        it('should flush automatically when buffer size is reached', () => {
            ErrorLogger.configure({ enableMonitoring: true, bufferSize: 2 });
            ErrorLogger.log(new Error('Error 1'), { operation: 'auto-flush' });
            ErrorLogger.log(new Error('Error 2'), { operation: 'auto-flush' });

            // @ts-ignore
            expect(ErrorLogger.errorBuffer.length).toBe(0);
            expect(global.__errorLogEntries?.length).toBe(2);
        });
    });
});
