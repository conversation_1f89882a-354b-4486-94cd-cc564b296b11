import { describe, it, expect } from 'vitest';
import {
  genId,
  generateOptionKey,
  generateUUID,
  generateShortId,
} from '../ids';

describe('ID Generation Utilities', () => {
  describe('genId', () => {
    it('should generate an incremental ID', () => {
      const id1 = genId();
      const id2 = genId();
      expect(parseInt(id2, 10)).toBe(parseInt(id1, 10) + 1);
    });
  });

  describe('generateOptionKey', () => {
    it('should generate a unique option key with a prefix', () => {
      const key1 = generateOptionKey('test');
      const key2 = generateOptionKey('test');
      expect(key1).not.toBe(key2);
      expect(key1).toMatch(/^test_/);
    });
  });

  describe('generateUUID', () => {
    it('should generate a valid UUID v4', () => {
      const uuid = generateUUID();
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      expect(uuid).toMatch(uuidRegex);
    });
  });

  describe('generateShortId', () => {
    it('should generate a short ID of the specified length', () => {
      const shortId = generateShortId(10);
      expect(shortId).toHaveLength(10);
    });

    it('should generate a short ID of the default length', () => {
      const shortId = generateShortId();
      expect(shortId).toHaveLength(8);
    });
  });
});
