import { describe, it, expect } from 'vitest';
import {
  formatDate,
  formatNumber,
  formatTime,
  formatTimeMinutes,
  formatPercentage,
} from '../formatting';

describe('Formatting Utilities', () => {
  describe('formatDate', () => {
    it('should format a date object with default options', () => {
      const date = new Date('2025-07-23T12:00:00.000Z');
      // Note: The output can vary slightly based on the test runner's timezone.
      // This test is designed to be relatively robust.
      expect(formatDate(date)).toContain('Jul 23, 2025');
    });

    it('should format a date string with custom options', () => {
      const dateString = '2025-07-23T12:00:00.000Z';
      const options: Intl.DateTimeFormatOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      };
      expect(formatDate(dateString, options)).toBe('Wednesday, July 23, 2025');
    });
  });

  describe('formatNumber', () => {
    it('should format a number with default locale', () => {
      expect(formatNumber(1234567.89)).toBe('1,234,567.89');
    });

    it('should format a number with a specified locale', () => {
      // German uses a period for thousands and a comma for decimals
      expect(formatNumber(1234567.89, 'de-DE')).toBe('1.234.567,89');
    });
  });

  describe('formatTime', () => {
    it('should return "0s" for null, undefined, or invalid input', () => {
      expect(formatTime(null)).toBe('0s');
      expect(formatTime(undefined)).toBe('0s');
      expect(formatTime(NaN)).toBe('0s');
      expect(formatTime(-100)).toBe('0s');
    });

    it('should format time in seconds', () => {
      expect(formatTime(30)).toBe('30s');
    });

    it('should format time in minutes', () => {
      expect(formatTime(1500)).toBe('25m');
    });

    it('should format time in hours and minutes', () => {
      expect(formatTime(9000)).toBe('2h 30m');
    });
  });

  describe('formatTimeMinutes', () => {
    it('should format time in MM:SS format', () => {
      expect(formatTimeMinutes(150)).toBe('2:30');
      expect(formatTimeMinutes(45)).toBe('0:45');
    });
  });

  describe('formatPercentage', () => {
    it('should format a percentage with default decimals', () => {
      expect(formatPercentage(85.123)).toBe('85.1%');
    });

    it('should format a percentage with specified decimals', () => {
      expect(formatPercentage(85.123, 2)).toBe('85.12%');
    });
  });
});
