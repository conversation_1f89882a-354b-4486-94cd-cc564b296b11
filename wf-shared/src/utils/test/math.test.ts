import { describe, it, expect } from 'vitest';
import {
  calculatePercentage,
  clamp,
  roundTo,
  average,
  sum,
  max,
  min,
  isBetween,
} from '../math';

describe('Math Utilities', () => {
  describe('calculatePercentage', () => {
    it('should return 0 if total is 0', () => {
      expect(calculatePercentage(10, 0)).toBe(0);
    });

    it('should calculate the percentage with default decimals', () => {
      expect(calculatePercentage(1, 3)).toBe(33.3);
    });

    it('should calculate the percentage with specified decimals', () => {
      expect(calculatePercentage(1, 3, 2)).toBe(33.33);
    });
  });

  describe('clamp', () => {
    it('should return the value if it is within the range', () => {
      expect(clamp(5, 0, 10)).toBe(5);
    });

    it('should return the min value if the value is less than the min', () => {
      expect(clamp(-5, 0, 10)).toBe(0);
    });

    it('should return the max value if the value is greater than the max', () => {
      expect(clamp(15, 0, 10)).toBe(10);
    });
  });

  describe('roundTo', () => {
    it('should round to the default number of decimal places', () => {
      expect(roundTo(3.14159)).toBe(3.14);
    });

    it('should round to the specified number of decimal places', () => {
      expect(roundTo(3.14159, 3)).toBe(3.142);
    });
  });

  describe('average', () => {
    it('should return 0 for an empty array', () => {
      expect(average([])).toBe(0);
    });

    it('should calculate the average of an array of numbers', () => {
      expect(average([1, 2, 3, 4, 5])).toBe(3);
    });
  });

  describe('sum', () => {
    it('should return 0 for an empty array', () => {
      expect(sum([])).toBe(0);
    });

    it('should calculate the sum of an array of numbers', () => {
      expect(sum([1, 2, 3, 4, 5])).toBe(15);
    });
  });

  describe('max', () => {
    it('should return 0 for an empty array', () => {
      expect(max([])).toBe(0);
    });

    it('should find the maximum value in an array of numbers', () => {
      expect(max([1, 5, 2, 4, 3])).toBe(5);
    });
  });

  describe('min', () => {
    it('should return 0 for an empty array', () => {
      expect(min([])).toBe(0);
    });

    it('should find the minimum value in an array of numbers', () => {
      expect(min([5, 1, 2, 4, 3])).toBe(1);
    });
  });

  describe('isBetween', () => {
    it('should return true if the value is within the range', () => {
      expect(isBetween(5, 0, 10)).toBe(true);
    });

    it('should return false if the value is outside the range', () => {
      expect(isBetween(15, 0, 10)).toBe(false);
    });

    it('should return true if the value is equal to the min or max', () => {
      expect(isBetween(0, 0, 10)).toBe(true);
      expect(isBetween(10, 0, 10)).toBe(true);
    });
  });
});
