import { describe, it, expect } from 'vitest';
import {
  getScoreColor,
  getProgressColor,
  getPerformanceLevel,
  getScoreBadge,
  calculateCompletionRate,
  calculateAverageScore,
} from '../performance';

describe('Performance Utilities', () => {
  describe('getScoreColor', () => {
    it('should return green for 80% or higher', () => {
      expect(getScoreColor(80)).toBe('text-green-600');
      expect(getScoreColor(100)).toBe('text-green-600');
    });

    it('should return blue for 70-79%', () => {
      expect(getScoreColor(70)).toBe('text-blue-600');
      expect(getScoreColor(79)).toBe('text-blue-600');
    });

    it('should return yellow for 60-69%', () => {
      expect(getScoreColor(60)).toBe('text-yellow-600');
      expect(getScoreColor(69)).toBe('text-yellow-600');
    });

    it('should return red for below 60%', () => {
      expect(getScoreColor(59)).toBe('text-red-600');
      expect(getScoreColor(0)).toBe('text-red-600');
    });
  });

  describe('getProgressColor', () => {
    it('should return green background for 80% or higher', () => {
      expect(getProgressColor(80)).toBe('bg-green-500');
      expect(getProgressColor(100)).toBe('bg-green-500');
    });

    it('should return blue background for 70-79%', () => {
      expect(getProgressColor(70)).toBe('bg-blue-500');
      expect(getProgressColor(79)).toBe('bg-blue-500');
    });

    it('should return yellow background for 60-69%', () => {
      expect(getProgressColor(60)).toBe('bg-yellow-500');
      expect(getProgressColor(69)).toBe('bg-yellow-500');
    });

    it('should return red background for below 60%', () => {
      expect(getProgressColor(59)).toBe('bg-red-500');
      expect(getProgressColor(0)).toBe('bg-red-500');
    });
  });

  describe('getPerformanceLevel', () => {
    it('should return Excellent for 90% or higher', () => {
      expect(getPerformanceLevel(90)).toEqual({
        level: 'Excellent',
        color: 'text-green-600',
        bg: 'bg-green-100',
      });
    });

    it('should return Very Good for 80-89%', () => {
      expect(getPerformanceLevel(80)).toEqual({
        level: 'Very Good',
        color: 'text-blue-600',
        bg: 'bg-blue-100',
      });
    });

    it('should return Good for 70-79%', () => {
      expect(getPerformanceLevel(70)).toEqual({
        level: 'Good',
        color: 'text-yellow-600',
        bg: 'bg-yellow-100',
      });
    });

    it('should return Fair for 60-69%', () => {
      expect(getPerformanceLevel(60)).toEqual({
        level: 'Fair',
        color: 'text-orange-600',
        bg: 'bg-orange-100',
      });
    });

    it('should return Needs Improvement for below 60%', () => {
      expect(getPerformanceLevel(59)).toEqual({
        level: 'Needs Improvement',
        color: 'text-red-600',
        bg: 'bg-red-100',
      });
    });
  });

  describe('getScoreBadge', () => {
    it('should return Excellent badge for 90% or higher', () => {
      expect(getScoreBadge(90)).toEqual({
        text: 'Excellent',
        class: 'bg-green-100 text-green-800',
      });
    });

    it('should return Very Good badge for 80-89%', () => {
      expect(getScoreBadge(80)).toEqual({
        text: 'Very Good',
        class: 'bg-blue-100 text-blue-800',
      });
    });

    it('should return Good badge for 70-79%', () => {
      expect(getScoreBadge(70)).toEqual({
        text: 'Good',
        class: 'bg-yellow-100 text-yellow-800',
      });
    });

    it('should return Fair badge for 60-69%', () => {
      expect(getScoreBadge(60)).toEqual({
        text: 'Fair',
        class: 'bg-orange-100 text-orange-800',
      });
    });

    it('should return Needs Work badge for below 60%', () => {
      expect(getScoreBadge(59)).toEqual({
        text: 'Needs Work',
        class: 'bg-red-100 text-red-800',
      });
    });
  });

  describe('calculateCompletionRate', () => {
    it('should return 0 if total is 0', () => {
      expect(calculateCompletionRate(10, 0)).toBe(0);
    });

    it('should calculate completion rate correctly', () => {
      expect(calculateCompletionRate(5, 10)).toBe(50);
      expect(calculateCompletionRate(7, 8)).toBe(88);
      expect(calculateCompletionRate(10, 10)).toBe(100);
    });
  });

  describe('calculateAverageScore', () => {
    it('should return 0 for an empty array', () => {
      expect(calculateAverageScore([])).toBe(0);
    });

    it('should calculate average score correctly', () => {
      expect(calculateAverageScore([10, 20, 30])).toBe(20);
      expect(calculateAverageScore([10, 15, 20])).toBe(15);
      expect(calculateAverageScore([10, 10, 10, 10])).toBe(10);
      expect(calculateAverageScore([10.5, 11.5])).toBe(11);
      expect(calculateAverageScore([10.1, 10.2, 10.3])).toBe(10.2);
    });
  });
});
