import { describe, it, expect } from 'vitest';
import { cn, combineModuleClasses, getModuleClass, createModuleClassGetter } from '../css';

describe('CSS Utilities', () => {
  describe('cn', () => {
    it('should merge and deduplicate class names correctly', () => {
      expect(cn('bg-red-500', 'text-white', 'bg-blue-500')).toBe('text-white bg-blue-500');
    });

    it('should handle conditional classes', () => {
      expect(cn('base', { 'conditional-class': true }, { 'another-class': false })).toBe('base conditional-class');
    });
  });

  describe('combineModuleClasses', () => {
    const styles = {
      button: 'button_hash',
      active: 'active_hash',
      disabled: 'disabled_hash',
    };

    it('should combine base and conditional classes', () => {
      const className = combineModuleClasses(styles, 'button', { active: true });
      expect(className).toBe('button_hash active_hash');
    });

    it('should include additional classes', () => {
      const className = combineModuleClasses(styles, 'button', {}, 'extra-class');
      expect(className).toBe('button_hash extra-class');
    });
  });

  describe('getModuleClass', () => {
    const styles = {
      'my-class': 'my-class_hash',
    };

    it('should return the hashed class name if it exists', () => {
      expect(getModuleClass(styles, 'my-class')).toBe('my-class_hash');
    });

    it('should return the fallback if the class name does not exist', () => {
      expect(getModuleClass(styles, 'non-existent-class', 'fallback')).toBe('fallback');
    });
  });

  describe('createModuleClassGetter', () => {
    const styles = {
      button: 'button_hash',
      active: 'active_hash',
    };

    it('should create a function that gets module classes', () => {
      const getClass = createModuleClassGetter(styles);
      const buttonClass = getClass('button', { active: true }, 'extra-class');
      expect(buttonClass).toBe('button_hash active_hash extra-class');
    });
  });
});
