/**
 * Performance and scoring utility functions shared between frontend and admin portal.
 * This file contains ONLY safe, generic performance utilities - no admin-specific logic.
 * 
 * These utilities provide standardized ways to evaluate and display performance metrics,
 * ensuring consistent scoring and visual feedback across all applications.
 */

/**
 * Performance level information with display properties.
 * 
 * @example
 * ```typescript
 * const level: PerformanceLevel = {
 *   level: 'Excellent',
 *   color: 'text-green-600',
 *   bg: 'bg-green-100'
 * }
 * ```
 */
export interface PerformanceLevel {
  /** Human-readable performance level */
  level: string;
  /** CSS text color class */
  color: string;
  /** CSS background color class */
  bg: string;
}

/**
 * Score badge information with display properties.
 * 
 * @example
 * ```typescript
 * const badge: ScoreBadge = {
 *   text: 'Very Good',
 *   class: 'bg-blue-100 text-blue-800'
 * }
 * ```
 */
export interface ScoreBadge {
  /** Display text for the badge */
  text: string;
  /** CSS classes for styling the badge */
  class: string;
}

/**
 * Get CSS color class based on score percentage
 * @param percentage - Score percentage (0-100)
 * @returns CSS color class string
 */
export function getScoreColor(percentage: number): string {
  if (percentage >= 80) return 'text-green-600';
  if (percentage >= 70) return 'text-blue-600';
  if (percentage >= 60) return 'text-yellow-600';
  return 'text-red-600';
}

/**
 * Get CSS background color class based on progress percentage
 * @param percentage - Progress percentage (0-100)
 * @returns CSS background color class string
 */
export function getProgressColor(percentage: number): string {
  if (percentage >= 80) return 'bg-green-500';
  if (percentage >= 70) return 'bg-blue-500';
  if (percentage >= 60) return 'bg-yellow-500';
  return 'bg-red-500';
}

/**
 * Get performance level information based on percentage
 * @param percentage - Performance percentage (0-100)
 * @returns PerformanceLevel object with level, color, and background
 */
export function getPerformanceLevel(percentage: number): PerformanceLevel {
  if (percentage >= 90) {
    return { level: 'Excellent', color: 'text-green-600', bg: 'bg-green-100' };
  }
  if (percentage >= 80) {
    return { level: 'Very Good', color: 'text-blue-600', bg: 'bg-blue-100' };
  }
  if (percentage >= 70) {
    return { level: 'Good', color: 'text-yellow-600', bg: 'bg-yellow-100' };
  }
  if (percentage >= 60) {
    return { level: 'Fair', color: 'text-orange-600', bg: 'bg-orange-100' };
  }
  return { level: 'Needs Improvement', color: 'text-red-600', bg: 'bg-red-100' };
}

/**
 * Get score badge information based on score
 * @param score - Score value (0-100)
 * @returns ScoreBadge object with text and CSS classes
 */
export function getScoreBadge(score: number): ScoreBadge {
  if (score >= 90) {
    return { text: 'Excellent', class: 'bg-green-100 text-green-800' };
  }
  if (score >= 80) {
    return { text: 'Very Good', class: 'bg-blue-100 text-blue-800' };
  }
  if (score >= 70) {
    return { text: 'Good', class: 'bg-yellow-100 text-yellow-800' };
  }
  if (score >= 60) {
    return { text: 'Fair', class: 'bg-orange-100 text-orange-800' };
  }
  return { text: 'Needs Work', class: 'bg-red-100 text-red-800' };
}

/**
 * Calculate completion rate as percentage
 * @param answered - Number of answered questions
 * @param total - Total number of questions
 * @returns Completion rate percentage (0-100)
 */
export function calculateCompletionRate(answered: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((answered / total) * 100);
}

/**
 * Calculate average score from an array of scores
 * @param scores - Array of score values
 * @returns Average score rounded to 1 decimal place
 */
export function calculateAverageScore(scores: number[]): number {
  if (scores.length === 0) return 0;
  const sum = scores.reduce((acc, score) => acc + score, 0);
  return Math.round((sum / scores.length) * 10) / 10;
}