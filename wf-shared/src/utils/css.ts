// CSS utility functions shared between frontend and admin portal
// This file contains ONLY safe, generic CSS utilities - no admin-specific logic

import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * Utility function to merge CSS classes with Tailwind CSS conflict resolution
 * @param inputs - CSS class values to merge
 * @returns Merged CSS class string
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * CSS Modules utility functions for class name manipulation
 */

/**
 * Combines CSS modules classes with conditional logic
 * @param styles - CSS modules object
 * @param baseClass - Base class name
 * @param conditionalClasses - Object with boolean conditions
 * @param additionalClasses - Additional classes to merge
 * @returns Combined class string
 * 
 * @example
 * const className = combineModuleClasses(
 *   styles,
 *   'button',
 *   { active: isActive, disabled: isDisabled },
 *   'extra-class'
 * );
 */
export function combineModuleClasses(
  styles: Record<string, string>,
  baseClass: string,
  conditionalClasses: Record<string, boolean> = {},
  ...additionalClasses: ClassValue[]
): string {
  const baseClassName = styles[baseClass] || '';
  const conditionalClassNames = Object.entries(conditionalClasses)
    .filter(([, condition]) => condition)
    .map(([className]) => styles[className] || className);

  return cn(baseClassName, ...conditionalClassNames, ...additionalClasses);
}

/**
 * Safely gets a CSS module class name with fallback
 * @param styles - CSS modules object
 * @param className - Class name to get
 * @param fallback - Fallback class name if not found
 * @returns Class name or fallback
 */
export function getModuleClass(
  styles: Record<string, string>,
  className: string,
  fallback: string = ''
): string {
  return styles[className] || fallback;
}

/**
 * Creates a CSS modules class getter function for a component
 * @param styles - CSS modules object
 * @returns Function to get classes from the module
 * 
 * @example
 * const getClass = createModuleClassGetter(styles);
 * const buttonClass = getClass('button', { active: isActive });
 */
export function createModuleClassGetter(styles: Record<string, string>) {
  return (
    baseClass: string,
    conditionalClasses: Record<string, boolean> = {},
    ...additionalClasses: ClassValue[]
  ) => combineModuleClasses(styles, baseClass, conditionalClasses, ...additionalClasses);
}