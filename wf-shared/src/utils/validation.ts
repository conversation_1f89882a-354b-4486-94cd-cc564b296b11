// Validation utilities will be extracted here during migration
// This is a placeholder file

export const isValidEmail = (email: string): boolean => {
  if (!email || typeof email !== 'string') {
    return false;
  }
  // A more comprehensive regex for email validation (RFC 5322 compliant, but simplified for common use cases)
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

export const isStrongPassword = (password: string): boolean => {
  if (!password || typeof password !== 'string') {
    return false;
  }
  // Password must be at least 8 characters long, contain at least one uppercase letter, one lowercase letter, one number, and one special character.
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+}{"':;?/>.<,])(?=.{8,})/;
  return strongPasswordRegex.test(password);
};

export const validationUtils = {
  isValidEmail,
  isStrongPassword,
  // Will be populated from existing utilities
};