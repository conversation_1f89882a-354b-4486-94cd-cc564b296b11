/**
 * Utility functions exported for both frontend and admin portal.
 * These are SAFE utilities that contain no admin-specific logic.
 * 
 * This module provides a comprehensive set of utility functions for common tasks
 * including CSS class management, data formatting, mathematical calculations,
 * and date manipulation.
 * 
 * @example
 * ```typescript
 * import { cn, formatDate, calculatePercentage, generateUUID } from 'wf-shared/utils'
 * 
 * const className = cn('text-blue-500', { 'font-bold': isActive })
 * const formattedDate = formatDate(new Date())
 * const percentage = calculatePercentage(75, 100)
 * const id = generateUUID()
 * ```
 */

/** CSS utilities for class name management */
export * from './css'

/** Formatting utilities for display purposes */
export * from './formatting'

/** Performance and scoring utilities */
export * from './performance'

/** ID generation utilities */
export * from './ids'

/** Mathematical calculation utilities */
export * from './math'

/** Date manipulation utilities */
export * from './dates'

/** Error handling utilities */
export * from './errorHandling'