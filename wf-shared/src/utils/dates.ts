// Date utility functions shared between frontend and admin portal
// This file contains ONLY safe, generic date utilities - no admin-specific logic

/**
 * Get start of week (Monday) for a given date
 * @param date - Date to get start of week for (default: now)
 * @returns Date object set to start of week
 */
export function getStartOfWeek(date: Date = new Date()): Date {
  const startOfWeek = new Date(date.getTime());
  const dayOfWeek = startOfWeek.getUTCDay();
  const diff = startOfWeek.getUTCDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1); // Monday as first day
  startOfWeek.setUTCDate(diff);
  startOfWeek.setUTCHours(0, 0, 0, 0);
  return startOfWeek;
}

/**
 * Get end of week (Sunday) for a given date
 * @param date - Date to get end of week for (default: now)
 * @returns Date object set to end of week
 */
export function getEndOfWeek(date: Date = new Date()): Date {
  const endOfWeek = getStartOfWeek(date);
  endOfWeek.setUTCDate(endOfWeek.getUTCDate() + 6);
  endOfWeek.setUTCHours(23, 59, 59, 999);
  return endOfWeek;
}

/**
 * Get start of month for a given date
 * @param date - Date to get start of month for (default: now)
 * @returns Date object set to start of month
 */
export function getStartOfMonth(date: Date = new Date()): Date {
  const startOfMonth = new Date(date.getTime());
  startOfMonth.setUTCDate(1);
  startOfMonth.setUTCHours(0, 0, 0, 0);
  return startOfMonth;
}

/**
 * Get end of month for a given date
 * @param date - Date to get end of month for (default: now)
 * @returns Date object set to end of month
 */
export function getEndOfMonth(date: Date = new Date()): Date {
  const endOfMonth = new Date(date.getTime());
  endOfMonth.setUTCMonth(endOfMonth.getUTCMonth() + 1, 0);
  endOfMonth.setUTCHours(23, 59, 59, 999);
  return endOfMonth;
}

/**
 * Check if two dates are on the same day
 * @param date1 - First date
 * @param date2 - Second date
 * @returns True if dates are on the same day
 */
export function isSameDay(date1: Date, date2: Date): boolean {
  return (
    date1.getUTCFullYear() === date2.getUTCFullYear() &&
    date1.getUTCMonth() === date2.getUTCMonth() &&
    date1.getUTCDate() === date2.getUTCDate()
  );
}

/**
 * Check if a date is today
 * @param date - Date to check
 * @returns True if date is today
 */
export function isToday(date: Date): boolean {
  return isSameDay(date, new Date());
}

/**
 * Get days between two dates
 * @param startDate - Start date
 * @param endDate - End date
 * @returns Number of days between dates
 */
export function daysBetween(startDate: Date, endDate: Date): number {
  const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
  return Math.round(Math.abs((endDate.getTime() - startDate.getTime()) / oneDay));
}

/**
 * Add days to a date
 * @param date - Date to add days to
 * @param days - Number of days to add
 * @returns New Date object with added days
 */
export function addDays(date: Date, days: number): Date {
  const result = new Date(date.getTime());
  result.setUTCDate(result.getUTCDate() + days);
  return result;
}

/**
 * Subtract days from a date
 * @param date - Date to subtract days from
 * @param days - Number of days to subtract
 * @returns New Date object with subtracted days
 */
export function subtractDays(date: Date, days: number): Date {
  return addDays(date, -days);
}

/**
 * Get relative time string (e.g., "2 hours ago", "in 3 days")
 * @param date - Date to get relative time for
 * @param now - Reference date (default: current time)
 * @returns Relative time string
 */
export function getRelativeTime(date: Date, now: Date = new Date()): string {
  const diffMs = now.getTime() - date.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (Math.abs(diffMinutes) < 1) {
    return 'just now';
  } else if (Math.abs(diffMinutes) < 60) {
    return diffMinutes > 0 ? `${diffMinutes}m ago` : `in ${Math.abs(diffMinutes)}m`;
  } else if (Math.abs(diffHours) < 24) {
    return diffHours > 0 ? `${diffHours}h ago` : `in ${Math.abs(diffHours)}h`;
  } else if (Math.abs(diffDays) < 7) {
    return diffDays > 0 ? `${diffDays}d ago` : `in ${Math.abs(diffDays)}d`;
  } else {
    return date.toLocaleDateString();
  }
}