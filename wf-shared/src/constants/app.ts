// App-wide constants shared between frontend and admin portal
// This file contains ONLY safe, generic app constants - no admin-specific logic

export const APP_CONSTANTS = {
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 6,
    ADMIN_PAGE_SIZES: [10, 25, 50, 100],
    PAGE_RANGE_DELTA: 3,
  },
  STORAGE_KEYS: {
    LANGUAGE: 'language',
    THEME: 'theme', 
    QUIZ_MODE: 'quiz_mode',
    ADMIN_CREDENTIALS: 'admin-remembered-credentials',
    USER_CREDENTIALS: 'user-remembered-credentials',
    USER_SESSION: 'user-session',
    USER_PROGRESS: 'user-progress'
  },
  UI: {
    TOAST_LIMIT: 1,
    TOAST_REMOVE_DELAY: 1000000,
    MOBILE_BREAKPOINT: 768,
    SIDEBAR: {
      COOKIE_NAME: 'sidebar:state',
      COOKIE_MAX_AGE: 60 * 60 * 24 * 7, // 7 days
      WIDTH: '16rem',
      WIDTH_MOBILE: '18rem',
      WIDTH_ICON: '3rem',
      <PERSON><PERSON><PERSON><PERSON>ARD_SHORTCUT: 'b'
    }
  }
} as const;

// Type helpers
export type StorageKey = typeof APP_CONSTANTS.STORAGE_KEYS[keyof typeof APP_CONSTANTS.STORAGE_KEYS];
export type PageSize = typeof APP_CONSTANTS.PAGINATION.ADMIN_PAGE_SIZES[number];