// API and networking constants shared between frontend and admin portal
// This file contains ONLY safe, generic API constants - no admin-specific logic

export const API_CONSTANTS = {
  RETRY_CONFIG: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 2,
  },
  ERROR_TYPES: {
    NETWORK: 'network',
    AUTHENTICATION: 'authentication', 
    AUTHORIZATION: 'authorization',
    VALIDATION: 'validation',
    SERVER: 'server',
    UNKNOWN: 'unknown'
  },
  ERROR_CODES: {
    NETWORK_ERROR: 'NETWORK_ERROR',
    AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
    AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR', 
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    SERVER_ERROR: 'SERVER_ERROR'
  }
} as const;

// Type helpers for error handling
export type ErrorType = typeof API_CONSTANTS.ERROR_TYPES[keyof typeof API_CONSTANTS.ERROR_TYPES];
export type ErrorCode = typeof API_CONSTANTS.ERROR_CODES[keyof typeof API_CONSTANTS.ERROR_CODES];

// Retry configuration interface
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}