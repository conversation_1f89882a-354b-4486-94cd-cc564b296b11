// Validation constants shared between frontend and admin portal
// This file contains ONLY safe, generic validation constants - no admin-specific logic

export const VALIDATION_CONSTANTS = {
  MESSAGES: {
    REQUIRED_FIELD: 'This field is required',
    INVALID_EMAIL: 'Please enter a valid email address',
    PASSWORD_TOO_SHORT: 'Password must be at least 8 characters',
    PASSWORD_TOO_WEAK: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    INVALID_FORMAT: 'Invalid format',
    CONFIRM_PASSWORD_MISMATCH: 'Passwords do not match',
    INVALID_LENGTH: 'Invalid length',
    INVALID_CHARACTERS: 'Contains invalid characters'
  },
  PATTERNS: {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    STRONG_PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    PHONE: /^\+?[\d\s\-\(\)]+$/,
    USERNAME: /^[a-zA-Z0-9_-]{3,20}$/
  },
  LIMITS: {
    EMAIL_MAX_LENGTH: 254,
    PASSWORD_MIN_LENGTH: 8,
    PASSWORD_MAX_LENGTH: 128,
    USERNAME_MIN_LENGTH: 3,
    USERNAME_MAX_LENGTH: 20,
    TEXT_FIELD_MAX_LENGTH: 255,
    TEXTAREA_MAX_LENGTH: 2000
  }
} as const;

// Type helpers
export type ValidationMessage = typeof VALIDATION_CONSTANTS.MESSAGES[keyof typeof VALIDATION_CONSTANTS.MESSAGES];
export type ValidationPattern = typeof VALIDATION_CONSTANTS.PATTERNS[keyof typeof VALIDATION_CONSTANTS.PATTERNS];