// Repository Manager - Singleton pattern for repository access
// Handles database initialization and provides global repository container

import { createClient } from '@supabase/supabase-js'
import type { Database } from '../types'
import { DatabaseProvider } from './DatabaseProvider'

export interface RepositoryConfig {
  supabaseUrl: string
  supabaseAnonKey: string
}

export abstract class RepositoryManager {
  private static container?: Record<string, unknown>
  private static initialized = false

  /**
   * Initialize the repository system with database configuration
   */
  static initialize(config: RepositoryConfig): void {
    if (this.initialized) {
      // RepositoryManager already initialized - silently return
      return
    }

    // Create Supabase client
    const supabase = createClient<Database>(config.supabaseUrl, config.supabaseAnonKey)
    
    // Set as default client
    DatabaseProvider.getInstance().setDefaultClient(supabase)
    
    this.initialized = true
  }

  /**
   * Get the singleton repository container
   * Must be implemented by each application (frontend/admin)
   */
  static getContainer(): Record<string, unknown> {
    if (!this.initialized) {
      throw new Error('RepositoryManager not initialized. Call initialize() first.')
    }

    if (!this.container) {
      throw new Error('Repository container not set. Override setContainer() in your application.')
    }

    return this.container
  }

  /**
   * Set the repository container implementation
   * Called by application-specific repository managers
   */
  protected static setContainer(container: Record<string, unknown>): void {
    this.container = container
  }

  /**
   * Check if repository system is initialized
   */
  static isInitialized(): boolean {
    return this.initialized
  }

  /**
   * Reset for testing purposes
   */
  static reset(): void {
    this.container = undefined
    this.initialized = false
    DatabaseProvider.getInstance().clearDefaultClient()
  }
}