/**
 * Base storage repository implementation for browser storage operations.
 * Provides common functionality for localStorage and sessionStorage.
 * 
 * This abstract class implements the storage repository pattern for browser storage APIs,
 * offering standardized methods for data persistence with automatic serialization/deserialization
 * and consistent error handling.
 * 
 * @template T - The type of data this repository stores and retrieves
 * 
 * @example
 * ```typescript
 * class UserPreferencesRepository extends BaseStorageRepository<UserPreferences> {
 *   protected storage = localStorage
 *   protected keyPrefix = 'user_prefs'
 * }
 * 
 * const repo = new UserPreferencesRepository()
 * await repo.save('theme', { mode: 'dark', fontSize: 'large' })
 * const prefs = await repo.load('theme')
 * ```
 */

import type { IStorageRepository } from '../interfaces/IStorageRepository'
import { ErrorLogger } from '../../utils/errorHandling'

export abstract class BaseStorageRepository<T> implements IStorageRepository<T> {
  /**
   * The browser storage instance (localStorage or sessionStorage).
   * Must be implemented by concrete repository classes.
   */
  protected abstract storage: Storage
  
  /**
   * The key prefix to namespace stored data.
   * Must be implemented by concrete repository classes.
   */
  protected abstract keyPrefix: string

  /**
   * Generate the full storage key by combining prefix and key.
   * 
   * @param key - The base key for the data
   * @returns The full key with prefix for storage operations
   */
  protected getFullKey(key: string): string {
    return `${this.keyPrefix}:${key}`
  }

  /**
   * Save data to storage with automatic serialization.
   * 
   * @param key - The key to store the data under
   * @param data - The data to store
   * @throws Error if storage operation fails
   * 
   * @example
   * ```typescript
   * await repository.save('user-123', { name: 'John', email: '<EMAIL>' })
   * ```
   */
  async save(key: string, data: T): Promise<void> {
    try {
      const fullKey = this.getFullKey(key)
      const serializedData = JSON.stringify(data)
      this.storage.setItem(fullKey, serializedData)
    } catch (error) {
      throw new Error(`Failed to save data to storage: ${error}`)
    }
  }

  /**
   * Load data from storage with automatic deserialization.
   * 
   * @param key - The key to load data from
   * @returns The stored data if found, null otherwise
   * 
   * @example
   * ```typescript
   * const userData = await repository.load('user-123')
   * if (userData) {
   *   console.log('User name:', userData.name)
   * }
   * ```
   */
  async load(key: string): Promise<T | null> {
    const fullKey = this.getFullKey(key)
    
    try {
      const serializedData = this.storage.getItem(fullKey)
      
      if (!serializedData) {
        return null
      }

      return JSON.parse(serializedData) as T
    } catch (error) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error('Failed to load data from storage'),
        {
          operation: 'storage-load',
          metadata: {
            key: fullKey,
            originalKey: key,
            storageType: this.storage === localStorage ? 'localStorage' : 'sessionStorage'
          }
        },
        'low'
      );
      return null
    }
  }

  /**
   * Remove data from storage.
   * 
   * @param key - The key of the data to remove
   * @throws Error if removal operation fails
   * 
   * @example
   * ```typescript
   * await repository.remove('user-123')
   * ```
   */
  async remove(key: string): Promise<void> {
    try {
      const fullKey = this.getFullKey(key)
      this.storage.removeItem(fullKey)
    } catch (error) {
      throw new Error(`Failed to remove data from storage: ${error}`)
    }
  }

  /**
   * Clear all data from storage that matches this repository's prefix.
   * 
   * @throws Error if clear operation fails
   * 
   * @example
   * ```typescript
   * await repository.clear() // Removes all user preference data
   * ```
   */
  async clear(): Promise<void> {
    try {
      const keys = await this.getAllKeys()
      keys.forEach(key => this.storage.removeItem(key))
    } catch (error) {
      throw new Error(`Failed to clear storage: ${error}`)
    }
  }

  /**
   * Check if data exists in storage for the given key.
   * 
   * @param key - The key to check for existence
   * @returns True if data exists, false otherwise
   * 
   * @example
   * ```typescript
   * if (await repository.exists('user-123')) {
   *   console.log('User data found')
   * }
   * ```
   */
  async exists(key: string): Promise<boolean> {
    try {
      const fullKey = this.getFullKey(key)
      return this.storage.getItem(fullKey) !== null
    } catch {
      return false
    }
  }

  /**
   * Get all storage keys that match this repository's prefix.
   * 
   * @returns Array of full storage keys (including prefix)
   * 
   * @example
   * ```typescript
   * const keys = await repository.getAllKeys()
   * console.log('Stored keys:', keys) // ['user_prefs:theme', 'user_prefs:language']
   * ```
   */
  async getAllKeys(): Promise<string[]> {
    try {
      const keys: string[] = []
      const prefix = `${this.keyPrefix}:`
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key && key.startsWith(prefix)) {
          keys.push(key)
        }
      }
      
      return keys
    } catch {
      return []
    }
  }
}