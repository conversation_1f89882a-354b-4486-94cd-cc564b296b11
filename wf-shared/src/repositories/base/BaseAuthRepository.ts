/**
 * Base authentication repository implementation for user authentication operations.
 * Provides common authentication functionality using Supabase Auth.
 * 
 * This abstract class handles user authentication flows including sign up, sign in,
 * sign out, session management, and email confirmation with standardized error handling.
 * 
 * @example
 * ```typescript
 * class AuthRepository extends BaseAuthRepository {
 *   // Additional authentication methods can be added here
 * }
 * ```
 */

import type { SupabaseClient, User as SupabaseUser, Session } from '@supabase/supabase-js'
import type { Database } from '../../types'
import { DatabaseProvider } from '../DatabaseProvider'
import { ErrorLogger } from '../../utils/errorHandling'

/**
 * Response type for authentication operations
 */
export interface AuthResponse {
  /** The authenticated user object, null if authentication failed */
  user: SupabaseUser | null
  /** Error message if authentication failed */
  error?: string
}

/**
 * Data structure for user sign up operations
 */
export interface SignUpData {
  /** User's email address */
  email: string
  /** User's password */
  password: string
  /** Optional user's first name */
  firstName?: string
  /** Optional user's last name */
  lastName?: string
  /** Optional user's proficiency level ID */
  levelId?: string
  /** Optional user's role (defaults to 'user') */
  role?: string
}

export abstract class BaseAuthRepository {
  /**
   * The Supabase client instance used for authentication operations
   */
  protected db: SupabaseClient<Database>

  /**
   * Creates a new BaseAuthRepository instance.
   * 
   * @param database - Optional Supabase client instance. If not provided,
   *                   will use the default client from DatabaseProvider.
   */
  constructor(database?: SupabaseClient<Database>) {
    // Use provided database or fall back to default
    if (database) {
      this.db = database
    } else {
      this.db = DatabaseProvider.getInstance().getDefaultClient()
    }
  }

  /**
   * Sign up a new user with email and password.
   * 
   * Creates a new user account with the provided credentials and optional metadata.
   * User metadata (firstName, lastName, levelId) is stored in the auth user's data field.
   * 
   * @param data - The sign up data containing email, password, and optional user metadata
   * @returns Promise resolving to AuthResponse with user data or error message
   * 
   * @example
   * ```typescript
   * const result = await authRepository.signUp({
   *   email: '<EMAIL>',
   *   password: 'securePassword123',
   *   firstName: 'John',
   *   lastName: 'Doe',
   *   levelId: 'beginner'
   * })
   * 
   * if (result.user) {
   *   console.log('User created:', result.user.email)
   * } else {
   *   console.error('Sign up failed:', result.error)
   * }
   * ```
   */
  async signUp(data: SignUpData): Promise<AuthResponse> {
    try {
      const { email, password, firstName, lastName, levelId, role } = data

      const { data: authData, error } = await this.db.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName || '',
            last_name: lastName || '',
            level_id: levelId || '',
            role: role || 'user'
          }
        }
      })

      if (error) {
        return { user: null, error: this.handleAuthError(error) }
      }

      return { user: authData.user }
    } catch (error) {
      return { user: null, error: this.handleAuthError(error) }
    }
  }

  /**
   * Sign in an existing user with email and password.
   * 
   * Authenticates a user with their credentials and returns both user data and session information.
   * The session can be used to maintain authentication state across requests.
   * 
   * @param email - The user's email address
   * @param password - The user's password
   * @returns Promise resolving to AuthResponse with user data, session, or error message
   * 
   * @example
   * ```typescript
   * const result = await authRepository.signIn('<EMAIL>', 'password123')
   * 
   * if (result.user) {
   *   console.log('Sign in successful:', result.user.email)
   *   console.log('Session ID:', result.session?.access_token)
   * } else {
   *   console.error('Sign in failed:', result.error)
   * }
   * ```
   */
  async signIn(email: string, password: string): Promise<AuthResponse & { session?: Session }> {
    try {
      const { data, error } = await this.db.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        return { user: null, error: this.handleAuthError(error) }
      }

      return { user: data.user, session: data.session }
    } catch (error) {
      return { user: null, error: this.handleAuthError(error) }
    }
  }

  /**
   * Sign out the current user.
   * 
   * Terminates the current user session and clears authentication state.
   * After successful sign out, the user will need to sign in again to access protected resources.
   * 
   * @returns Promise resolving to an object that may contain an error message if sign out fails
   * 
   * @example
   * ```typescript
   * const result = await authRepository.signOut()
   * 
   * if (result.error) {
   *   console.error('Sign out failed:', result.error)
   * } else {
   *   console.log('Sign out successful')
   * }
   * ```
   */
  async signOut(): Promise<{ error?: string }> {
    try {
      const { error } = await this.db.auth.signOut()
      
      if (error) {
        return { error: this.handleAuthError(error) }
      }
      
      return {}
    } catch (error) {
      return { error: this.handleAuthError(error) }
    }
  }

  /**
   * Get the current user session.
   * 
   * Retrieves the current authentication session and associated user data.
   * This method checks for an active session and returns both session and user information.
   * 
   * @returns Promise resolving to an object containing user, session, and optional error
   * 
   * @example
   * ```typescript
   * const result = await authRepository.getCurrentSession()
   * 
   * if (result.session) {
   *   console.log('Active session for user:', result.user?.email)
   *   console.log('Session expires at:', result.session.expires_at)
   * } else {
   *   console.log('No active session')
   * }
   * ```
   */
  async getCurrentSession(): Promise<{ user: SupabaseUser | null; session: Session | null; error?: string }> {
    try {
      const { data: { session }, error } = await this.db.auth.getSession()
      
      if (error) {
        return { user: null, session: null, error: this.handleAuthError(error) }
      }
      
      return { user: session?.user || null, session }
    } catch (error) {
      return { user: null, session: null, error: this.handleAuthError(error) }
    }
  }

  /**
   * Get the current authenticated user.
   * 
   * Retrieves the currently authenticated user's data from the active session.
   * This method validates the current session and returns user information.
   * 
   * @returns Promise resolving to an object containing user data and optional error
   * 
   * @example
   * ```typescript
   * const result = await authRepository.getCurrentUser()
   * 
   * if (result.user) {
   *   console.log('Current user:', result.user.email)
   *   console.log('User metadata:', result.user.user_metadata)
   * } else {
   *   console.log('No authenticated user')
   * }
   * ```
   */
  async getCurrentUser(): Promise<{ user: SupabaseUser | null; error?: string }> {
    try {
      const { data: { user }, error } = await this.db.auth.getUser()
      
      if (error) {
        return { user: null, error: this.handleAuthError(error) }
      }
      
      return { user }
    } catch (error) {
      return { user: null, error: this.handleAuthError(error) }
    }
  }

  /**
   * Listen for authentication state changes.
   * 
   * Sets up a listener for authentication events such as sign in, sign out, and token refresh.
   * The callback function will be called whenever the authentication state changes.
   * 
   * @param callback - Function to call when authentication state changes
   * @returns Subscription object that can be used to unsubscribe from the listener
   * 
   * @example
   * ```typescript
   * const subscription = authRepository.onAuthStateChange((event, session) => {
   *   console.log('Auth event:', event)
   *   if (session) {
   *     console.log('User signed in:', session.user.email)
   *   } else {
   *     console.log('User signed out')
   *   }
   * })
   * 
   * // Later, unsubscribe
   * subscription.data.subscription.unsubscribe()
   * ```
   */
  onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return this.db.auth.onAuthStateChange(callback)
  }

  /**
   * Resend email confirmation.
   * 
   * Resends the email confirmation to a user who has signed up but not yet confirmed their email.
   * This is useful when the original confirmation email was not received or has expired.
   * 
   * @param email - The email address to resend confirmation to
   * @returns Promise resolving to an object that may contain an error message if resend fails
   * 
   * @example
   * ```typescript
   * const result = await authRepository.resendConfirmation('<EMAIL>')
   * 
   * if (result.error) {
   *   console.error('Failed to resend confirmation:', result.error)
   * } else {
   *   console.log('Confirmation email resent successfully')
   * }
   * ```
   */
  async resendConfirmation(email: string): Promise<{ error?: string }> {
    try {
      const { error } = await this.db.auth.resend({
        type: 'signup',
        email
      })

      if (error) {
        return { error: this.handleAuthError(error) }
      }

      return {}
    } catch (error) {
      return { error: this.handleAuthError(error) }
    }
  }

  /**
   * Handle authentication errors.
   * 
   * Processes authentication errors and converts them to user-friendly error messages.
   * This method centralizes error handling and provides consistent error messaging
   * across all authentication operations.
   * 
   * @param error - The error object from Supabase or other sources
   * @returns A user-friendly error message string
   * 
   * @example
   * ```typescript
   * // This method is typically called internally by other methods
   * const friendlyMessage = this.handleAuthError(supabaseError)
   * console.log(friendlyMessage) // "Invalid email or password"
   * ```
   */
  protected handleAuthError(error: unknown): string {
    if (!error) {
      return 'An unexpected error occurred'
    }

    // Extract error message and code
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const errorObj = error as any;
    const message = errorObj?.message || (typeof error === 'string' ? error : 'An unexpected error occurred')
    const code = errorObj?.code || 'UNKNOWN_ERROR';
    
    // Log the actual error for debugging purposes
    ErrorLogger.log(
      error instanceof Error ? error : new Error(message),
      {
        operation: 'auth-error',
        metadata: {
          errorCode: code,
          originalMessage: message,
          errorType: typeof error
        }
      },
      'medium'
    );
    
    // Return user-friendly error messages
    if (message.includes('Invalid login credentials')) {
      return 'Invalid email or password'
    }
    if (message.includes('Email not confirmed')) {
      return 'Please confirm your email address'
    }
    if (message.includes('User already registered')) {
      return 'An account with this email already exists'
    }
    
    return message
  }
}