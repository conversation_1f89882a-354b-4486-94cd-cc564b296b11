/**
 * Base repository interface for data access layer.
 * Defines common CRUD operations for all repositories.
 * 
 * This interface establishes a consistent contract for all repository implementations,
 * ensuring standardized data access patterns across the application.
 * 
 * @template T - The entity type this repository manages
 * @template TCreate - The type for creating new entities (defaults to Partial<T>)
 * @template TUpdate - The type for updating entities (defaults to Partial<T>)
 * 
 * @example
 * ```typescript
 * class UserRepository implements IRepository<User, CreateUserData, UpdateUserData> {
 *   async getAll(params) { ... }
 *   async getById(id) { ... }
 *   // ... other methods
 * }
 * ```
 */

import type { PaginationParams, ApiResponse } from '../../types'

export interface IRepository<T, TCreate = Partial<T>, TUpdate = Partial<T>> {
  /**
   * Get all items with optional pagination and filtering.
   * 
   * @param params - Optional parameters for pagination and filtering
   * @returns Promise resolving to paginated results with metadata
   */
  getAll(params?: PaginationParams & Record<string, unknown>): Promise<ApiResponse<{
    data: T[]
    total: number
    page: number
    pageSize: number
  }>>

  /**
   * Get a single item by its unique identifier.
   * 
   * @param id - The unique identifier of the item to retrieve
   * @returns Promise resolving to the item if found
   */
  getById(id: string): Promise<ApiResponse<T>>

  /**
   * Create a new item in the repository.
   * 
   * @param data - The data for creating the new item
   * @returns Promise resolving to the created item
   */
  create(data: TCreate): Promise<ApiResponse<T>>

  /**
   * Update an existing item in the repository.
   * 
   * @param id - The unique identifier of the item to update
   * @param data - The partial data to update
   * @returns Promise resolving to the updated item
   */
  update(id: string, data: TUpdate): Promise<ApiResponse<T>>

  /**
   * Delete an item from the repository.
   * 
   * @param id - The unique identifier of the item to delete
   * @returns Promise resolving to success status
   */
  delete(id: string): Promise<ApiResponse<void>>

  /**
   * Check if an item exists in the repository.
   * 
   * @param id - The unique identifier to check for existence
   * @returns Promise resolving to true if the item exists
   */
  exists(id: string): Promise<boolean>

  /**
   * Get the total count of items in the repository.
   * 
   * @param filters - Optional filters to apply when counting
   * @returns Promise resolving to the total count of items
   */
  count(filters?: Record<string, unknown>): Promise<number>
}