// Storage repository interface for localStorage/sessionStorage operations
// Handles browser storage with type safety

export interface IStorageRepository<T> {
  /**
   * Save data to storage
   */
  save(key: string, data: T): Promise<void>

  /**
   * Load data from storage
   */
  load(key: string): Promise<T | null>

  /**
   * Remove data from storage
   */
  remove(key: string): Promise<void>

  /**
   * Clear all data from storage
   */
  clear(): Promise<void>

  /**
   * Check if key exists in storage
   */
  exists(key: string): Promise<boolean>

  /**
   * Get all keys from storage
   */
  getAllKeys(): Promise<string[]>
}