// Shared repository exports
// Base classes and interfaces for repository pattern implementation

// Interfaces
export type { IRepository } from './interfaces/IRepository'
export type { IStorageRepository } from './interfaces/IStorageRepository'

// Base Classes
export { BaseApiRepository } from './base/BaseApiRepository'
export { BaseStorageRepository } from './base/BaseStorageRepository'
export { BaseAuthRepository, type AuthResponse, type SignUpData } from './base/BaseAuthRepository'

// Database Provider
export { DatabaseProvider } from './DatabaseProvider'

// Repository Manager
export { RepositoryManager, type RepositoryConfig } from './RepositoryManager'