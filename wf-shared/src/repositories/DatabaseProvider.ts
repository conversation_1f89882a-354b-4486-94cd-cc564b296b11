// Database provider for repository pattern
// Manages default database client instances

import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '../types'

export class DatabaseProvider {
  private static instance: DatabaseProvider
  private defaultClient?: SupabaseClient<Database>

  private constructor() {}

  static getInstance(): DatabaseProvider {
    if (!DatabaseProvider.instance) {
      DatabaseProvider.instance = new DatabaseProvider()
    }
    return DatabaseProvider.instance
  }

  /**
   * Set the default database client
   */
  setDefaultClient(client: SupabaseClient<Database>): void {
    this.defaultClient = client
  }

  /**
   * Get the default database client
   */
  getDefaultClient(): SupabaseClient<Database> {
    if (!this.defaultClient) {
      throw new Error('Default database client not initialized. Call setDefaultClient() first.')
    }
    return this.defaultClient
  }

  /**
   * Check if default client is set
   */
  hasDefaultClient(): boolean {
    return !!this.defaultClient
  }

  /**
   * Clear the default client (useful for testing)
   */
  clearDefaultClient(): void {
    this.defaultClient = undefined
  }
}