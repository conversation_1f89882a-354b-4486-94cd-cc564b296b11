/**
 * Base service class with all common functionality
 * 
 * This module provides a ready-to-use base service class that combines all mixins
 * for maximum functionality. Use this as the foundation for your service classes
 * when you need comprehensive service capabilities.
 * 
 * Features included:
 * - Repository call handling with error management
 * - Structured logging with context support
 * - Input validation and sanitization
 * - In-memory caching with TTL support
 */

import {
  WithRepositoryCalls,
  WithLogging,
  WithValidation,
  WithCaching,
  RepositoryCallable
} from './ServiceMixins';

/**
 * Constructor type for mixins
 */
type Constructor<T = Record<string, unknown>> = new (...args: unknown[]) => T;

/**
 * Combined mixin with all common service functionality
 * 
 * This function combines all available mixins into a single mixin that provides:
 * - Repository calls (WithRepositoryCalls)
 * - Logging (WithLogging)
 * - Validation (WithValidation)
 * - Caching (WithCaching)
 * 
 * @param Base - Base class to extend
 * @returns Extended class with all mixin functionality
 * 
 * @example
 * ```typescript
 * class MyService extends WithAllServiceMixins(class implements RepositoryCallable {}) {
 *   static async getUser(id: string) {
 *     const service = new MyService();
 *     
 *     // All mixin functionality is available
 *     service.logInfo('Fetching user');
 *     const validation = service.validateRequired({ id }, ['id']);
 *     if (validation.error) return validation;
 *     
 *     const cached = service.getCached(`user_${id}`);
 *     if (cached) return { data: cached, error: null };
 *     
 *     const result = await service.callRepository(...);
 *     if (result.data) service.setCached(`user_${id}`, result.data);
 *     
 *     return result;
 *   }
 * }
 * ```
 */
export function WithAllServiceMixins<TBase extends Constructor<RepositoryCallable>>(Base: TBase) {
  return WithCaching(WithValidation(WithLogging(WithRepositoryCalls(Base))));
}

/**
 * Base service class with all mixins applied
 * 
 * This is the recommended base class for most service implementations.
 * It provides all common service functionality out of the box:
 * 
 * **Repository Calls:**
 * - `callRepository()` - Make single repository calls with error handling
 * - `callRepositoriesParallel()` - Make multiple repository calls in parallel
 * - `hasRepositoryErrors()` - Check if any repository calls failed
 * - `getFirstRepositoryError()` - Get first error from repository results
 * 
 * **Logging:**
 * - `logInfo()` - Log informational messages
 * - `logError()` - Log error messages with optional error object
 * - `logWarning()` - Log warning messages
 * - `logDebug()` - Log debug messages (development only)
 * 
 * **Validation:**
 * - `validateRequired()` - Validate required parameters
 * - `validateEmail()` - Validate email format
 * - `validateId()` - Validate UUID format
 * - `sanitizeString()` - Sanitize string input
 * 
 * **Caching:**
 * - `getCached()` - Get cached data if available and not expired
 * - `setCached()` - Set data in cache with TTL
 * - `clearCached()` - Clear specific cache entry
 * - `clearAllCache()` - Clear all cached data
 * - `getCacheStats()` - Get cache statistics
 * - `cleanupExpiredCache()` - Clean up expired cache entries
 * 
 * @example
 * ```typescript
 * export class UserService extends BaseService {
 *   static async getUser(id: string) {
 *     const service = new UserService();
 *     
 *     // Validate input
 *     const validation = service.validateRequired({ id }, ['id']);
 *     if (validation.error) return validation;
 *     
 *     if (!service.validateId(id)) {
 *       return { data: null, error: 'Invalid ID format' };
 *     }
 *     
 *     // Log operation
 *     service.logInfo(`Fetching user: ${id}`, 'UserService');
 *     
 *     // Check cache
 *     const cacheKey = `user_${id}`;
 *     const cached = service.getCached(cacheKey);
 *     if (cached) {
 *       service.logDebug('Returning cached user data', 'UserService');
 *       return { data: cached, error: null };
 *     }
 *     
 *     // Make repository call
 *     const result = await service.callRepository(
 *       () => repositories.userApi.getById(id),
 *       'User fetch error',
 *       'Failed to fetch user'
 *     );
 *     
 *     // Cache successful result
 *     if (result.data) {
 *       service.setCached(cacheKey, result.data, 300000); // 5 minutes
 *       service.logInfo(`User fetched and cached: ${id}`, 'UserService');
 *     } else {
 *       service.logError('Failed to fetch user', result.error, 'UserService');
 *     }
 *     
 *     return result;
 *   }
 * 
 *   static async getUserStats(userId: string) {
 *     const service = new UserService();
 *     
 *     // Make parallel repository calls
 *     const results = await service.callRepositoriesParallel([
 *       {
 *         call: () => repositories.userApi.getById(userId),
 *         errorContext: 'User fetch error',
 *         defaultErrorMessage: 'Failed to fetch user'
 *       },
 *       {
 *         call: () => repositories.userApi.getCount(),
 *         errorContext: 'User count error',
 *         defaultErrorMessage: 'Failed to get user count'
 *       }
 *     ]);
 *     
 *     // Check for errors
 *     if (service.hasRepositoryErrors(results)) {
 *       const firstError = service.getFirstRepositoryError(results);
 *       service.logError('Failed to fetch user stats', firstError, 'UserService');
 *       return { data: null, error: firstError };
 *     }
 *     
 *     const [userResult, countResult] = results;
 *     return {
 *       data: {
 *         user: userResult.data,
 *         totalUsers: countResult.data
 *       },
 *       error: null
 *     };
 *   }
 * }
 * ```
 */
export class BaseService extends WithAllServiceMixins(class implements RepositoryCallable {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}) {
  constructor() {
    super();
  }
}

/**
 * Type alias for the base service class
 * Use this when you need to reference the BaseService type
 */
export type BaseServiceType = BaseService;

/**
 * Interface that describes all the methods available on BaseService
 * Useful for type checking and documentation
 */
export interface IBaseService extends RepositoryCallable {
  // Repository call methods
  callRepository<T>(
    repositoryCall: () => Promise<T>,
    errorContext: string,
    defaultErrorMessage: string,
    logError?: boolean
  ): Promise<T>;

  callRepositoriesParallel<T extends readonly unknown[]>(calls: T): Promise<T>;
  hasRepositoryErrors<T extends readonly unknown[]>(results: T): boolean;
  getFirstRepositoryError<T extends readonly unknown[]>(results: T): string | null;
  
  // Logging methods
  logInfo(message: string, context?: string): void;
  logError(message: string, error?: unknown, context?: string): void;
  logWarning(message: string, context?: string): void;
  logDebug(message: string, context?: string): void;
  
  // Validation methods
  validateRequired(params: Record<string, unknown>, requiredFields: string[]): { data: null; error: string | null; };
  validateEmail(email: string): boolean;
  validateId(id: string): boolean;
  sanitizeString(input: string): string;
  
  // Caching methods
  getCached<T>(key: string): T | null;
  setCached<T>(key: string, data: T, ttlMs?: number): void;
  clearCached(key: string): void;
  clearAllCache(): void;
  getCacheStats(): { size: number; expired: number };
  cleanupExpiredCache(): number;
}

/**
 * Default export for convenience
 */
export default BaseService;
