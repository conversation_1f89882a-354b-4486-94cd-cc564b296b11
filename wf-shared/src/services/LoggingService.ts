/**
 * Centralized Logging Service for WordFormation Application
 * Provides structured logging with production monitoring integration
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,  
  ERROR = 3
}

export interface LogContext {
  component?: string;
  userId?: string;
  operation?: string;
  metadata?: Record<string, unknown>;
}

export class LoggingService {
  private static instance: LoggingService;
  private logLevel: LogLevel;
  
  constructor(logLevel: LogLevel = LogLevel.INFO) {
    this.logLevel = logLevel;
  }
  
  static getInstance(): LoggingService {
    if (!LoggingService.instance) {
      LoggingService.instance = new LoggingService();
    }
    return LoggingService.instance;
  }
  
  info(message: string, context?: LogContext): void {
    this.log(LogLevel.INFO, message, context);
  }
  
  error(message: string, error?: unknown, context?: LogContext): void {
    this.log(LogLevel.ERROR, message, { ...context, error });
  }
  
  warn(message: string, context?: LogContext): void {
    this.log(LogLevel.WARN, message, context);
  }
  
  debug(message: string, context?: LogContext): void {
    this.log(LogLevel.DEBUG, message, context);
  }
  
  private log(level: LogLevel, message: string, context?: LogContext): void {
    if (level < this.logLevel) return;
    
    const timestamp = new Date().toISOString();
    const logData = {
      timestamp,
      level: LogLevel[level],
      message,
      ...context
    };
    
    // Development: console output
    if (import.meta.env.DEV) {
      const consoleMethod = level === LogLevel.ERROR ? 'error' : 
                           level === LogLevel.WARN ? 'warn' : 'log';
      console[consoleMethod](`[${logData.level}] ${logData.message}`, logData);
    }
    
    // Production: send to monitoring service (Sentry)
    if (import.meta.env.PROD && level >= LogLevel.WARN) {
      // Integration with Sentry or other monitoring
      this.sendToMonitoring(logData);
    }
  }
  
  private sendToMonitoring(logData: any): void {
    // Sentry integration here
    // For now, structured console output in production
    console.warn('[MONITORING]', JSON.stringify(logData));
  }
}

export const logger = LoggingService.getInstance();