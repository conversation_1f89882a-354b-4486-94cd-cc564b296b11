/**
 * Service helper utilities for consistent error handling and repository calls
 * 
 * This module provides standardized helper functions for service layers to:
 * - Handle repository call results consistently
 * - Convert between repository and service response formats
 * - Validate parameters and sanitize errors
 * - Create standardized responses
 */

import { ErrorLogger } from '../utils/errorHandling';

/**
 * Standard repository response format
 * Used by repository layer to return results
 */
export interface RepositoryResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
  };
}

/**
 * Standard service response format
 * Used by service layer to return results to consumers
 */
export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
}

/**
 * Helper function to handle repository call results with consistent error handling
 * Converts repository response format to service response format
 * 
 * @param repositoryCall - Function that returns a repository response
 * @param errorContext - Context string for error logging
 * @param defaultErrorMessage - Default error message if none provided
 * @param logError - Whether to log errors to console (default: true)
 * @returns Promise resolving to standardized service response
 * 
 * @example
 * ```typescript
 * const result = await handleRepositoryCall(
 *   () => repositories.userApi.getById(id),
 *   'User fetch error',
 *   'Failed to fetch user'
 * );
 * 
 * if (result.data) {
 *   // Handle success
 * } else {
 *   // Handle error: result.error
 * }
 * ```
 */
export async function handleRepositoryCall<T>(
  repositoryCall: () => Promise<RepositoryResponse<T>>,
  errorContext: string,
  defaultErrorMessage: string,
  logError = true
): Promise<ServiceResponse<T>> {
  try {
    const result = await repositoryCall();
    
    if (result.success && result.data !== undefined) {
      return {
        data: result.data,
        error: null
      };
    } else {
      if (logError && result.error) {
        ErrorLogger.log(
          new Error(result.error.message), // Log the specific, detailed error
          {
            operation: errorContext,
            metadata: {
              errorCode: result.error.code,
              defaultErrorMessage,
              repositoryError: true
            }
          },
          'medium'
        );
      }
      // Return the generic, safe error message to the caller
      return {
        data: null,
        error: defaultErrorMessage
      };
    }
  } catch (error) {
    if (logError) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error(String(error)),
        {
          operation: errorContext,
          metadata: {
            defaultErrorMessage,
            repositoryCallException: true
          }
        },
        'high'
      );
    }
    // Return the generic, safe error message to the caller
    return {
      data: null,
      error: defaultErrorMessage
    };
  }
}

/**
 * Helper function for repository calls that only return success/error (no data)
 * Useful for operations like delete, update, etc.
 * 
 * @param repositoryCall - Function that returns success/error only
 * @param errorContext - Context string for error logging
 * @param defaultErrorMessage - Default error message if none provided
 * @param logError - Whether to log errors to console (default: true)
 * @returns Promise resolving to service response with null data
 * 
 * @example
 * ```typescript
 * const result = await handleRepositoryCallSuccess(
 *   () => repositories.userApi.delete(id),
 *   'User delete error',
 *   'Failed to delete user'
 * );
 * 
 * if (!result.error) {
 *   // Success
 * } else {
 *   // Handle error: result.error
 * }
 * ```
 */
export async function handleRepositoryCallSuccess(
  repositoryCall: () => Promise<{ success: boolean; error?: { message: string; code?: string } }>,
  errorContext: string,
  defaultErrorMessage: string,
  logError = true
): Promise<ServiceResponse<null>> {
  try {
    const result = await repositoryCall();
    
    if (result.success) {
      return {
        data: null,
        error: null
      };
    } else {
      if (logError && result.error) {
        ErrorLogger.log(
          new Error(result.error.message), // Log the specific, detailed error
          {
            operation: errorContext,
            metadata: {
              errorCode: result.error.code,
              defaultErrorMessage,
              repositorySuccessError: true
            }
          },
          'medium'
        );
      }
      // Return the generic, safe error message to the caller
      return {
        data: null,
        error: defaultErrorMessage
      };
    }
  } catch (error) {
    if (logError) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error(String(error)),
        {
          operation: errorContext,
          metadata: {
            defaultErrorMessage,
            repositoryCallException: true
          }
        },
        'high'
      );
    }
    // Return the generic, safe error message to the caller
    return {
      data: null,
      error: defaultErrorMessage
    };
  }
}

/**
 * Helper function to create a standardized error response
 * 
 * @param errorMessage - The error message
 * @param errorContext - Optional context for logging
 * @returns Standardized error response
 */
export function createErrorResponse<T>(
  errorMessage: string,
  errorContext?: string
): ServiceResponse<T> {
  if (errorContext) {
    // Log error: errorMessage - suppressed for production
  }
  return {
    data: null,
    error: errorMessage
  };
}

/**
 * Helper function to create a standardized success response
 * 
 * @param data - The success data
 * @returns Standardized success response
 */
export function createSuccessResponse<T>(data: T): ServiceResponse<T> {
  return {
    data,
    error: null
  };
}

/**
 * Helper function to validate required parameters
 * 
 * @param params - Object containing parameters to validate
 * @param requiredFields - Array of required field names
 * @returns Error message if validation fails, null if success
 * 
 * @example
 * ```typescript
 * const error = validateRequiredParams(
 *   { email: '<EMAIL>', name: '' },
 *   ['email', 'name']
 * );
 * 
 * if (error) {
 *   return createErrorResponse(error);
 * }
 * ```
 */
export function validateRequiredParams(
  params: Record<string, unknown>,
  requiredFields: string[]
): string | null {
  for (const field of requiredFields) {
    if (params[field] === undefined || params[field] === null || params[field] === '') {
      return `Missing required parameter: ${field}`;
    }
  }
  return null;
}

/**
 * Helper function to sanitize error messages for user display
 * Ensures error messages are safe and user-friendly
 * 
 * @param error - Error object, string, or any value
 * @returns Sanitized error message string
 */
export function sanitizeErrorMessage(error: unknown): string {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  if ((error as any)?.message) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (error as any).message;
  }
  
  return 'An unexpected error occurred';
}

/**
 * Helper function to check if a service response indicates an error
 * 
 * @param response - Service response to check
 * @returns True if response contains an error
 */
export function isServiceError<T>(response: ServiceResponse<T>): boolean {
  return response.error !== null;
}

/**
 * Helper function to check if a service response indicates success
 * 
 * @param response - Service response to check
 * @returns True if response is successful (has data and no error)
 */
export function isServiceSuccess<T>(response: ServiceResponse<T>): boolean {
  return response.error === null && response.data !== null;
}
