/**
 * TypeScript mixins for common service functionality
 * 
 * This module provides reusable functionality that can be mixed into service classes:
 * - Repository call handling with error management
 * - Structured logging with context support
 * - Input validation and sanitization
 * - In-memory caching with TTL support
 * 
 * Mixins allow you to compose functionality from multiple sources into a single class,
 * promoting code reuse and maintaining clean separation of concerns.
 */

import { handleRepositoryCall, ServiceResponse, RepositoryResponse } from './ServiceHelpers';
import { GlobalCacheManager } from './CacheManager';

/**
 * Constructor type for mixins
 * Represents any class constructor that can be extended
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Constructor<T = Record<string, unknown>> = new (...args: any[]) => T;

/**
 * Base interface for classes that can use repository mixins
 * Marker interface - no required methods, just indicates compatibility
 */
export type RepositoryCallable = Record<string, unknown>;

/**
 * Mixin that provides repository call functionality
 * Adds methods for making repository calls with consistent error handling
 * 
 * @param Base - Base class to extend
 * @returns Extended class with repository call methods
 * 
 * @example
 * ```typescript
 * class MyService extends WithRepositoryCalls(class implements RepositoryCallable {}) {
 *   static async getUser(id: string) {
 *     const service = new MyService();
 *     return service.callRepository(
 *       () => repositories.userApi.getById(id),
 *       'User fetch error',
 *       'Failed to fetch user'
 *     );
 *   }
 * }
 * ```
 */
export function WithRepositoryCalls<TBase extends Constructor<RepositoryCallable>>(Base: TBase) {
  return class extends Base {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    constructor(...args: any[]) {
      super(...args);
    }
    /**
     * Make a repository call with error handling
     * 
     * @param repositoryCall - Function that returns a repository response
     * @param errorContext - Context string for error logging
     * @param defaultErrorMessage - Default error message if none provided
     * @param logError - Whether to log errors to console (default: true)
     * @returns Promise resolving to standardized service response
     */
    public async callRepository<T>(
      repositoryCall: () => Promise<RepositoryResponse<T>>,
      errorContext: string,
      defaultErrorMessage: string,
      logError = true
    ): Promise<ServiceResponse<T>> {
      return handleRepositoryCall(repositoryCall, errorContext, defaultErrorMessage, logError);
    }

    /**
     * Make multiple repository calls in parallel
     * Useful for fetching related data simultaneously
     * 
     * @param calls - Array of repository call configurations
     * @returns Promise resolving to array of service responses
     * 
     * @example
     * ```typescript
     * const results = await service.callRepositoriesParallel([
     *   {
     *     call: () => repositories.userApi.getById(id),
     *     errorContext: 'User fetch error',
     *     defaultErrorMessage: 'Failed to fetch user'
     *   },
     *   {
     *     call: () => repositories.userApi.getCount(),
     *     errorContext: 'User count error',
     *     defaultErrorMessage: 'Failed to get user count'
     *   }
     * ]);
     * ```
     */
    public async callRepositoriesParallel<T extends readonly unknown[]>(
      calls: {
        [K in keyof T]: {
          call: () => Promise<RepositoryResponse<T[K]>>;
          errorContext: string;
          defaultErrorMessage: string;
        }
      }
    ): Promise<{
      [K in keyof T]: ServiceResponse<T[K]>
    }> {
      const promises = calls.map(({ call, errorContext, defaultErrorMessage }) =>
        this.callRepository(call, errorContext, defaultErrorMessage)
      );
      
      const results = await Promise.all(promises);
      return results as { [K in keyof T]: ServiceResponse<T[K]> };
    }

    /**
     * Check if any repository calls failed
     * 
     * @param results - Array of service responses to check
     * @returns True if any response contains an error
     */
    public hasRepositoryErrors<T extends readonly ServiceResponse<unknown>[]>(
      results: T
    ): boolean {
      return results.some(result => result.error !== null);
    }

    /**
     * Get first error from repository results
     * 
     * @param results - Array of service responses
     * @returns First error message found, or null if no errors
     */
    public getFirstRepositoryError<T extends readonly ServiceResponse<unknown>[]>(
      results: T
    ): string | null {
      const errorResult = results.find(result => result.error !== null);
      return errorResult?.error || null;
    }
  };
}

/**
 * Mixin that provides logging functionality
 * Adds structured logging methods with context support
 * 
 * @param Base - Base class to extend
 * @returns Extended class with logging methods
 * 
 * @example
 * ```typescript
 * class MyService extends WithLogging(class {}) {
 *   static async doSomething() {
 *     const service = new MyService();
 *     service.logInfo('Starting operation', 'MyService');
 *     // ... do work ...
 *     service.logInfo('Operation completed', 'MyService');
 *   }
 * }
 * ```
 */
export function WithLogging<TBase extends Constructor>(Base: TBase) {
  return class extends Base {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    constructor(...args: any[]) {
      super(...args);
    }
    /**
     * Log informational messages
     * 
     * @param message - Message to log
     * @param context - Optional context for the log message
     */
    public logInfo(_message: string, _context?: string): void {
      if (_context) {
        // Log info: [context] message - suppressed for production
      } else {
        // Log info: message - suppressed for production
      }
    }

    /**
     * Log error messages with optional error object
     * 
     * @param message - Error message to log
     * @param error - Optional error object for additional details
     * @param context - Optional context for the log message
     */
    public logError(_message: string, _error?: unknown, _context?: string): void {
      if (_context) {
        // Log error: [context] message, error - suppressed for production
      } else {
        // Log error: message, error - suppressed for production
      }
    }

    /**
     * Log warning messages
     * 
     * @param message - Warning message to log
     * @param context - Optional context for the log message
     */
    public logWarning(_message: string, _context?: string): void {
      if (_context) {
        // Log warning: [context] message - suppressed for production
      } else {
        // Log warning: message - suppressed for production
      }
    }

    /**
     * Log debug messages (only in development environment)
     * 
     * @param message - Debug message to log
     * @param context - Optional context for the log message
     */
    public logDebug(_message: string, _context?: string): void {
      if (process.env.NODE_ENV === 'development') {
        if (_context) {
          // Log debug: [context] message - suppressed for production
        } else {
          // Log debug: message - suppressed for production
        }
      }
    }
  };
}

/**
 * Mixin that provides validation functionality
 * Adds input validation and sanitization methods
 * 
 * @param Base - Base class to extend
 * @returns Extended class with validation methods
 */
export function WithValidation<TBase extends Constructor>(Base: TBase) {
  return class extends Base {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    constructor(...args: any[]) {
      super(...args);
    }
    /**
     * Validate required parameters
     * 
     * @param params - Object containing parameters to validate
     * @param requiredFields - Array of required field names
     * @returns Service response with error if validation fails, success if valid
     */
    public validateRequired(
      params: Record<string, unknown>,
      requiredFields: string[]
    ): ServiceResponse<null> {
      for (const field of requiredFields) {
        if (params[field] === undefined || params[field] === null || params[field] === '') {
          return {
            data: null,
            error: `Missing required parameter: ${field}`
          };
        }
      }
      return { data: null, error: null };
    }

    /**
     * Validate email format using regex
     * 
     * @param email - Email string to validate
     * @returns True if email format is valid
     */
    public validateEmail(email: string): boolean {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    }

    /**
     * Validate ID format (UUID)
     * 
     * @param id - ID string to validate
     * @returns True if ID format is valid UUID
     */
    public validateId(id: string): boolean {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      return uuidRegex.test(id);
    }

    /**
     * Sanitize string input by trimming and removing dangerous characters
     *
     * @param input - String to sanitize
     * @returns Sanitized string
     */
    public sanitizeString(input: string): string {
      return input.trim().replace(/[<>]/g, '');
    }
  };
}

/**
 * Mixin that provides caching functionality
 * Adds in-memory caching with TTL (Time To Live) support
 *
 * @param Base - Base class to extend
 * @returns Extended class with caching methods
 *
 * @example
 * ```typescript
 * class MyService extends WithCaching(class {}) {
 *   static async getUser(id: string) {
 *     const service = new MyService();
 *
 *     // Check cache first
 *     const cached = service.getCached(`user_${id}`);
 *     if (cached) return { data: cached, error: null };
 *
 *     // Fetch from repository
 *     const result = await fetchUser(id);
 *
 *     // Cache the result
 *     if (result.data) {
 *       service.setCached(`user_${id}`, result.data, 300000); // 5 minutes
 *     }
 *
 *     return result;
 *   }
 * }
 * ```
 */
export function WithCaching<TBase extends Constructor>(Base: TBase) {
  return class extends Base {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    constructor(...args: any[]) {
      super(...args);
    }
    
    /**
     * Get the global cache manager instance
     */
    public get cacheManager() {
      return GlobalCacheManager.getInstance();
    }
    
    /**
     * Generate a namespaced cache key for this service
     */
    public getNamespacedKey(key: string): string {
      const serviceName = this.constructor.name;
      return `${serviceName}:${key}`;
    }

    /**
     * Get cached data if available and not expired
     *
     * @param key - Cache key to retrieve
     * @returns Cached data if available and not expired, null otherwise
     */
    public getCached<T>(key: string): T | null {
      const namespacedKey = this.getNamespacedKey(key);
      return this.cacheManager.get<T>(namespacedKey);
    }

    /**
     * Set data in cache with TTL
     *
     * @param key - Cache key to store under
     * @param data - Data to cache
     * @param ttlMs - Time to live in milliseconds (default: 5 minutes)
     */
    public setCached<T>(key: string, data: T, ttlMs = 300000): void {
      const namespacedKey = this.getNamespacedKey(key);
      this.cacheManager.set(namespacedKey, data, { ttl: ttlMs });
    }

    /**
     * Clear specific cache entry
     *
     * @param key - Cache key to clear
     */
    public clearCached(key: string): void {
      const namespacedKey = this.getNamespacedKey(key);
      this.cacheManager.delete(namespacedKey);
    }

    /**
     * Clear all cached data for this service
     */
    public clearAllCache(): void {
      const serviceName = this.constructor.name;
      this.cacheManager.invalidateByPattern({ prefix: `${serviceName}:` });
    }

    /**
     * Get cache statistics for this service
     *
     * @returns Object with global cache statistics
     */
    public getCacheStats() {
      return this.cacheManager.getStats();
    }

    /**
     * Clean up expired cache entries globally
     *
     * @returns Number of entries removed
     */
    public cleanupExpiredCache(): number {
      return this.cacheManager.cleanup();
    }
    
    /**
     * Subscribe to cache events for this service's keys
     * 
     * @param events - Events to listen for
     * @param callback - Callback function to execute
     * @returns Unsubscribe function
     */
    public subscribeToCacheEvents(
      events: Array<'set' | 'get' | 'delete' | 'expire' | 'evict' | 'clear'>,
      callback: (key: string) => void
    ): () => void {
      const serviceName = this.constructor.name;
      return this.cacheManager.subscribe(
        { prefix: `${serviceName}:` },
        events,
        callback
      );
    }
  };
}
