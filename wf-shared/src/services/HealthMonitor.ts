/**
 * Health Monitor - Comprehensive system health monitoring
 * Part of Technical Debt Solution - Phase 3, Task 3.1
 * 
 * Provides real-time health monitoring for all system components including
 * database connections, authentication services, cache systems, and circuit breakers.
 */

import { generateUUID } from '../utils/ids';
import { <PERSON>rror<PERSON>ogger } from '../utils/errorHandling';
import { GlobalCacheManager } from './CacheManager';
import { CircuitBreakerFactory } from './CircuitBreaker';
import { DatabaseProvider } from '../repositories/DatabaseProvider';
import {
  DEFAULT_HEALTH_CONFIG,
  type HealthStatus,
  type ServiceHealth,
  type HealthCheckConfig,
  type HealthMonitorConfig,
  type HealthHistoryEntry,
  type HealthStats,
  type HealthEvent,
  type HealthEventType,
  type HealthEventCallback
} from '../types/health';

/**
 * Comprehensive health monitoring system for all application components
 * 
 * Features:
 * - Real-time health status monitoring
 * - Configurable health checks for different services
 * - Historical health data tracking
 * - Event-driven health notifications
 * - Integration with circuit breakers and caches
 * - Automated alerting and recovery detection
 * 
 * @example
 * ```typescript
 * const healthMonitor = HealthMonitor.getInstance();
 * 
 * // Get overall system health
 * const health = await healthMonitor.getOverallHealth();
 * console.log('System status:', health.overall);
 * 
 * // Subscribe to health events
 * healthMonitor.on('SYSTEM_UNHEALTHY', (event) => {
 *   console.error('System unhealthy:', event.details);
 *   // Trigger alerts, notifications, etc.
 * });
 * 
 * // Start continuous monitoring
 * await healthMonitor.startMonitoring();
 * ```
 */
export class HealthMonitor {
  private static instance: HealthMonitor;
  private config: HealthMonitorConfig;
  private healthHistory = new Map<string, HealthHistoryEntry[]>();
  private currentHealth = new Map<string, ServiceHealth>();
  private eventListeners = new Map<HealthEventType, Set<HealthEventCallback>>();
  private monitoringTimers = new Map<string, NodeJS.Timeout>();
  private isMonitoring = false;
  private startTime = Date.now();

  private constructor(config: Partial<HealthMonitorConfig> = {}) {
    this.config = { ...DEFAULT_HEALTH_CONFIG, ...config };
    this.initializeServices();
  }

  /**
   * Get the singleton instance of the health monitor
   * 
   * @param config - Optional configuration override (only used on first call)
   * @returns The global health monitor instance
   */
  static getInstance(config?: Partial<HealthMonitorConfig>): HealthMonitor {
    if (!HealthMonitor.instance) {
      HealthMonitor.instance = new HealthMonitor(config);
    }
    return HealthMonitor.instance;
  }

  /**
   * Get overall system health status
   * 
   * @returns Promise resolving to complete system health status
   */
  async getOverallHealth(): Promise<HealthStatus> {
    const serviceHealthPromises = Object.entries(this.config.services).map(
      async ([serviceName, serviceConfig]) => {
        const health = await this.checkServiceHealth(serviceName, serviceConfig);
        return [serviceName, health] as [string, ServiceHealth];
      }
    );

    const serviceHealthResults = await Promise.all(serviceHealthPromises);
    const services = Object.fromEntries(serviceHealthResults) as HealthStatus['services'];

    // Determine overall system health
    const serviceStatuses = Object.values(services).map(s => s.status);
    const overall = this.calculateOverallHealth(serviceStatuses);

    const healthStatus: HealthStatus = {
      overall,
      services,
      timestamp: Date.now(),
      uptime: Date.now() - this.startTime,
      version: process.env.npm_package_version || '1.0.0'
    };

    // Emit health events if status changed
    this.checkForHealthEvents(healthStatus);

    return healthStatus;
  }

  /**
   * Get health status for a specific service
   * 
   * @param serviceName - Name of the service to check
   * @returns Promise resolving to service health status
   */
  async getServiceHealth(serviceName: string): Promise<ServiceHealth | null> {
    const serviceConfig = this.config.services[serviceName];
    if (!serviceConfig) {
      return null;
    }

    return await this.checkServiceHealth(serviceName, serviceConfig);
  }

  /**
   * Get health history for a service within a time range
   * 
   * @param serviceName - Service name to get history for
   * @param timeRange - Time range in milliseconds (from now backwards)
   * @returns Array of health history entries
   */
  getHealthHistory(serviceName: string, timeRange: number): HealthHistoryEntry[] {
    const history = this.healthHistory.get(serviceName) || [];
    const cutoffTime = Date.now() - timeRange;
    
    return history.filter(entry => entry.timestamp >= cutoffTime);
  }

  /**
   * Get health statistics for a service
   * 
   * @param serviceName - Service name to get statistics for
   * @param timeRange - Time range in milliseconds for statistics calculation
   * @returns Health statistics object
   */
  getHealthStats(serviceName: string, timeRange: number = 24 * 60 * 60 * 1000): HealthStats {
    const history = this.getHealthHistory(serviceName, timeRange);
    const current = this.currentHealth.get(serviceName);

    if (history.length === 0) {
      return {
        serviceName,
        uptimePercentage: current?.status === 'up' ? 100 : 0,
        averageResponseTime: current?.responseTime || 0,
        totalChecks: 0,
        failedChecks: 0,
        currentStatusDuration: 0,
        trend: 'stable'
      };
    }

    const totalChecks = history.length;
    const successfulChecks = history.filter(h => h.health.status === 'up').length;
    const failedChecks = totalChecks - successfulChecks;
    const uptimePercentage = (successfulChecks / totalChecks) * 100;

    const responseTimes = history
      .map(h => h.health.responseTime)
      .filter((rt): rt is number => rt !== undefined);
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0;

    const lastFailure = history
      .reverse()
      .find(h => h.health.status !== 'up');
    const lastFailureTime = lastFailure?.timestamp;

    // Calculate current status duration
    const currentStatus = current?.status || 'down';
    const statusChangeIndex = history.findIndex((_, i) => 
      i === 0 || (i > 0 && history[i - 1].health.status !== currentStatus)
    );
    const currentStatusDuration = statusChangeIndex >= 0 
      ? Date.now() - history[statusChangeIndex].timestamp 
      : 0;

    // Calculate trend
    const recentHistory = history.slice(-10); // Last 10 checks
    const trend = this.calculateHealthTrend(recentHistory);

    return {
      serviceName,
      uptimePercentage,
      averageResponseTime,
      totalChecks,
      failedChecks,
      lastFailureTime,
      currentStatusDuration,
      trend
    };
  }

  /**
   * Add event listener for health events
   * 
   * @param eventType - Type of health event to listen for
   * @param callback - Callback function to execute
   * @returns Unsubscribe function
   */
  on(eventType: HealthEventType, callback: HealthEventCallback): () => void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    
    const listeners = this.eventListeners.get(eventType)!;
    listeners.add(callback);

    // Return unsubscribe function
    return () => {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(eventType);
      }
    };
  }

  /**
   * Remove all event listeners for a specific event type
   * 
   * @param eventType - Event type to clear (optional, clears all if not provided)
   */
  off(eventType?: HealthEventType): void {
    if (eventType) {
      this.eventListeners.delete(eventType);
    } else {
      this.eventListeners.clear();
    }
  }

  /**
   * Start continuous health monitoring
   * 
   * @returns Promise that resolves when monitoring is started
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;

    // Start individual service monitors
    for (const [serviceName, serviceConfig] of Object.entries(this.config.services)) {
      if (serviceConfig.enabled) {
        this.startServiceMonitoring(serviceName, serviceConfig);
      }
    }

    // Perform initial health check
    await this.getOverallHealth();

    ErrorLogger.log(
      new Error('Health monitoring started'),
      {
        operation: 'health-monitor-start',
        metadata: {
          servicesCount: Object.keys(this.config.services).length,
          globalInterval: this.config.globalInterval
        }
      },
      'low'
    );
  }

  /**
   * Stop continuous health monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;

    // Clear all monitoring timers
    for (const timer of this.monitoringTimers.values()) {
      clearInterval(timer);
    }
    this.monitoringTimers.clear();

    ErrorLogger.log(
      new Error('Health monitoring stopped'),
      {
        operation: 'health-monitor-stop',
        metadata: {
          uptime: Date.now() - this.startTime
        }
      },
      'low'
    );
  }

  /**
   * Register a custom health check for a service
   * 
   * @param serviceName - Name of the service
   * @param config - Health check configuration
   */
  registerHealthCheck(serviceName: string, config: HealthCheckConfig): void {
    this.config.services[serviceName] = config;

    if (this.isMonitoring && config.enabled) {
      this.startServiceMonitoring(serviceName, config);
    }
  }

  /**
   * Initialize default services
   */
  private initializeServices(): void {
    // Set up default health check functions
    this.config.services.database.healthCheckFn = this.checkDatabaseHealth.bind(this);
    this.config.services.auth.healthCheckFn = this.checkAuthServiceHealth.bind(this);
    this.config.services.cache.healthCheckFn = this.checkCacheHealth.bind(this);
  }

  /**
   * Check health of a specific service
   * 
   * @param serviceName - Name of the service to check
   * @param config - Service configuration
   * @returns Promise resolving to service health
   */
  private async checkServiceHealth(
    serviceName: string, 
    config: HealthCheckConfig
  ): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
      // Use custom health check function if provided
      const healthCheck = config.healthCheckFn || this.getDefaultHealthCheck(serviceName);
      
      const customHealth = await Promise.race([
        healthCheck(),
        this.createTimeoutPromise(config.timeout)
      ]);

      const responseTime = Date.now() - startTime;
      const health: ServiceHealth = {
        status: 'up',
        responseTime,
        lastCheckTime: Date.now(),
        message: 'Service is healthy',
        ...customHealth
      };

      // Store current health and history
      this.updateServiceHealth(serviceName, health, responseTime);

      return health;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      const health: ServiceHealth = {
        status: 'down',
        responseTime,
        lastCheckTime: Date.now(),
        message: error instanceof Error ? error.message : 'Service check failed',
        metadata: {
          error: error instanceof Error ? error.message : String(error)
        }
      };

      // Log health check failure
      ErrorLogger.log(
        error instanceof Error ? error : new Error(String(error)),
        {
          operation: 'health-check-failed',
          metadata: {
            serviceName,
            responseTime,
            timeout: config.timeout
          }
        },
        'medium'
      );

      // Store current health and history
      this.updateServiceHealth(serviceName, health, responseTime);

      return health;
    }
  }

  /**
   * Database health check implementation
   */
  private async checkDatabaseHealth(): Promise<Partial<ServiceHealth>> {
    try {
      const db = DatabaseProvider.getInstance().getDefaultClient();
      const startTime = Date.now();
      
      // Simple query to test database connectivity
      const { error } = await db.from('users').select('id').limit(1).single();
      
      const responseTime = Date.now() - startTime;
      
      if (error && !error.message.includes('multiple (or no) rows returned')) {
        throw new Error(`Database error: ${error.message}`);
      }

      return {
        status: 'up',
        responseTime,
        message: 'Database connection healthy'
      };
    } catch (error) {
      return {
        status: 'down',
        message: error instanceof Error ? error.message : 'Database connection failed'
      };
    }
  }

  /**
   * Authentication service health check implementation
   */
  private async checkAuthServiceHealth(): Promise<Partial<ServiceHealth>> {
    try {
      const db = DatabaseProvider.getInstance().getDefaultClient();
      const startTime = Date.now();
      
      // Test auth service by checking session
      const { error } = await db.auth.getSession();
      const responseTime = Date.now() - startTime;
      
      if (error && !error.message.includes('session_not_found')) {
        throw new Error(`Auth service error: ${error.message}`);
      }

      return {
        status: 'up',
        responseTime,
        message: 'Authentication service healthy'
      };
    } catch (error) {
      return {
        status: 'down',
        message: error instanceof Error ? error.message : 'Auth service check failed'
      };
    }
  }

  /**
   * Cache health check implementation
   */
  private async checkCacheHealth(): Promise<Partial<ServiceHealth>> {
    try {
      const cacheManager = GlobalCacheManager.getInstance();
      const testKey = `health-check-${generateUUID()}`;
      const testData = { timestamp: Date.now() };
      
      const startTime = Date.now();
      
      // Test cache write and read
      cacheManager.set(testKey, testData, { ttl: 1000 });
      const retrieved = cacheManager.get(testKey);
      cacheManager.delete(testKey);
      
      const responseTime = Date.now() - startTime;
      
      if (!retrieved || (retrieved as typeof testData).timestamp !== testData.timestamp) {
        throw new Error('Cache read/write test failed');
      }

      // Get comprehensive cache health status
      const cacheHealth = cacheManager.getHealthStatus();
      const stats = cacheManager.getStats();
      
      // Map cache health to service health status
      const serviceStatus = cacheHealth.status === 'healthy' ? 'up' : 
                           cacheHealth.status === 'degraded' ? 'degraded' : 'down';
      
      return {
        status: serviceStatus,
        responseTime,
        message: cacheHealth.status === 'healthy' ? 'Cache system healthy' : 
                `Cache ${cacheHealth.status}: ${cacheHealth.recommendations.join(', ')}`,
        metadata: {
          cacheSize: stats.size,
          hitRate: cacheHealth.hitRate,
          memoryUsage: cacheHealth.memoryUsage,
          recommendations: cacheHealth.recommendations
        }
      };
    } catch (error) {
      return {
        status: 'down',
        message: error instanceof Error ? error.message : 'Cache health check failed'
      };
    }
  }

  /**
   * Circuit breaker health check implementation
   */
  private async checkCircuitBreakerHealth(): Promise<Partial<ServiceHealth>> {
    try {
      const allStats = CircuitBreakerFactory.getAllStats();
      const circuitNames = Object.keys(allStats);
      
      if (circuitNames.length === 0) {
        return {
          status: 'up',
          message: 'No circuit breakers configured',
          metadata: { circuitBreakers: [] }
        };
      }

      let overallStatus: ServiceHealth['status'] = 'up';
      const circuitDetails: Array<{
        name: string;
        status: string;
        state: string;
        failureRate: number;
        recommendations: string[];
      }> = [];

      // Check health of all circuit breakers
      for (const name of circuitNames) {
        try {
          const instance = CircuitBreakerFactory.getInstance(name);
          const health = instance.getHealthStatus();
          
          circuitDetails.push({
            name,
            status: health.status,
            state: health.state,
            failureRate: health.failureRate,
            recommendations: health.recommendations
          });

          // Determine overall status based on worst individual status
          if (health.status === 'unhealthy' && overallStatus !== 'down') {
            overallStatus = 'down';
          } else if (health.status === 'degraded' && overallStatus === 'up') {
            overallStatus = 'degraded';
          }
        } catch (error) {
          // If we can't get health for a circuit breaker, that's concerning
          overallStatus = 'down';
          circuitDetails.push({
            name,
            status: 'error',
            state: 'unknown',
            failureRate: 0,
            recommendations: [`Error checking ${name}: ${error instanceof Error ? error.message : String(error)}`]
          });
        }
      }

      const unhealthyCircuits = circuitDetails.filter(c => c.status !== 'healthy').length;
      const totalCircuits = circuitDetails.length;

      return {
        status: overallStatus,
        message: overallStatus === 'up' 
          ? `All ${totalCircuits} circuit breakers healthy`
          : `${unhealthyCircuits}/${totalCircuits} circuit breakers need attention`,
        metadata: {
          totalCircuitBreakers: totalCircuits,
          unhealthyCircuitBreakers: unhealthyCircuits,
          circuitDetails
        }
      };

    } catch (error) {
      return {
        status: 'down',
        message: error instanceof Error ? error.message : 'Circuit breaker health check failed',
        metadata: { error: String(error) }
      };
    }
  }

  /**
   * Get default health check function for a service
   */
  private getDefaultHealthCheck(serviceName: string): () => Promise<Partial<ServiceHealth>> {
    switch (serviceName) {
      case 'database':
        return this.checkDatabaseHealth.bind(this);
      case 'auth':
        return this.checkAuthServiceHealth.bind(this);
      case 'cache':
        return this.checkCacheHealth.bind(this);
      case 'circuit-breaker':
        return this.checkCircuitBreakerHealth.bind(this);
      default:
        return async () => ({ status: 'up', message: 'Default health check passed' });
    }
  }

  /**
   * Create a timeout promise for health checks
   */
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Health check timed out after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Update service health in current status and history
   */
  private updateServiceHealth(
    serviceName: string, 
    health: ServiceHealth, 
    checkDuration: number
  ): void {
    // Update current health
    this.currentHealth.set(serviceName, health);

    // Update health history
    const history = this.healthHistory.get(serviceName) || [];
    const entry: HealthHistoryEntry = {
      timestamp: Date.now(),
      health,
      checkDuration
    };

    history.push(entry);

    // Maintain history size limits
    const maxSize = this.config.maxHistorySize;
    if (history.length > maxSize) {
      history.splice(0, history.length - maxSize);
    }

    // Remove old entries based on retention period
    const cutoffTime = Date.now() - this.config.historyRetention;
    const validEntries = history.filter(e => e.timestamp >= cutoffTime);
    
    this.healthHistory.set(serviceName, validEntries);
  }

  /**
   * Start monitoring for a specific service
   */
  private startServiceMonitoring(serviceName: string, config: HealthCheckConfig): void {
    // Clear existing timer if any
    const existingTimer = this.monitoringTimers.get(serviceName);
    if (existingTimer) {
      clearInterval(existingTimer);
    }

    // Start new monitoring timer
    const timer = setInterval(async () => {
      await this.checkServiceHealth(serviceName, config);
    }, config.interval);

    this.monitoringTimers.set(serviceName, timer);
  }

  /**
   * Calculate overall system health from individual service statuses
   */
  private calculateOverallHealth(statuses: ServiceHealth['status'][]): HealthStatus['overall'] {
    const downCount = statuses.filter(s => s === 'down').length;
    const degradedCount = statuses.filter(s => s === 'degraded').length;

    if (downCount > 0) {
      return 'unhealthy';
    }
    if (degradedCount > 0) {
      return 'degraded';
    }
    return 'healthy';
  }

  /**
   * Calculate health trend based on recent history
   */
  private calculateHealthTrend(history: HealthHistoryEntry[]): HealthStats['trend'] {
    if (history.length < 5) {
      return 'stable';
    }

    const recent = history.slice(-5);
    const older = history.slice(-10, -5);

    const recentUptime = recent.filter(h => h.health.status === 'up').length / recent.length;
    const olderUptime = older.filter(h => h.health.status === 'up').length / older.length;

    if (recentUptime > olderUptime + 0.2) {
      return 'improving';
    }
    if (recentUptime < olderUptime - 0.2) {
      return 'degrading';
    }
    return 'stable';
  }

  /**
   * Check for health events and emit notifications
   */
  private checkForHealthEvents(currentHealth: HealthStatus): void {
    // Implementation would compare with previous health status
    // and emit appropriate events - simplified for now
    
    if (currentHealth.overall === 'unhealthy') {
      this.emitHealthEvent('SYSTEM_UNHEALTHY', {
        currentStatus: 'down',
        severity: 'critical',
        details: { services: currentHealth.services }
      });
    }
  }

  /**
   * Emit health event to all registered listeners
   */
  private emitHealthEvent(
    type: HealthEventType, 
    eventData: Partial<HealthEvent>
  ): void {
    const listeners = this.eventListeners.get(type);
    if (!listeners || listeners.size === 0) {
      return;
    }

    const event: HealthEvent = {
      type,
      timestamp: Date.now(),
      currentStatus: 'up',
      severity: 'medium',
      ...eventData
    };

    for (const listener of listeners) {
      try {
        listener(event);
      } catch (error) {
        ErrorLogger.log(
          error instanceof Error ? error : new Error(String(error)),
          {
            operation: 'health-event-listener',
            metadata: { eventType: type }
          },
          'low'
        );
      }
    }
  }

  /**
   * Cleanup resources when shutting down
   */
  destroy(): void {
    this.stopMonitoring();
    this.eventListeners.clear();
    this.healthHistory.clear();
    this.currentHealth.clear();
  }
}

// Initialize cleanup on process exit
if (typeof process !== 'undefined') {
  process.on('exit', () => {
    const instance = HealthMonitor.getInstance();
    instance.destroy();
  });
}