/**
 * Global Cache Manager - Singleton cache implementation
 * Part of Technical Debt Solution - Phase 1, Task 1.2
 * 
 * Replaces instance-based caching with a shared, global cache system.
 * Provides features like pub/sub invalidation, memory management, and statistics.
 */

import { generateUUID } from '../utils/ids';
import { <PERSON>rror<PERSON>ogger } from '../utils/errorHandling';
import type {
  CacheEntry,
  CacheConfig,
  CacheStats,
  CacheInvalidationPattern,
  CacheEventCallback,
  CacheEventType,
  CacheSubscription,
  CacheSetOptions,
  CacheGetOptions,
  CacheOperationResult
} from '../types/cache';

/**
 * Default configuration for the cache manager
 */
const DEFAULT_CONFIG: CacheConfig = {
  defaultTtl: 5 * 60 * 1000, // 5 minutes
  maxSize: 10000,
  cleanupInterval: 60 * 1000, // 1 minute
  enableAutoCleanup: true,
  enableStats: true,
};

/**
 * Global cache manager implementing singleton pattern with advanced features
 * 
 * Features:
 * - Singleton pattern for shared cache across all service instances
 * - LRU eviction policy when cache reaches max size
 * - Automatic cleanup of expired entries
 * - Pub/sub pattern for cache event notifications
 * - Pattern-based invalidation (prefix, suffix, regex)
 * - Comprehensive statistics and monitoring
 * - Memory usage tracking and management
 * 
 * @example
 * ```typescript
 * const cache = GlobalCacheManager.getInstance();
 * 
 * // Basic usage
 * cache.set('user:123', userData, { ttl: 300000 });
 * const user = cache.get<UserData>('user:123');
 * 
 * // Pattern-based invalidation
 * cache.invalidateByPattern({ prefix: 'user:' });
 * 
 * // Event subscription
 * cache.subscribe({ prefix: 'session:' }, ['expire'], (key) => {
 *   console.log(`Session expired: ${key}`);
 * });
 * ```
 */
export class GlobalCacheManager {
  private static instance: GlobalCacheManager;
  private cache = new Map<string, CacheEntry>();
  private subscribers = new Map<string, CacheSubscription>();
  private accessOrder = new Map<string, number>(); // For LRU tracking
  private stats: CacheStats;
  private config: CacheConfig;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private accessCounter = 0;

  private constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.stats = this.initializeStats();
    this.setupCleanupTimer();
  }

  /**
   * Get the singleton instance of the cache manager
   * 
   * @param config - Optional configuration override (only used on first call)
   * @returns The global cache manager instance
   */
  static getInstance(config?: Partial<CacheConfig>): GlobalCacheManager {
    if (!GlobalCacheManager.instance) {
      GlobalCacheManager.instance = new GlobalCacheManager(config);
    }
    return GlobalCacheManager.instance;
  }

  /**
   * Store data in the cache with optional configuration
   * 
   * @param key - Cache key
   * @param data - Data to cache
   * @param options - Cache options (TTL, tags, etc.)
   * @returns Operation result
   */
  set<T>(key: string, data: T, options: CacheSetOptions = {}): CacheOperationResult<T> {
    try {
      const {
        ttl = this.config.defaultTtl,
        tags = [],
        overwrite = true,
        metadata = {}
      } = options;

      // Check if key exists and overwrite is disabled
      if (!overwrite && this.cache.has(key)) {
        return {
          success: false,
          error: `Key '${key}' already exists and overwrite is disabled`,
          found: true
        };
      }

      // Ensure cache size limits
      this.ensureCacheSize();

      // Create cache entry
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl,
        tags: tags.length > 0 ? tags : undefined,
        metadata: Object.keys(metadata).length > 0 ? metadata : undefined
      };

      // Store in cache and update access order
      this.cache.set(key, entry);
      this.updateAccessOrder(key);

      // Update statistics
      if (this.config.enableStats) {
        this.updateMemoryUsage();
      }

      // Notify subscribers
      this.notifySubscribers('set', key, entry);

      return {
        success: true,
        data,
        metadata: { cached: true, ttl, tags }
      };

    } catch (error) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error(String(error)),
        { operation: 'cache-set', metadata: { key, options } },
        'medium'
      );
      
      return {
        success: false,
        error: 'Failed to set cache entry'
      };
    }
  }

  /**
   * Retrieve data from the cache
   * 
   * @param key - Cache key
   * @param options - Retrieval options
   * @returns Cached data or null if not found/expired
   */
  get<T>(key: string, options: CacheGetOptions = {}): T | null {
    try {
      const {
        updateAccessTime = true,
        includeExpired = false
      } = options;

      const entry = this.cache.get(key) as CacheEntry<T> | undefined;
      
      if (!entry) {
        if (this.config.enableStats) {
          this.stats.misses++;
        }
        this.notifySubscribers('get', key);
        return null;
      }

      // Check expiration
      const now = Date.now();
      const isExpired = (now - entry.timestamp) > entry.ttl;

      if (isExpired && !includeExpired) {
        // Remove expired entry
        this.cache.delete(key);
        this.accessOrder.delete(key);
        
        if (this.config.enableStats) {
          this.stats.misses++;
          this.stats.expired++;
          this.updateMemoryUsage();
        }
        
        this.notifySubscribers('expire', key, entry);
        return null;
      }

      // Update access tracking
      if (updateAccessTime) {
        this.updateAccessOrder(key);
      }

      // Update statistics
      if (this.config.enableStats) {
        this.stats.hits++;
        this.updateTopKeys(key);
      }

      this.notifySubscribers('get', key, entry);
      return entry.data;

    } catch (error) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error(String(error)),
        { operation: 'cache-get', metadata: { key, options } },
        'low'
      );
      
      if (this.config.enableStats) {
        this.stats.misses++;
      }
      
      return null;
    }
  }

  /**
   * Check if a key exists in the cache (without affecting access order)
   * 
   * @param key - Cache key to check
   * @returns True if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    const now = Date.now();
    const isExpired = (now - entry.timestamp) > entry.ttl;
    
    if (isExpired) {
      // Clean up expired entry
      this.cache.delete(key);
      this.accessOrder.delete(key);
      if (this.config.enableStats) {
        this.stats.expired++;
        this.updateMemoryUsage();
      }
      return false;
    }

    return true;
  }

  /**
   * Remove a specific key from the cache
   * 
   * @param key - Cache key to remove
   * @returns True if key was removed, false if not found
   */
  delete(key: string): boolean {
    const entry = this.cache.get(key);
    const deleted = this.cache.delete(key);
    
    if (deleted) {
      this.accessOrder.delete(key);
      if (this.config.enableStats) {
        this.updateMemoryUsage();
      }
      this.notifySubscribers('delete', key, entry);
    }

    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const oldSize = this.cache.size;
    this.cache.clear();
    this.accessOrder.clear();
    
    if (this.config.enableStats) {
      this.stats.evicted += oldSize;
      this.updateMemoryUsage();
    }
    
    this.notifySubscribers('clear', '*');
  }

  /**
   * Invalidate cache entries matching a pattern
   * 
   * @param pattern - Pattern to match keys against
   * @returns Number of entries invalidated
   */
  invalidateByPattern(pattern: CacheInvalidationPattern): number {
    const keysToDelete: string[] = [];

    for (const key of this.cache.keys()) {
      if (this.matchesPattern(key, pattern)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.length;
  }

  /**
   * Invalidate cache entries by tags
   * 
   * @param tags - Tags to match
   * @returns Number of entries invalidated
   */
  invalidateByTags(tags: string[]): number {
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.length;
  }

  /**
   * Subscribe to cache events for specific key patterns
   * 
   * @param pattern - Pattern to match keys
   * @param events - Events to listen for
   * @param callback - Callback function to execute
   * @returns Unsubscribe function
   */
  subscribe(
    pattern: CacheInvalidationPattern,
    events: CacheEventType[],
    callback: CacheEventCallback
  ): () => void {
    const subscription: CacheSubscription = {
      id: generateUUID(),
      pattern,
      events,
      callback
    };

    this.subscribers.set(subscription.id, subscription);

    // Return unsubscribe function
    return () => {
      this.subscribers.delete(subscription.id);
    };
  }

  /**
   * Get current cache statistics
   * 
   * @returns Current cache statistics
   */
  getStats(): CacheStats {
    if (this.config.enableStats) {
      return {
        ...this.stats,
        size: this.cache.size,
        hitRate: this.stats.hits + this.stats.misses > 0 
          ? (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100 
          : 0
      };
    }

    return this.initializeStats();
  }

  /**
   * Reset cache statistics
   */
  resetStats(): void {
    this.stats = this.initializeStats();
  }

  /**
   * Manually trigger cleanup of expired entries
   * 
   * @returns Number of entries cleaned up
   */
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if ((now - entry.timestamp) > entry.ttl) {
        this.cache.delete(key);
        this.accessOrder.delete(key);
        this.notifySubscribers('expire', key, entry);
        cleaned++;
      }
    }

    if (cleaned > 0 && this.config.enableStats) {
      this.stats.expired += cleaned;
      this.updateMemoryUsage();
    }

    return cleaned;
  }

  /**
   * Initialize statistics object
   */
  private initializeStats(): CacheStats {
    return {
      size: 0,
      hits: 0,
      misses: 0,
      hitRate: 0,
      expired: 0,
      evicted: 0,
      memoryUsage: 0,
      topKeys: []
    };
  }

  /**
   * Setup automatic cleanup timer
   */
  private setupCleanupTimer(): void {
    if (this.config.enableAutoCleanup && this.config.cleanupInterval > 0) {
      this.cleanupTimer = setInterval(() => {
        this.cleanup();
      }, this.config.cleanupInterval);
    }
  }

  /**
   * Ensure cache doesn't exceed maximum size using LRU eviction
   */
  private ensureCacheSize(): void {
    if (this.cache.size >= this.config.maxSize) {
      // Find least recently used key
      const lruKey = this.findLeastRecentlyUsedKey();
      if (lruKey) {
        const entry = this.cache.get(lruKey);
        this.cache.delete(lruKey);
        this.accessOrder.delete(lruKey);
        
        if (this.config.enableStats) {
          this.stats.evicted++;
        }
        
        this.notifySubscribers('evict', lruKey, entry);
      }
    }
  }

  /**
   * Find the least recently used cache key
   */
  private findLeastRecentlyUsedKey(): string | null {
    let lruKey: string | null = null;
    let oldestAccess = Infinity;

    for (const [key, accessTime] of this.accessOrder.entries()) {
      if (accessTime < oldestAccess) {
        oldestAccess = accessTime;
        lruKey = key;
      }
    }

    return lruKey;
  }

  /**
   * Update access order for LRU tracking
   */
  private updateAccessOrder(key: string): void {
    this.accessOrder.set(key, ++this.accessCounter);
  }

  /**
   * Update memory usage estimation
   */
  private updateMemoryUsage(): void {
    if (!this.config.enableStats) return;

    // Rough estimation of memory usage
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += JSON.stringify(entry).length * 2; // Rough byte estimation
    }
    this.stats.memoryUsage = totalSize;
  }

  /**
   * Update top accessed keys statistics
   */
  private updateTopKeys(key: string): void {
    if (!this.config.enableStats) return;

    const existing = this.stats.topKeys.find(item => item.key === key);
    if (existing) {
      existing.accessCount++;
    } else {
      this.stats.topKeys.push({ key, accessCount: 1 });
    }

    // Keep only top 10 keys
    this.stats.topKeys.sort((a, b) => b.accessCount - a.accessCount);
    this.stats.topKeys = this.stats.topKeys.slice(0, 10);
  }

  /**
   * Check if a key matches a given pattern
   */
  private matchesPattern(key: string, pattern: CacheInvalidationPattern): boolean {
    if (typeof pattern === 'string') {
      return key === pattern;
    }

    if (pattern instanceof RegExp) {
      return pattern.test(key);
    }

    if (typeof pattern === 'object') {
      if ('prefix' in pattern) {
        return key.startsWith(pattern.prefix);
      }
      if ('suffix' in pattern) {
        return key.endsWith(pattern.suffix);
      }
      if ('contains' in pattern) {
        return key.includes(pattern.contains);
      }
    }

    return false;
  }

  /**
   * Notify subscribers of cache events
   */
  private notifySubscribers(
    eventType: CacheEventType,
    key: string,
    entry?: CacheEntry
  ): void {
    for (const subscription of this.subscribers.values()) {
      if (subscription.events.includes(eventType) && 
          this.matchesPattern(key, subscription.pattern)) {
        try {
          subscription.callback(key, entry);
        } catch (error) {
          ErrorLogger.log(
            error instanceof Error ? error : new Error(String(error)),
            { 
              operation: 'cache-subscriber-notification',
              metadata: { eventType, key, subscriptionId: subscription.id }
            },
            'low'
          );
        }
      }
    }
  }

  /**
   * Warm cache with frequently accessed data
   * 
   * @param warmupData - Array of key-value pairs to pre-load
   * @param options - Cache warming options
   */
  warmCache(
    warmupData: Array<{ key: string; data: unknown; ttl?: number; tags?: string[] }>,
    options: { batchSize?: number; delayBetweenBatches?: number } = {}
  ): Promise<void> {
    const { batchSize = 50, delayBetweenBatches = 100 } = options;
    
    return new Promise((resolve) => {
      const processBatch = (batch: typeof warmupData, index: number) => {
        batch.forEach(({ key, data, ttl, tags }) => {
          this.set(key, data, { ttl, tags });
        });

        if (index < warmupData.length) {
          setTimeout(() => {
            const nextBatch = warmupData.slice(index, index + batchSize);
            processBatch(nextBatch, index + batchSize);
          }, delayBetweenBatches);
        } else {
          resolve();
        }
      };

      if (warmupData.length > 0) {
        const firstBatch = warmupData.slice(0, batchSize);
        processBatch(firstBatch, batchSize);
      } else {
        resolve();
      }
    });
  }

  /**
   * Get detailed cache analytics
   * 
   * @returns Comprehensive cache analytics
   */
  getAnalytics(): {
    performance: {
      hitRate: number;
      missRate: number;
      averageAccessTime: number;
      hotKeys: Array<{ key: string; accessCount: number; lastAccessed: number }>;
    };
    memory: {
      totalSize: number;
      averageEntrySize: number;
      largestEntry: { key: string; size: number };
      memoryEfficiency: number;
    };
    health: {
      expiredEntries: number;
      stalenessRatio: number;
      fragmentationRatio: number;
      recommendedActions: string[];
    };
  } {
    const stats = this.getStats();
    const entries = Array.from(this.cache.entries());
    const now = Date.now();

    // Performance metrics
    const totalRequests = stats.hits + stats.misses;
    const hitRate = totalRequests > 0 ? (stats.hits / totalRequests) * 100 : 0;
    const missRate = 100 - hitRate;

    // Hot keys analysis
    const hotKeys = stats.topKeys.map(({ key, accessCount }) => ({
      key,
      accessCount,
      lastAccessed: this.accessOrder.get(key) || 0
    })).slice(0, 10);

    // Memory analysis
    const entrySizes = entries.map(([key, entry]) => {
      const size = JSON.stringify({ key, entry }).length * 2; // Rough byte estimate
      return { key, size };
    });

    const totalSize = entrySizes.reduce((sum, { size }) => sum + size, 0);
    const averageEntrySize = entries.length > 0 ? totalSize / entries.length : 0;
    const largestEntry = entrySizes.reduce((largest, current) => 
      current.size > largest.size ? current : largest, { key: '', size: 0 }
    );

    // Health metrics
    let expiredCount = 0;
    let staleCount = 0;

    entries.forEach(([, entry]) => {
      if (now - entry.timestamp > entry.ttl) {
        expiredCount++;
      } else if (now - entry.timestamp > entry.ttl * 0.8) {
        staleCount++;
      }
    });

    const stalenessRatio = entries.length > 0 ? (staleCount / entries.length) * 100 : 0;
    const fragmentationRatio = stats.evicted > 0 ? (stats.evicted / (stats.evicted + entries.length)) * 100 : 0;

    // Recommendations
    const recommendations: string[] = [];
    if (hitRate < 60) recommendations.push('Consider increasing TTL or implementing cache warming');
    if (expiredCount > entries.length * 0.2) recommendations.push('Increase cleanup frequency');
    if (stalenessRatio > 30) recommendations.push('Consider shorter TTL for better data freshness');
    if (fragmentationRatio > 25) recommendations.push('Consider increasing cache size to reduce evictions');
    if (totalSize > 50 * 1024 * 1024) recommendations.push('Monitor memory usage - cache approaching size limits');

    return {
      performance: {
        hitRate,
        missRate,
        averageAccessTime: 0, // Would need timing instrumentation
        hotKeys
      },
      memory: {
        totalSize,
        averageEntrySize,
        largestEntry,
        memoryEfficiency: entries.length > 0 ? (stats.hits / entries.length) : 0
      },
      health: {
        expiredEntries: expiredCount,
        stalenessRatio,
        fragmentationRatio,
        recommendedActions: recommendations
      }
    };
  }

  /**
   * Implement intelligent cache preloading based on access patterns
   * 
   * @param predictor - Function to predict keys that should be preloaded
   * @param loader - Function to load data for predicted keys
   */
  async intelligentPreload<T>(
    predictor: (stats: typeof this.stats) => string[],
    loader: (keys: string[]) => Promise<Array<{ key: string; data: T; ttl?: number }>>
  ): Promise<number> {
    const predictedKeys = predictor(this.stats);
    
    if (predictedKeys.length === 0) {
      return 0;
    }

    try {
      const dataToCache = await loader(predictedKeys);
      
      let cached = 0;
      dataToCache.forEach(({ key, data, ttl }) => {
        // Only cache if not already present or if existing data is stale
        const existing = this.cache.get(key);
        if (!existing || (Date.now() - existing.timestamp) > (existing.ttl * 0.7)) {
          this.set(key, data, { ttl });
          cached++;
        }
      });

      return cached;
    } catch (error) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error('Cache preload failed'),
        {
          operation: 'cache-intelligent-preload',
          metadata: {
            predictedKeysCount: predictedKeys.length,
            error: error instanceof Error ? error.message : String(error)
          }
        },
        'medium'
      );
      return 0;
    }
  }

  /**
   * Handle memory pressure by intelligently evicting entries
   * 
   * @param targetReductionPercent - Percentage of cache to clear (0-100)
   * @param strategy - Eviction strategy to use
   */
  handleMemoryPressure(
    targetReductionPercent = 25,
    strategy: 'lru' | 'least-accessed' | 'oldest' | 'largest' = 'lru'
  ): number {
    const currentSize = this.cache.size;
    const targetReduction = Math.floor((currentSize * targetReductionPercent) / 100);
    
    if (targetReduction === 0) {
      return 0;
    }

    const entries = Array.from(this.cache.entries());
    let toEvict: string[] = [];

    switch (strategy) {
      case 'lru':
        // Already implemented in ensureCacheSize, but we'll do a batch version
        const sortedByAccess = entries
          .map(([key]) => ({ key, lastAccess: this.accessOrder.get(key) || 0 }))
          .sort((a, b) => a.lastAccess - b.lastAccess)
          .slice(0, targetReduction)
          .map(({ key }) => key);
        toEvict = sortedByAccess;
        break;

      case 'least-accessed':
        const accessCounts = new Map<string, number>();
        this.stats.topKeys.forEach(({ key, accessCount }) => {
          accessCounts.set(key, accessCount);
        });
        
        toEvict = entries
          .map(([key]) => ({ key, count: accessCounts.get(key) || 0 }))
          .sort((a, b) => a.count - b.count)
          .slice(0, targetReduction)
          .map(({ key }) => key);
        break;

      case 'oldest':
        toEvict = entries
          .sort(([, a], [, b]) => a.timestamp - b.timestamp)
          .slice(0, targetReduction)
          .map(([key]) => key);
        break;

      case 'largest':
        const withSizes = entries.map(([key, entry]) => ({
          key,
          size: JSON.stringify(entry).length
        })).sort((a, b) => b.size - a.size);
        
        toEvict = withSizes.slice(0, targetReduction).map(({ key }) => key);
        break;
    }

    // Perform eviction
    let evicted = 0;
    toEvict.forEach(key => {
      if (this.cache.delete(key)) {
        this.accessOrder.delete(key);
        evicted++;
      }
    });

    if (this.config.enableStats && evicted > 0) {
      this.stats.evicted += evicted;
      this.updateMemoryUsage();
    }

    // Notify about mass eviction
    this.notifySubscribers('evict', `batch:${strategy}:${evicted}`);

    return evicted;
  }

  /**
   * Get health status of the cache for monitoring integration
   * 
   * @returns Health status with detailed metrics
   */
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    hitRate: number;
    memoryUsage: number;
    size: number;
    recommendations: string[];
  } {
    const stats = this.getStats();
    const analytics = this.getAnalytics();
    
    // Health indicators
    const lowHitRate = stats.hitRate < 60;
    const highMemoryUsage = stats.memoryUsage > 100 * 1024 * 1024; // 100MB
    const nearCapacity = stats.size > this.config.maxSize * 0.9;
    const highStaleness = analytics.health.stalenessRatio > 30;
    const highFragmentation = analytics.health.fragmentationRatio > 25;
    
    // Determine health status
    let status: 'healthy' | 'degraded' | 'unhealthy';
    const recommendations = [...analytics.health.recommendedActions];
    
    if (highMemoryUsage && nearCapacity) {
      status = 'unhealthy';
      recommendations.push('Critical: Cache memory usage and capacity near limits');
    } else if (lowHitRate || nearCapacity || highStaleness || highFragmentation) {
      status = 'degraded';
      if (lowHitRate) recommendations.push(`Low hit rate: ${stats.hitRate.toFixed(1)}%`);
      if (nearCapacity) recommendations.push(`Near capacity: ${stats.size}/${this.config.maxSize}`);
    } else {
      status = 'healthy';
    }
    
    return {
      status,
      hitRate: stats.hitRate,
      memoryUsage: stats.memoryUsage,
      size: stats.size,
      recommendations
    };
  }

  /**
   * Cleanup resources when shutting down
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clear();
    this.subscribers.clear();
  }
}

// Initialize cleanup on process exit
if (typeof process !== 'undefined') {
  process.on('exit', () => {
    const instance = GlobalCacheManager.getInstance();
    instance.destroy();
  });
}