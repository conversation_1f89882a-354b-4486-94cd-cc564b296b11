/**
 * Service utilities and base classes for wf-shared
 * 
 * This module provides comprehensive service layer utilities including:
 * - Helper functions for consistent error handling
 * - TypeScript mixins for reusable functionality
 * - Base service class with all common features
 * 
 * @example Basic Usage
 * ```typescript
 * import { BaseService } from 'wf-shared/services';
 * 
 * export class UserService extends BaseService {
 *   static async getUser(id: string) {
 *     const service = new UserService();
 *     return service.callRepository(...);
 *   }
 * }
 * ```
 * 
 * @example Individual Mixins
 * ```typescript
 * import { WithLogging, WithValidation } from 'wf-shared/services';
 * 
 * export class CustomService extends WithValidation(WithLogging(class {})) {
 *   // Only logging and validation functionality
 * }
 * ```
 * 
 * @example Helper Functions
 * ```typescript
 * import { handleRepositoryCall, createErrorResponse } from 'wf-shared/services';
 * 
 * const result = await handleRepositoryCall(
 *   () => repositories.userApi.getById(id),
 *   'User fetch error',
 *   'Failed to fetch user'
 * );
 * ```
 */

// Export all helper functions
export {
  handleRepositoryCall,
  handleRepositoryCallSuccess,
  createErrorResponse,
  createSuccessResponse,
  validateRequiredParams,
  sanitizeErrorMessage,
  isServiceError,
  isServiceSuccess,
  type RepositoryResponse,
  type ServiceResponse
} from './ServiceHelpers';

// Export all individual mixins
export {
  WithRepositoryCalls,
  WithLogging,
  WithValidation,
  WithCaching,
  type RepositoryCallable
} from './ServiceMixins';

// Export base service and combined mixins
export {
  BaseService,
  WithAllServiceMixins,
  type BaseServiceType,
  type IBaseService
} from './BaseService';

// Export cache manager
export {
  GlobalCacheManager
} from './CacheManager';

// Export circuit breaker
export {
  CircuitBreaker,
  CircuitBreakerFactory
} from './CircuitBreaker';

// Export health monitor
export {
  HealthMonitor
} from './HealthMonitor';

// Default export for convenience (most common use case)
export { default } from './BaseService';

/**
 * Convenience re-exports for common patterns
 * Note: Import individual functions/classes directly for better tree-shaking
 */
