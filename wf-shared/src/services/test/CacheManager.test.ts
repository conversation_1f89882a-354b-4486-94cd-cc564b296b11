import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { GlobalCacheManager } from '../CacheManager';
import { ErrorLogger } from '../../utils/errorHandling';
import { generateUUID } from '../../utils/ids'; // Will be mocked

// Mock ErrorLogger to prevent actual console output and track calls
vi.mock('../../utils/errorHandling', () => ({
  ErrorLogger: {
    log: vi.fn(),
    configure: vi.fn(),
    resetStats: vi.fn(),
    getStats: vi.fn(() => ({
      totalErrors: 0,
      errorsBySeverity: { low: 0, medium: 0, high: 0, critical: 0 },
      errorsByOperation: {},
      lastErrorTime: 0,
    })),
  },
}));

// Mock generateUUID to return predictable UUIDs
vi.mock('../../utils/ids', () => ({
  generateUUID: vi.fn(() => 'mock-uuid-123'),
}));

describe('GlobalCacheManager', () => {
  let cacheManager: GlobalCacheManager;

  beforeEach(() => {
    // Reset the singleton instance before each test
    // @ts-ignore
    GlobalCacheManager.instance = undefined;
    cacheManager = GlobalCacheManager.getInstance(); // Initialize with default config for most tests

    // Reset mocks
    vi.clearAllMocks();
    vi.useFakeTimers(); // Control Date.now()
  });

  afterEach(() => {
    vi.useRealTimers(); // Restore real timers
    // Ensure cleanup timer is cleared if set during tests
    // @ts-ignore
    if (cacheManager.cleanupTimer) {
      // @ts-ignore
      clearInterval(cacheManager.cleanupTimer);
    }
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance every time', () => {
      const instance1 = GlobalCacheManager.getInstance();
      const instance2 = GlobalCacheManager.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should apply config only on the first getInstance call', () => {
      // @ts-ignore
      GlobalCacheManager.instance = undefined; // Ensure fresh instance
      const instance1 = GlobalCacheManager.getInstance({ defaultTtl: 1000 });
      // @ts-ignore
      expect(instance1.config.defaultTtl).toBe(1000);

      const instance2 = GlobalCacheManager.getInstance({ defaultTtl: 5000 });
      // @ts-ignore
      expect(instance2.config.defaultTtl).toBe(1000); // Should still be 1000
    });
  });

  describe('Configuration', () => {
    it('should allow configuring cache properties', () => {
      // Ensure a fresh instance for this test to apply new config
      // @ts-ignore
      GlobalCacheManager.instance = undefined;
      const configuredCacheManager = GlobalCacheManager.getInstance({ defaultTtl: 60000, maxSize: 500 });
      // @ts-ignore
      expect(configuredCacheManager.config.defaultTtl).toBe(60000);
      // @ts-ignore
      expect(configuredCacheManager.config.maxSize).toBe(500);
    });
  });

  describe('set', () => {
    it('should set and retrieve a value', () => {
      const result = cacheManager.set('key1', 'value1');
      expect(result.success).toBe(true);
      expect(cacheManager.get('key1')).toBe('value1');
    });

    it('should return false for overwrite if key exists and overwrite is disabled', () => {
      cacheManager.set('key1', 'value1');
      const result = cacheManager.set('key1', 'value2', { overwrite: false });
      expect(result.success).toBe(false);
      expect(result.error).toContain('already exists');
      expect(cacheManager.get('key1')).toBe('value1'); // Value should not be overwritten
    });

    it('should overwrite value if overwrite is true (default)', () => {
      cacheManager.set('key1', 'value1');
      cacheManager.set('key1', 'value2');
      expect(cacheManager.get('key1')).toBe('value2');
    });

    it('should log error if setting fails', () => {
      // Simulate an error during set (e.g., invalid data, though hard to trigger with current impl)
      // For demonstration, we'll force an error by passing a non-string key to Map.set
      // @ts-ignore
      cacheManager.cache.set = vi.fn(() => { throw new Error('Forced set error'); });
      cacheManager.set('key1', 'value1');
      expect(ErrorLogger.log).toHaveBeenCalled();
    });

    it('should update stats on set', () => {
      const cacheManagerWithStats = GlobalCacheManager.getInstance({ enableStats: true });
      cacheManagerWithStats.set('key1', 'value1');
      const stats = cacheManagerWithStats.getStats();
      expect(stats.hits).toBe(0);
      expect(stats.misses).toBe(0);
      expect(stats.size).toBe(1);
      expect(stats.memoryUsage).toBeGreaterThan(0);
    });
  });

  describe('get', () => {
    it('should return null for a non-existent key', () => {
      expect(cacheManager.get('nonexistent')).toBeNull();
    });

    it('should return null for an expired key', () => {
      cacheManager.set('key1', 'value1', { ttl: 100 });
      vi.advanceTimersByTime(101);
      expect(cacheManager.get('key1')).toBeNull();
    });

    it('should return expired key if includeExpired is true', () => {
      cacheManager.set('key1', 'value1', { ttl: 100 });
      vi.advanceTimersByTime(101);
      expect(cacheManager.get('key1', { includeExpired: true })).toBe('value1');
    });

    it('should update access order on get', () => {
      cacheManager.set('key1', 'value1');
      cacheManager.set('key2', 'value2');
      cacheManager.get('key1'); // Access key1
      // @ts-ignore
      expect(cacheManager.accessOrder.get('key1')).toBeGreaterThan(cacheManager.accessOrder.get('key2'));
    });

    it('should not update access order if updateAccessTime is false', () => {
      cacheManager.set('key1', 'value1');
      cacheManager.set('key2', 'value2');
      // @ts-ignore
      const initialAccessOrderKey1 = cacheManager.accessOrder.get('key1');
      cacheManager.get('key1', { updateAccessTime: false });
      // @ts-ignore
      expect(cacheManager.accessOrder.get('key1')).toBe(initialAccessOrderKey1);
    });

    it('should log error if getting fails', () => {
      // Simulate an error during get
      // @ts-ignore
      cacheManager.cache.get = vi.fn(() => { throw new Error('Forced get error'); });
      cacheManager.get('key1');
      expect(ErrorLogger.log).toHaveBeenCalled();
    });

    it('should update stats on get (hits and misses)', () => {
      const cacheManagerWithStats = GlobalCacheManager.getInstance({ enableStats: true });
      cacheManagerWithStats.set('key1', 'value1');
      cacheManagerWithStats.get('key1'); // Hit
      cacheManagerWithStats.get('key2'); // Miss
      const stats = cacheManagerWithStats.getStats();
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
    });
  });

  describe('has', () => {
    it('should return true for an existing, non-expired key', () => {
      cacheManager.set('key1', 'value1');
      expect(cacheManager.has('key1')).toBe(true);
    });

    it('should return false for a non-existent key', () => {
      expect(cacheManager.has('nonexistent')).toBe(false);
    });

    it('should return false for an expired key and remove it', () => {
      cacheManager.set('key1', 'value1', { ttl: 100 });
      vi.advanceTimersByTime(101);
      expect(cacheManager.has('key1')).toBe(false);
      // @ts-ignore
      expect(cacheManager.cache.has('key1')).toBe(false); // Should be removed
    });
  });

  describe('delete', () => {
    it('should delete an existing key', () => {
      cacheManager.set('key1', 'value1');
      expect(cacheManager.delete('key1')).toBe(true);
      expect(cacheManager.get('key1')).toBeNull();
    });

    it('should return false for a non-existent key', () => {
      expect(cacheManager.delete('nonexistent')).toBe(false);
    });

    it('should update memory usage on delete', () => {
      const cacheManagerWithStats = GlobalCacheManager.getInstance({ enableStats: true });
      cacheManagerWithStats.set('key1', 'value1');
      const initialMemory = cacheManagerWithStats.getStats().memoryUsage;
      cacheManagerWithStats.delete('key1');
      const finalMemory = cacheManagerWithStats.getStats().memoryUsage;
      expect(finalMemory).toBeLessThan(initialMemory);
    });
  });

  describe('clear', () => {
    it('should clear all cache entries', () => {
      cacheManager.set('key1', 'value1');
      cacheManager.set('key2', 'value2');
      cacheManager.clear();
      // @ts-ignore
      expect(cacheManager.cache.size).toBe(0);
      // @ts-ignore
      expect(cacheManager.accessOrder.size).toBe(0);
    });

    it('should update stats on clear', () => {
      const cacheManagerWithStats = GlobalCacheManager.getInstance({ enableStats: true });
      cacheManagerWithStats.set('key1', 'value1');
      cacheManagerWithStats.set('key2', 'value2');
      cacheManagerWithStats.clear();
      const stats = cacheManagerWithStats.getStats();
      expect(stats.evicted).toBe(2); // Cleared entries are counted as evicted
      expect(stats.memoryUsage).toBe(0);
    });
  });
});
