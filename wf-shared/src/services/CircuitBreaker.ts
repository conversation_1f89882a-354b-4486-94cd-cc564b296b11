/**
 * Circuit Breaker implementation for external service call protection
 * Part of Technical Debt Solution - Phase 1, Task 1.3
 * 
 * Implements the circuit breaker pattern to prevent cascade failures and
 * improve system resilience when calling external services like Supabase.
 */

import { ErrorLogger } from '../utils/errorHandling';
import {
  CIRCUIT_BREAKER_PRESETS,
  type CircuitState,
  type CircuitBreakerConfig,
  type CircuitBreakerStats,
  type CircuitBreakerResult,
  type CircuitBreakerEvent,
  type CircuitBreakerEventType,
  type CircuitBreakerEventCallback
} from '../types/circuitBreaker';

/**
 * Default configuration for circuit breaker
 */
const DEFAULT_CONFIG: CircuitBreakerConfig = {
  failureThreshold: 5,
  resetTimeout: 30000, // 30 seconds
  monitoringWindow: 60000, // 1 minute
  timeoutMs: 10000, // 10 seconds
  successThreshold: 3,
  name: 'default'
};

/**
 * Circuit Breaker implementation following the classic pattern
 * 
 * States:
 * - CLOSED: Normal operation, all requests pass through
 * - OPEN: Failure threshold exceeded, all requests rejected immediately
 * - HALF_OPEN: Testing if service has recovered, limited requests allowed
 * 
 * Features:
 * - Configurable failure thresholds and timeouts
 * - Automatic state management and recovery attempts
 * - Comprehensive statistics and monitoring
 * - Event-driven notifications for state changes
 * - Operation timeout handling
 * - Health check integration
 * 
 * @example
 * ```typescript
 * const breaker = new CircuitBreaker({
 *   failureThreshold: 3,
 *   resetTimeout: 15000,
 *   timeoutMs: 5000
 * });
 * 
 * breaker.on('STATE_CHANGE', (event) => {
 *   console.log(`Circuit breaker ${event.previousState} -> ${event.state}`);
 * });
 * 
 * try {
 *   const result = await breaker.execute(async () => {
 *     return await database.query('SELECT * FROM users');
 *   });
 *   
 *   if (result.success) {
 *     console.log('Data:', result.data);
 *   }
 * } catch (error) {
 *   console.error('Circuit breaker rejected or failed:', error);
 * }
 * ```
 */
export class CircuitBreaker {
  private state: CircuitState = 'CLOSED';
  private config: CircuitBreakerConfig;
  private stats: CircuitBreakerStats;
  private eventListeners = new Map<CircuitBreakerEventType, Set<CircuitBreakerEventCallback>>();
  private recentFailures: number[] = []; // Timestamps of recent failures
  private responseTimes: number[] = []; // For average response time calculation

  constructor(config: Partial<CircuitBreakerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.stats = this.initializeStats();
  }

  /**
   * Execute an operation through the circuit breaker
   * 
   * @param operation - Async operation to execute
   * @returns Promise resolving to circuit breaker result
   */
  async execute<T>(operation: () => Promise<T>): Promise<CircuitBreakerResult<T>> {
    this.stats.totalRequests++;

    // Check if circuit is OPEN
    if (this.state === 'OPEN') {
      if (this.shouldAttemptReset()) {
        this.changeState('HALF_OPEN');
      } else {
        this.stats.rejectedRequests++;
        this.emitEvent('REJECTED');
        return {
          success: false,
          error: 'Circuit breaker is OPEN',
          rejected: true,
          circuitState: this.state
        };
      }
    }

    const startTime = Date.now();

    try {
      // Execute operation with timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Operation timed out after ${this.config.timeoutMs}ms`));
        }, this.config.timeoutMs);
      });

      const result = await Promise.race([
        operation(),
        timeoutPromise
      ]);

      // Operation succeeded
      const responseTime = Date.now() - startTime;
      this.recordSuccess(responseTime);
      
      return {
        success: true,
        data: result,
        responseTime,
        circuitState: this.state
      };

    } catch (error) {
      // Operation failed
      const responseTime = Date.now() - startTime;
      const isTimeout = error instanceof Error && error.message.includes('timed out');
      
      this.recordFailure(error instanceof Error ? error : new Error(String(error)));
      
      this.emitEvent(isTimeout ? 'TIMEOUT' : 'FAILURE', { error, responseTime });

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        responseTime,
        circuitState: this.state
      };
    }
  }

  /**
   * Get current circuit breaker statistics
   * 
   * @returns Current statistics
   */
  getStats(): CircuitBreakerStats {
    return {
      ...this.stats,
      failureRate: this.calculateFailureRate(),
      averageResponseTime: this.calculateAverageResponseTime()
    };
  }

  /**
   * Reset circuit breaker to CLOSED state
   * Clears all failure history and statistics
   */
  reset(): void {
    this.changeState('CLOSED');
    this.recentFailures = [];
    this.responseTimes = [];
    this.stats = this.initializeStats();
    this.emitEvent('RESET_ATTEMPTED');
  }

  /**
   * Force circuit breaker to specific state (primarily for testing)
   * 
   * @param state - State to force
   */
  forceState(state: CircuitState): void {
    this.changeState(state);
  }


  /**
   * Add event listener for circuit breaker events
   * 
   * @param eventType - Type of event to listen for
   * @param callback - Callback function to execute
   * @returns Unsubscribe function
   */
  on(eventType: CircuitBreakerEventType, callback: CircuitBreakerEventCallback): () => void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    
    const listeners = this.eventListeners.get(eventType)!;
    listeners.add(callback);

    // Return unsubscribe function
    return () => {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(eventType);
      }
    };
  }

  /**
   * Remove all event listeners for a specific event type
   * 
   * @param eventType - Event type to clear (optional, clears all if not provided)
   */
  off(eventType?: CircuitBreakerEventType): void {
    if (eventType) {
      this.eventListeners.delete(eventType);
    } else {
      this.eventListeners.clear();
    }
  }

  /**
   * Get health status of the circuit breaker for monitoring integration
   * 
   * @returns Health status with detailed metrics
   */
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    state: CircuitState;
    failureRate: number;
    responseTime: number;
    recommendations: string[];
  } {
    const stats = this.getStats();
    const now = Date.now();
    
    // Health indicators
    const isCircuitOpen = this.state === 'OPEN';
    const highFailureRate = stats.failureRate > 50;
    const recentFailure = stats.lastFailureTime > 0 && (now - stats.lastFailureTime) < 300000; // 5 minutes
    const slowResponse = stats.averageResponseTime > this.config.timeoutMs * 0.8;
    
    // Determine health status
    let status: 'healthy' | 'degraded' | 'unhealthy';
    const recommendations: string[] = [];
    
    if (isCircuitOpen) {
      status = 'unhealthy';
      recommendations.push('Circuit is OPEN - external service is unavailable');
      recommendations.push('Check external service health and connectivity');
    } else if (highFailureRate || slowResponse) {
      status = 'degraded';
      if (highFailureRate) {
        recommendations.push(`High failure rate: ${stats.failureRate.toFixed(1)}%`);
      }
      if (slowResponse) {
        recommendations.push(`Slow response time: ${stats.averageResponseTime.toFixed(0)}ms`);
      }
    } else {
      status = 'healthy';
    }
    
    if (recentFailure && status === 'healthy') {
      status = 'degraded';
      recommendations.push('Recent failures detected - monitor closely');
    }
    
    return {
      status,
      state: this.state,
      failureRate: stats.failureRate,
      responseTime: stats.averageResponseTime,
      recommendations
    };
  }

  /**
   * Initialize statistics object
   */
  private initializeStats(): CircuitBreakerStats {
    return {
      state: this.state,
      failureCount: 0,
      successCount: 0,
      lastFailureTime: 0,
      lastSuccessTime: 0,
      totalRequests: 0,
      rejectedRequests: 0,
      averageResponseTime: 0,
      failureRate: 0
    };
  }

  /**
   * Record a successful operation
   * 
   * @param responseTime - Response time in milliseconds
   */
  private recordSuccess(responseTime: number): void {
    this.stats.successCount++;
    this.stats.lastSuccessTime = Date.now();
    this.responseTimes.push(responseTime);
    
    // Keep only recent response times (last 100)
    if (this.responseTimes.length > 100) {
      this.responseTimes = this.responseTimes.slice(-100);
    }

    this.emitEvent('SUCCESS', { responseTime });

    // Handle state transitions for HALF_OPEN
    if (this.state === 'HALF_OPEN') {
      if (this.stats.successCount >= this.config.successThreshold) {
        this.changeState('CLOSED');
      }
    }
  }

  /**
   * Record a failed operation
   * 
   * @param error - Error that occurred
   */
  private recordFailure(error: Error): void {
    const now = Date.now();
    this.stats.failureCount++;
    this.stats.lastFailureTime = now;
    this.recentFailures.push(now);

    // Clean up old failures outside monitoring window
    this.cleanupOldFailures();

    // Log the failure
    ErrorLogger.log(error, {
      operation: 'circuit-breaker-failure',
      metadata: {
        circuitName: this.config.name,
        state: this.state,
        failureCount: this.stats.failureCount,
        recentFailures: this.recentFailures.length
      }
    }, 'medium');

    // Check if we should open the circuit
    if (this.state === 'CLOSED' && this.recentFailures.length >= this.config.failureThreshold) {
      this.changeState('OPEN');
    } else if (this.state === 'HALF_OPEN') {
      // Any failure in HALF_OPEN state returns to OPEN
      this.changeState('OPEN');
    }
  }

  /**
   * Clean up failure records outside the monitoring window
   */
  private cleanupOldFailures(): void {
    const cutoff = Date.now() - this.config.monitoringWindow;
    this.recentFailures = this.recentFailures.filter(time => time > cutoff);
  }

  /**
   * Check if we should attempt to reset from OPEN to HALF_OPEN
   */
  private shouldAttemptReset(): boolean {
    if (this.state !== 'OPEN') return false;

    const timeSinceLastFailure = Date.now() - this.stats.lastFailureTime;
    return timeSinceLastFailure >= this.config.resetTimeout;
  }

  /**
   * Calculate current failure rate as percentage
   */
  private calculateFailureRate(): number {
    if (this.stats.totalRequests === 0) return 0;
    return (this.stats.failureCount / this.stats.totalRequests) * 100;
  }

  /**
   * Calculate average response time
   */
  private calculateAverageResponseTime(): number {
    if (this.responseTimes.length === 0) return 0;
    const sum = this.responseTimes.reduce((acc, time) => acc + time, 0);
    return sum / this.responseTimes.length;
  }

  /**
   * Change circuit breaker state and emit event
   * 
   * @param newState - New state to transition to
   */
  private changeState(newState: CircuitState): void {
    if (newState === this.state) return;

    const previousState = this.state;
    this.state = newState;
    this.stats.state = newState;

    // Reset counters for new state
    if (newState === 'CLOSED') {
      this.stats.failureCount = 0;
      this.stats.successCount = 0;
    } else if (newState === 'HALF_OPEN') {
      this.stats.successCount = 0; // Reset success count for HALF_OPEN test
    }

    this.emitEvent('STATE_CHANGE', { previousState });
  }

  /**
   * Emit event to all registered listeners
   * 
   * @param type - Event type
   * @param metadata - Additional event metadata
   */
  private emitEvent(type: CircuitBreakerEventType, metadata: Record<string, unknown> = {}): void {
    const listeners = this.eventListeners.get(type);
    if (!listeners || listeners.size === 0) return;

    const event: CircuitBreakerEvent = {
      type,
      circuitName: this.config.name || 'unnamed',
      state: this.state,
      timestamp: Date.now(),
      ...metadata
    };

    for (const listener of listeners) {
      try {
        listener(event);
      } catch (error) {
        ErrorLogger.log(
          error instanceof Error ? error : new Error(String(error)),
          {
            operation: 'circuit-breaker-event-listener',
            metadata: { eventType: type, circuitName: this.config.name }
          },
          'low'
        );
      }
    }
  }
}

/**
 * Circuit Breaker Factory for creating pre-configured instances
 */
export class CircuitBreakerFactory {
  private static instances = new Map<string, CircuitBreaker>();

  /**
   * Get or create a circuit breaker instance with a specific configuration
   * 
   * @param name - Unique name for the circuit breaker
   * @param config - Configuration (uses preset if string provided)
   * @returns Circuit breaker instance
   */
  static getInstance(
    name: string, 
    config: Partial<CircuitBreakerConfig> | keyof typeof CIRCUIT_BREAKER_PRESETS = 'STANDARD'
  ): CircuitBreaker {
    if (this.instances.has(name)) {
      return this.instances.get(name)!;
    }

    let finalConfig: Partial<CircuitBreakerConfig>;
    
    if (typeof config === 'string') {
      finalConfig = { ...CIRCUIT_BREAKER_PRESETS[config], name };
    } else {
      finalConfig = { ...config, name };
    }

    const instance = new CircuitBreaker(finalConfig);
    this.instances.set(name, instance);
    
    return instance;
  }

  /**
   * Clear all cached instances (primarily for testing)
   */
  static clearInstances(): void {
    this.instances.clear();
  }

  /**
   * Get statistics for all circuit breaker instances
   */
  static getAllStats(): Record<string, CircuitBreakerStats> {
    const stats: Record<string, CircuitBreakerStats> = {};
    
    for (const [name, instance] of this.instances.entries()) {
      stats[name] = instance.getStats();
    }
    
    return stats;
  }
}