{"name": "wf-shared", "version": "1.0.0", "description": "Shared types, constants and utilities for Word Formation platform", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js", "require": "./dist/types/index.js"}, "./constants": {"types": "./dist/constants/index.d.ts", "import": "./dist/constants/index.js", "require": "./dist/constants/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "import": "./dist/utils/index.js", "require": "./dist/utils/index.js"}, "./repositories": {"types": "./dist/repositories/index.d.ts", "import": "./dist/repositories/index.js", "require": "./dist/repositories/index.js"}, "./services": {"types": "./dist/services/index.d.ts", "import": "./dist/services/index.js", "require": "./dist/services/index.js"}}, "scripts": {"build": "tsc", "watch": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext .ts --fix", "test": "vitest", "test:coverage": "vitest run --coverage"}, "keywords": ["types", "constants", "utilities", "word-formation"], "author": "Word Formation Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.51.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.31.0", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "files": ["dist/**/*", "README.md"]}