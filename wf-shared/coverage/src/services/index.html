
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35.49% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>585/1648</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.73% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>115/139</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">58.22% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>46/79</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35.49% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>585/1648</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="BaseService.ts"><a href="BaseService.ts.html">BaseService.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	</tr>

<tr>
	<td class="file low" data-value="CacheManager.ts"><a href="CacheManager.ts.html">CacheManager.ts</a></td>
	<td data-value="42.21" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 42%"></div><div class="cover-empty" style="width: 58%"></div></div>
	</td>
	<td data-value="42.21" class="pct low">42.21%</td>
	<td data-value="559" class="abs low">236/559</td>
	<td data-value="79.16" class="pct medium">79.16%</td>
	<td data-value="48" class="abs medium">38/48</td>
	<td data-value="53.57" class="pct medium">53.57%</td>
	<td data-value="28" class="abs medium">15/28</td>
	<td data-value="42.21" class="pct low">42.21%</td>
	<td data-value="559" class="abs low">236/559</td>
	</tr>

<tr>
	<td class="file medium" data-value="CircuitBreaker.ts"><a href="CircuitBreaker.ts.html">CircuitBreaker.ts</a></td>
	<td data-value="73.91" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 73%"></div><div class="cover-empty" style="width: 27%"></div></div>
	</td>
	<td data-value="73.91" class="pct medium">73.91%</td>
	<td data-value="276" class="abs medium">204/276</td>
	<td data-value="85.96" class="pct high">85.96%</td>
	<td data-value="57" class="abs high">49/57</td>
	<td data-value="80.95" class="pct high">80.95%</td>
	<td data-value="21" class="abs high">17/21</td>
	<td data-value="73.91" class="pct medium">73.91%</td>
	<td data-value="276" class="abs medium">204/276</td>
	</tr>

<tr>
	<td class="file low" data-value="HealthMonitor.ts"><a href="HealthMonitor.ts.html">HealthMonitor.ts</a></td>
	<td data-value="29.17" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 29%"></div><div class="cover-empty" style="width: 71%"></div></div>
	</td>
	<td data-value="29.17" class="pct low">29.17%</td>
	<td data-value="497" class="abs low">145/497</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="30" class="abs high">24/30</td>
	<td data-value="38.46" class="pct low">38.46%</td>
	<td data-value="26" class="abs low">10/26</td>
	<td data-value="29.17" class="pct low">29.17%</td>
	<td data-value="497" class="abs low">145/497</td>
	</tr>

<tr>
	<td class="file low" data-value="ServiceHelpers.ts"><a href="ServiceHelpers.ts.html">ServiceHelpers.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="153" class="abs low">0/153</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="153" class="abs low">0/153</td>
	</tr>

<tr>
	<td class="file low" data-value="ServiceMixins.ts"><a href="ServiceMixins.ts.html">ServiceMixins.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="145" class="abs low">0/145</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="145" class="abs low">0/145</td>
	</tr>

<tr>
	<td class="file low" data-value="index.ts"><a href="index.ts.html">index.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-25T01:16:37.694Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    