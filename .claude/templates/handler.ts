import { useState, useMemo, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ServiceName } from '@/services/ServiceName'
import { ServiceResponse } from 'wf-shared/types'

// Types
interface FilterState {
  search: string
  category: string
  status: string
}

interface PaginationState {
  currentPage: number
  pageSize: number
  totalPages: number
}

export const useComponentHandler = () => {
  // 1. State declarations
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    category: '',
    status: '',
  })
  
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    pageSize: 10,
    totalPages: 1,
  })

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<string | null>(null)

  // 2. Data fetching (React Query)
  const queryClient = useQueryClient()
  
  const {
    data: rawData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['entity', pagination.currentPage, filters],
    queryFn: () => ServiceName.fetchData({
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      filters
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // 3. Mutations
  const createMutation = useMutation({
    mutationFn: ServiceName.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['entity'] })
      setIsModalOpen(false)
    },
    onError: (error) => {
      console.error('Create error:', error)
    },
  })

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      ServiceName.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['entity'] })
      setIsModalOpen(false)
      setSelectedItem(null)
    },
    onError: (error) => {
      console.error('Update error:', error)
    },
  })

  const deleteMutation = useMutation({
    mutationFn: ServiceName.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['entity'] })
    },
    onError: (error) => {
      console.error('Delete error:', error)
    },
  })

  // 4. Event handlers
  const handleFilterChange = useCallback((key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setPagination(prev => ({ ...prev, currentPage: 1 })) // Reset to first page
  }, [])

  const handleCreate = useCallback(() => {
    setSelectedItem(null)
    setIsModalOpen(true)
  }, [])

  const handleEdit = useCallback((id: string) => {
    setSelectedItem(id)
    setIsModalOpen(true)
  }, [])

  const handleDelete = useCallback(async (id: string) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      deleteMutation.mutate(id)
    }
  }, [deleteMutation])

  const handleSubmit = useCallback(async (formData: any) => {
    try {
      if (selectedItem) {
        updateMutation.mutate({ id: selectedItem, data: formData })
      } else {
        createMutation.mutate(formData)
      }
    } catch (error) {
      console.error('Submit error:', error)
    }
  }, [selectedItem, createMutation, updateMutation])

  const handlePageChange = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }))
  }, [])

  const handleRefetch = useCallback(() => {
    refetch()
  }, [refetch])

  // 5. Computed values
  const data = useMemo(() => {
    if (!rawData) return []
    
    // Update pagination with server response
    if (rawData.totalPages !== pagination.totalPages) {
      setPagination(prev => ({ ...prev, totalPages: rawData.totalPages }))
    }
    
    return rawData.items || []
  }, [rawData, pagination.totalPages])

  const isLoading = useMemo(() => {
    return isLoading || createMutation.isPending || updateMutation.isPending || deleteMutation.isPending
  }, [isLoading, createMutation.isPending, updateMutation.isPending, deleteMutation.isPending])

  const selectedItemData = useMemo(() => {
    if (!selectedItem || !data) return null
    return data.find(item => item.id === selectedItem)
  }, [selectedItem, data])

  const filteredCount = useMemo(() => {
    return rawData?.totalCount || 0
  }, [rawData?.totalCount])

  // 6. Return organized object
  return {
    // Data
    data,
    selectedItemData,
    filteredCount,
    
    // State
    loading: isLoading,
    error: error instanceof Error ? error.message : null,
    filters,
    isModalOpen,
    selectedItem,
    
    // Actions
    actions: {
      // CRUD operations
      create: handleCreate,
      edit: handleEdit,
      delete: handleDelete,
      submit: handleSubmit,
      refetch: handleRefetch,
      
      // UI state management
      openModal: () => setIsModalOpen(true),
      closeModal: () => {
        setIsModalOpen(false)
        setSelectedItem(null)
      },
      
      // Filtering
      updateFilter: handleFilterChange,
      clearFilters: () => setFilters({
        search: '',
        category: '',
        status: '',
      }),
      
      // Pagination
      pagination: {
        currentPage: pagination.currentPage,
        totalPages: pagination.totalPages,
        pageSize: pagination.pageSize,
        nextPage: () => handlePageChange(pagination.currentPage + 1),
        previousPage: () => handlePageChange(pagination.currentPage - 1),
        goToPage: handlePageChange,
      },
    },
  }
}