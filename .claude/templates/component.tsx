import React from 'react'

// Third-party libraries
import { useQuery } from '@tanstack/react-query'

// Internal components & UI
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

// Services & utilities
import { ServiceName } from '@/services/ServiceName'
import { formatDate } from 'wf-shared/utils'

// Icons (grouped)
import { Plus, Edit, Trash2, Eye } from 'lucide-react'

// Local imports last
import { useComponentHandler } from './Component.handler'
import styles from './Component.module.css'

export default function ComponentName() {
  // Handler hook (contains all logic)
  const { data, loading, error, actions } = useComponentHandler()

  // Early returns (loading, error states)
  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-200 rounded"></div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 mb-4">
          <h3 className="text-lg font-medium">Error Loading Data</h3>
          <p className="text-sm">{error}</p>
        </div>
        <Button onClick={actions.refetch} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  // Empty state
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8">
        <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Found</h3>
        <p className="text-gray-600 mb-4">Get started by creating your first item</p>
        <Button onClick={actions.create}>
          <Plus className="h-4 w-4 mr-2" />
          Create New Item
        </Button>
      </div>
    )
  }

  // Main render
  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <div>
          <h1 className={styles.title}>Component Title</h1>
          <p className="text-gray-600">Component description</p>
        </div>
        <Button onClick={actions.create}>
          <Plus className="h-4 w-4 mr-2" />
          Add New
        </Button>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data.map((item) => (
          <Card key={item.id} className={styles.card}>
            <CardHeader>
              <CardTitle className="flex justify-between items-center">
                <span>{item.title}</span>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => actions.edit(item.id)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => actions.delete(item.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">{item.description}</p>
              <p className="text-sm text-gray-500 mt-2">
                Created: {formatDate(item.created_at)}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination (if needed) */}
      {actions.pagination && (
        <div className="mt-6 flex justify-center space-x-2">
          <Button
            variant="outline"
            disabled={actions.pagination.currentPage === 1}
            onClick={actions.pagination.previousPage}
          >
            Previous
          </Button>
          <span className="px-4 py-2 text-sm text-gray-600">
            Page {actions.pagination.currentPage} of {actions.pagination.totalPages}
          </span>
          <Button
            variant="outline"
            disabled={actions.pagination.currentPage === actions.pagination.totalPages}
            onClick={actions.pagination.nextPage}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}