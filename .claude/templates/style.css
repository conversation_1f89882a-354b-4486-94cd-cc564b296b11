/* Component.module.css - CSS Modules Template */

/* Import shared modules when available */
@import '../../../styles/shared/components/buttons.module.css';
@import '../../../styles/shared/components/cards.module.css';
@import '../../../styles/shared/layouts/containers.module.css';

/* Main container */
.container {
  @apply space-y-6 p-6;
}

/* Header section */
.header {
  @apply flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6;
}

.title {
  @apply text-3xl font-bold text-gray-900 dark:text-white;
}

.subtitle {
  @apply text-gray-600 dark:text-gray-300;
}

/* Content sections */
.content {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

/* Card components */
.card {
  composes: cardBase from '../../../styles/shared/components/cards.module.css';
  @apply p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer;
}

.cardHeader {
  @apply flex justify-between items-start mb-3;
}

.cardTitle {
  @apply text-lg font-semibold text-gray-900 dark:text-white line-clamp-2;
}

.cardContent {
  @apply text-gray-700 dark:text-gray-300 text-sm line-clamp-3;
}

.cardFooter {
  @apply flex justify-between items-center mt-4 pt-3 border-t border-gray-200 dark:border-gray-700;
}

/* Status indicators */
.statusBadge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.statusActive {
  composes: statusBadge;
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.statusInactive {
  composes: statusBadge;
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

.statusPending {
  composes: statusBadge;
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

/* Loading states */
.loadingContainer {
  @apply animate-pulse space-y-4;
}

.loadingItem {
  @apply h-16 bg-gray-200 dark:bg-gray-700 rounded;
}

.skeleton {
  @apply h-4 bg-gray-200 dark:bg-gray-700 rounded;
}

/* Empty states */
.emptyContainer {
  @apply text-center py-12;
}

.emptyIcon {
  @apply h-12 w-12 text-gray-400 mx-auto mb-4;
}

.emptyTitle {
  @apply text-lg font-medium text-gray-900 dark:text-white mb-2;
}

.emptyDescription {
  @apply text-gray-600 dark:text-gray-400 mb-4;
}

/* Error states */
.errorContainer {
  @apply text-center py-8;
}

.errorIcon {
  @apply h-12 w-12 text-red-400 mx-auto mb-4;
}

.errorTitle {
  @apply text-lg font-medium text-red-900 dark:text-red-200 mb-2;
}

.errorDescription {
  @apply text-red-600 dark:text-red-400 mb-4;
}

/* Form elements */
.formGroup {
  @apply space-y-2;
}

.formLabel {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.formInput {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700;
}

.formError {
  @apply text-red-600 dark:text-red-400 text-sm;
}

/* Responsive utilities */
.hideOnMobile {
  @apply hidden sm:block;
}

.hideOnDesktop {
  @apply block sm:hidden;
}

.stackOnMobile {
  @apply flex flex-col sm:flex-row;
}

/* Dark mode specific styles */
:global(.dark) .customDarkStyle {
  background-color: theme('colors.gray.800');
  color: white;
}

/* Component-specific animations */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.fadeInScale {
  animation: fadeInScale 0.2s ease-out;
}

/* Hover effects */
.hoverScale {
  composes: transitionTransform from '../../../styles/shared/animations/transitions.module.css';
  transform: scale(1);
}

.hoverScale:hover {
  transform: scale(1.02);
}

/* Media queries for responsive design */
@media (max-width: theme('screens.sm')) {
  .container {
    @apply p-4 space-y-4;
  }
  
  .header {
    @apply flex-col items-start gap-2;
  }
  
  .content {
    @apply grid-cols-1 gap-4;
  }
}

@media (min-width: theme('screens.lg')) {
  .content {
    @apply grid-cols-3;
  }
}

@media (min-width: theme('screens.xl')) {
  .content {
    @apply grid-cols-4;
  }
}