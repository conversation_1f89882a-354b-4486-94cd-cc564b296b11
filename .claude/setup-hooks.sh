#!/bin/bash
# Setup pre-commit hooks to enforce coding standards for WordFormation project

echo "🚀 Setting up pre-commit hooks for WordFormation project..."

# Create hooks directory if it doesn't exist
mkdir -p .git/hooks

# Frontend pre-commit hook
echo "📁 Setting up frontend pre-commit hook..."
cat > .git/hooks/pre-commit-frontend << 'EOF'
#!/bin/bash
echo "🔍 Running frontend pre-commit checks..."

cd wf-frontend

# Check TypeScript compilation
echo "⚡ Checking TypeScript compilation..."
if ! npx tsc --noEmit; then
    echo "❌ TypeScript compilation failed! Please fix errors before committing."
    exit 1
fi

# Run ESLint
echo "🔧 Running ESLint..."
if ! npm run lint; then
    echo "❌ ESLint checks failed! Please fix linting errors before committing."
    echo "💡 Try running 'npm run lint:fix' to auto-fix some issues."
    exit 1
fi

# Run tests
echo "🧪 Running tests..."
if ! npm test; then
    echo "❌ Tests failed! Please fix failing tests before committing."
    exit 1
fi

echo "✅ Frontend pre-commit checks passed!"
cd ..
EOF

# Admin Portal pre-commit hook
echo "📁 Setting up admin portal pre-commit hook..."
cat > .git/hooks/pre-commit-admin << 'EOF'
#!/bin/bash
echo "🔍 Running admin portal pre-commit checks..."

cd wf-admin-portal

# Check TypeScript compilation
echo "⚡ Checking TypeScript compilation..."
if ! npx tsc --noEmit; then
    echo "❌ TypeScript compilation failed! Please fix errors before committing."
    exit 1
fi

# Run ESLint
echo "🔧 Running ESLint..."
if ! npm run lint; then
    echo "❌ ESLint checks failed! Please fix linting errors before committing."
    echo "💡 Try running 'npm run lint:fix' to auto-fix some issues."
    exit 1
fi

# Run tests
echo "🧪 Running tests..."
if ! npm test; then
    echo "❌ Tests failed! Please fix failing tests before committing."
    exit 1
fi

echo "✅ Admin portal pre-commit checks passed!"
cd ..
EOF

# Main pre-commit hook that runs both
echo "🔗 Setting up main pre-commit hook..."
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "🚀 WordFormation Pre-commit Hook"
echo "================================"

# Check if we're in the root directory
if [ ! -f "documentation/DEVELOPMENT_STANDARDS.md" ]; then
    echo "❌ Not in WordFormation root directory!"
    exit 1
fi

# Detect which files are being committed
FRONTEND_FILES=$(git diff --cached --name-only | grep "^wf-frontend/" | head -1)
ADMIN_FILES=$(git diff --cached --name-only | grep "^wf-admin-portal/" | head -1)

# Run frontend checks if frontend files are modified
if [ ! -z "$FRONTEND_FILES" ]; then
    echo "📂 Frontend files detected, running frontend checks..."
    if ! .git/hooks/pre-commit-frontend; then
        exit 1
    fi
fi

# Run admin portal checks if admin files are modified
if [ ! -z "$ADMIN_FILES" ]; then
    echo "📂 Admin portal files detected, running admin portal checks..."
    if ! .git/hooks/pre-commit-admin; then
        exit 1
    fi
fi

# Check coding standards compliance
echo "📋 Checking coding standards compliance..."

# Look for common violations in staged files
STAGED_TS_FILES=$(git diff --cached --name-only | grep "\\.tsx\\?$")

if [ ! -z "$STAGED_TS_FILES" ]; then
    echo "🔍 Checking for coding standard violations..."
    
    # Check for 'any' types
    if git diff --cached | grep -q ": any\|<any>\|any\[\]"; then
        echo "❌ Found 'any' types in staged files!"
        echo "💡 Please use proper TypeScript types instead of 'any'"
        echo "📖 See documentation/DEVELOPMENT_STANDARDS.md for guidance"
        exit 1
    fi
    
    # Check for direct API calls in .tsx files
    TSX_FILES=$(echo "$STAGED_TS_FILES" | grep "\\.tsx$")
    if [ ! -z "$TSX_FILES" ]; then
        if git diff --cached | grep -E "(fetch\(|axios\.|supabase\.)" | grep -q "\.tsx"; then
            echo "❌ Found direct API calls in .tsx files!"
            echo "💡 Use service layer and handler hooks instead"
            echo "📖 See documentation/DEVELOPMENT_STANDARDS.md for 3-file pattern"
            exit 1
        fi
    fi
    
    echo "✅ Coding standards check passed!"
fi

echo "🎉 All pre-commit checks passed! Ready to commit."
EOF

# Make hooks executable
chmod +x .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit-frontend
chmod +x .git/hooks/pre-commit-admin

echo ""
echo "✅ Pre-commit hooks installed successfully!"
echo ""
echo "📋 What was installed:"
echo "  • TypeScript compilation checks"
echo "  • ESLint validation"
echo "  • Test execution"
echo "  • Coding standards validation"
echo "  • 'any' type detection"
echo "  • Direct API call detection in .tsx files"
echo ""
echo "🔧 How to use:"
echo "  • Hooks run automatically on 'git commit'"
echo "  • Fix any errors before committing"
echo "  • Use 'npm run lint:fix' for auto-fixable issues"
echo ""
echo "⚙️  Manual execution:"
echo "  • Test frontend: .git/hooks/pre-commit-frontend"
echo "  • Test admin: .git/hooks/pre-commit-admin"
echo "  • Test both: .git/hooks/pre-commit"
echo ""
echo "📖 For more info, see documentation/DEVELOPMENT_STANDARDS.md"