# WordFormation API Documentation Overview

This documentation provides comprehensive API references for the WordFormation monorepo, a language learning platform built with React, TypeScript, and Supabase.

## 📚 Documentation Structure

### Core Modules

1. **[Types](modules/types.html)** - All TypeScript type definitions
   - API response types
   - Authentication interfaces  
   - Quiz and content types
   - Database schema types

2. **[Services](modules/services.html)** - Business logic and service layer
   - Base service classes with mixins
   - Repository call handlers
   - Validation and logging utilities

3. **[Repositories](modules/repositories.html)** - Data access layer
   - Base repository implementations
   - Authentication repositories
   - Storage repositories
   - Database providers

4. **[Utils](modules/utils.html)** - Utility functions
   - CSS class management
   - Date/time formatting
   - Mathematical calculations
   - Performance metrics

## 🏗️ Architecture Patterns

### Repository Pattern
The project implements a clean repository pattern:
- **BaseApiRepository** - Generic CRUD operations for API endpoints
- **BaseAuthRepository** - Authentication-specific operations
- **BaseStorageRepository** - Browser storage operations

### Service Layer with Mixins
Services use TypeScript mixins for reusable functionality:
- **WithRepositoryCalls** - Standardized repository call handling
- **WithLogging** - Structured logging capabilities  
- **WithValidation** - Input validation and sanitization
- **WithCaching** - In-memory caching with TTL support

### Type Safety
Comprehensive TypeScript types ensure:
- Consistent API contracts across the monorepo
- Type-safe database operations
- Standardized error handling

## 🚀 Quick Start

### Using Shared Types
```typescript
import { ApiResponse, QuizWithRelations } from 'wf-shared/types'

const response: ApiResponse<QuizWithRelations> = {
  success: true,
  data: { /* quiz data */ }
}
```

### Using Service Mixins
```typescript
import { BaseService } from 'wf-shared/services'

class MyService extends BaseService {
  async fetchData() {
    return this.callRepository(
      () => repositories.api.getData(),
      'Data fetch error',
      'Failed to fetch data'
    )
  }
}
```

### Using Utilities
```typescript
import { formatDate, calculatePercentage, cn } from 'wf-shared/utils'

const date = formatDate(new Date())
const score = calculatePercentage(85, 100)  
const className = cn('btn', { 'btn-primary': isActive })
```

## 📖 Key Features

- **Comprehensive JSDoc** - All public APIs documented with examples
- **TypeScript First** - Full type safety across the codebase
- **Monorepo Support** - Shared code across frontend and admin portal
- **Hybrid Architecture** - Clean separation of concerns
- **Modern Patterns** - Repository pattern, service mixins, and more

## 🔍 Navigation Tips

- Use the **search function** in the top toolbar to quickly find APIs
- Browse by **module** to understand the overall structure
- Check **interfaces** for data contract definitions
- Explore **classes** for implementation details
- Review **functions** for utility and helper methods

## 📝 Documentation Standards

All public APIs include:
- Clear descriptions of purpose and functionality
- Parameter types and descriptions
- Return type information
- Usage examples where applicable
- Notes about error conditions and edge cases

For questions or contributions, refer to the project repository.