<!DOCTYPE html><html class="default" lang="en" data-base="../"><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>APP_CONSTANTS | WordFormation Monorepo Documentation - v1.0.0</title><meta name="description" content="Documentation for WordFormation Monorepo Documentation"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script><script async src="../assets/hierarchy.js" id="tsd-hierarchy-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => window.app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><a href="../index.html" class="title">WordFormation Monorepo Documentation - v1.0.0</a><div id="tsd-toolbar-links"></div><button id="tsd-search-trigger" class="tsd-widget" aria-label="Search"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></button><dialog id="tsd-search" aria-label="Search"><input role="combobox" id="tsd-search-input" aria-controls="tsd-search-results" aria-autocomplete="list" aria-expanded="true" autocapitalize="off" autocomplete="off" placeholder="Search the docs" maxLength="100"/><ul role="listbox" id="tsd-search-results"></ul><div id="tsd-search-status" aria-live="polite" aria-atomic="true"><div>Preparing search index...</div></div></dialog><a href="#" class="tsd-widget menu" id="tsd-toolbar-menu-trigger" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb" aria-label="Breadcrumb"><li><a href="../modules/index.html">index</a></li><li><a href="" aria-current="page">APP_CONSTANTS</a></li></ul><h1>Variable APP_CONSTANTS<code class="tsd-tag">Const</code></h1></div><div class="tsd-signature"><span class="tsd-kind-variable">APP_CONSTANTS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">PAGINATION</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">DEFAULT_PAGE_SIZE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">6</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">ADMIN_PAGE_SIZES</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-keyword">readonly</span> <span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">10</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">25</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">50</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">100</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">PAGE_RANGE_DELTA</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">3</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">STORAGE_KEYS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">LANGUAGE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;language&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">THEME</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;theme&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">QUIZ_MODE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;quiz_mode&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">ADMIN_CREDENTIALS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;admin-remembered-credentials&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">USER_CREDENTIALS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;user-remembered-credentials&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">USER_SESSION</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;user-session&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">USER_PROGRESS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;user-progress&quot;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">UI</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">TOAST_LIMIT</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">1</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">TOAST_REMOVE_DELAY</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">1000000</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">MOBILE_BREAKPOINT</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">768</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">SIDEBAR</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">COOKIE_NAME</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;sidebar:state&quot;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">COOKIE_MAX_AGE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">WIDTH</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;16rem&quot;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">WIDTH_MOBILE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;18rem&quot;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">WIDTH_ICON</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;3rem&quot;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">KEYBOARD_SHORTCUT</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;b&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol"> = ...</span></div><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter"><h5 id="pagination"><code class="tsd-tag">Readonly</code><span class="tsd-kind-property">PAGINATION</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">DEFAULT_PAGE_SIZE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">6</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">ADMIN_PAGE_SIZES</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-keyword">readonly</span> <span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">10</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">25</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">50</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">100</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">PAGE_RANGE_DELTA</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">3</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></h5></li><li class="tsd-parameter"><h5 id="storage_keys"><code class="tsd-tag">Readonly</code><span class="tsd-kind-property">STORAGE_KEYS</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">LANGUAGE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;language&quot;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">THEME</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;theme&quot;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">QUIZ_MODE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;quiz_mode&quot;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">ADMIN_CREDENTIALS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;admin-remembered-credentials&quot;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">USER_CREDENTIALS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;user-remembered-credentials&quot;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">USER_SESSION</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;user-session&quot;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">USER_PROGRESS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;user-progress&quot;</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></h5></li><li class="tsd-parameter"><h5 id="ui"><code class="tsd-tag">Readonly</code><span class="tsd-kind-property">UI</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">TOAST_LIMIT</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">1</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">TOAST_REMOVE_DELAY</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">1000000</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">MOBILE_BREAKPOINT</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">768</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">SIDEBAR</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">COOKIE_NAME</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;sidebar:state&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">COOKIE_MAX_AGE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">WIDTH</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;16rem&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">WIDTH_MOBILE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;18rem&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">WIDTH_ICON</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;3rem&quot;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">KEYBOARD_SHORTCUT</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;b&quot;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></h5></li></ul></div><aside class="tsd-sources"><ul><li>Defined in src/constants/app.ts:4</li></ul></aside></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h3>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">WordFormation Monorepo Documentation - v1.0.0</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
