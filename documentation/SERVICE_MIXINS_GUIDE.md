# Service Architecture Guide

> **📍 Location**: This document is part of the centralized documentation in `/documentation/`. 
> **See Also**: [DEVELOPMENT_STANDARDS.md](DEVELOPMENT_STANDARDS.md), [TECHNICAL_ARCHITECTURE.md](TECHNICAL_ARCHITECTURE.md)

## Overview
This guide explains how to use the service architecture utilities available in wf-shared, including helper functions, TypeScript mixins, and the BaseService class for common service functionality.

## New Structure (Reorganized)
The service utilities have been moved to a dedicated `services` folder in wf-shared with better naming:

- `wf-shared/src/services/ServiceHelpers.ts` - Helper functions for error handling and responses
- `wf-shared/src/services/ServiceMixins.ts` - Individual TypeScript mixins
- `wf-shared/src/services/BaseService.ts` - Complete base service class with all mixins
- `wf-shared/src/services/index.ts` - Clean exports for easy importing

## Available Mixins

### 1. WithRepositoryCalls
Provides repository call functionality with consistent error handling.

**Methods:**
- `callRepository<T>(repository<PERSON>all, errorContext, defaultErrorMessage, logError?)` - Make a single repository call
- `callRepositoriesParallel<T>(calls)` - Make multiple repository calls in parallel
- `hasRepositoryErrors(results)` - Check if any repository calls failed
- `getFirstRepositoryError(results)` - Get the first error from repository results

### 2. WithLogging
Provides logging capabilities with context support.

**Methods:**
- `logInfo(message, context?)` - Log info messages
- `logError(message, error?, context?)` - Log error messages
- `logWarning(message, context?)` - Log warning messages
- `logDebug(message, context?)` - Log debug messages (development only)

### 3. WithValidation
Provides input validation and sanitization.

**Methods:**
- `validateRequired(params, requiredFields)` - Validate required parameters
- `validateEmail(email)` - Validate email format
- `validateId(id)` - Validate UUID format
- `sanitizeString(input)` - Sanitize string input

### 4. WithCaching
Provides in-memory caching with TTL support.

**Methods:**
- `getCached<T>(key)` - Get cached data if available and not expired
- `setCached<T>(key, data, ttlMs?)` - Set data in cache with TTL (default 5 minutes)
- `clearCached(key)` - Clear specific cache entry
- `clearAllCache()` - Clear all cache

## Usage Examples

### Simple Usage (Recommended)
```typescript
import { BaseService } from 'wf-shared'

export class UserService extends BaseService {
  static async getUser(id: string) {
    const service = new UserService()
    
    // Validate input
    const validation = service.validateRequired({ id }, ['id'])
    if (validation.error) return validation
    
    // Log operation
    service.logInfo(`Fetching user: ${id}`)
    
    // Check cache
    const cached = service.getCached(`user_${id}`)
    if (cached) return { data: cached, error: null }
    
    // Make repository call
    const result = await service.callRepository(
      () => repositories.userApi.getById(id),
      'User fetch error',
      'Failed to fetch user'
    )
    
    // Cache result
    if (result.data) {
      service.setCached(`user_${id}`, result.data)
    }
    
    return result
  }
}
```

### Combined Mixin Usage
```typescript
import { WithAllServiceMixins, RepositoryCallable } from 'wf-shared'

export class CustomService extends WithAllServiceMixins(class implements RepositoryCallable {}) {
  static async processData(data: any) {
    const service = new CustomService()
    
    // All mixin functionality available
    service.logInfo('Processing data')
    
    const result = await service.callRepository(...)
    return result
  }
}
```

### Individual Mixin Usage
```typescript
import { WithLogging, WithValidation } from 'wf-shared'

export class ValidationService extends WithValidation(WithLogging(class {})) {
  static async validateUser(userData: any) {
    const service = new ValidationService()
    
    // Only validation and logging functionality
    service.logInfo('Validating user data')
    
    const validation = service.validateRequired(userData, ['email', 'name'])
    if (validation.error) {
      service.logError('Validation failed', validation.error)
      return validation
    }
    
    return { data: userData, error: null }
  }
}
```

### Parallel Repository Calls
```typescript
export class DashboardService extends BaseService {
  static async getDashboardData() {
    const service = new DashboardService()
    
    const results = await service.callRepositoriesParallel([
      {
        call: () => repositories.userApi.getCount(),
        errorContext: 'User count error',
        defaultErrorMessage: 'Failed to get user count'
      },
      {
        call: () => repositories.quizApi.getCount(),
        errorContext: 'Quiz count error',
        defaultErrorMessage: 'Failed to get quiz count'
      }
    ])
    
    if (service.hasRepositoryErrors(results)) {
      const error = service.getFirstRepositoryError(results)
      return { data: null, error }
    }
    
    const [userCount, quizCount] = results
    return {
      data: {
        users: userCount.data,
        quizzes: quizCount.data
      },
      error: null
    }
  }
}
```

## Benefits

1. **Code Reuse**: Common functionality shared across all services
2. **Consistency**: Standardized error handling, logging, and validation
3. **Type Safety**: Full TypeScript support with proper typing
4. **Flexibility**: Choose only the mixins you need
5. **Maintainability**: Centralized common functionality in wf-shared
6. **Testing**: Easier to test and mock individual functionality

## Best Practices

1. **Use BaseService** for most cases - it includes all functionality
2. **Use individual mixins** when you only need specific functionality
3. **Always validate inputs** using the validation mixin
4. **Log operations** for better debugging and monitoring
5. **Cache expensive operations** using the caching mixin
6. **Handle errors consistently** using the repository call mixin

## Migration from Old Pattern

### Before (Instance-based with local helpers)
```typescript
import { handleRepositoryCall } from './helpers'

export class UserService {
  async getUser(id: string) {
    return handleRepositoryCall(...)
  }
}
```

### After (Static with shared mixins)
```typescript
import { BaseService } from 'wf-shared'

export class UserService extends BaseService {
  static async getUser(id: string) {
    const service = new UserService()
    return service.callRepository(...)
  }
}
```
