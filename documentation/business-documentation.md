# Business Documentation for English Learning Web Application

## Product Overview and Target Audience

This web application is designed to enhance English language skills, specifically focusing on word formation, suffixes, prefixes, and parts of speech (noun, verb, adjective, adverb) through interactive quizzes. The application targets high school and adult learners at CEFR levels A2 to C2, providing a **flexible, personalized learning experience** where users can choose any combination of CEFR level and grammar category based on their interests and learning goals.

The primary goal is to facilitate grammar learning with engaging content, immediate feedback in practice mode, and comprehensive review in test mode, all while tracking user progress across multiple categories to motivate continued learning and skill development.

- **Target Audience**: High school students and adult learners seeking to improve their English grammar and vocabulary skills, ranging from A2 (elementary) to C2 (proficiency) CEFR levels.

## 📋 **Project Vision**

The WordFormation project aims to create an engaging English learning platform that empowers students to master grammar through personalized, interactive learning experiences. Our vision is to provide flexible, comprehensive grammar education that adapts to individual learning needs and goals.

## User Stories

### Student Perspective
- **As a student, I want to choose any CEFR level and grammar category combination, so I can focus on specific areas of interest or weakness.**
- **As a student, I want to practice B1 suffixes even though I'm registered as A2, so I can challenge myself in specific grammar areas.**
- **As a student, I want to focus only on prefixes across all levels, so I can master this specific grammar concept.**
- **As a student, I want to review A2 basics while working on B2 content, so I can reinforce fundamentals while advancing.**
- As a student, I want to track my quiz progress by category and level, so I can see my improvement in specific grammar areas over time.
- As a student, I want to choose between Practice and Test modes, so I can learn with immediate feedback or test my knowledge without hints.
- As a student, I want to see a summary after each quiz, so I can review my performance and decide to retry or move forward.
- **As a student, I want to see recommended quizzes based on my performance, so I can follow an optimal learning path.**
- **As a student, I want to see my progress across all categories and levels in a visual dashboard, so I can understand my overall learning journey.**
- **As a student, I want clear feedback during the registration process, so I understand when email confirmation is required and what steps to take next.**
- **As a student, I want to be able to resend confirmation emails, so I can complete my registration if I don't receive the initial email.**
- **As a student, I want informative error messages during authentication, so I understand what went wrong and how to fix it.**

### Admin Perspective
- As an admin, I want to create and edit quiz questions for all CEFR levels and categories, so I can ensure comprehensive content coverage.
- **As an admin, I want to tag questions by CEFR level, category, and difficulty, so quizzes are appropriately challenging and properly categorized.**
- As an admin, I want to preview quizzes, so I can verify the content before publishing.
- **As an admin, I want to monitor content balance across all level/category combinations, so I can ensure adequate quiz availability.**
- **As an admin, I want to analyze user engagement patterns across categories, so I can optimize content and user experience.**
- **As an admin, I want to manage user accounts and email confirmation status, so I can assist users with authentication issues.**
- **As an admin, I want to configure email confirmation settings, so I can balance security with user experience.**

## Functional Requirements

### Quiz Selection System
- **Flexible Level Selection**: Users can choose any CEFR level (A2, B1, B2, C1, C2) regardless of their registered level
- **Category-Based Learning**: Users can select from four grammar categories:
  - **Word Formation**: General word transformation exercises
  - **Suffixes**: Focus on suffix patterns (-tion, -ness, -ful, -ly, etc.)
  - **Prefixes**: Focus on prefix patterns (un-, re-, pre-, dis-, etc.)
  - **Parts of Speech**: Noun/verb/adjective/adverb transformations
- **Two-Dimensional Quiz Browser**: Visual interface showing available quizzes in a Level × Category grid
- **Progress Tracking by Category**: Separate progress tracking for each CEFR level and category combination
- **Smart Recommendations**: Suggest appropriate quizzes based on user performance and learning patterns
- **Difficulty Indicators**: Visual cues showing quiz difficulty within each category and level

### Authentication System
- **Email Confirmation Required**: Users must confirm their email address before accessing the application
- **User Registration**: Collect email, password, full name, and preferred starting CEFR level
- **Secure Access**: Authentication required for progress tracking and personalized experiences

### Quiz System
- Each quiz consists of 20 multiple-choice questions (4 choices per question), displayed one at a time.
- **Content Organization**: Each CEFR level (A2 to C2) has multiple quizzes for each of the 4 categories (word_formation, suffixes, prefixes, parts_of_speech)
- **Open Access Model**: Users can attempt any level/category combination without restrictions
- **Recommendation Engine**: Suggest appropriate difficulty based on user performance history
- **Warning System**: Alert users when attempting significantly higher levels than their demonstrated ability
- Quiz questions are stored in a database, accessible for creation and editing via an admin panel.

### Modes
- **Practice Mode**: After selecting an option, the application shows the correct answer, a brief grammar rule (e.g., suffix usage), and an example sentence to reinforce learning.
- **Test Mode**: No explanations are provided during the quiz. After completion, an answer sheet is displayed with the user's score, correct/incorrect answers, and correct answers for review.

### Progress Tracking
- **Category-Level Progress**: Track completion, accuracy, and improvement for each CEFR level × category combination
- **Overall Progress Dashboard**: Aggregate view showing progress across all categories and levels
- **Visual Progress Indicators**: Progress bars, completion percentages, and achievement badges for each category
- **Performance Analytics**: Track learning patterns, identify strengths and weaknesses, suggest focus areas
- **Achievement System**: Badges for category completion, cross-level mastery, streaks, and special accomplishments
- **Learning Path Visualization**: Show recommended progression through categories and levels
- Users must log in to track progress.

### User Flow
- **Dashboard**: Visual overview of all available quizzes organized by level and category
- **Quiz Selection**: Two-step selection process (Level → Category) with visual indicators
- **Mode Selection**: Choose Practice or Test mode with clear differentiation
- **Quiz Taking**: Answer questions with immediate feedback (Practice) or delayed feedback (Test)
- **Results & Recommendations**: View performance and get suggestions for next quizzes
- Handle edge cases:
  - **Quiz Abandonment**: Save progress if a user leaves a quiz incomplete, allowing resumption later
  - **Network Errors**: Display user-friendly messages and implement additional error-handling mechanisms to ensure data consistency and user experience

### Authentication Requirements
- User authentication is required for user login and registration to enable progress tracking and personalized experiences.

## Non-Functional Requirements

### Performance Requirements
- Fast page loading and responsive user interface
- Efficient quiz browser supporting multiple categories and levels
- Quick authentication and form submission processes

### Scalability Requirements
- Support for up to 1,000 concurrent users
- Scalable content management across 4 categories × 5 CEFR levels
- Efficient progress tracking and analytics

### Accessibility Requirements
- WCAG 2.1 Level AA compliance for users with disabilities
- Keyboard and screen reader navigation support
- Clear announcements for category and level selection

### User Experience Requirements
- Intuitive quiz selection interface with clear visual hierarchy
- Comprehensive progress visualization across categories and levels
- Smart recommendations for optimal learning paths
- Graceful error handling with helpful guidance
- Consistent visual feedback and automatic navigation

### Security and Privacy Requirements
- Mandatory email confirmation for account security
- Secure user data protection and session management
- Controlled but flexible quiz content access

### Localization Requirements
- English UI with infrastructure for future language support
- Customizable email templates for branding

## Content Requirements

### Question Bank Requirements
- **Comprehensive Coverage**: Minimum 20 questions per CEFR level per category (400+ total questions)
- **Quality Standards**: All questions must include explanations, grammar rules, and example usage
- **Difficulty Progression**: Questions within each category should progress from basic to advanced
- **Category-Specific Focus**:
  - **Word Formation**: General transformation exercises
  - **Suffixes**: Specific suffix patterns and rules
  - **Prefixes**: Specific prefix patterns and rules  
  - **Parts of Speech**: Focused noun/verb/adjective/adverb transformations

---

*For technical implementation details, database schema, and system architecture, see [Technical Specifications](technical-specifications.md)*

*For current implementation status and task tracking, see [Implementation Roadmap](implementation-roadmap.md)*
