# English Learning Web Application - Testing Guide

## 📋 Overview

This document provides comprehensive testing guidelines for the English Learning Web Application, including manual testing checklists, automation scenarios, and regression test procedures.

**Application URL**: http://localhost:5174/ (or http://localhost:5173/)
**Supabase Project**: Word Formation (famwultgmmzlkmtzumhq)
**Database**: PostgreSQL with Row Level Security (RLS)

---

## 🚨 **IMPORTANT: Email Confirmation Issue**

**Current Issue**: The Supabase project has email confirmation enabled, which means:
- Sign-up creates the user account but requires email verification
- Users won't be automatically signed in after registration
- The loading spinner may appear to hang because the user isn't immediately authenticated

**Testing Workaround**:
1. Use a real email address for sign-up testing
2. Check your email for the confirmation link
3. Click the confirmation link to verify the account
4. Then try signing in with the same credentials

**Alternative**: For testing purposes, you can manually confirm users in the database:
```sql
-- Manually confirm a user for testing
UPDATE auth.users 
SET email_confirmed_at = NOW() 
WHERE email = '<EMAIL>';
```

---

## 🔧 Test Environment Setup

**For complete environment setup instructions, see [Technical Specifications - Environment Setup](TECHNICAL_SPECIFICATIONS.md#environment-setup)**

### Testing-Specific Configuration
- Ensure test database is configured
- Use test-specific environment variables
- Verify all dependencies are installed

### Running Tests
```bash
npm test          # Run all tests
npm run test:ui   # Run tests with UI (if available)
```

---

## 🧪 Manual Testing Checklist

### 1. Authentication Flow Testing

#### 1.1 Sign Up Process
- [ ] **Navigate to Sign Up**
  - URL: `/` (redirects to login if not authenticated)
  - Click "Sign Up" tab/button
  
- [ ] **Form Validation**
  - [ ] Empty email field shows validation error
  - [ ] Invalid email format shows error
  - [ ] Password less than 6 characters shows error
  - [ ] Password confirmation mismatch shows error
  - [ ] CEFR level selection is required
  
- [ ] **Successful Sign Up** ⚠️ **Email Confirmation Required**
  - [ ] Use valid email format: `test+{timestamp}@example.com` or real email
  - [ ] Password: minimum 6 characters
  - [ ] Confirm password matches
  - [ ] Select CEFR level (A2, B1, B2, C1, C2)
  - [ ] Success message appears: "Account created successfully! Please check your email..."
  - [ ] User profile created in Supabase `users` table
  - [ ] **Check email for confirmation link**
  - [ ] **Click confirmation link to verify account**
  - [ ] Form automatically switches to sign-in mode after 3 seconds
  
- [ ] **Error Handling**
  - [ ] Duplicate email shows appropriate error
  - [ ] Network errors handled gracefully
  - [ ] Loading states displayed during submission
  - [ ] Loading spinner stops after response (success or error)

#### 1.2 Sign In Process
- [ ] **Navigate to Sign In**
  - Default view on `/` when not authenticated
  
- [ ] **Form Validation**
  - [ ] Empty fields show validation errors
  - [ ] Invalid email format shows error
  
- [ ] **Successful Sign In**
  - [ ] Use confirmed email credentials (after email verification)
  - [ ] Success message appears
  - [ ] Redirected to dashboard (`/dashboard`)
  - [ ] User session established
  
- [ ] **Error Handling**
  - [ ] Invalid credentials show error
  - [ ] Unconfirmed email shows appropriate error
  - [ ] Account not found shows error
  - [ ] Network errors handled gracefully

#### 1.3 Authentication State Management
- [ ] **Session Persistence**
  - [ ] Refresh page maintains login state
  - [ ] Browser restart maintains session (if remember me)
  - [ ] Session expires appropriately
  
- [ ] **Protected Routes**
  - [ ] Unauthenticated users redirected to login
  - [ ] Authenticated users can access dashboard
  - [ ] Navigation updates based on auth state

#### 1.4 Sign Out Process
- [ ] **Sign Out Functionality**
  - [ ] Sign out button visible when authenticated
  - [ ] Click sign out clears session
  - [ ] Redirected to login page
  - [ ] Cannot access protected routes after sign out

### 2. Dashboard Testing

#### 2.1 User Profile Display
- [ ] **Profile Information**
  - [ ] User name displayed correctly
  - [ ] Email displayed correctly
  - [ ] CEFR level displayed correctly
  - [ ] Join date/created date shown
  
- [ ] **Profile Editing** (if implemented)
  - [ ] Can update name
  - [ ] Can update CEFR level
  - [ ] Changes saved to database
  - [ ] Validation on profile updates

#### 2.2 Quiz Navigation
- [ ] **Available Quizzes**
  - [ ] A2 Word Formation quiz visible
  - [ ] Quiz metadata displayed (title, description, questions count)
  - [ ] Difficulty level and time limit shown
  - [ ] Quiz cards are clickable
  
- [ ] **Quiz Filtering** (if implemented)
  - [ ] Filter by CEFR level
  - [ ] Filter by category
  - [ ] Filter by difficulty

#### 2.3 Progress Overview
- [ ] **Statistics Display**
  - [ ] Total quizzes attempted
  - [ ] Average score
  - [ ] Best score
  - [ ] Current streak
  - [ ] Longest streak
  
- [ ] **Progress Charts** (if implemented)
  - [ ] Progress over time
  - [ ] Performance by category
  - [ ] CEFR level progression

### 3. Quiz System Testing

#### 3.1 Quiz Initialization
- [ ] **Starting a Quiz**
  - [ ] Click on A2 Word Formation quiz
  - [ ] Quiz introduction/instructions displayed
  - [ ] Start quiz button functional
  - [ ] Timer starts (if applicable)
  
- [ ] **Quiz Mode Selection** (if implemented)
  - [ ] Practice mode available
  - [ ] Test mode available
  - [ ] Mode affects scoring/tracking

#### 3.2 Question Flow
- [ ] **Question Display**
  - [ ] Question text clear and readable
  - [ ] Word in parentheses highlighted (e.g., "TEACH")
  - [ ] Four multiple choice options displayed
  - [ ] Question number and total shown (e.g., "1 of 20")
  
- [ ] **Answer Selection**
  - [ ] Can select one option
  - [ ] Selected option highlighted
  - [ ] Can change selection before submitting
  - [ ] Submit button enabled after selection
  
- [ ] **Navigation**
  - [ ] Next button advances to next question
  - [ ] Previous button returns to previous question (if enabled)
  - [ ] Progress indicator updates
  - [ ] Cannot skip questions without answering

#### 3.3 Question Content Validation
- [ ] **A2 Word Formation Questions** (Sample validation)
  - [ ] Question 1: "She is a very good ____." (TEACH) → Answer: "teacher"
  - [ ] Question 2: "He drives very ____." (CARE) → Answer: "carefully"
  - [ ] Question 3: "This book is very ____." (HELP) → Answer: "helpful"
  - [ ] All 20 questions load correctly
  - [ ] Options are randomized (if implemented)

#### 3.4 Answer Feedback
- [ ] **Immediate Feedback** (if enabled)
  - [ ] Correct answers highlighted in green
  - [ ] Incorrect answers highlighted in red
  - [ ] Explanation text displayed
  - [ ] Grammar rule shown
  - [ ] Example usage provided
  
- [ ] **End-of-Quiz Feedback**
  - [ ] Final score calculated correctly
  - [ ] Percentage score displayed
  - [ ] Number of correct answers shown
  - [ ] Time taken displayed (if timed)
  - [ ] Performance summary provided

#### 3.5 Quiz Completion
- [ ] **Score Calculation**
  - [ ] Correct answers counted accurately
  - [ ] Percentage calculated correctly (correct/total * 100)
  - [ ] Score saved to database
  
- [ ] **Data Persistence**
  - [ ] Quiz attempt saved to `quiz_attempts` table
  - [ ] Individual answers saved to `user_answers` table
  - [ ] User progress updated in `user_progress` table
  - [ ] Streak calculations updated correctly

### 4. Database Integration Testing

#### 4.1 User Management
- [ ] **User Creation**
  - [ ] New user record in `auth.users` table
  - [ ] Corresponding record in `public.users` table
  - [ ] Trigger function executes correctly
  - [ ] Default CEFR level set if not provided
  - [ ] Email confirmation status tracked correctly
  
- [ ] **User Profile Updates**
  - [ ] Profile changes reflected in database
  - [ ] Updated timestamp modified
  - [ ] RLS policies enforced (users can only see/edit own data)

#### 4.2 Quiz Data Integrity
- [ ] **Quiz Attempts**
  - [ ] New record created for each quiz attempt
  - [ ] User ID correctly linked
  - [ ] Quiz ID correctly linked
  - [ ] Mode (practice/test) recorded
  - [ ] Timestamps accurate
  
- [ ] **Answer Recording**
  - [ ] All 20 answers recorded
  - [ ] Correct/incorrect status accurate
  - [ ] Time taken per question (if tracked)
  - [ ] Foreign key relationships maintained

#### 4.3 Progress Tracking
- [ ] **Progress Updates**
  - [ ] Total attempts incremented
  - [ ] Total correct answers accumulated
  - [ ] Average score recalculated
  - [ ] Best score updated if improved
  - [ ] Streak calculations correct
  
- [ ] **Badge System** (if implemented)
  - [ ] "First Steps" badge awarded after first quiz
  - [ ] "Perfect Score" badge for 100% score
  - [ ] Streak badges for consecutive good scores
  - [ ] Level completion badges

### 5. User Interface Testing

#### 5.1 Responsive Design
- [ ] **Mobile View (320px - 768px)**
  - [ ] Login form responsive
  - [ ] Dashboard layout adapts
  - [ ] Quiz questions readable
  - [ ] Navigation accessible
  - [ ] Touch targets appropriate size
  
- [ ] **Tablet View (768px - 1024px)**
  - [ ] Layout optimized for tablet
  - [ ] Content properly spaced
  - [ ] Navigation intuitive
  
- [ ] **Desktop View (1024px+)**
  - [ ] Full layout utilized
  - [ ] Sidebar navigation (if applicable)
  - [ ] Content well-organized

#### 5.2 Accessibility
- [ ] **Keyboard Navigation**
  - [ ] Tab order logical
  - [ ] All interactive elements accessible
  - [ ] Focus indicators visible
  - [ ] Enter key submits forms
  
- [ ] **Screen Reader Support**
  - [ ] Alt text for images
  - [ ] Proper heading hierarchy
  - [ ] Form labels associated
  - [ ] ARIA attributes where needed
  
- [ ] **Color and Contrast**
  - [ ] Sufficient color contrast
  - [ ] Information not conveyed by color alone
  - [ ] Focus indicators visible

#### 5.3 Performance
- [ ] **Loading Times**
  - [ ] Initial page load < 3 seconds
  - [ ] Quiz questions load quickly
  - [ ] Smooth transitions between questions
  - [ ] No unnecessary re-renders
  
- [ ] **Error Handling**
  - [ ] Network errors displayed clearly
  - [ ] Retry mechanisms available
  - [ ] Graceful degradation

---

## 🤖 Automation Test Scenarios

### Test Data Management

#### Test User Accounts
```javascript
const testUsers = {
  newUser: {
    email: `test+${Date.now()}@example.com`,
    password: 'TestPass123!',
    name: 'Test User',
    cefrLevel: 'A2'
  },
  existingUser: {
    email: '<EMAIL>',
    password: 'ExistingPass123!',
    name: 'Existing User',
    cefrLevel: 'B1'
  }
};
```

#### Database Test Data
```sql
-- Clean up test data
DELETE FROM user_answers WHERE attempt_id IN (
  SELECT id FROM quiz_attempts WHERE user_id IN (
    SELECT id FROM users WHERE email LIKE '<EMAIL>'
  )
);
DELETE FROM quiz_attempts WHERE user_id IN (
  SELECT id FROM users WHERE email LIKE '<EMAIL>'
);
DELETE FROM user_progress WHERE user_id IN (
  SELECT id FROM users WHERE email LIKE '<EMAIL>'
);
DELETE FROM users WHERE email LIKE '<EMAIL>';

-- Manually confirm test users for automated testing
UPDATE auth.users 
SET email_confirmed_at = NOW() 
WHERE email LIKE '<EMAIL>';
```

### E2E Test Scenarios

#### Scenario 1: Complete User Journey
```javascript
describe('Complete User Journey', () => {
  it('should allow user to sign up, take quiz, and view results', async () => {
    // 1. Sign up new user
    await signUp(testUsers.newUser);
    
    // 2. Manually confirm email for testing
    await confirmUserEmail(testUsers.newUser.email);
    
    // 3. Sign in with confirmed account
    await signIn(testUsers.newUser);
    
    // 4. Navigate to dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // 5. Start A2 quiz
    await page.click('[data-testid="a2-word-formation-quiz"]');
    
    // 6. Complete all 20 questions
    for (let i = 1; i <= 20; i++) {
      await answerQuestion(i, getCorrectAnswer(i));
      await page.click('[data-testid="next-question"]');
    }
    
    // 7. Verify results
    await expect(page.locator('[data-testid="final-score"]')).toBeVisible();
    
    // 8. Verify database records
    const attempt = await getQuizAttempt(testUsers.newUser.email);
    expect(attempt.score).toBeGreaterThan(0);
    expect(attempt.is_completed).toBe(true);
  });
});
```

#### Scenario 2: Authentication Flow
```javascript
describe('Authentication Flow', () => {
  it('should handle complete auth cycle', async () => {
    // Test sign up
    await testSignUp();
    
    // Test email confirmation requirement
    await testEmailConfirmation();
    
    // Test sign in with confirmed account
    await testSignIn();
    
    // Test sign out
    await testSignOut();
    
    // Test protected routes
    await testProtectedRoutes();
  });
});
```

#### Scenario 3: Quiz Functionality
```javascript
describe('Quiz Functionality', () => {
  beforeEach(async () => {
    await signIn(testUsers.existingUser);
  });
  
  it('should handle quiz completion correctly', async () => {
    await startQuiz('A2 Word Formation');
    await completeQuiz(correctAnswers);
    await verifyQuizResults();
    await verifyDatabaseUpdates();
  });
  
  it('should handle quiz interruption and resume', async () => {
    await startQuiz('A2 Word Formation');
    await answerPartialQuiz(10); // Answer first 10 questions
    await page.reload(); // Simulate interruption
    await verifyQuizState(); // Should resume from question 11
  });
});
```

### API Testing Scenarios

#### Supabase API Tests
```javascript
describe('Supabase Integration', () => {
  it('should create user profile on signup', async () => {
    const response = await supabase.auth.signUp(testUsers.newUser);
    expect(response.error).toBeNull();
    
    // Verify user profile created
    const { data: profile } = await supabase
      .from('users')
      .select('*')
      .eq('email', testUsers.newUser.email)
      .single();
    
    expect(profile).toBeTruthy();
    expect(profile.cefr_level).toBe(testUsers.newUser.cefrLevel);
  });
  
  it('should enforce RLS policies', async () => {
    // Try to access another user's data
    const { data, error } = await supabase
      .from('quiz_attempts')
      .select('*')
      .eq('user_id', 'other-user-id');
    
    expect(data).toHaveLength(0); // Should not return other user's data
  });
  
  it('should handle email confirmation requirement', async () => {
    const { data, error } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'password123'
    });
    
    expect(error).toBeNull();
    expect(data.user.email_confirmed_at).toBeNull(); // Should require confirmation
  });
});
```

---

## 🔄 Regression Testing Procedures

### Pre-Release Testing Checklist

#### Critical Path Testing (Must Pass)
- [ ] User can sign up with new account (with email confirmation)
- [ ] User can confirm email and sign in
- [ ] User can complete A2 Word Formation quiz
- [ ] Quiz results are saved correctly
- [ ] User progress is updated
- [ ] User can sign out successfully

#### Extended Testing (Should Pass)
- [ ] All form validations work
- [ ] Responsive design functions on all screen sizes
- [ ] Error handling works for network issues
- [ ] Database integrity maintained
- [ ] Performance meets requirements
- [ ] Email confirmation flow works correctly

#### Browser Compatibility
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

### Automated Regression Suite

#### Daily Smoke Tests
```bash
# Run critical path tests
npm run test:smoke

# Run API integration tests
npm run test:api

# Run database integrity tests
npm run test:db
```

#### Weekly Full Regression
```bash
# Run all tests
npm run test:all

# Run performance tests
npm run test:performance

# Run accessibility tests
npm run test:a11y

# Run cross-browser tests
npm run test:cross-browser
```

### Performance Benchmarks

#### Load Time Targets
- Initial page load: < 3 seconds
- Quiz question transitions: < 500ms
- Form submissions: < 2 seconds
- Database queries: < 1 second

#### Memory Usage
- Initial load: < 50MB
- After quiz completion: < 100MB
- No memory leaks during extended use

---

## 🐛 Bug Reporting Template

### Bug Report Format
```markdown
## Bug Report

**Title**: [Brief description of the issue]

**Environment**:
- Browser: [Chrome/Firefox/Safari/Edge + version]
- OS: [Windows/macOS/Linux + version]
- Screen size: [Desktop/Tablet/Mobile]
- URL: [Specific page where bug occurred]

**Steps to Reproduce**:
1. [First step]
2. [Second step]
3. [Third step]

**Expected Behavior**:
[What should happen]

**Actual Behavior**:
[What actually happened]

**Screenshots/Videos**:
[Attach if applicable]

**Console Errors**:
[Any JavaScript errors from browser console]

**Database State**:
[Relevant database records if applicable]

**Email Confirmation Status**:
[If related to authentication, include email confirmation status]

**Severity**: [Critical/High/Medium/Low]
**Priority**: [P1/P2/P3/P4]
```

---

## 📊 Test Metrics and Reporting

### Key Metrics to Track
- Test pass rate (target: >95%)
- Test execution time
- Code coverage (target: >80%)
- Bug detection rate
- Mean time to resolution
- Email confirmation success rate

### Test Reports
- Daily test execution summary
- Weekly regression test report
- Monthly quality metrics dashboard
- Release readiness report

---

## 🔧 MCP Integration for Automation

### MCP Test Commands
```javascript
// Example MCP commands for automated testing
const mcpCommands = {
  // Database operations
  createTestUser: 'mcp_supabase_execute_sql',
  confirmTestUser: 'mcp_supabase_execute_sql',
  cleanupTestData: 'mcp_supabase_execute_sql',
  verifyQuizData: 'mcp_supabase_execute_sql',
  
  // Application testing
  runSmokeTests: 'run_terminal_cmd',
  runFullTestSuite: 'run_terminal_cmd',
  checkPerformance: 'run_terminal_cmd',
  
  // Environment management
  setupTestEnv: 'edit_file',
  deployToStaging: 'run_terminal_cmd',
  rollbackChanges: 'run_terminal_cmd'
};
```

### Automated Test Execution
```bash
# MCP-driven test execution
mcp run-test-suite --environment=staging --suite=regression
mcp verify-database --project=word-formation --checks=integrity
mcp generate-report --type=quality --period=weekly
```

---

## 📝 Notes for Future Development

### Test Coverage Gaps
- [ ] Multi-language support testing
- [ ] Offline functionality testing
- [ ] Advanced quiz features (hints, bookmarks)
- [ ] Social features (leaderboards, sharing)
- [ ] Payment integration testing (if premium features added)
- [ ] Email confirmation automation for testing

### Automation Opportunities
- [ ] Visual regression testing
- [ ] Load testing with multiple concurrent users
- [ ] Automated accessibility scanning
- [ ] Cross-browser testing automation
- [ ] Database migration testing
- [ ] Email confirmation testing automation

### Monitoring and Alerting
- [ ] Real-time error tracking
- [ ] Performance monitoring
- [ ] User behavior analytics
- [ ] Database performance metrics
- [ ] Uptime monitoring
- [ ] Email delivery monitoring

---

## 🔧 **Quick Fix for Testing**

### Manual Email Confirmation for Testing
If you need to test the application without waiting for email confirmation:

1. **Sign up with any email**
2. **Run this SQL command in Supabase**:
```sql
UPDATE auth.users 
SET email_confirmed_at = NOW() 
WHERE email = '<EMAIL>';
```
3. **Now you can sign in normally**

### Disable Email Confirmation (Production Setting)
To disable email confirmation entirely (not recommended for production):
1. Go to Supabase Dashboard → Authentication → Settings
2. Turn off "Enable email confirmations"
3. Users will be automatically confirmed on sign-up

---

**Document Version**: 1.1  
**Last Updated**: June 14, 2024  
**Next Review**: July 14, 2024 