# Claude Configuration for WordFormation Project

This directory contains configuration files and templates to ensure <PERSON> follows the project's coding standards consistently.

## 📁 Directory Structure

```
.claude/
├── CODING_STANDARDS.md    # Comprehensive coding standards for Claude
├── README.md              # This file - explains .claude folder purpose
├── settings.local.json    # Claude settings with hooks and permissions
├── setup-hooks.sh         # Script to install git pre-commit hooks
├── .mcp.json             # MCP (Model Context Protocol) configuration
└── templates/             # Updated code templates for current architecture
    ├── component.tsx      # React component template (CSS modules)
    ├── handler.ts         # Handler hook template (wf-shared imports)
    ├── style.css          # CSS Modules template (not .ts)
    └── index.ts           # Export template
```

## 🤖 For Claude: Essential Files

### 1. **CODING_STANDARDS.md** - Complete Development Rules
- Mandatory workflow steps and non-negotiable rules
- 3-file pattern architecture (Component.tsx → Component.handler.ts → Component.module.css)
- TypeScript strict conventions and patterns
- Service layer standards and repository pattern
- CSS Modules integration with shared patterns
- Development checklists and quality gates

### 2. **Main Project Documentation**
For general development guidance, reference:
- **[documentation/DEVELOPMENT_STANDARDS.md](../documentation/DEVELOPMENT_STANDARDS.md)** - Comprehensive coding guidelines
- **[documentation/TECHNICAL_ARCHITECTURE.md](../documentation/TECHNICAL_ARCHITECTURE.md)** - System design patterns
- **[documentation/CSS_ARCHITECTURE.md](../documentation/CSS_ARCHITECTURE.md)** - CSS modules system

## 🛠️ Setup Instructions

### For Developers

1. **Install pre-commit hooks:**
   ```bash
   cd /path/to/WordFormation
   ./.claude/setup-hooks.sh
   ```

2. **Verify setup:**
   ```bash
   # Test TypeScript compilation
   cd wf-frontend && npx tsc --noEmit
   cd wf-admin-portal && npx tsc --noEmit
   
   # Test linting
   npm run lint
   
   # Test hooks
   .git/hooks/pre-commit
   ```

### For Claude Users

When working with Claude, always:

1. **Reference the rules first:**
   ```
   "Please read .claude/CODING_STANDARDS.md before starting"
   ```

2. **Specify validation requirements:**
   ```
   "After changes, run TypeScript and lint checks"
   ```

3. **Use explicit task instructions:**
   ```
   "Follow the 3-file pattern and service layer requirements"
   ```

## 🔧 Configuration Details

### settings.local.json
- **Permissions**: Allowed/denied operations
- **Hooks**: Pre/post code change validations
- **Reminders**: Key rules to follow
- **Workflows**: Standard development sequences

### Templates (Updated for Current Architecture)
- **component.tsx**: React component with CSS modules and wf-shared imports
- **handler.ts**: Business logic hook with wf-shared types and React Query
- **style.css**: CSS Modules template with shared composition patterns
- **index.ts**: Clean export patterns

## ✅ Enforcement Features

### Pre-commit Hooks
- TypeScript compilation validation
- ESLint checks
- Test execution
- Coding standards verification
- Direct API call detection
- 'any' type detection

### Claude Settings
- Automatic coding standards reminders
- Post-change TypeScript validation
- Workflow-based development steps

## 🚨 Common Violations

### Blocked by Hooks
- TypeScript compilation errors
- ESLint failures
- Failing tests
- Use of `any` types
- Direct API calls in .tsx files

### Architecture Violations
- Missing 3-file pattern
- Business logic in .tsx files
- Bypassing service layer
- Incorrect import order

## 📖 Usage Examples

### Starting New Component
```bash
# 1. Create component directory
mkdir src/pages/NewPage/

# 2. Copy templates
cp .claude/templates/component.tsx src/pages/NewPage/NewPage.tsx
cp .claude/templates/handler.ts src/pages/NewPage/NewPage.handler.ts
cp .claude/templates/style.css src/pages/NewPage/NewPage.module.css
cp .claude/templates/index.ts src/pages/NewPage/index.ts

# 3. Update content following patterns and import paths
```

### Code Review Checklist
```bash
# 1. TypeScript validation
npx tsc --noEmit

# 2. Lint check
npm run lint

# 3. Test execution
npm test

# 4. Architecture review
# - 3-file pattern ✓
# - Service layer used ✓
# - No direct API calls ✓
```

## 🔄 Maintenance

### Updating Standards
1. Modify `CODING_STANDARDS.md`
2. Update templates if needed
3. Adjust hooks in `setup-hooks.sh`
4. Test with sample code changes

### Adding New Rules
1. Document in `CODING_STANDARDS.md`
2. Add to `checklist.md` validation
3. Update hooks for enforcement
4. Create examples in templates

---

**Last Updated**: Current session  
**Purpose**: Ensure consistent, high-quality code following established patterns  
**Enforcement**: Automated hooks + Claude configuration