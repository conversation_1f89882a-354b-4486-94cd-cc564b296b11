# Claude Development Checklist

## 📋 Pre-Development Checklist
**Run BEFORE starting any code task:**

### 1. Rules & Standards Review
- [ ] Read current `.claude/CODING_STANDARDS.md` section relevant to task
- [ ] Review 3-file pattern requirements
- [ ] Check import order conventions
- [ ] Understand service layer patterns

### 2. Current Code Status Check
- [ ] Verify existing file organization follows 3-file pattern
- [ ] Check no TypeScript errors exist: `npx tsc --noEmit`
- [ ] Confirm ESLint is working: `npm run lint`
- [ ] Verify tests are passing: `npm test`

### 3. Environment Validation
- [ ] Correct directory (frontend/ or admin-portal/)
- [ ] Dependencies installed: `npm install`
- [ ] Environment variables configured
- [ ] Supabase connection working

## 🔧 During Development Checklist

### Code Quality Standards
- [ ] Follow import order: React → Libraries → UI → Services → Icons → Local
- [ ] Use proper TypeScript (no `any` types)
- [ ] Put ALL business logic in `.handler.ts` files only
- [ ] Use service layer for ALL API calls (never direct in .tsx)
- [ ] Include proper error handling with try/catch
- [ ] Use descriptive variable names (`isLoading` not `loading`)

### Component Structure
- [ ] `.tsx` file contains ONLY UI rendering
- [ ] `.handler.ts` file contains ALL state and business logic
- [ ] `.style.ts` file for styling (optional)
- [ ] `index.ts` file for clean exports
- [ ] Handler hook returns organized object structure

### TypeScript Requirements
- [ ] All props properly typed
- [ ] No implicit `any` types
- [ ] Proper interface definitions
- [ ] Database types extended, not duplicated
- [ ] Service responses properly typed

## ✅ Post-Development Checklist
**Run AFTER completing any code changes:**

### 1. Code Compilation
- [ ] Frontend TypeScript check: `cd frontend && npx tsc --noEmit`
- [ ] Admin Portal TypeScript check: `cd admin-portal && npx tsc --noEmit`
- [ ] Zero TypeScript errors allowed

### 2. Code Quality
- [ ] ESLint check: `npm run lint`
- [ ] No ESLint warnings or errors
- [ ] Code formatting: `npm run format` (if available)

### 3. Testing
- [ ] Unit tests: `npm test`
- [ ] All tests passing
- [ ] Coverage maintained or improved

### 4. Architecture Verification
- [ ] 3-file pattern maintained for all components
- [ ] No business logic in .tsx files
- [ ] Service layer used for all API calls
- [ ] Handler hooks properly structured
- [ ] Import order conventions followed

### 5. Functional Testing
- [ ] Application builds: `npm run build`
- [ ] Manual testing of changed functionality
- [ ] No console errors in browser
- [ ] Responsive design maintained

## 🚨 Red Flags - Stop Development If Found

### Immediate Blockers
- ❌ Any TypeScript compilation errors
- ❌ Direct API calls in .tsx files
- ❌ Business logic in .tsx files
- ❌ Use of `any` types
- ❌ ESLint errors
- ❌ Broken test suite

### Architecture Violations
- ❌ Components not following 3-file pattern
- ❌ Handler hooks not returning organized objects
- ❌ Service layer bypassed
- ❌ Improper import order
- ❌ Missing error handling

## 📝 Quick Commands Reference

```bash
# Type checking
npx tsc --noEmit                    # Check TypeScript compilation
cd frontend && npx tsc --noEmit     # Frontend only
cd admin-portal && npx tsc --noEmit # Admin portal only

# Code quality
npm run lint                        # ESLint check
npm run lint:fix                    # Auto-fix ESLint issues
npm run format                      # Code formatting (if available)

# Testing
npm test                           # Run all tests
npm run test:coverage              # Test with coverage
npm run test:watch                 # Watch mode

# Building
npm run build                      # Production build
npm run dev                        # Development server
```

## 📚 Quick Reference Links

- **Coding Standards**: `.claude/CODING_STANDARDS.md`
- **Project Context**: `.claude/CLAUDE.md`
- **Component Templates**: `.claude/templates/`
- **Documentation**: `documentation/technical/`

---

**Remember**: This checklist should be followed for EVERY code task, no matter how small. Consistency in following these standards ensures maintainable, high-quality code.