# Development Workflow

## 🚀 **Overview**
This document outlines the development workflow, contribution guidelines, and quality standards for the WordFormation project.

## 🏗️ **Development Environment Setup**

**For complete environment setup instructions, see [Technical Specifications - Environment Setup](TECHNICAL_SPECIFICATIONS.md#environment-setup)**

This section covers the development workflow after your environment is configured.

## 🔀 **Git Workflow (GitHub Flow)**

### **Branch Strategy**
```
main (production)
├── feature/category-selection-ui
├── feature/quiz-submission-api
├── bugfix/authentication-redirect
└── hotfix/critical-security-patch
```

### **Branch Naming Conventions**
- `feature/description-in-kebab-case` - New features
- `bugfix/description-in-kebab-case` - Bug fixes
- `hotfix/description-in-kebab-case` - Critical production fixes
- `refactor/description-in-kebab-case` - Code refactoring
- `test/description-in-kebab-case` - Test improvements

### **Development Workflow Steps**

#### **1. Create Feature Branch**
```bash
# Start from main
git checkout main
git pull origin main

# Create feature branch
git checkout -b feature/category-selection-ui
```

#### **2. Development Process**
```bash
# Make changes and commit frequently
git add .
git commit -m "feat: add category card component

- Implement responsive category cards
- Add progress indicators
- Include hover animations
- Add accessibility attributes

Closes #123"
```

#### **3. Commit Message Standards**
```
<type>(<scope>): <subject>

<body>

<footer>
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```bash
git commit -m "feat(quiz): add category-based quiz fetching

- Implement getQuizzesByCategory function
- Add proper error handling
- Include loading states
- Add TypeScript types

Closes #145"

git commit -m "fix(auth): resolve email confirmation redirect issue

- Fix redirect after email confirmation
- Update confirmation page messaging
- Add proper error handling

Fixes #178"
```

#### **4. Push and Create Pull Request**
```bash
# Push feature branch
git push origin feature/category-selection-ui

# Create Pull Request via GitHub interface
```

## 📋 **Pull Request Process**

### **PR Requirements**
- [ ] **Clear title and description** following template
- [ ] **Reference related issues** with "Closes #123"
- [ ] **Include screenshots** for UI changes
- [ ] **Add tests** for new functionality
- [ ] **Update documentation** if needed
- [ ] **Check all CI tests pass**
- [ ] **Request appropriate reviewers**

### **Pull Request Template**
```markdown
## 📝 Description
Brief description of changes made.

## 🔗 Related Issue
Closes #123

## 🧪 Testing
- [ ] Unit tests added/updated
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Cross-browser testing (if UI changes)

## 📸 Screenshots (if applicable)
[Add screenshots for UI changes]

## 🚀 Deployment Notes
Any special deployment considerations?

## ✅ Checklist
- [ ] Code follows project standards
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
- [ ] No breaking changes (or properly documented)
```

### **Code Review Standards**

#### **Reviewer Checklist**
- [ ] **Code Quality**: Follows TypeScript and React best practices
- [ ] **Performance**: No obvious performance issues
- [ ] **Security**: No security vulnerabilities introduced
- [ ] **Testing**: Adequate test coverage
- [ ] **Accessibility**: WCAG 2.1 AA compliance for UI changes
- [ ] **Documentation**: Code is well-documented
- [ ] **Breaking Changes**: Properly handled and documented

#### **Review Process**
1. **Automated Checks**: CI/CD pipeline must pass
2. **Code Review**: At least 1 approval from team lead
3. **Testing**: QA review for significant features
4. **Merge**: Squash and merge to main

## 🧪 **Testing Strategy**

### **Test Types and Coverage**

#### **Unit Tests (>80% coverage required)**
```bash
# Run unit tests
npm run test

# Run with coverage
npm run test:coverage

# Watch mode for development
npm run test:watch
```

**Testing Patterns:**
```typescript
// Component testing
describe('CategoryCard', () => {
  it('should display category name and progress', () => {
    render(<CategoryCard category="word_formation" progress={80} />)
    expect(screen.getByText('Word Formation')).toBeInTheDocument()
    expect(screen.getByText('80%')).toBeInTheDocument()
  })

  it('should handle click events', async () => {
    const onSelect = vi.fn()
    render(<CategoryCard onSelect={onSelect} />)
    
    await user.click(screen.getByRole('button'))
    expect(onSelect).toHaveBeenCalled()
  })
})

// Service testing
describe('QuizService', () => {
  it('should fetch quizzes by category', async () => {
    const quizzes = await getQuizzesByCategory('A2', 'word_formation')
    expect(quizzes).toHaveLength(5)
    expect(quizzes[0]).toHaveProperty('id')
  })
})
```

#### **Integration Tests**
```bash
# Run integration tests
npm run test:integration
```

**Focus Areas:**
- Authentication flow end-to-end
- Quiz taking complete journey
- Progress tracking accuracy
- Category-based filtering

#### **E2E Tests (Playwright)**
```bash
# Run E2E tests
npm run test:e2e

# Run with UI
npm run test:e2e:ui
```

**Critical User Flows:**
- User registration and email confirmation
- Complete quiz taking journey (practice and test modes)
- Category selection and progress tracking
- Dashboard functionality

### **Testing Before Merge**

#### **Local Testing Checklist**
```bash
# 1. Run all tests
npm run test
npm run test:integration
npm run test:e2e

# 2. Check code quality
npm run lint
npm run type-check

# 3. Build verification
npm run build

# 4. Manual testing
npm run dev
# Test your changes in browser
```

#### **CI/CD Pipeline**
```yaml
# Automated on every PR:
- ESLint and Prettier checks
- TypeScript compilation
- Unit tests (>80% coverage)
- Integration tests
- Build verification
- Security audit
```

## 🚀 **Deployment Process**

### **Environments**

#### **Development**
- **Branch**: Any feature branch
- **URL**: Local development (localhost:5173)
- **Database**: Personal Supabase project
- **Purpose**: Feature development and testing

#### **Staging**
- **Branch**: `develop` (integration branch)
- **URL**: https://wordformation-staging.vercel.app
- **Database**: Staging Supabase project
- **Purpose**: Integration testing and QA review

#### **Production**
- **Branch**: `main`
- **URL**: https://wordformation.app
- **Database**: Production Supabase project
- **Purpose**: Live application

### **Deployment Pipeline**

#### **Automatic Deployments**
```bash
# Push to main → Production deployment
git push origin main

# Push to develop → Staging deployment
git push origin develop

# Feature branches → Preview deployments
git push origin feature/category-ui
# Creates: https://wordformation-pr-123.vercel.app
```

#### **Manual Deployment (if needed)**
```bash
# Production deployment
npm run build
npm run deploy:production

# Staging deployment
npm run deploy:staging
```

### **Database Migrations**
```bash
# Apply database changes
# 1. Update schema in database/schema.sql
# 2. Test in development environment
# 3. Apply to staging for testing
# 4. Apply to production after approval
```

## 🔧 **Code Quality Standards**

### **Automated Checks**
```bash
# Linting
npm run lint          # ESLint
npm run lint:fix      # Auto-fix issues

# Formatting
npm run format        # Prettier
npm run format:check  # Verify formatting

# Type checking
npm run type-check    # TypeScript
```

### **Pre-commit Hooks (Husky)**
```bash
# Automatically runs before each commit:
- ESLint check
- Prettier formatting
- TypeScript compilation
- Unit tests for changed files
```

### **Code Standards Enforcement**

#### **ESLint Configuration**
```json
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "react/prop-types": "off",
    "jsx-a11y/anchor-is-valid": "error"
  }
}
```

#### **TypeScript Configuration**
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "exactOptionalPropertyTypes": true
  }
}
```

## 📚 **Documentation Standards**

### **Code Documentation**
```typescript
/**
 * Fetches quizzes for a specific category and CEFR level
 * @param cefrLevel - The CEFR level (A2, B1, B2, C1, C2)
 * @param category - The grammar category
 * @returns Promise resolving to array of quizzes
 * @throws {QuizFetchError} When quiz fetching fails
 */
export async function getQuizzesByCategory(
  cefrLevel: CEFRLevel,
  category: CategoryType
): Promise<Quiz[]> {
  // Implementation
}
```

### **Component Documentation**
```typescript
/**
 * CategoryCard displays a grammar category with progress information
 * 
 * @example
 * ```tsx
 * <CategoryCard
 *   category="word_formation"
 *   progress={75}
 *   onSelect={handleCategorySelect}
 * />
 * ```
 */
interface CategoryCardProps {
  /** The grammar category type */
  category: CategoryType
  /** Progress percentage (0-100) */
  progress: number
  /** Callback when category is selected */
  onSelect: (category: CategoryType) => void
}
```

## 🐛 **Bug Reporting and Issue Management**

### **Issue Labels**
- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Improvements or additions to docs
- `good first issue` - Good for newcomers
- `help wanted` - Extra attention is needed
- `priority:high` - Critical issues
- `category:frontend` - Frontend related
- `category:backend` - Backend related
- `category:database` - Database related

### **Bug Report Template**
```markdown
## 🐛 Bug Description
Clear description of the bug

## 🔄 Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. See error

## ✅ Expected Behavior
What should happen

## ❌ Actual Behavior
What actually happens

## 📱 Environment
- Browser: [e.g. Chrome 91]
- Device: [e.g. iPhone 12]
- OS: [e.g. iOS 14.6]

## 📸 Screenshots
If applicable, add screenshots
```

## 🚨 **Emergency Procedures**

### **Hotfix Process**
```bash
# 1. Create hotfix branch from main
git checkout main
git checkout -b hotfix/critical-security-fix

# 2. Make minimal fix
# 3. Test thoroughly
# 4. Create PR with "HOTFIX" label
# 5. Get immediate review
# 6. Deploy directly to production after approval
```

### **Rollback Process**
```bash
# If production issue detected:
# 1. Identify last known good commit
# 2. Create rollback PR
# 3. Deploy immediately
# 4. Post-mortem analysis
```

---

**Status**: Complete Development Workflow  
**Next Step**: Team Onboarding and Implementation  
**Dependencies**: Repository setup, CI/CD configuration 