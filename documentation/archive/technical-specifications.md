# Technical Specifications - WordFormation Project

## 🛠️ **Technology Stack**

### **Frontend Stack**
```json
{
  "framework": "React 18.2+",
  "language": "TypeScript 5.0+",
  "buildTool": "Vite 4.0+",
  "styling": "Tailwind CSS 3.3+",
  "routing": "React Router 6.8+",
  "stateManagement": "React Context + Hooks",
  "testing": "Vitest + React Testing Library",
  "codeQuality": "ESLint + Prettier"
}
```

### **Backend Stack**
```json
{
  "database": "Supabase (PostgreSQL 15+)",
  "authentication": "Supabase Auth",
  "apiClient": "@supabase/supabase-js 2.38+",
  "emailService": "Supabase Email (dev) / SendGrid (prod)",
  "fileStorage": "Supabase Storage",
  "realtime": "Supabase Realtime"
}
```

### **Development Tools**
```json
{
  "nodeVersion": "18.17.0+",
  "packageManager": "npm 9.6.0+",
  "editor": "VS Code (recommended)",
  "gitWorkflow": "GitHub Flow",
  "deployment": "Vercel (frontend) + Supabase (backend)"
}
```

## 🏗️ **Project Structure**

### **Root Directory**
```
WordFormation/
├── index.html              # Main entry point HTML template
├── package.json            # Project metadata, scripts, and dependencies
├── vite.config.ts          # Vite configuration with TypeScript support
├── tsconfig.json           # TypeScript configuration with strict typing
├── tsconfig.node.json      # TypeScript configuration for Node.js files
├── tailwind.config.js      # Tailwind CSS configuration with custom themes
├── postcss.config.js       # PostCSS configuration for CSS processing
├── .env.example            # Environment variables template
└── .env.local              # Local environment variables (not in git)
```

### **Source Code Structure (`src/`)**
```
src/
├── main.tsx                         # Entry point for React application
├── index.css                        # Global CSS and Tailwind directives
├── App.tsx                          # Root component with routing and layout
├── types/                           # TypeScript type definitions
│   ├── index.ts                     # Main application types
│   ├── database.ts                  # Supabase database schema types
│   └── vite-env.d.ts               # Vite environment types
├── screens/                         # Screen components (three-file pattern)
│   ├── LoginScreen/                 # Authentication screen
│   │   ├── LoginScreen.tsx          # Pure UI component
│   │   ├── LoginScreen.handler.ts   # Business logic and state management
│   │   └── LoginScreen.style.ts     # Styling constants and theme integration
│   ├── HomeScreen/                  # Main dashboard/home screen
│   │   ├── HomeScreen.tsx           # Pure UI component
│   │   ├── HomeScreen.handler.ts    # Business logic and state management
│   │   └── HomeScreen.style.ts      # Styling constants and theme integration
│   └── QuizScreen/                  # Quiz interface screen
│       ├── QuizScreen.tsx           # Pure UI component
│       ├── QuizScreen.handler.ts    # Business logic and state management
│       └── QuizScreen.style.ts      # Styling constants and theme integration
├── components/                      # Reusable UI components
│   ├── Layout/                      # Layout components
│   │   └── ResponsiveContainer.tsx  # Responsive layout wrappers
│   ├── ProtectedRoute.tsx           # Route protection component
│   ├── QuizQuestion.tsx             # Individual question component
│   ├── QuizResults.tsx              # Quiz results display
│   ├── QuizModeSelection.tsx        # Quiz mode selection
│   └── Progress.tsx                 # Progress tracking component
├── context/                         # React Context providers
│   └── AuthContext.tsx              # Authentication context
└── services/                        # External integrations and business logic
    ├── supabaseClient.ts            # Supabase client setup
    ├── authService.ts               # Authentication services
    └── quizService.ts               # Quiz-related API operations
```

### **Database Assets**
```
database/
├── schema.sql          # Complete database schema (331 lines)
├── sample_data.sql     # Sample questions data (146 lines)
└── migrations/         # Future migration files
```

### **Documentation Structure**
```
documentation/
├── business-documentation.md    # Business requirements and user stories
├── technical-specifications.md # This file - technical implementation guide
├── implementation-roadmap.md   # Project roadmap and task tracking
├── documentation-strategy.md   # Documentation organization strategy
├── technical/                  # Specialized technical documentation
│   ├── development-workflow.md # Git workflow, PR process, code standards
│   ├── ui-design-system.md    # Design patterns, components, layouts
│   ├── testing-guide.md       # Test patterns, coverage, automation
│   ├── react-patterns.md      # React best practices & patterns
│   └── code-snippets.md       # Reusable code patterns
├── archive/                    # Historical/obsolete files
└── tasks/                      # Detailed task breakdowns by domain
```

### **Technical Documentation References**

For detailed technical procedures and guidelines, see the specialized documentation:

- **🔧 [Development Workflow](technical/development-workflow.md)** - Git branching strategy, PR process, code review standards, CI/CD pipeline
- **🎨 [UI Design System](technical/ui-design-system.md)** - Component specifications, design patterns, responsive layouts, wireframes
- **🧪 [Testing Guide](technical/testing-guide.md)** - Testing pyramid, unit/integration/E2E patterns, manual procedures
- **⚛️ [React Patterns](technical/react-patterns.md)** - React best practices, hooks patterns, performance optimization
- **📝 [Code Snippets](technical/code-snippets.md)** - Reusable components, utility functions, common patterns

## 🔧 **Environment Setup**

### **Prerequisites**
```bash
# Required software versions
node --version    # 18.17.0+
npm --version     # 9.6.0+
git --version     # 2.40.0+
```

### **Development Setup**
```bash
# 1. Clone repository
git clone <repository-url>
cd WordFormation

# 2. Install dependencies
npm install

# 3. Environment configuration
cp .env.example .env.local
# Fill in Supabase credentials:
# VITE_SUPABASE_URL=your-supabase-url
# VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# 4. Database setup
# Apply schema to your Supabase project:
# - Copy content from database/schema.sql
# - Execute in Supabase SQL Editor
# - Copy content from database/sample_data.sql
# - Execute in Supabase SQL Editor

# 5. Start development server
npm run dev
```

### **Required Environment Variables**
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Optional: Development flags
VITE_ENABLE_MOCK_AUTH=false
VITE_DEBUG_MODE=true
```

## 🏗️ **Technical Architecture**

### **TypeScript-First Development**
The project has been completely migrated to TypeScript with:
- **Strict Type Checking**: Maximum type safety enabled
- **Path Aliases**: Clean imports with @/ prefix (@/components, @/services, etc.)
- **React Optimizations**: Proper JSX and React types configuration
- **Vite Integration**: Fast development server with TypeScript support
- **Comprehensive Types**: Full type coverage for all components and services

### **Component Architecture**

#### **Screen Components (Three-File Pattern)**
All screen-level components follow the established three-file pattern:

- **LoginScreen/**: Authentication and user registration
  - **LoginScreen.tsx**: Pure UI for forms and authentication interface
  - **LoginScreen.handler.ts**: Form state, validation, and authentication logic
  - **LoginScreen.style.ts**: Form styling, input states, and responsive design

- **HomeScreen/**: Main application dashboard/home screen
  - **HomeScreen.tsx**: Pure UI for category grid and quiz display
  - **HomeScreen.handler.ts**: Data fetching, filtering, and navigation logic
  - **HomeScreen.style.ts**: Home layout, card styling, and grid responsiveness

- **QuizScreen/**: Interactive quiz interface
  - **QuizScreen.tsx**: Pure UI for questions, options, and feedback display
  - **QuizScreen.handler.ts**: Quiz state, question navigation, and results calculation
  - **QuizScreen.style.ts**: Quiz styling, option states, and progress indicators

#### **Reusable Components**
- **QuizQuestion.tsx**: Individual question display component
- **QuizResults.tsx**: Quiz results and performance analysis
- **QuizModeSelection.tsx**: Mode selection interface
- **Progress.tsx**: Progress tracking and statistics
- **ProtectedRoute.tsx**: Route-level authentication guard

#### **Layout Components**
- **ResponsiveContainer.tsx**: Responsive layout wrappers and containers

#### **Context Providers**
- **AuthContext.tsx**: Global authentication state management

### **State Management Architecture**

#### **React Context Patterns**
```typescript
// AuthContext pattern
interface AuthContextType {
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, userData: UserMetadata) => Promise<void>
  signOut: () => Promise<void>
}

// Usage in components
const { user, loading, signIn } = useAuth()
```

#### **Component State Patterns**
```typescript
// Quiz state management
interface QuizState {
  currentQuestionIndex: number
  answers: Record<string, string>
  timeSpent: number
  mode: 'practice' | 'test'
  isSubmitted: boolean
}

// Using useReducer for complex state
const [quizState, dispatch] = useReducer(quizReducer, initialState)
```

## 📡 **API Specifications**

### **Supabase Table Operations**

#### **Quiz Fetching**
```typescript
// Get quizzes by category and level
interface GetQuizzesParams {
  cefr_level: 'A2' | 'B1' | 'B2' | 'C1' | 'C2'
  category: 'word_formation' | 'suffixes' | 'prefixes' | 'parts_of_speech'
}

// API Call
const { data: quizzes } = await supabase
  .from('quizzes')
  .select('*')
  .eq('cefr_level', params.cefr_level)
  .eq('category', params.category)
  .eq('is_active', true)
```

#### **Question Fetching**
```typescript
// Get questions for a quiz
interface GetQuestionsParams {
  quiz_id: string
}

const { data: questions } = await supabase
  .from('questions')
  .select('*')
  .eq('quiz_id', params.quiz_id)
  .order('order_index')
```

#### **Quiz Submission**
```typescript
// Submit quiz attempt
interface QuizSubmission {
  quiz_id: string
  mode: 'practice' | 'test'
  answers: Array<{
    question_id: string
    user_answer: string
    is_correct: boolean
    time_taken: number
  }>
  total_score: number
  time_taken: number
}

// Two-step process:
// 1. Insert quiz attempt
// 2. Insert user answers
// 3. Trigger progress update via update_user_progress()
```

### **Authentication APIs**
```typescript
// Registration
interface SignUpData {
  email: string
  password: string
  options: {
    data: {
      name: string
      cefr_level: string
    }
  }
}

// Sign in
interface SignInData {
  email: string
  password: string
}
```

### **Database Integration**

#### **Supabase Implementation**
- **Type-Safe Queries**: All database operations use generated TypeScript types
- **Row Level Security**: Users can only access their own data
- **Real-time Updates**: Supabase subscriptions for live data updates
- **Error Handling**: Comprehensive error handling with typed responses
- **Authentication**: Integrated email/password auth with confirmation

#### **Data Models**
```typescript
// Core data types
interface User {
  id: string
  email: string
  name: string
  cefr_level: string
  created_at: string
  updated_at: string
}

interface Quiz {
  id: string
  cefr_level: string
  category: string
  title: string
  description: string
  total_questions: number
  time_limit_minutes: number
  is_active: boolean
}

interface Question {
  id: string
  quiz_id: string
  question_text: string
  option_a: string
  option_b: string
  option_c: string
  option_d: string
  correct_answer: string
  explanation: string
  grammar_rule: string
  example_usage: string
  difficulty_level: number
  order_index: number
}
```

## 🎨 **UI/UX Implementation**

### **Responsive Design**
- **Mobile-first approach** using Tailwind CSS
- **Responsive containers** and cards for all screen sizes
- **Touch-friendly interface** optimized for mobile devices
- **Consistent spacing, typography, and visual hierarchy**

### **Visual Feedback System**
- **Color-coded answer feedback** (green for correct, red for incorrect)
- **Progress bars and completion indicators**
- **Loading states and comprehensive error handling**
- **Smooth transitions and micro-animations**
- **High contrast accessibility-friendly color schemes**

### **Design System**

#### **Color Palette**
```css
/* Primary Colors */
--primary-50: #eff6ff;
--primary-500: #3b82f6;
--primary-600: #2563eb;
--primary-700: #1d4ed8;

/* Category Colors */
--word-formation: #10b981;  /* Green */
--suffixes: #f59e0b;        /* Amber */
--prefixes: #8b5cf6;        /* Violet */
--parts-of-speech: #ef4444; /* Red */

/* Status Colors */
--success: #22c55e;
--warning: #f59e0b;
--error: #ef4444;
--info: #3b82f6;
```

#### **Typography Scale**
```css
/* Font sizes (Tailwind classes) */
text-xs: 12px
text-sm: 14px
text-base: 16px
text-lg: 18px
text-xl: 20px
text-2xl: 24px
text-3xl: 30px
```

#### **Spacing System**
```css
/* Base unit: 4px (Tailwind spacing) */
space-1: 4px
space-2: 8px
space-4: 16px
space-6: 24px
space-8: 32px
space-12: 48px
```

### **Component Specifications**

#### **Category Selection Grid**
```typescript
interface CategoryGridProps {
  selectedLevel: CEFRLevel
  selectedCategory?: CategoryType
  onCategorySelect: (category: CategoryType) => void
  userProgress: Record<string, ProgressData>
}

// Layout: 2x2 grid on mobile, 4x1 on desktop
// Each card shows: icon, title, progress indicator, question count
```

#### **Quiz Question Component**
```typescript
interface QuizQuestionProps {
  question: Question
  currentIndex: number
  totalQuestions: number
  selectedAnswer?: string
  onAnswerSelect: (answer: string) => void
  mode: 'practice' | 'test'
  showFeedback?: boolean
}
```

#### **Progress Dashboard**
```typescript
interface ProgressDashboardProps {
  userProgress: UserProgress[]
  completedQuizzes: number
  totalQuizzes: number
  streakData: StreakData
  recommendedQuizzes: Quiz[]
}
```

## 🧪 **Testing Standards**

### **Testing Requirements**
- **Unit Tests**: All components and services require >80% coverage
- **Integration Tests**: Critical user flows (auth, quiz taking)
- **E2E Tests**: Complete user journeys across categories

### **Test File Structure**
```
src/
├── components/
│   └── QuizQuestion/
│       ├── QuizQuestion.tsx
│       ├── QuizQuestion.test.tsx
│       └── QuizQuestion.stories.tsx (optional)
```

### **Test Patterns**
```typescript
// Component testing pattern
describe('QuizQuestion', () => {
  it('renders question text correctly', () => {
    render(<QuizQuestion {...mockProps} />)
    expect(screen.getByText(mockQuestion.question_text)).toBeInTheDocument()
  })

  it('handles answer selection in practice mode', async () => {
    const onAnswerSelect = vi.fn()
    render(<QuizQuestion {...mockProps} mode="practice" onAnswerSelect={onAnswerSelect} />)
    
    await user.click(screen.getByText('Option A'))
    expect(onAnswerSelect).toHaveBeenCalledWith('Option A')
  })
})
```

## 📏 **Code Standards**

### **TypeScript Configuration**
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "exactOptionalPropertyTypes": true
  }
}
```

### **Naming Conventions**
```typescript
// Components: PascalCase
export const QuizQuestion: React.FC<QuizQuestionProps> = ({ ... }) => {}

// Hooks: camelCase with 'use' prefix
export const useQuizProgress = () => {}

// Types: PascalCase with descriptive names
interface QuizQuestionProps {}
type CategoryType = 'word_formation' | 'suffixes' | 'prefixes' | 'parts_of_speech'

// Constants: SCREAMING_SNAKE_CASE
const MAX_QUIZ_QUESTIONS = 20
const CEFR_LEVELS = ['A2', 'B1', 'B2', 'C1', 'C2'] as const
```

### **Import Organization**
```typescript
// 1. React imports
import React, { useState, useEffect } from 'react'

// 2. Third-party imports
import { supabase } from '@/services/supabaseClient'

// 3. Internal imports
import { QuizQuestion } from '@/components/Quiz/QuizQuestion'
import { useAuth } from '@/context/AuthContext'
import type { Quiz, Question } from '@/types'
```

## 🏗️ **File Organization Standards**

### **Screen Component Pattern**
Every screen must follow the **three-file pattern** for maintainability and separation of concerns:

```
src/screens/LoginScreen/
├── LoginScreen.tsx        # React component (UI only)
├── LoginScreen.handler.ts # Business logic and state management  
├── LoginScreen.style.ts   # Styling and theme-related code
```

#### **File Responsibilities**
- **`.tsx` file**: Pure UI rendering, event binding, component lifecycle
- **`.handler.ts` file**: State management, business logic, data transformation
- **`.style.ts` file**: Component-specific styles, theme integration, responsive design

### **API Call Architecture**
Maintain clean separation between UI and data layers:

```typescript
// ❌ DON'T: Call APIs directly from screen components
const LoginScreen = () => {
  const handleLogin = async () => {
    const response = await fetch('/api/auth/login') // ❌ Direct API call
  }
}

// ✅ DO: Call APIs through handlers and services
const LoginScreen = () => {
  const { handleLogin } = useLoginHandler() // ✅ Through handler
}

// LoginScreen.handler.ts
export const useLoginHandler = () => {
  const handleLogin = async (credentials: LoginCredentials) => {
    return await AuthService.login(credentials) // ✅ Through service
  }
}
```

#### **Implementation Pattern Example**
```typescript
// Screen.tsx - Pure UI Component
const Screen: React.FC = () => {
  const {
    data, loading, error,
    handleAction
  } = useScreenHandler(); // All logic in handler
  
  return <div>{/* Pure UI rendering */}</div>;
};

// Screen.handler.ts - Business Logic
export const useScreenHandler = () => {
  const [data, setData] = useState<Data | null>(null);
  
  const loadData = async () => {
    // Uses service layer, not direct API calls
    const { data, error } = await DataService.getData();
    if (data) setData(data);
  };
  
  return { data, loadData };
};
```

#### **Data Flow Architecture**
```
Screen Component (.tsx)
    ↓ user interactions
Handler (.handler.ts)
    ↓ business logic
Service Layer
    ↓ API calls
External APIs / Database
```

### **Service Layer Standards**
Services handle all external communications and reusable business logic:

```typescript
// services/AuthService.ts
export class AuthService {
  // Handle all authentication API calls
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    // API communication logic
  }
  
  // Manage local database operations
  static async saveUserSession(session: UserSession): Promise<void> {
    // Local storage logic
  }
  
  // Contain reusable business logic
  static validateCredentials(credentials: LoginCredentials): ValidationResult {
    // Business validation logic
  }
}
```

#### **Service Implementation Pattern**
```typescript
// services/DataService.ts
export class DataService {
  /**
   * Generic data fetching pattern
   */
  static async getDataByParams(
    param1: string, 
    param2: string
  ): Promise<{ data: DataType | null; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('table_name')
        .select('*')
        .eq('field1', param1)
        .eq('field2', param2)
        .limit(1)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      // Transform to expected type
      const transformedData: DataType = {
        id: data.id,
        // ... other fields
      };

      return { data: transformedData, error: null };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      };
    }
  }
}
```

### **Code Size Limits**
Maintain readability and maintainability through size constraints:

- **General Rule**: Maximum **500 lines** per file
- **Exception**: Screen `.tsx` files may exceed 500 lines when necessary for complex UI
- **Enforcement**: Use ESLint rules to warn at 400 lines, error at 500 lines
- **Refactoring Trigger**: When approaching limits, extract components or utilities

#### **Size Management Strategies**
```typescript
// When a handler grows too large, split by concern:
// LoginScreen.handler.ts (main handler)
// LoginScreen.validation.ts (validation logic)
// LoginScreen.utils.ts (utility functions)

// When a screen grows too large, extract components:
// LoginScreen.tsx (main screen)
// components/LoginForm.tsx (form component)
// components/LoginHeader.tsx (header component)
```

### **Implementation Patterns**

#### **Complete Three-File Pattern Example**
```typescript
// Screen.tsx - Pure UI Component
import React from 'react';
import { useScreenHandler } from './Screen.handler';
import { containerStyles, buttonStyles } from './Screen.style';

const Screen: React.FC = () => {
  const {
    data, loading, error,
    handleAction
  } = useScreenHandler(); // All business logic in handler

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return (
    <div className={containerStyles.wrapper}>
      {/* Pure UI rendering only */}
    </div>
  );
};

// Screen.handler.ts - Business Logic
export const useScreenHandler = (): ScreenHandler => {
  const [data, setData] = useState<DataType | null>(null);
  const [loading, setLoading] = useState(true);
  
  const loadData = async () => {
    try {
      setLoading(true);
      // Uses service layer only
      const { data, error } = await DataService.getData();
      if (error) throw new Error(error);
      setData(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { data, loading, loadData };
};

// Screen.style.ts - Styling Constants
export const containerStyles = {
  wrapper: 'min-h-screen bg-gray-50',
  header: 'mb-6',
  title: 'text-xl font-semibold text-gray-900'
};

export const getClassNameFunction = (
  condition1: boolean,
  condition2: boolean
): string => {
  // Dynamic styling logic
  return condition1 ? 'style-set-a' : 'style-set-b';
};
```

#### **Handler Pattern Best Practices**
```typescript
// Strong typing for handler interface
interface ScreenHandler {
  // Core state - only what UI needs
  data: DataType | null;
  loading: boolean;
  error: string | null;
  
  // Computed properties - derived from state
  computedProperty: ComputedType;
  progress: number;
  canProceed: boolean;
  
  // Event handlers - all user interactions
  handleAction: (param: string) => void;
  handleSubmit: () => Promise<void>;
}

// Clean separation of concerns in handler
export const useScreenHandler = (): ScreenHandler => {
  // 1. State management
  const [state, setState] = useState(initialState);
  
  // 2. Side effects
  useEffect(() => {
    loadData();
  }, []);
  
  // 3. Business logic functions
  const calculateResults = () => {
    // Business logic here
  };
  
  // 4. Event handlers
  const handleAction = (param: string) => {
    // User interaction logic
  };
  
  // 5. Return clean interface
  return {
    // State
    data: state.data,
    loading: state.loading,
    
    // Computed
    progress: calculateProgress(),
    
    // Handlers
    handleAction,
    handleSubmit
  };
};
```

### **Development Workflow**

#### **Code Quality Standards**
- **TypeScript First**: All new code follows TypeScript patterns
- **Component Structure**: Functional components with hooks
- **Type Safety**: Comprehensive typing for props and state
- **Error Boundaries**: Proper error handling throughout
- **Accessibility**: WCAG guidelines followed
- **File Organization**: Follow three-file pattern for all screens

#### **Architecture Principles**
1. **Type-First Development**: Strict TypeScript with comprehensive type definitions
2. **Modularity**: Small, reusable components with clear interfaces
3. **Separation of Concerns**: UI, state, and business logic properly separated
4. **Feature-Based Organization**: Components grouped by functionality
5. **Scalability**: Structure supports growth with maintainable patterns
6. **Clean Architecture**: Screen → Handler → Service → API flow

## 🔒 **Security Requirements**

### **Environment Security**
- Never commit `.env` files with real credentials
- Use different Supabase projects for dev/staging/production
- Implement proper RLS policies (already done in schema)

### **Input Validation**
```typescript
// Validate all user inputs
const validateQuizSubmission = (submission: QuizSubmission): boolean => {
  // Validate quiz_id exists and user has access
  // Validate answer format and content
  // Validate timing data is reasonable
  return isValid
}
```

### **Authentication Guards**
```typescript
// Protect routes requiring authentication
export const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth()
  
  if (loading) return <LoadingSpinner />
  if (!user) return <Navigate to="/login" />
  
  return <>{children}</>
}
```

## 📱 **Responsive Design Requirements**

### **Breakpoints** (Tailwind CSS)
```css
sm: 640px   /* Small devices */
md: 768px   /* Medium devices */
lg: 1024px  /* Large devices */
xl: 1280px  /* Extra large devices */
2xl: 1536px /* 2X large devices */
```

### **Mobile-First Approach**
```tsx
// Example responsive component
<div className="
  grid grid-cols-1        /* Mobile: single column */
  md:grid-cols-2          /* Tablet: 2 columns */
  lg:grid-cols-4          /* Desktop: 4 columns */
  gap-4 p-4
">
  {categories.map(category => (
    <CategoryCard key={category.id} {...category} />
  ))}
</div>
```

## 🚀 **Performance Requirements**

### **Bundle Size Targets**
- Initial bundle: < 500KB gzipped
- Component chunks: < 100KB each
- Lazy load non-critical components

### **Loading Performance**
- First Contentful Paint: < 1.5s
- Largest Contentful Paint: < 2.5s
- Cumulative Layout Shift: < 0.1

### **Optimization Strategies**
```typescript
// Lazy loading for routes
const Dashboard = lazy(() => import('@/pages/Dashboard'))
const QuizPage = lazy(() => import('@/pages/QuizPage'))

// Memoization for expensive calculations
const sortedQuizzes = useMemo(() => 
  quizzes.sort((a, b) => a.difficulty_level - b.difficulty_level),
  [quizzes]
)
```

## 🎯 **Quick Reference for New Development**

### **Creating a New Screen Component**
Follow this pattern for all new screen components:

```bash
# 1. Create screen directory structure
mkdir src/screens/YourScreen
touch src/screens/YourScreen/YourScreen.tsx
touch src/screens/YourScreen/YourScreen.handler.ts  
touch src/screens/YourScreen/YourScreen.style.ts

# 2. Follow the established three-file pattern
```

### **Development Checklist**
For every new screen component:

- [ ] **UI Component** (.tsx): Only JSX and event binding
- [ ] **Handler** (.handler.ts): All useState, useEffect, business logic
- [ ] **Styles** (.style.ts): All styling constants and helper functions
- [ ] **Types**: Strong TypeScript interfaces for handler return
- [ ] **Service Integration**: Use existing service layer pattern
- [ ] **Error Handling**: Consistent error patterns across loading/error states
- [ ] **Responsive Design**: Mobile-first with Tailwind classes

### **Architecture Benefits**
This pattern provides:

- **Maintainability**: Business logic separated from UI
- **Testability**: Handler functions can be unit tested independently
- **Reusability**: Service layer can be used across multiple screens  
- **Scalability**: Pattern scales from simple forms to complex interfaces
- **Type Safety**: Full TypeScript coverage with strongly typed interfaces
- **Performance**: Clean separation enables better optimization

---

**Last Updated**: December 2024  
**Technical Lead**: Architecture and implementation standards  
**Document Purpose**: Core tech stack, architecture patterns, and development standards  
**For current implementation status and progress tracking, see [Implementation Roadmap](implementation-roadmap.md)** 