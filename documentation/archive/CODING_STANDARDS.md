# Word Formation Project - Coding Standards & Conventions

## Project Overview
- **Type**: Full-stack web application with separate frontend and admin portal
- **Tech Stack**: React + TypeScript + Vite + Tailwind CSS + Supabase
- **Structure**: Monorepo with `frontend/` and `admin-portal/` directories

## Architecture & File Structure

### Project Structure
```
WordFormation/
├── frontend/                 # User-facing application
├── admin-portal/            # Content management interface  
├── database/               # SQL schema and sample data
└── documentation/          # Project documentation
```

### Page/Screen Organization (3-File Pattern)
Every page/screen MUST follow this exact structure:

```
src/pages/PageName/ or src/screens/ScreenName/
├── PageName.tsx            # Pure UI component
├── PageName.handler.ts     # Business logic & state management
├── PageName.module.css     # CSS Modules (preferred)
└── index.ts               # Clean export
```

**Example:**
```typescript
// QuizManagement/QuizManagement.tsx
export default function QuizManagement() {
  const { data, actions } = useQuizManagementHandler()
  return <div>...</div>
}

// QuizManagement/QuizManagement.handler.ts
export const useQuizManagementHandler = () => {
  // All logic here
  return { data, actions }
}

// QuizManagement/index.ts
export { default } from './QuizManagement'
```

## TypeScript Standards

### Type Organization
- **Database types**: Generated from Supabase in `types/database.ts`
- **Application types**: Extend database types in `types/index.ts`
- **Component props**: Define inline or in same file
- **API responses**: Specific interfaces for service layer

### Type Patterns
```typescript
// Extend database types, don't duplicate
export interface User extends DbUser {
  level?: Level
  email_confirmed_at?: string
}

// Use Partial for updates
static async updateQuiz(id: string, updates: Partial<DbQuiz>) {}

// Proper service response typing
interface ServiceResponse<T> {
  data: T | null
  error: string | null
}
```

### Import Conventions
```typescript
// 1. React imports first
import { useState, useEffect } from 'react'

// 2. Third-party libraries
import { useQuery } from '@tanstack/react-query'

// 3. Internal components & UI
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

// 4. Services & utilities
import { AdminService } from '@/services/adminService'
import { formatDate } from 'wf-shared/utils'

// 5. Icons (grouped)
import { Plus, Edit, Trash2, Eye } from 'lucide-react'

// 6. Local imports last
import { usePageHandler } from './Page.handler'
import styles from './Page.module.css'
```

## React Patterns

### Component Structure
```typescript
export default function ComponentName() {
  // 1. Handler hook (contains all logic)
  const { data, actions, state } = useComponentHandler()
  
  // 2. Early returns (loading, error states)
  if (loading) return <LoadingComponent />
  if (error) return <ErrorComponent />
  
  // 3. Main render
  return <div>...</div>
}
```

### Handler Hook Pattern
```typescript
export const useComponentHandler = () => {
  // 1. State declarations
  const [localState, setLocalState] = useState()
  
  // 2. Data fetching (React Query)
  const { data, isLoading, error } = useQuery({
    queryKey: ['key'],
    queryFn: serviceCall,
  })
  
  // 3. Event handlers
  const handleAction = () => {}
  
  // 4. Computed values
  const derivedData = useMemo(() => {}, [data])
  
  // 5. Return organized object
  return {
    // Data
    data,
    derivedData,
    
    // State
    localState,
    isLoading,
    error,
    
    // Actions
    handleAction,
  }
}
```

## Service Layer Standards

### Service Class Pattern
```typescript
export class ServiceName {
  static async methodName(params: ParamsType) {
    try {
      const { data, error } = await supabase
        .from('table')
        .select('*')
      
      if (error) throw error
      return data
    } catch (error) {
      console.error('Error in methodName:', error)
      throw error
    }
  }
}
```

### Supabase Query Patterns
```typescript
// Use explicit joins for relations
.select(`
  *,
  categories!inner(id, name, key),
  levels!inner(id, name, key)
`, { count: 'exact' })

// Proper filtering
if (filters.category) {
  query = query.eq('categories.key', filters.category)
}

// Pagination
const from = (page - 1) * pageSize
const to = from + pageSize - 1
query = query.range(from, to)
```

## Styling Standards

### Tailwind CSS Conventions
- **Prefer utility classes** over custom CSS
- **Use design system colors**: `text-gray-900`, `bg-blue-50`
- **Consistent spacing**: `space-y-6`, `gap-4`
- **Responsive design**: `grid-cols-1 md:grid-cols-2 lg:grid-cols-4`

### CSS Modules (Current Standard)

We use **CSS Modules** for component styling to achieve better performance, maintainability, and developer experience.

#### File Structure
```
src/
└── components/
    └── ComponentName/
        ├── ComponentName.tsx
        ├── ComponentName.module.css     # ✅ Preferred
        ├── ComponentName.handler.ts     # If needed
        └── __tests__/
            └── ComponentName.test.tsx
```

#### CSS Module Naming Conventions

**File Naming:**
- Use `.module.css` extension
- Match component name: `ComponentName.module.css`

**Class Naming:**
- Use camelCase for class names
- Descriptive and semantic names
- Avoid abbreviations unless well-known

```css
/* ComponentName.module.css */
.container { /* ✅ Good */ }
.primaryButton { /* ✅ Good */ }
.isActive { /* ✅ Good - boolean state */ }

.cont { /* ❌ Avoid - unclear abbreviation */ }
.btn-primary { /* ❌ Avoid - kebab-case */ }
```

#### Tailwind Integration in CSS Modules

**Using Tailwind Theme Functions:**
```css
.container {
  padding: theme('spacing.4');
  background-color: theme('colors.white');
  border-radius: theme('borderRadius.lg');
  box-shadow: theme('boxShadow.sm');
}

.title {
  font-size: theme('fontSize.xl');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
}
```

**Component Usage:**
```typescript
import styles from './ComponentName.module.css';

const ComponentName = () => {
  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Title</h1>
    </div>
  );
};
```

#### Legacy .style.ts Pattern (Deprecated)

**❌ Avoid - Old Pattern:**
```typescript
// Component.style.ts - DEPRECATED
export const styles = {
  container: 'bg-white p-4 rounded-lg shadow-sm',
  title: 'text-xl font-semibold text-gray-900'
};

// Usage - DEPRECATED
import { styles } from './Component.style.ts';
<div className={styles.container}>
```

## State Management

### React Query Standards
```typescript
// Query keys should be arrays
queryKey: ['entity', id, filters]

// Use proper error handling
const { data, isLoading, error, refetch } = useQuery({
  queryKey: ['quizzes', page, filters],
  queryFn: () => ServiceClass.method(params),
})
```

### Local State Patterns
```typescript
// Object state for related values
const [filters, setFilters] = useState({
  category: '',
  level: '',
  status: '',
})

// Update patterns
const handleFilterChange = (key: string, value: string) => {
  setFilters(prev => ({ ...prev, [key]: value }))
}
```

## Error Handling

### Service Layer
```typescript
try {
  const { data, error } = await supabaseCall()
  if (error) throw error
  return data
} catch (error) {
  console.error('Descriptive error message:', error)
  throw error
}
```

### Component Layer
```typescript
if (error) {
  return (
    <ErrorComponent 
      title="Error Loading Data"
      message={error instanceof Error ? error.message : 'Unexpected error'}
      onRetry={() => refetch()}
    />
  )
}
```

## UI Patterns

### Loading States
```typescript
if (isLoading) {
  return (
    <div className="animate-pulse space-y-4">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-16 bg-gray-200 rounded"></div>
      ))}
    </div>
  )
}
```

### Empty States
```typescript
{data?.length === 0 ? (
  <div className="text-center py-8">
    <Icon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
    <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Found</h3>
    <p className="text-gray-600">Helpful message about the empty state</p>
    <Button onClick={handleCreate}>Create New Item</Button>
  </div>
) : (
  // Regular content
)}
```

### Pagination Pattern
```typescript
{totalPages > 1 && (
  <div className="mt-6 flex justify-center space-x-2">
    <Button 
      variant="outline" 
      disabled={page === 1}
      onClick={() => setPage(page - 1)}
    >
      Previous
    </Button>
    <span className="px-4 py-2 text-sm text-gray-600">
      Page {page} of {totalPages}
    </span>
    <Button 
      variant="outline" 
      disabled={page === totalPages}
      onClick={() => setPage(page + 1)}
    >
      Next
    </Button>
  </div>
)}
```

## Database Integration

### Type Safety
```typescript
// Use generated database types
import type { Database } from '@/types'
export type DbQuiz = Database['public']['Tables']['quizzes']['Row']

// Extend for application use
export interface Quiz extends DbQuiz {
  category?: Category
  level?: Level
}
```

### Query Optimization
```typescript
// Use select with specific fields for joins
.select(`
  id, title, description,
  categories(id, name),
  levels(id, name)
`)

// Apply filters before joins when possible
.eq('is_active', true)
.eq('category_id', categoryId)
```

## Testing Standards

### Component Testing
- Test user interactions, not implementation details
- Mock external dependencies (services, APIs)
- Use proper test data factories
- Follow AAA pattern (Arrange, Act, Assert)

### Handler Testing
- Test business logic in isolation
- Mock React Query and services
- Verify state transitions
- Test error scenarios

## Code Quality Rules

### General
- **No any types** - Use proper TypeScript
- **No console.log** in production code
- **Descriptive variable names** - `isLoading` not `loading`
- **Early returns** for readability
- **Single responsibility** - One concern per file/function

### React Specific
- **Custom hooks** for business logic
- **Pure components** when possible
- **Proper dependency arrays** in useEffect/useMemo
- **Event handler naming**: `handleActionName`

### File Naming
- **PascalCase** for components: `QuizManagement.tsx`
- **camelCase** for handlers: `quizManagement.handler.ts`
- **kebab-case** for utilities: `format-date.ts`

## Dependencies & Tools

### Core Stack
- **React 18** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **TanStack Query** for server state
- **React Router** for navigation
- **Supabase** for backend services

### UI Components
- **Radix UI** primitives for accessibility
- **Lucide React** for icons
- **Custom design system** built on Tailwind

### Development
- **ESLint** + **TypeScript ESLint**
- **Prettier** for formatting
- **Vitest** for testing
- **TypeScript** strict mode

## Environment & Configuration

### Environment Variables
```bash
# Frontend & Admin Portal
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### Path Aliases
```typescript
// Use @ for src root
import { Component } from '@/components/Component'
import { service } from '@/services/service'
```

## Git & Deployment

### Commit Conventions
- Use descriptive commit messages
- Include context about what and why
- Reference issue numbers when applicable

### Branch Strategy
- `master` for production
- Feature branches for development
- Create PRs for code review

---

**Note**: This document should be referenced for all development work on the Word Formation project. Any deviations should be discussed and documented as updates to these standards.