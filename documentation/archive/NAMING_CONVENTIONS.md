# WF-Frontend Naming Conventions

## Overview
This document establishes consistent naming conventions for the wf-frontend codebase to improve readability, maintainability, and reduce errors.

## Current Issues Identified
- Inconsistent error variable naming (`_error` vs `error` vs `quizError`)
- Mix of generic and descriptive variable names
- Variable reference mismatches (destructuring with `_answersError` but referencing `answersError`)
- Inconsistent data variable naming patterns

## 1. Variable Naming

### Error Variables
**Standard**: Use descriptive names without underscore prefixes for active variables.

```typescript
// ✅ GOOD - Consistent descriptive naming
const { data: userData, error: userError } = await api.getUser();
const { data: quizData, error: quizError } = await api.getQuiz();
const { data: questionsData, error: questionsError } = await api.getQuestions();

if (userError) {
  return { success: false, error: userError.message };
}

// ❌ BAD - Inconsistent naming
const { data: userData, error: _userError } = await api.getUser();
const { data: quizData, error: quizError } = await api.getQuiz();
const { data: questionsData, error: _questionsError } = await api.getQuestions();

if (userError) { // ReferenceError! Should be _userError
  return { success: false, error: userError.message };
}
```

### Data Variables
**Standard**: Use descriptive names that indicate the type of data.

```typescript
// ✅ GOOD - Clear, descriptive names
const { data: userData } = await userApi.getProfile();
const { data: quizzes } = await quizApi.getAll();
const { data: questions } = await quizApi.getQuestions();
const { data: categories } = await quizApi.getCategories();

// ❌ BAD - Generic names that don't indicate content
const { data } = await userApi.getProfile();
const { data: data1 } = await quizApi.getAll();
const { data: response } = await quizApi.getQuestions();
```

### Generic Patterns
```typescript
// Error naming pattern: [context]Error
const userError = new Error('User not found');
const authError = new Error('Authentication failed');
const quizError = new Error('Quiz not found');

// Data naming pattern: [context]Data or [context] (plural for collections)
const userData = { id: '1', name: 'John' };
const quizzes = [{ id: '1', title: 'Quiz 1' }];
const questions = [{ id: '1', text: 'Question 1' }];
```

## 2. Function Naming

### Handlers
**Standard**: Use descriptive verb-noun combinations.

```typescript
// ✅ GOOD - Clear action and target
const handleAnswerSelect = (answer: string) => { };
const handleQuizSubmit = () => { };
const handleUserUpdate = (data: UserData) => { };

// ❌ BAD - Generic or unclear
const handleClick = () => { };
const handle = () => { };
const doSomething = () => { };
```

### Services
**Standard**: Use clear action verbs.

```typescript
// ✅ GOOD - Clear action verbs
export const getUserProfile = async (id: string) => { };
export const updateUserProfile = async (id: string, data: UpdateData) => { };
export const deleteUser = async (id: string) => { };

// ❌ BAD - Unclear actions
export const user = async (id: string) => { };
export const manageUser = async (id: string, data: any) => { };
```

## 3. Component Naming

### Component Files
**Standard**: PascalCase for component names, matching file names.

```typescript
// ✅ GOOD - Clear, descriptive component names
export const QuizScreen: React.FC = () => { };
export const UserProfileCard: React.FC = () => { };
export const NavigationMenu: React.FC = () => { };

// File names should match: QuizScreen.tsx, UserProfileCard.tsx, NavigationMenu.tsx
```

### Props Interfaces
**Standard**: Component name + "Props" suffix.

```typescript
// ✅ GOOD - Clear interface naming
interface QuizScreenProps {
  quizId: string;
  onComplete: () => void;
}

interface UserProfileCardProps {
  user: User;
  editable?: boolean;
}
```

## 4. Type Definitions

### Interfaces
**Standard**: PascalCase with descriptive names.

```typescript
// ✅ GOOD - Clear, descriptive interfaces
interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
}

interface QuizAttempt {
  id: string;
  userId: string;
  quizId: string;
  score: number;
  completedAt: string;
}
```

### Type Aliases
**Standard**: PascalCase for complex types, camelCase for simple aliases.

```typescript
// ✅ GOOD - Appropriate type naming
type QuizMode = 'practice' | 'test';
type UserRole = 'student' | 'teacher' | 'admin';

interface ApiResponse<T> {
  data: T | null;
  error: string | null;
}
```

## 5. File and Directory Naming

### Files
**Standard**: 
- Components: PascalCase (e.g., `QuizScreen.tsx`)
- Services: camelCase (e.g., `quizService.ts`)
- Utilities: camelCase (e.g., `errorRecovery.ts`)
- Tests: match source file + `.test` (e.g., `QuizScreen.test.tsx`)

### Directories
**Standard**: camelCase for most directories, PascalCase for component directories.

```
src/
├── components/          # camelCase
│   └── QuizScreen/     # PascalCase (component directory)
├── services/           # camelCase
├── utils/              # camelCase
├── types/              # camelCase
└── repositories/       # camelCase
```

## 6. Constants and Configuration

### Constants
**Standard**: SCREAMING_SNAKE_CASE for true constants, camelCase for configuration objects.

```typescript
// ✅ GOOD - Clear constant naming
export const DEFAULT_PAGE_SIZE = 10;
export const MAX_RETRY_ATTEMPTS = 3;
export const API_ENDPOINTS = {
  users: '/api/users',
  quizzes: '/api/quizzes'
} as const;

// Configuration objects
export const quizConfig = {
  timeLimit: 3600,
  passingScore: 70
};
```

## 7. Async Operations

### Promise/Async Function Naming
**Standard**: Use action verbs that indicate async nature when not obvious.

```typescript
// ✅ GOOD - Clear async operations
export const fetchUserProfile = async (id: string) => { };
export const saveQuizAttempt = async (data: QuizData) => { };
export const loadQuizQuestions = async (quizId: string) => { };

// ✅ ALSO GOOD - When context makes async nature clear
export const getUserProfile = async (id: string) => { };
export const updateUserProfile = async (id: string, data: UserData) => { };
```

## 8. Error Handling Patterns

### Standard Error Destructuring
```typescript
// ✅ GOOD - Consistent error handling
const { data: userData, error: userError } = await userService.getProfile(id);
if (userError) {
  console.error('Failed to fetch user profile:', userError);
  return { success: false, error: userError.message };
}

const { data: quizData, error: quizError } = await quizService.getQuiz(quizId);
if (quizError) {
  console.error('Failed to fetch quiz:', quizError);
  return { success: false, error: quizError.message };
}
```

### Error Response Types
```typescript
// ✅ GOOD - Consistent error response structure
interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success?: boolean;
}

interface ApiError {
  message: string;
  code: string;
  details?: any;
}
```

## Implementation Guidelines

1. **Gradual Migration**: Apply these conventions during regular development cycles
2. **Code Reviews**: Use these standards as checklist items in code reviews
3. **Linting Rules**: Consider adding ESLint rules to enforce naming patterns
4. **Team Training**: Ensure all team members understand these conventions

## Priority Order for Implementation

1. **High Priority**: Fix variable reference errors (e.g., `_answersError` vs `answersError`)
2. **Medium Priority**: Standardize error and data variable naming
3. **Low Priority**: Apply to existing code during refactoring

---

*This document should be updated as naming patterns evolve and new use cases are identified.*