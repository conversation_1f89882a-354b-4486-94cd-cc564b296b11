# wf-shared Package Overview

> **📍 Location**: This document is part of the centralized documentation in `/documentation/`. 
> **See Also**: [TECHNICAL_ARCHITECTURE.md](TECHNICAL_ARCHITECTURE.md), [SERVICE_MIXINS_GUIDE.md](SERVICE_MIXINS_GUIDE.md)

Shared types, constants, and utilities for Word Formation platform.

## 🔒 Security Notice

This package contains **ONLY** safe, non-sensitive code:
- Database type definitions
- API interfaces  
- Pure utility functions
- Application constants

❌ **This package NEVER contains:**
- Admin-specific business logic
- Sensitive API implementations
- User authentication logic
- Database access implementations

## 📦 Usage

```typescript
// Import types
import { DatabaseTypes, ApiTypes } from 'wf-shared/types'

// Import constants
import { API_CONSTANTS } from 'wf-shared/constants'

// Import utilities
import { validationUtils } from 'wf-shared/utils'
```

## 🏗️ Structure

```
src/
├── types/          # Shared TypeScript types
├── constants/      # Application constants
└── utils/          # Pure utility functions
```

## 🛡️ Security Validation

Before each release, verify:
1. No admin-specific code
2. No sensitive business logic
3. No authentication implementations
4. Only pure, safe utilities

## 📋 Migration Status

This package is part of the Hybrid Architecture migration. See `../HYBRID_ARCHITECTURE_MIGRATION_PLAN.md` for current status.