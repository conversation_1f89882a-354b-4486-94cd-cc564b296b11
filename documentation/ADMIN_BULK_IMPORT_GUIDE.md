# Admin Portal - Bulk Import Guide

> **📍 Location**: This document is part of the centralized documentation in `/documentation/`. 
> **See Also**: [Service Mixins Guide](SERVICE_MIXINS_GUIDE.md), [Technical Architecture](TECHNICAL_ARCHITECTURE.md)

## Overview
The bulk import system supports three types of data import:
1. **Quiz Import** - Import quiz metadata from CSV
2. **Question Import** - Import questions with options and explanations from CSV  
3. **SQL Import** - Execute SQL files directly (like your existing import files)

## 1. Quiz Import Template

### CSV Structure
```csv
key,title,description,category_key,level_key,quiz_type_key,difficulty_level,total_questions,time_limit_minutes,instructions,quiz_config,is_active
```

### Field Descriptions
- **key**: Unique identifier for the quiz (e.g., `wf_quiz_a2_001`)
- **title**: Display name of the quiz
- **description**: Detailed description of quiz content
- **category_key**: Must match existing category keys:
  - `word_formation`
  - `grammar` 
  - `vocabulary`
  - `toeic`
  - `ielts`
- **level_key**: Must match existing level keys:
  - `A1`, `A2`, `B1`, `B2`, `C1`, `C2`
- **quiz_type_key**: Must match existing quiz type keys:
  - `word_formation`
  - `multiple_choice`
  - `fill_in_blank`
  - `true_false`
- **difficulty_level**: Numeric value (1-5)
- **total_questions**: Number of questions in the quiz
- **time_limit_minutes**: Time limit in minutes
- **instructions**: Instructions shown to students
- **quiz_config**: JSON configuration string
- **is_active**: `true` or `false`

### Example Quiz Config JSON
```json
{
  "shuffle_questions": true,
  "show_feedback_immediately": false,
  "attempts_allowed": 1
}
```

## 2. Question Import Template

### CSV Structure
```csv
quiz_key,question_type_key,question_text,difficulty_level,order_index,metadata,is_active,option_a_key,option_a_text,option_a_correct,option_b_key,option_b_text,option_b_correct,option_c_key,option_c_text,option_c_correct,option_d_key,option_d_text,option_d_correct,explanation_type,explanation_text,explanation_metadata
```

### Field Descriptions
- **quiz_key**: Must match an existing quiz key
- **question_type_key**: Must match existing question type keys:
  - `fill_in_blank`
  - `multiple_choice`
  - `true_false`
- **question_text**: The question text (use `______` for fill-in-blank)
- **difficulty_level**: Numeric value (1-5)
- **order_index**: Position of question in quiz (1, 2, 3, etc.)
- **metadata**: JSON string with additional data
- **is_active**: `true` or `false`
- **option_x_key**: Option identifier (A, B, C, D)
- **option_x_text**: Option text content
- **option_x_correct**: `true` for correct answer, `false` for others
- **explanation_type**: Usually `correct_answer`
- **explanation_text**: Detailed explanation of the correct answer
- **explanation_metadata**: JSON string with explanation metadata

### Question Types

#### Fill-in-the-Blank
- Use `______` in question text where answer goes
- Provide 4 options with different word forms
- Example: "He is a very ______ person."

#### Multiple Choice
- Complete question with multiple answer choices
- Provide 4 options
- Example: "What is the capital of France?"

#### True/False
- Statement that can be true or false
- Only use options A and B (True/False)
- Leave options C and D empty

### Example Question Metadata JSON
```json
{
  "focus": "Adjective suffix -ive",
  "word_family": "create"
}
```

### Example Explanation Metadata JSON
```json
{
  "option_key": "B",
  "grammar_point": "adjective_formation"
}
```

## 3. SQL Import Template

### Structure
The SQL template follows the same structure as your existing files:

1. **Quiz Insert**: Creates the quiz record
2. **Questions Insert**: Creates all questions for the quiz
3. **Options Insert**: Creates 4 options per question
4. **Explanations Insert**: Creates explanations for each question

### Key Points
- Uses `uuid_generate_v4()` for unique IDs
- References foreign keys using subqueries
- Maintains proper order_index for questions
- Maintains proper sort_order for options
- Includes comprehensive metadata

### Foreign Key References
```sql
-- Category reference
(SELECT id FROM public.categories WHERE key = 'word_formation')

-- Level reference  
(SELECT id FROM public.levels WHERE key = 'A2')

-- Quiz type reference
(SELECT id FROM public.quiz_types WHERE key = 'word_formation')

-- Question type reference
(SELECT id FROM public.question_types WHERE key = 'fill_in_blank')
```

## Usage Instructions

### 1. Download Templates
1. Go to Bulk Import page
2. Select import type (Quiz, Question, or SQL)
3. Click "Download Template"
4. Open the downloaded file

### 2. Fill in Your Data
- **CSV**: Edit in Excel, Google Sheets, or text editor
- **SQL**: Edit in any text editor or SQL tool

### 3. Upload and Import
1. Select the same import type you downloaded
2. Upload your completed file
3. Click "Import" to process
4. Review results and fix any errors

## Best Practices

### Data Validation
- Ensure all foreign key references exist
- Use unique quiz keys
- Maintain sequential order_index for questions
- Verify JSON syntax in config fields

### Error Handling
- Check import results for errors
- Fix validation issues before re-importing
- Test with small batches first

### Performance
- Import quizzes before questions
- Use batch imports for large datasets
- Monitor database performance during imports

## Troubleshooting

### Common Errors
1. **Foreign key not found**: Check category_key, level_key, quiz_type_key
2. **Duplicate key**: Use unique quiz keys
3. **JSON syntax error**: Validate JSON in config fields
4. **Missing required fields**: Ensure all required fields are filled

### Support
- Check validation messages in import results
- Use templates as reference for correct format
- Test imports with sample data first
