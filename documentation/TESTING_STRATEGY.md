# Unit Testing Strategy & Action Plan

> **📍 Location**: This document is part of the centralized documentation in `/documentation/`. 
> **See Also**: [DEVELOPMENT_STANDARDS.md](DEVELOPMENT_STANDARDS.md), [TECHNICAL_DEBT_IMPLEMENTATION_GUIDE.md](TECHNICAL_DEBT_IMPLEMENTATION_GUIDE.md)

This document outlines the strategy and action plan for implementing a comprehensive unit testing suite for the `wf-shared` project. Its purpose is to provide a clear roadmap, track progress, and serve as a reference to resume work at any point.

## 1. Current State

The `wf-shared` project currently lacks a unit testing framework and has no test coverage. This plan addresses this by introducing `vitest` for testing and systematically adding tests to the codebase.

## 2. Testing Framework & Tooling

-   **Framework:** [Vitest](https://vitest.dev/) will be used for its speed, modern features, and seamless integration with TypeScript.
-   **Coverage:** Coverage reports will be generated to track progress and identify untested code paths.

## 3. Action Plan

This plan is divided into phases to ensure a structured and incremental approach.

### Phase 1: Setup and Initial Configuration

-   [x] **Install Dependencies:** Install `vitest` and required dependencies.
-   [x] **Configure Vitest:** Create `vitest.config.ts` to define the testing environment, including TypeScript path aliases.
-   [x] **Update `package.json`:** Add `test` and `test:coverage` scripts.

### Phase 2: Initial Test Suite for `src/utils`

This phase focuses on the `src/utils` directory, as it contains pure functions that are ideal candidates for unit testing.

-   [x] **`src/utils/math.ts`**
    -   [x] Test `sum` with positive numbers, negative numbers, and zero.
    -   [x] Test `subtract` with various number combinations.
-   [x] **`src/utils/dates.ts`**
    -   [x] Test `formatDate` with different date objects and format strings.
    -   [x] Test `isFutureDate` with dates in the future, past, and present.
-   [x] **`src/utils/validation.ts`**
    -   [x] Test `isValidEmail` with valid and invalid email formats.
    -   [x] Test `isStrongPassword` with passwords of varying strength.
-   [x] **`src/utils/css.ts`**
    -   [x] Test `cn` with different class name combinations.
-   [x] **`src/utils/errorHandling.ts`**
    -   [x] Test error handling utilities to ensure they process errors correctly.
-   [x] **`src/utils/formatting.ts`**
    -   [x] Test formatting functions with various inputs.
-   [x] **`src/utils/ids.ts`**
    -   [x] Test ID generation functions for uniqueness and format.
-   [x] **`src/utils/performance.ts`**
    -   [x] Test performance measurement utilities.

### Phase 3: Testing `src/services`

Testing services will require mocking dependencies to isolate business logic.

-   [x] **`src/services/CacheManager.ts`**
    -   [x] Test caching and retrieval logic.
-   [x] **`src/services/CircuitBreaker.ts`**
    -   [x] Test circuit breaker state transitions.
-   [x] **`src/services/HealthMonitor.ts`**
    -   [x] Test health check monitoring.

### Phase 4: Integration Testing for `src/repositories`

Repositories interact with external databases and are better suited for integration tests. This phase will be planned in more detail after the completion of the unit testing phases.

-   [x] **Define Strategy:** Outline a strategy for integration testing `src/repositories`.
    -   **Environment Setup:** Use a dedicated test database instance (e.g., local Supabase or separate test project).
    -   **Test Data Management:** Implement `beforeAll`/`afterAll` hooks for seeding and cleaning up test data.
    -   **Testing Scope:** Focus on CRUD operations, data persistence, retrieval accuracy, edge cases, and error handling.
    -   **Tooling:** Continue using `vitest` and Supabase client directly.
-   [ ] **Implement Tests:** Write integration tests for repository methods.

## 4. How to Run Tests

-   **Run all tests:** `npm test`
-   **Generate coverage report:** `npm run test:coverage`

---
*This document will be updated as the project evolves and new testing requirements emerge.*
