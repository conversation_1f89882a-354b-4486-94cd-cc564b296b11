# Service Architecture Patterns

> **📍 Location**: This document is part of the centralized documentation in `/documentation/`. 
> **See Also**: [SERVICE_MIXINS_GUIDE.md](SERVICE_MIXINS_GUIDE.md), [DEVELOPMENT_STANDARDS.md](DEVELOPMENT_STANDARDS.md)

This guide covers the refactored service layer with **dependency injection** and **instance-based architecture** for better testability and maintainability.

## Structure

### Core Components
- **`BaseService.ts`** - Abstract base class with common CRUD operations and database injection
- **`ServiceContainer.ts`** - Dependency injection container managing service instances
- **`IDatabase`** - Interface for database abstraction (enables mocking)

### Domain Services
- **`UserService.ts`** - User management operations
- **`QuizService.ts`** - Quiz management operations  
- **`QuestionService.ts`** - Question, options, and explanations management
- **`ReferenceService.ts`** - Categories, levels, and quiz types
- **`ValidationService.ts`** - Content validation and issue detection
- **`DashboardService.ts`** - Dashboard stats and analytics

### Testing
- **`__tests__/UserService.test.ts`** - Example unit tests showing mocking capabilities

## Usage

### Recommended: Service Container
```typescript
import { serviceContainer } from '@/services'

// In handlers or components
const users = await serviceContainer.userService.getUsers({ page: 1, pageSize: 10 })
const quizzes = await serviceContainer.quizService.getQuizzes({ page: 1, pageSize: 10 })
```

### Advanced: Direct Instantiation
```typescript
import { UserService } from '@/services'

// Create service instance (uses default database)
const userService = new UserService()
const users = await userService.getUsers({ page: 1, pageSize: 10 })
```

### Testing: Mock Database
```typescript
import { UserService, ServiceContainer } from '@/services'

// Create mock database
const mockDb = new MockDatabase()

// Option 1: Direct injection
const userService = new UserService(mockDb)

// Option 2: Test container
const testContainer = ServiceContainer.createTestInstance(mockDb)
const userService = testContainer.userService
```

## Benefits

1. **Dependency Injection** - Easy to mock databases and external dependencies
2. **Instance Methods** - Better object-oriented design and testability
3. **Interface Segregation** - `IDatabase` interface allows multiple database implementations
4. **Singleton Pattern** - ServiceContainer manages instances efficiently
5. **Mock-Friendly** - Easy to create test doubles and stubs
6. **Type Safety** - Full TypeScript support with proper interfaces
7. **Memory Efficiency** - Lazy loading of service instances

## Testing Benefits

### Before (Static Methods)
```typescript
// Hard to test - can't mock static methods easily
test('should get users', () => {
  // UserService.getUsers() calls real database
  // No way to inject mock database
})
```

### After (Instance Methods)
```typescript
// Easy to test - can inject mock database
test('should get users', () => {
  const mockDb = new MockDatabase()
  const userService = new UserService(mockDb)
  
  // Test with complete control over database responses
  mockDb.setMockData('users', [{ id: '1', name: 'Test User' }])
  const result = await userService.getUsers({ page: 1, pageSize: 10 })
  
  expect(result.data).toHaveLength(1)
})
```

## Migration Status

- ✅ **BaseService** - Refactored to instance methods with DI
- ✅ **UserService** - Updated to instance methods
- ✅ **ServiceContainer** - Created for dependency management
- ✅ **Example Tests** - Demonstrating mock capabilities
- 🔄 **Other Services** - In progress (QuizService partially updated)
- 📋 **Handlers** - UserManagement updated, others need migration

## Architecture Principles

1. **SOLID Principles** - Single responsibility, dependency inversion
2. **Testability First** - Every service can be unit tested in isolation
3. **Clean Architecture** - Clear separation between data access and business logic
4. **Dependency Injection** - Services receive their dependencies rather than creating them