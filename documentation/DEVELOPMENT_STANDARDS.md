# Development Standards

> Comprehensive coding guidelines and development practices for the Word Formation Quiz Platform

## 🤖 FOR CLAUDE: MANDATORY WORKFLOW

### **BEFORE ANY CODE WORK:**
1. ✅ **ALWAYS verify** TypeScript compilation with `npx tsc --noEmit`
2. ✅ **ALWAYS check** ESLint compliance with `npm run lint`
3. ✅ **ALWAYS follow** 3-file pattern: `.tsx` (UI only) → `.handler.ts` (logic) → `.module.css` (optional)
4. ✅ **ALWAYS use** service layer - NO direct API calls in components
5. ✅ **FOR DATABASE WORK** - ALWAYS use Supabase MCP tools for schema inspection, data queries, and database operations

### **NON-NEGOTIABLES (MUST STOP IF VIOLATED):**
- ❌ **NEVER** use `any` types - Use proper TypeScript
- ❌ **NEVER** allow TypeScript compilation errors
- ❌ **NEVER** put business logic in .tsx files
- ❌ **NEVER** make direct API calls from components
- ❌ **NEVER** violate import order conventions
- ✅ **ALWAYS** use handler hooks for state management
- ✅ **ALWAYS** follow 3-file pattern for components
- ✅ **ALWAYS** include proper error handling

### **SUCCESS CRITERIA FOR EVERY TASK:**
- Zero TypeScript errors: `npx tsc --noEmit`
- All components follow 3-file pattern
- Business logic in `.handler.ts` files only
- Service layer used for all API calls
- ESLint passes: `npm run lint`
- Tests passing: `npm test`

## 🎯 Core Development Principles

### 1. Type Safety First
- **TypeScript strict mode** enabled across all projects
- **No `any` types** - Use proper TypeScript interfaces
- **Interface-driven development** for all data structures
- **Generic types** for reusable components and utilities

### 2. Component Architecture
- **3-file pattern** mandatory for complex components
- **Single responsibility** principle per file
- **Pure UI components** with separated logic
- **Composable patterns** using CSS modules composition

### 3. Code Quality Gates
- **ESLint + Prettier** with pre-commit hooks
- **TypeScript compilation** must pass before commits
- **Test coverage** for critical business logic
- **Bundle analysis** for security validation

## 📁 File Organization Standards

### 3-File Component Pattern
Every complex component MUST follow this structure:

```
ComponentName/
├── ComponentName.tsx          # Pure UI rendering only
├── ComponentName.handler.ts   # State management and logic
├── ComponentName.module.css   # Component-specific styles (optional)
└── index.ts                   # Clean exports
```

**Quick Setup with Templates:**
```bash
# Copy templates for new component
cp .claude/templates/component.tsx src/pages/NewPage/NewPage.tsx
cp .claude/templates/handler.ts src/pages/NewPage/NewPage.handler.ts
cp .claude/templates/style.css src/pages/NewPage/NewPage.module.css
cp .claude/templates/index.ts src/pages/NewPage/index.ts

# Templates include:
# ✅ Proper import order and wf-shared integration
# ✅ CSS modules with composition patterns
# ✅ Handler hooks with service layer patterns
# ✅ TypeScript interfaces and error handling
```

#### Example Implementation:
```typescript
// QuizScreen.tsx - UI Only
import { useQuizScreenHandler } from './QuizScreen.handler';
import styles from './QuizScreen.module.css';

export default function QuizScreen() {
  const { 
    quiz, 
    currentQuestion, 
    handleAnswer, 
    loading 
  } = useQuizScreenHandler();

  if (loading) return <LoadingState />;

  return (
    <div className={styles.container}>
      <QuizQuestion 
        question={currentQuestion}
        onAnswer={handleAnswer}
      />
    </div>
  );
}

// QuizScreen.handler.ts - Logic Only  
export const useQuizScreenHandler = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { data: quiz, isLoading } = useQuizQuery();
  
  const handleAnswer = useCallback((answer: string) => {
    // Business logic here - NO UI concerns
    saveAnswer(currentIndex, answer);
    setCurrentIndex(prev => prev + 1);
  }, [currentIndex]);

  return {
    quiz,
    currentQuestion: quiz?.questions[currentIndex],
    handleAnswer,
    loading: isLoading
  };
};

// QuizScreen.module.css - Styles Only
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: theme('spacing.6');
}
```

### Service Layer Architecture
```typescript
// Service Pattern - Business Logic
export class QuizService extends BaseService {
  constructor(private repository: QuizRepository) {
    super();
  }

  async getQuizByLevel(level: string): Promise<ServiceResponse<Quiz>> {
    try {
      const quiz = await this.repository.findByLevel(level);
      return this.createSuccessResponse(quiz);
    } catch (error) {
      return this.createErrorResponse(error);
    }
  }
}

// Repository Pattern - Data Access
export class QuizRepository extends BaseRepository {
  async findByLevel(level: string): Promise<Quiz> {
    const { data, error } = await this.supabase
      .from('quizzes')
      .select(`
        *,
        questions(*, question_options(*)),
        level:levels(*)
      `)
      .eq('level_id', level)
      .single();

    if (error) throw this.handleDatabaseError(error);
    return this.transformQuizData(data);
  }
}

// Handler Pattern - Component Logic
export const useQuizHandler = (quizId: string) => {
  const quizService = useService(QuizService);
  
  const { data: quiz, error, isLoading } = useQuery({
    queryKey: ['quiz', quizId],
    queryFn: () => quizService.getQuizById(quizId)
  });

  return { quiz, error, loading: isLoading };
};
```

## 🎨 CSS Architecture Standards

### CSS Modules Composition
```css
/* Base patterns compose together for DRY principles */
.primaryButton {
  composes: buttonBase from '../shared/components/buttons.module.css';
  composes: transitionColors from '../shared/animations/transitions.module.css';
  background-color: theme('colors.blue.600');
}

.primaryButton:hover {
  background-color: theme('colors.blue.700');
}

/* Dark mode support */
:global(.dark) .primaryButton {
  background-color: theme('colors.blue.500');
}
```

### Shared Modules Usage
```typescript
// Import shared patterns first
import { buttonStyles, cardStyles } from '@/styles/shared';
import componentStyles from './Component.module.css';

// Combine shared and component-specific styles
<button className={buttonStyles.buttonPrimary}>
  Primary Action
</button>

<div className={cardStyles.cardInteractive}>
  <div className={componentStyles.customContent}>
    Component-specific styling
  </div>
</div>
```

### CSS Organization Hierarchy
1. **Shared modules** - Use existing patterns
2. **Component-specific** - Only when shared patterns insufficient  
3. **Tailwind utilities** - For one-off adjustments
4. **Inline styles** - Avoid except for dynamic values

## 🔧 TypeScript Standards

### Interface Design
```typescript
// Database entity interfaces
interface DbQuiz {
  id: string;
  title: string;
  level_id: string;
  created_at: string;
  updated_at: string;
}

// Application domain interfaces  
interface Quiz {
  id: string;
  title: string;
  level: Level;
  questions: Question[];
  metadata: QuizMetadata;
}

// Component props interfaces
interface QuizScreenProps {
  quizId: string;
  onComplete: (result: QuizResult) => void;
  className?: string;
}

// Handler return interfaces
interface QuizScreenHandler {
  quiz: Quiz | null;
  currentQuestion: Question | null;
  progress: number;
  loading: boolean;
  error: string | null;
  handleAnswer: (answer: string) => void;
  handleNext: () => void;
  handlePrevious: () => void;
}
```

### Error Handling Patterns
```typescript
// Service layer error handling
interface ServiceResponse<T> {
  data: T | null;
  error: ServiceError | null;
}

interface ServiceError {
  message: string;
  type: 'network' | 'validation' | 'authentication' | 'server';
  code?: string;
  retryable: boolean;
}

// Component error boundaries
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return function WrappedComponent(props: P) {
    return (
      <ErrorBoundary fallback={<ErrorFallback />}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
};
```

### Generic Type Patterns
```typescript
// Repository base class with generics
abstract class BaseRepository<T, CreateT = Partial<T>, UpdateT = Partial<T>> {
  abstract findById(id: string): Promise<T>;
  abstract create(data: CreateT): Promise<T>;
  abstract update(id: string, data: UpdateT): Promise<T>;
  abstract delete(id: string): Promise<void>;
}

// Service response with generics
interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}
```

## 📝 Naming Conventions

### Variable Naming Standards

#### Error Variables
Use descriptive names without underscore prefixes for active variables:

```typescript
// ✅ GOOD - Consistent descriptive naming
const { data: userData, error: userError } = await api.getUser();
const { data: quizData, error: quizError } = await api.getQuiz();
const { data: questionsData, error: questionsError } = await api.getQuestions();

if (userError) {
  return { success: false, error: userError.message };
}

// ❌ BAD - Inconsistent naming
const { data: userData, error: _userError } = await api.getUser();
const { data: quizData, error: quizError } = await api.getQuiz();
```

#### Data Variables
Use descriptive names that indicate the type of data:

```typescript
// ✅ GOOD - Clear, descriptive names
const { data: userData } = await userApi.getProfile();
const { data: quizzes } = await quizApi.getAll();
const { data: questions } = await quizApi.getQuestions();
const { data: categories } = await quizApi.getCategories();

// ❌ BAD - Generic names that don't indicate content
const { data } = await userApi.getProfile();
const { data: data1 } = await quizApi.getAll();
```

#### Generic Patterns
```typescript
// Error naming pattern: [context]Error
const userError = new Error('User not found');
const authError = new Error('Authentication failed');
const quizError = new Error('Quiz not found');

// Data naming pattern: [context]Data or [context] (plural for collections)
const userData = { id: '1', name: 'John' };
const quizzes = [{ id: '1', title: 'Quiz 1' }];
const questions = [{ id: '1', text: 'Question 1' }];
```

### Function Naming Standards

#### Handlers
Use descriptive verb-noun combinations:

```typescript
// ✅ GOOD - Clear action and target
const handleAnswerSelect = (answer: string) => { };
const handleQuizSubmit = () => { };
const handleUserUpdate = (data: UserData) => { };

// ❌ BAD - Generic or unclear
const handleClick = () => { };
const handle = () => { };
const doSomething = () => { };
```

#### Services
Use clear action verbs:

```typescript
// ✅ GOOD - Clear action verbs
export const getUserProfile = async (id: string) => { };
export const updateUserProfile = async (id: string, data: UpdateData) => { };
export const deleteUser = async (id: string) => { };

// ❌ BAD - Unclear actions
export const user = async (id: string) => { };
export const manageUser = async (id: string, data: any) => { };
```

## 🧪 Testing Standards

### Unit Testing Pattern
```typescript
// Handler testing
describe('useQuizScreenHandler', () => {
  it('should handle answer selection correctly', () => {
    const { result } = renderHook(() => useQuizScreenHandler('quiz-1'));
    
    act(() => {
      result.current.handleAnswer('option-a');
    });
    
    expect(result.current.currentIndex).toBe(1);
    expect(mockSaveAnswer).toHaveBeenCalledWith(0, 'option-a');
  });
});

// Service testing
describe('QuizService', () => {
  it('should return quiz by level', async () => {
    const mockQuiz = createMockQuiz();
    mockRepository.findByLevel.mockResolvedValue(mockQuiz);
    
    const response = await quizService.getQuizByLevel('A1');
    
    expect(response.data).toEqual(mockQuiz);
    expect(response.error).toBeNull();
  });
});
```

### Component Testing Pattern
```typescript
// Component integration testing
describe('QuizScreen', () => {
  it('should display quiz questions correctly', async () => {
    render(<QuizScreen quizId="quiz-1" onComplete={mockOnComplete} />);
    
    await waitFor(() => {
      expect(screen.getByText('Question 1')).toBeInTheDocument();
    });
    
    const answerButton = screen.getByText('Option A');
    fireEvent.click(answerButton);
    
    expect(screen.getByText('Question 2')).toBeInTheDocument();
  });
});
```

## 🚀 Performance Standards

### Code Splitting Patterns
```typescript
// Route-based code splitting
const QuizScreen = lazy(() => import('../screens/QuizScreen'));
const ProfileScreen = lazy(() => import('../screens/ProfileScreen'));

// Component-based code splitting for large features
const AdminDashboard = lazy(() => 
  import('../pages/AdminDashboard').then(module => ({
    default: module.AdminDashboard
  }))
);
```

### Query Optimization
```typescript
// TanStack Query patterns
export const useQuizQuery = (quizId: string) => {
  return useQuery({
    queryKey: ['quiz', quizId],
    queryFn: () => quizService.getQuizById(quizId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      if (error.type === 'authentication') return false;
      return failureCount < 3;
    }
  });
};

// Prefetching for better UX
export const usePrefetchQuiz = () => {
  const queryClient = useQueryClient();
  
  return useCallback((quizId: string) => {
    queryClient.prefetchQuery({
      queryKey: ['quiz', quizId],
      queryFn: () => quizService.getQuizById(quizId)
    });
  }, [queryClient]);
};
```

### Bundle Optimization
```typescript
// Tree-shaking friendly imports
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

// NOT: import * as UI from '@/components/ui';

// Dynamic imports for large dependencies
const ChartComponent = lazy(() => 
  import('recharts').then(module => ({
    default: module.LineChart
  }))
);
```

## 🎨 UI Pattern Standards

### Loading States
Always provide loading states for async operations:

```typescript
if (isLoading) {
  return (
    <div className="animate-pulse space-y-4">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-16 bg-gray-200 rounded"></div>
      ))}
    </div>
  )
}
```

### Empty States
Provide helpful empty states with actions:

```typescript
{data?.length === 0 ? (
  <div className="text-center py-8">
    <Icon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
    <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Found</h3>
    <p className="text-gray-600">Helpful message about the empty state</p>
    <Button onClick={handleCreate}>Create New Item</Button>
  </div>
) : (
  // Regular content
)}
```

### Error Display
Consistent error handling with retry options:

```typescript
if (error) {
  return (
    <ErrorComponent 
      title="Error Loading Data"
      message={error instanceof Error ? error.message : 'Unexpected error'}
      onRetry={() => refetch()}
    />
  )
}
```

### Pagination Pattern
Standard pagination implementation:

```typescript
{totalPages > 1 && (
  <div className="mt-6 flex justify-center space-x-2">
    <Button 
      variant="outline" 
      disabled={page === 1}
      onClick={() => setPage(page - 1)}
    >
      Previous
    </Button>
    <span className="px-4 py-2 text-sm text-gray-600">
      Page {page} of {totalPages}
    </span>
    <Button 
      variant="outline" 
      disabled={page === totalPages}
      onClick={() => setPage(page + 1)}
    >
      Next
    </Button>
  </div>
)}
```

## 🔐 Security Standards

### Input Validation
```typescript
// Zod schema validation
const QuizSubmissionSchema = z.object({
  quizId: z.string().uuid(),
  answers: z.array(z.object({
    questionId: z.string().uuid(),
    selectedOption: z.string().min(1).max(1000),
    timeSpent: z.number().positive().max(3600)
  })).min(1).max(100)
});

// Component validation
export const useQuizSubmission = () => {
  const submitQuiz = useMutation({
    mutationFn: async (data: unknown) => {
      const validatedData = QuizSubmissionSchema.parse(data);
      return quizService.submitQuiz(validatedData);
    },
    onError: (error) => {
      if (error instanceof ZodError) {
        handleValidationError(error);
      }
    }
  });
  
  return { submitQuiz };
};
```

### Authentication Patterns
```typescript
// Protected route wrapper
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: UserRole
) => {
  return function AuthenticatedComponent(props: P) {
    const { user, loading } = useAuth();
    
    if (loading) return <LoadingScreen />;
    if (!user) return <Navigate to="/login" />;
    if (requiredRole && user.role !== requiredRole) {
      return <UnauthorizedScreen />;
    }
    
    return <Component {...props} />;
  };
};
```

## 🛠️ Development Workflow

### Pre-commit Checklist
```bash
# Mandatory checks before any commit
npx tsc --noEmit          # TypeScript compilation
npm run lint              # ESLint validation  
npm run test              # Unit tests
npm run build             # Production build test
```

### Git Commit Standards
```bash
# Commit message format
feat: add quiz submission functionality

- Implement QuizSubmission component with validation
- Add optimistic updates for better UX  
- Include error handling with retry logic
- Add comprehensive unit tests

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>
```

### Git Pre-commit Hooks Setup
For automated code quality enforcement, **new team members MUST install git pre-commit hooks:**

```bash
# 🚨 REQUIRED FOR ALL NEW DEVELOPERS
# Run this ONCE after cloning the repository
./.claude/setup-hooks.sh

# What this installer script does:
# 📁 Creates actual hook files in .git/hooks/ directory:
#    ├── .git/hooks/pre-commit           # Main hook coordinator
#    ├── .git/hooks/pre-commit-frontend  # wf-frontend validation
#    └── .git/hooks/pre-commit-admin     # wf-admin-portal validation

# What the installed hooks check:
# ✅ TypeScript compilation (npx tsc --noEmit)
# ✅ ESLint validation (npm run lint)
# ✅ Test execution (npm test)
# ✅ Coding standards (no 'any' types, no direct API calls in .tsx)

# After installation, hooks run automatically on 'git commit'
# Commits will be BLOCKED if quality checks fail
```

**📋 New Developer Onboarding Checklist:**
```bash
# 1. Clone repository
git clone <repo-url>
cd WordFormation

# 2. Install dependencies
cd wf-frontend && npm install
cd ../wf-admin-portal && npm install
cd ..

# 3. 🚨 CRITICAL: Install git hooks (prevents bad commits)
./.claude/setup-hooks.sh

# 4. Verify setup worked
ls -la .git/hooks/
# Should see: pre-commit, pre-commit-frontend, pre-commit-admin

# 5. Test hooks work
.git/hooks/pre-commit  # Should run validation checks
```

**Important Notes:**
- ❌ **The hooks are NOT tracked in git** (`.git/hooks/` is local only)
- ✅ **Every developer must run the installer** after cloning  
- 🔄 **Re-run installer** if project structure changes
- 📁 **`.claude/setup-hooks.sh`** is the installer template, **`.git/hooks/`** contains actual hooks

### Code Review Guidelines
1. **TypeScript**: No `any` types, proper interfaces
2. **Architecture**: 3-file pattern followed correctly
3. **Testing**: Critical paths covered with tests
4. **Performance**: No unnecessary re-renders or API calls
5. **Security**: Input validation and error handling
6. **Accessibility**: Proper ARIA labels and keyboard navigation

## 📋 Quality Checklists

### Component Development Checklist
- [ ] TypeScript interfaces defined for all props and state
- [ ] 3-file pattern implemented (if complex component)
- [ ] Error boundaries implemented for error handling
- [ ] Loading and empty states designed
- [ ] Accessibility attributes added (ARIA, keyboard nav)
- [ ] Responsive design tested on mobile/desktop
- [ ] Dark mode styling implemented
- [ ] Unit tests written for critical functionality
- [ ] Performance optimized (memoization, lazy loading)

### Feature Development Checklist  
- [ ] Database schema updated (if needed)
- [ ] API interfaces defined in wf-shared
- [ ] Repository pattern implemented
- [ ] Service layer with error handling
- [ ] Component handlers with proper state management
- [ ] Shared CSS modules used where possible
- [ ] Comprehensive testing (unit + integration)
- [ ] Bundle analysis confirms no security leaks
- [ ] Documentation updated

### Release Checklist
- [ ] All TypeScript compilation passes
- [ ] All tests pass (unit + integration)
- [ ] ESLint passes with no warnings
- [ ] Bundle analysis shows no admin code in frontend
- [ ] Performance metrics within acceptable ranges
- [ ] Security validation completed
- [ ] Documentation updated
- [ ] Migration scripts tested (if applicable)

## 📋 Development Workflow Checklists

### Pre-Development Checklist
**Run BEFORE starting any code task:**

#### 1. Rules & Standards Review
- [ ] Read current coding standards section relevant to task
- [ ] Review 3-file pattern requirements
- [ ] Check import order conventions
- [ ] Understand service layer patterns

#### 2. Current Code Status Check
- [ ] Verify existing file organization follows 3-file pattern
- [ ] Check no TypeScript errors exist: `npx tsc --noEmit`
- [ ] Confirm ESLint is working: `npm run lint`
- [ ] Verify tests are passing: `npm test`

#### 3. Environment Validation
- [ ] Correct directory (wf-frontend/ or wf-admin-portal/)
- [ ] Dependencies installed: `npm install`
- [ ] Environment variables configured
- [ ] Supabase connection working

### During Development Checklist

#### Code Quality Standards
- [ ] Follow import order: React → Libraries → UI → Services → Icons → Local
- [ ] Use proper TypeScript (no `any` types)
- [ ] Put ALL business logic in `.handler.ts` files only
- [ ] Use service layer for ALL API calls (never direct in .tsx)

#### Architecture Verification
- [ ] 3-file pattern maintained for all components
- [ ] No business logic in .tsx files
- [ ] Service layer used for all API calls
- [ ] Handler hooks properly structured
- [ ] Import order conventions followed

#### Testing & Validation
- [ ] Unit tests: `npm test`
- [ ] All tests passing
- [ ] Coverage maintained or improved
- [ ] Application builds: `npm run build`
- [ ] Manual testing of changed functionality
- [ ] No console errors in browser
- [ ] Responsive design maintained

### 🚨 Red Flags - Stop Development If Found

#### Immediate Blockers
- ❌ Any TypeScript compilation errors
- ❌ Direct API calls in .tsx files
- ❌ Business logic in .tsx files
- ❌ Use of `any` types
- ❌ ESLint errors
- ❌ Broken test suite

#### Architecture Violations
- ❌ Components not following 3-file pattern
- ❌ Handler hooks not returning organized objects
- ❌ Service layer bypassed
- ❌ Improper import order
- ❌ Missing error handling

### 📝 Quick Commands Reference

```bash
# Type checking
npx tsc --noEmit                    # Check TypeScript compilation
cd wf-frontend && npx tsc --noEmit  # Frontend only
cd wf-admin-portal && npx tsc --noEmit # Admin portal only

# Code quality
npm run lint                        # ESLint check
npm run lint:fix                    # Auto-fix ESLint issues

# Testing
npm test                           # Run all tests
npm run test:coverage              # Test with coverage
```

## 🔄 Maintenance Standards

### Dependency Management
```json
{
  "dependencies": {
    "react": "^18.0.0",
    "typescript": "^5.0.0"
  },
  "peerDependencies": {
    "wf-shared": "workspace:*"
  }
}
```

### Version Update Process
1. **Minor Updates**: Update regularly with testing
2. **Major Updates**: Plan migration with compatibility testing
3. **Security Updates**: Apply immediately with validation
4. **Peer Dependencies**: Coordinate updates across packages

---

**Standards Version**: 2.0  
**Last Updated**: 2025-07-24  
**Compliance Status**: Enforced ✅